import request from '@/utils/request'

// 查询再保理赔险种明细列表
export function listClaim(query) {
  return request({
    url: '/huida-reinsurance/dws/prp/claim/list',
    method: 'get',
    params: query
  })
}

// 查询再保理赔险种明细详细
export function getClaim(Id) {
  return request({
    url: '/huida-reinsurance/dws/prp/claim/' + Id,
    method: 'get'
  })
}

// 新增再保理赔险种明细
export function addClaim(data) {
  return request({
    url: '/huida-reinsurance/dws/prp/claim',
    method: 'post',
    data: data
  })
}

// 修改再保理赔险种明细
export function updateClaim(data) {
  return request({
    url: '/huida-reinsurance/dws/prp/claim',
    method: 'put',
    data: data
  })
}

// 删除再保理赔险种明细
export function delClaim(Id) {
  return request({
    url: '/huida-reinsurance/dws/prp/claim/' + Id,
    method: 'delete'
  })
}

// 推送再保理赔险种明细
export function pushClaim(data) {
  return request({
    url: '/huida-reinsurance/dws/prp/claim/push',
    method: 'post',
    data: data
  })
}

// 检查再保理赔险种明细是否存在
export function existsClaim(data) {
  return request({
    url: '/huida-reinsurance/dws/prp/claim/exists',
    method: 'post',
    data: data
  })
}

// 生成再保理赔险种明细数据
export function generateClaimData(data) {
  return request({
    url: '/huida-reinsurance/dws/prp/claim/generate',
    method: 'post',
    params: data
  })
}
