import request from '@/utils/request'

// 查询保单登记再保合同信息列表
export function listInsureCont(query) {
  return request({
    url: '/huida-reinsurance/dws/prp/insure/cont/list',
    method: 'get',
    params: query
  })
}

// 查询保单登记再保合同信息详细
export function getInsureCont(Id) {
  return request({
    url: '/huida-reinsurance/dws/prp/insure/cont/' + Id,
    method: 'get'
  })
}

// 新增保单登记再保合同信息
export function addInsureCont(data) {
  return request({
    url: '/huida-reinsurance/dws/prp/insure/cont',
    method: 'post',
    data: data
  })
}

// 修改保单登记再保合同信息
export function updateInsureCont(data) {
  return request({
    url: '/huida-reinsurance/dws/prp/insure/cont',
    method: 'put',
    data: data
  })
}

// 删除保单登记再保合同信息
export function delInsureCont(Id) {
  return request({
    url: '/huida-reinsurance/dws/prp/insure/cont/' + Id,
    method: 'delete'
  })
}

// 推送保单登记再保合同信息
export function pushInsureCont(data) {
  return request({
    url: '/huida-reinsurance/dws/prp/insure/cont/push',
    method: 'post',
    data: data
  })
}

// 检查保单登记再保合同信息是否存在
export function existsInsureCont(data) {
  return request({
    url: '/huida-reinsurance/dws/prp/insure/cont/exists',
    method: 'post',
    data: data
  })
}

// 生成再保合同信息表数据
export function generateInsureContData(params) {
  return request({
    url: '/huida-reinsurance/dws/prp/insure/cont/generate',
    method: 'post',
    params: params
  })
}
