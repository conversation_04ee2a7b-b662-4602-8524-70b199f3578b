import request from '@/utils/request'

// 查询保单登记再保产品信息列表
export function listProduct(query) {
  return request({
    url: '/huida-reinsurance/dws/prp/product/list',
    method: 'get',
    params: query
  })
}

// 查询保单登记再保产品信息详细
export function getProduct(Id) {
  return request({
    url: '/huida-reinsurance/dws/prp/product/' + Id,
    method: 'get'
  })
}

// 新增保单登记再保产品信息
export function addProduct(data) {
  return request({
    url: '/huida-reinsurance/dws/prp/product',
    method: 'post',
    data: data
  })
}

// 修改保单登记再保产品信息
export function updateProduct(data) {
  return request({
    url: '/huida-reinsurance/dws/prp/product',
    method: 'put',
    data: data
  })
}

// 删除保单登记再保产品信息
export function delProduct(Id) {
  return request({
    url: '/huida-reinsurance/dws/prp/product/' + Id,
    method: 'delete'
  })
}

// 推送保单登记再保产品信息
export function pushProduct(Ids) {
  return request({
    url: '/huida-reinsurance/dws/prp/product/push/' + Ids,
    method: 'get'
  })
}

// 检查保单登记再保产品信息是否存在
export function existsProduct(data) {
  return request({
    url: '/huida-reinsurance/dws/prp/product/exists',
    method: 'post',
    data: data
  })
}

// 生成再保产品信息表数据
export function generateProduct(params) {
  return request({
    url: '/huida-reinsurance/dws/prp/product/generate',
    method: 'post',
    params: params
  })
}
