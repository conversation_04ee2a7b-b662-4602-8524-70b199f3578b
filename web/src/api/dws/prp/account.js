import request from '@/utils/request'

// 查询保单登记再保账单信息列表
export function listAccount(query) {
  return request({
    url: '/huida-reinsurance/dws/prp/account/list',
    method: 'get',
    params: query
  })
}

// 查询保单登记再保账单信息详细
export function getAccount(Id) {
  return request({
    url: '/huida-reinsurance/dws/prp/account/' + Id,
    method: 'get'
  })
}

// 新增保单登记再保账单信息
export function addAccount(data) {
  return request({
    url: '/huida-reinsurance/dws/prp/account',
    method: 'post',
    data: data
  })
}

// 修改保单登记再保账单信息
export function updateAccount(data) {
  return request({
    url: '/huida-reinsurance/dws/prp/account',
    method: 'put',
    data: data
  })
}

// 删除保单登记再保账单信息
export function delAccount(Id) {
  return request({
    url: '/huida-reinsurance/dws/prp/account/' + Id,
    method: 'delete'
  })
}

// 推送保单登记再保账单信息
export function pushAccount(data) {
  return request({
    url: '/huida-reinsurance/dws/prp/account/push',
    method: 'post',
    data: data
  })
}

// 检查保单登记再保账单信息是否存在
export function existsAccount(data) {
  return request({
    url: '/huida-reinsurance/dws/prp/account/exists',
    method: 'post',
    data: data
  })
}

// 生成再保账单信息表数据
export function generateAccountData(params) {
  return request({
    url: '/huida-reinsurance/dws/prp/account/generate',
    method: 'post',
    params: params
  })
}
