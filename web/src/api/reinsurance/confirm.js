import request from "@/utils/request";

// 账单列表
export function billList(params) {
  return request({
    url: "/huida-reinsurance/settle/bill/list",
    method: "get",
    params,
  });
}

export function billAdd(data) {
  return request({
    url: "/huida-reinsurance/settle/bill",
    method: "post",
    data,
  });
}

export function billEdit(data) {
  return request({
    url: "/huida-reinsurance/settle/bill",
    method: "put",
    data,
  });
}

export function billExport(params) {
  const pathSegments = params.map((item) => encodeURIComponent(item)).join(",");
  return request({
    url: "/huida-reinsurance/settle/bill/export/detail/" + pathSegments,
    method: "get",
    timeout: 100000,
    responseType: "blob",
  });
}
