import request from '@/utils/request'

// 查询再保报表列表
export function listReport(query) {
  return request({
    url: '/huida-reinsurance/reinsurance/report/template/list',
    method: 'get',
    params: query
  })
}
// 查询再保报表列表
export function listReportReinsurance(query) {
  return request({
    url: '/huida-reinsurance/reinsurance/report/list',
    method: 'get',
    params: query
  })
}

// 查询再保报表详细
export function getReport(id) {
  return request({
    url: '/huida-reinsurance/reinsurance/report/template/' + id,
    method: 'get'
  })
}

// 新增再保报表
export function addReport(data) {
  return request({
    url: '/huida-reinsurance/reinsurance/report/template',
    method: 'post',
    data: data
  })
}
// 新增再保报表
export function ReportStatistics(data) {
  return request({
    url: '/huida-reinsurance/reinsurance/report/statistics',
    method: 'post',
    data: data
  })
}

// 修改再保报表
export function updateReport(data) {
  return request({
    url: '/huida-reinsurance/reinsurance/report/template',
    method: 'put',
    data: data
  })
}
// 修改再保报表
export function columnReport() {
  return request({
    url: '/huida-reinsurance/reinsurance/report/template/column',
    method: 'get',
  })
}

// 删除再保报表
export function delReport(id) {
  return request({
    url: '/huida-reinsurance/reinsurance/report/template/' + id,
    method: 'delete'
  })
}

//删除
export function deleteReinsuranceReportData(id) {
  return request({
    url: 'huida-reinsurance/reinsurance/businessReport/reportDelete/' + id,
    method: 'delete'
  })
}
