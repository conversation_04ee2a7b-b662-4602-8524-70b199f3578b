import request from "@/utils/request";

// 查询监管报表列表
export function listReport(params) {
  return request({
    url: "/huida-reinsurance/regulatory/report/list",
    method: "get",
    params,
  });
}

// 生成监管报表数据前校验
export function checkData(data) {
  return request({
    url: "/huida-reinsurance/regulatory/report/exists",
    method: "post",
    data,
  });
}

// 生成监管报表数据
export function createData(data) {
  return request({
    url: "/huida-reinsurance/regulatory/report",
    method: "post",
    data,
  });
}

// 查询East再保账单信息列表
export function listEastBill(params) {
  return request({
    url: "/huida-reinsurance/regulatory/report/bill/east/list",
    method: "get",
    params,
  });
}

// 查询East再保产品信息列表
export function listEastProduct(params) {
  return request({
    url: "/huida-reinsurance/regulatory/report/product/east/list",
    method: "get",
    params,
  });
}

// 查询East再保合同信息列表
export function listEastContract(params) {
  return request({
    url: "/huida-reinsurance/regulatory/report/contract/east/list",
    method: "get",
    params,
  });
}

// 删除East报表数据
export function deleteEastData(reportCode, id) {
  return request({
    url: `/huida-reinsurance/regulatory/report/remove/east/${reportCode}/${id}`,
    method: "delete",
  });
}

// 推动East报表数据
export function pushEastData(reportCode, ids) {
  return request({
    url: `/huida-reinsurance/regulatory/report/push/east/${reportCode}/${ids}`,
    method: "get",
  });
}



// 查询保单登记再保账单信息列表
export function listPrpAccount(params) {
  return request({
    url: "/huida-reinsurance/regulatory/report/account/prp/list",
    method: "get",
    params,
  });
}

// 查询保单登记再保产品信息列表
export function listPrpProduct(params) {
  return request({
    url: "/huida-reinsurance/regulatory/report/product/prp/list",
    method: "get",
    params,
  });
}

// 查询保单登记再保合同信息列表
export function listPrpContract(params) {
  return request({
    url: "/huida-reinsurance/regulatory/report/contract/prp/list",
    method: "get",
    params,
  });
}

// 删除保单登记报表数据
export function deletePrpData(reportCode, id) {
  return request({
    url: `/huida-reinsurance/regulatory/report/remove/prp/${reportCode}/${id}`,
    method: "delete",
  });
}

// 推送保单登记报表数据
export function pushPrpData(reportCode, ids) {
  return request({
    url: `/huida-reinsurance/regulatory/report/push/prp/${reportCode}/${ids}`,
    method: "get",
  });
}
