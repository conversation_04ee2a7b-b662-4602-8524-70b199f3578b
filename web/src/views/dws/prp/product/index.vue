<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      label-width="100px"
    >
      <el-form-item label="推送状态" prop="PushStatus">
        <el-select
          v-model="queryParams.PushStatus"
          placeholder="推送状态"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.regulator_report_push_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="推送日期" prop="PushDate">
        <el-date-picker
          v-model="queryParams.PushDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="date"
          placeholder="推送日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="small"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="small" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <div style="margin-bottom: 10px">
      <el-button
        type="success"
        plain
        icon="el-icon-upload2"
        size="small"
        @click="importConfig.visible = true"
        >人工导入LRProduct表</el-button
      >
      <el-button
        type="warning"
        plain
        icon="el-icon-download"
        size="small"
        @click="handleExport"
        >导出LRProduct表</el-button
      >
      <el-button type="primary" plain size="small" @click="handlePush"
        >推送</el-button
      >
    </div>

    <el-table
      v-loading="loading"
      :data="reportList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" align="center" fixed="left" />
      <el-table-column label="序号" type="index" width="80" align="center">
        <template slot-scope="scope">
          <span>{{
            queryParams.pageSize * (queryParams.pageNum - 1) + scope.$index + 1
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="交易编码"
        align="center"
        prop="TransactionNo"
        width="250px"
        show-overflow-tooltip
      />
      <el-table-column
        label="保险机构代码"
        align="center"
        prop="CompanyCode"
        width="120px"
      />
      <el-table-column
        label="再保险公司代码"
        align="center"
        prop="ReinsurerCode"
        width="150px"
      />
      <el-table-column
        label="再保险公司名称"
        align="center"
        prop="ReinsurerName"
        width="250px"
        show-overflow-tooltip
      />
      <el-table-column
        label="再保险合同号码"
        align="center"
        prop="ReInsuranceContNo"
        width="150px"
      />
      <el-table-column
        label="再保险合同名称"
        align="center"
        prop="ReInsuranceContName"
        width="200px"
        show-overflow-tooltip
      />
      <el-table-column
        label="合同附约类型"
        align="center"
        prop="ContOrAmendmentType"
        width="120px"
      >
        <template slot-scope="scope">
          <dict-tag :options="dict.type.prp_contract_type" :value="scope.row.ContOrAmendmentType"/>
        </template>
      </el-table-column>
      <el-table-column
        label="再保险附约主合同号"
        align="center"
        prop="MainReInsuranceContNo"
        width="200px"
        show-overflow-tooltip
      />
      <el-table-column
        label="产品编码"
        align="center"
        prop="ProductCode"
        width="150px"
      />
      <el-table-column
        label="产品名称"
        align="center"
        prop="ProductName"
        width="200px"
        show-overflow-tooltip
      />
      <el-table-column
        label="团个性质"
        align="center"
        prop="GPFlag"
        width="150px"
      >
        <template slot-scope="scope">
          <dict-tag :options="dict.type.prp_gp_flag" :value="scope.row.GPFlag"/>
        </template>
      </el-table-column>
      <el-table-column label="险类代码" align="center" prop="ProductType" width="150px" />
      <el-table-column
        label="责任代码"
        align="center"
        prop="LiabilityCode"
        width="150px"
      />
      <el-table-column
        label="责任名称"
        align="center"
        prop="LiabilityName"
        width="150px"
      />
      <el-table-column
        label="保险期限类型"
        align="center"
        prop="TermType"
        width="150px"
      >
        <template slot-scope="scope">
          <dict-tag :options="dict.type.prp_period_type" :value="scope.row.TermType"/>
        </template>
      </el-table-column>
      <el-table-column
        label="分保方式"
        align="center"
        prop="ReinsurMode"
        width="200px"
      >
        <template slot-scope="scope">
          <dict-tag :options="dict.type.prp_reinsu_mode" :value="scope.row.ReinsurMode"/>
        </template>
      </el-table-column>
      <el-table-column label="自留额" align="center" prop="RetentionAmount" width="150px" />
      <el-table-column
        label="自留比例"
        align="center"
        prop="RetentionPercentage"
        width="150px"
      />
      <el-table-column
        label="分保比例"
        align="center"
        prop="QuotaSharePercentage"
        width="150px"
      />
      <el-table-column
        label="再保人参与份额比例"
        align="center"
        prop="ReinsuranceShare"
        width="150px"
      />
      <el-table-column
        label="所属年份"
        align="center"
        prop="ReportYear"
        width="120px"
      />
      <el-table-column
        label="所属月份"
        align="center"
        prop="ReportMonth"
        width="120px"
      />
      <el-table-column
        label="数据来源"
        align="center"
        prop="DataSource"
        width="120px"
      >
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.regulator_report_data_source"
            :value="scope.row.DataSource"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="推送状态"
        align="center"
        prop="PushStatus"
        width="120px"
      >
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.regulator_report_push_status"
            :value="scope.row.PushStatus"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="推送日期"
        align="center"
        prop="PushDate"
        width="120px"
      />
      <el-table-column
        label="推送人"
        align="center"
        prop="PushBy"
        width="120px"
      />
      <el-table-column label="操作" align="center" width="80px" fixed="right">
        <template slot-scope="scope">
          <el-popconfirm
            title="确认删除该条数据？"
            @confirm="handleDelete(scope.row)"
          >
            <el-button
              :disabled="scope.row.PushStatus == 1"
              type="text"
              style="color: #f56c6c"
              size="small"
              slot="reference"
              >删除</el-button
            >
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <ImportExcel
      :config="importConfig"
      @close="importConfig.visible = false"
      @uploadEnd="uploadEnd"
    />
  </div>
</template>

<script>
import {
  listPrpProduct,
  deletePrpData,
  pushPrpData,
} from "@/api/reinsurance/regulatoryReport.js";
import ImportExcel from "@/components/ImportExcel";

export default {
  name: "ProductInformation",
  dicts: [
    "regulator_report_push_status",
    "regulator_report_data_source",
    "prp_contract_type",
    "prp_gp_flag",
    "prp_reinsu_mode",
    "prp_reinsu_class",
    "prp_period_type",
  ],
  components: { ImportExcel },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 字典表格数据
      reportList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        PushStatus: undefined,
        PushDate: undefined,
        ReportYear: undefined,
      },
      ids: [],
      importVisible: true,
      importConfig: {
        accept: ".xls, .xlsx",
        tips: "仅允许导入xls 、xlsx格式文件。",
        title: "导入LRProduct信息",
        visible: false,
        importUrl: "/huida-reinsurance/regulatory/report/import/prp/1",
      },
    };
  },
  created() {
    this.queryParams.ReportYear = this.$route.query.reportYear;
    this.handleQuery();
  },
  activated() {
    if (this.$route.query.reportYear !== this.queryParams.ReportYear) {
      this.queryParams.ReportYear = this.$route.query.reportYear;
      this.handleQuery();
    }
  },
  methods: {
    handlePush() {
      if (this.ids.length === 0) {
        this.$message({
          message: "请选择要推送的数据",
          type: "error",
        });
        return;
      }
      let arr = [];
      for (let item of this.ids) {
        if (item.PushStatus === 1) {
          this.$message({
            message: "存在已推送的数据，请重新选择！",
            type: "error",
          });
          return;
        } else {
          arr.push(item.Id);
        }
      }
      pushPrpData(1, arr.join(",")).then((res) => {
        this.$message({
          message: "推送成功",
          type: "success",
        });
        this.getList();
      });
    },
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item);
    },
    uploadEnd(res) {
      this.handleQuery();
      this.importConfig.visible = false;
    },
    handleDelete(data) {
      deletePrpData(1, data.Id).then((response) => {
        this.$message({
          message: "删除成功",
          type: "success",
        });
        this.getList();
      });
    },
    /** 查询字典类型列表 */
    getList() {
      this.loading = true;
      listPrpProduct(this.queryParams).then((response) => {
        this.reportList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    handleExport() {
      this.download(
        "huida-reinsurance/regulatory/report/export/prp/1",
        {
          ...this.queryParams,
        },
        `LRProduct表_${new Date().getTime()}.xlsx`
      );
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
  },
};
</script>
