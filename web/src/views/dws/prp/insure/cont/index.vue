<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      label-width="120px"
    >
      <el-form-item label="流水号" prop="TransactionNo">
        <el-input
          v-model="queryParams.TransactionNo"
          placeholder="请输入流水号"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="再保险合同号码" prop="ReInsuranceContNo">
        <el-input
          v-model="queryParams.ReInsuranceContNo"
          placeholder="请输入再保险合同号码"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="再保险合同名称" prop="ReInsuranceContName">
        <el-input
          v-model="queryParams.ReInsuranceContName"
          placeholder="请输入再保险合同名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="合同附约类型" prop="ContOrAmendmentType">
        <el-select
          v-model="queryParams.ContOrAmendmentType"
          placeholder="请选择合同附约类型"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.prp_contract_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="合同属性" prop="ContAttribute">
        <el-select
          v-model="queryParams.ContAttribute"
          placeholder="请选择合同属性"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.prp_contract_attr"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="合同状态" prop="ContStatus">
        <el-select
          v-model="queryParams.ContStatus"
          placeholder="请选择合同状态"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.prp_cont_pol_duty_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="再保险公司代码" prop="ReinsurerCode">
        <el-input
          v-model="queryParams.ReinsurerCode"
          placeholder="请输入再保险公司代码"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属年份" prop="ReportYear">
        <el-input
          v-model="queryParams.ReportYear"
          placeholder="请输入所属年份"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="推送状态" prop="PushStatus">
        <el-select
          v-model="queryParams.PushStatus"
          placeholder="推送状态"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.regulator_report_push_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="small"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="small" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <div style="margin-bottom: 10px">
      <el-button
        type="primary"
        plain
        icon="el-icon-plus"
        size="small"
        @click="handleAdd"
        v-hasPermi="['dws:prp:insure:cont:add']"
        >新增</el-button
      >
      <el-button
        type="success"
        plain
        icon="el-icon-edit"
        size="small"
        :disabled="single"
        @click="handleUpdate"
        v-hasPermi="['dws:prp:insure:cont:edit']"
        >修改</el-button
      >
      <el-button
        type="danger"
        plain
        icon="el-icon-delete"
        size="small"
        :disabled="multiple"
        @click="handleDelete"
        v-hasPermi="['dws:prp:insure:cont:remove']"
        >删除</el-button
      >
      <el-button
        type="success"
        plain
        icon="el-icon-upload2"
        size="small"
        @click="importConfig.visible = true"
        v-hasPermi="['dws:prp:insure:cont:import']"
        >人工导入</el-button
      >
      <el-button
        type="warning"
        plain
        icon="el-icon-download"
        size="small"
        @click="handleExport"
        v-hasPermi="['dws:prp:insure:cont:export']"
        >导出</el-button
      >
      <el-button
        type="primary"
        plain
        size="small"
        @click="handlePush"
        v-hasPermi="['dws:prp:insure:cont:push']"
        >推送</el-button
      >
    </div>

    <el-table
      v-loading="loading"
      :data="insureContList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" align="center" fixed="left" />
      <el-table-column label="序号" type="index" width="80" align="center">
        <template slot-scope="scope">
          <span>{{
            queryParams.pageSize * (queryParams.pageNum - 1) + scope.$index + 1
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="流水号"
        align="center"
        prop="TransactionNo"
        width="200px"
        show-overflow-tooltip
      />
      <el-table-column
        label="再保险合同号码"
        align="center"
        prop="ReInsuranceContNo"
        width="180px"
        show-overflow-tooltip
      />
      <el-table-column
        label="再保险合同名称"
        align="center"
        prop="ReInsuranceContName"
        width="200px"
        show-overflow-tooltip
      />
      <el-table-column
        label="合同附约类型"
        align="center"
        prop="ContOrAmendmentType"
        width="120px"
      >
        <template slot-scope="scope">
          <dict-tag :options="dict.type.prp_contract_type" :value="scope.row.ContOrAmendmentType"/>
        </template>
      </el-table-column>
      <el-table-column
        label="合同属性"
        align="center"
        prop="ContAttribute"
        width="120px"
      >
        <template slot-scope="scope">
          <dict-tag :options="dict.type.prp_contract_attr" :value="scope.row.ContAttribute"/>
        </template>
      </el-table-column>
      <el-table-column
        label="合同状态"
        align="center"
        prop="ContStatus"
        width="100px"
      >
        <template slot-scope="scope">
          <dict-tag :options="dict.type.prp_cont_pol_duty_status" :value="scope.row.ContStatus"/>
        </template>
      </el-table-column>
      <el-table-column
        label="再保险公司代码"
        align="center"
        prop="ReinsurerCode"
        width="150px"
      />
      <el-table-column
        label="再保险公司名称"
        align="center"
        prop="ReinsurerName"
        width="200px"
        show-overflow-tooltip
      />
      <el-table-column
        label="所属年份"
        align="center"
        prop="ReportYear"
        width="100px"
      />
      <el-table-column
        label="所属月份"
        align="center"
        prop="ReportMonth"
        width="100px"
      />
      <el-table-column
        label="推送状态"
        align="center"
        prop="PushStatus"
        width="100px"
      >
        <template slot-scope="scope">
          <dict-tag :options="dict.type.regulator_report_push_status" :value="scope.row.PushStatus"/>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
        width="160"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['dws:prp:insure:cont:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['dws:prp:insure:cont:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改保单登记再保合同信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="150px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="流水号" prop="TransactionNo">
              <el-input v-model="form.TransactionNo" placeholder="请输入流水号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="保险机构代码" prop="CompanyCode">
              <el-input v-model="form.CompanyCode" placeholder="请输入保险机构代码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="再保险合同号码" prop="ReInsuranceContNo">
              <el-input v-model="form.ReInsuranceContNo" placeholder="请输入再保险合同号码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="再保险合同名称" prop="ReInsuranceContName">
              <el-input v-model="form.ReInsuranceContName" placeholder="请输入再保险合同名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="再保险合同简称" prop="ReInsuranceContTitle">
              <el-input v-model="form.ReInsuranceContTitle" placeholder="请输入再保险合同简称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="再保险附约主合同号" prop="MainReInsuranceContNo">
              <el-input v-model="form.MainReInsuranceContNo" placeholder="请输入再保险附约主合同号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="合同附约类型" prop="ContOrAmendmentType">
              <el-select v-model="form.ContOrAmendmentType" placeholder="请选择合同附约类型">
                <el-option
                  v-for="dict in dict.type.prp_contract_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合同属性" prop="ContAttribute">
              <el-select v-model="form.ContAttribute" placeholder="请选择合同属性">
                <el-option
                  v-for="dict in dict.type.prp_contract_attr"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog :title="importConfig.title" :visible.sync="importConfig.visible" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="importConfig.headers"
        :action="importConfig.url"
        :disabled="importConfig.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="importConfig.updateSupport" /> 是否更新已经存在的数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="importConfig.visible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listInsureCont, getInsureCont, delInsureCont, addInsureCont, updateInsureCont, pushInsureCont } from "@/api/dws/prp/insure/cont";
import { getToken } from "@/utils/auth";

export default {
  name: "InsureCont",
  dicts: ['prp_contract_type', 'prp_contract_attr', 'prp_cont_pol_duty_status', 'regulator_report_push_status', 'regulator_report_data_source'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 保单登记再保合同信息表格数据
      insureContList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        TransactionNo: null,
        CompanyCode: null,
        ReInsuranceContNo: null,
        ReInsuranceContName: null,
        ReInsuranceContTitle: null,
        MainReInsuranceContNo: null,
        ContOrAmendmentType: null,
        ContAttribute: null,
        ContStatus: null,
        TreatyOrFacultativeFlag: null,
        ContSigndate: null,
        PeriodFrom: null,
        PeriodTo: null,
        ContType: null,
        ReinsurerCode: null,
        ReinsurerName: null,
        ChargeType: null,
        ReportYear: null,
        ReportMonth: null,
        AccountPeriod: null,
        DataSource: null,
        PushStatus: null,
        PushDate: null,
        PushBy: null,
        Remark: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        TransactionNo: [
          { required: true, message: "流水号不能为空", trigger: "blur" },
          { max: 64, message: "流水号长度不能超过64个字符", trigger: "blur" }
        ],
        CompanyCode: [
          { required: true, message: "保险机构代码不能为空", trigger: "blur" },
          { max: 64, message: "保险机构代码长度不能超过64个字符", trigger: "blur" }
        ],
        ReInsuranceContNo: [
          { required: true, message: "再保险合同号码不能为空", trigger: "blur" },
          { max: 64, message: "再保险合同号码长度不能超过64个字符", trigger: "blur" }
        ],
        ReInsuranceContName: [
          { required: true, message: "再保险合同名称不能为空", trigger: "blur" },
          { max: 256, message: "再保险合同名称长度不能超过256个字符", trigger: "blur" }
        ],
        ReInsuranceContTitle: [
          { required: true, message: "再保险合同简称不能为空", trigger: "blur" },
          { max: 256, message: "再保险合同简称长度不能超过256个字符", trigger: "blur" }
        ],
        MainReInsuranceContNo: [
          { required: true, message: "再保险附约主合同号不能为空", trigger: "blur" },
          { max: 64, message: "再保险附约主合同号长度不能超过64个字符", trigger: "blur" }
        ],
        ContOrAmendmentType: [
          { required: true, message: "合同附约类型不能为空", trigger: "change" }
        ],
        ContAttribute: [
          { required: true, message: "合同属性不能为空", trigger: "change" }
        ],
        ContStatus: [
          { required: true, message: "合同状态不能为空", trigger: "change" }
        ],
        TreatyOrFacultativeFlag: [
          { required: true, message: "合同/临分标志不能为空", trigger: "blur" }
        ],
        ContSigndate: [
          { required: true, message: "合同签署日期不能为空", trigger: "blur" }
        ],
        PeriodFrom: [
          { required: true, message: "合同生效起期不能为空", trigger: "blur" }
        ],
        PeriodTo: [
          { required: true, message: "合同生效止期不能为空", trigger: "blur" }
        ],
        ContType: [
          { required: true, message: "合同类型不能为空", trigger: "blur" }
        ],
        ReinsurerCode: [
          { required: true, message: "再保险公司代码不能为空", trigger: "blur" },
          { max: 64, message: "再保险公司代码长度不能超过64个字符", trigger: "blur" }
        ],
        ReinsurerName: [
          { required: true, message: "再保险公司名称不能为空", trigger: "blur" },
          { max: 256, message: "再保险公司名称长度不能超过256个字符", trigger: "blur" }
        ],
        ChargeType: [
          { required: true, message: "佣金核算方式不能为空", trigger: "blur" }
        ],
        ReportYear: [
          { required: true, message: "所属年份不能为空", trigger: "blur" }
        ],
        ReportMonth: [
          { required: true, message: "所属月份不能为空", trigger: "blur" },
          { type: 'number', min: 1, max: 12, message: "所属月份必须在1-12之间", trigger: "blur" }
        ],
        AccountPeriod: [
          { required: true, message: "所属账期不能为空", trigger: "blur" },
          { max: 64, message: "所属账期长度不能超过64个字符", trigger: "blur" }
        ],
        DataSource: [
          { required: true, message: "数据来源不能为空", trigger: "change" }
        ],
        PushStatus: [
          { required: true, message: "推送状态不能为空", trigger: "change" }
        ]
      },
      // 导入参数
      importConfig: {
        title: "保单登记再保合同信息导入",
        visible: false,
        isUploading: false,
        updateSupport: 0,
        headers: { Authorization: "Bearer " + getToken() },
        url: process.env.VUE_APP_BASE_API + "/huida-reinsurance/dws/prp/insure/cont/import"
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询保单登记再保合同信息列表 */
    getList() {
      this.loading = true;
      listInsureCont(this.queryParams).then(response => {
        this.insureContList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        Id: null,
        TransactionNo: null,
        CompanyCode: "000166",
        ReInsuranceContNo: null,
        ReInsuranceContName: null,
        ReInsuranceContTitle: null,
        MainReInsuranceContNo: null,
        ContOrAmendmentType: null,
        ContAttribute: null,
        ContStatus: null,
        TreatyOrFacultativeFlag: null,
        ContSigndate: null,
        PeriodFrom: null,
        PeriodTo: null,
        ContType: null,
        ReinsurerCode: null,
        ReinsurerName: null,
        ChargeType: null,
        ReportYear: null,
        ReportMonth: null,
        AccountPeriod: null,
        DataSource: 1,
        PushStatus: 0,
        PushDate: null,
        PushBy: null,
        Remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.Id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加保单登记再保合同信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const Id = row.Id || this.ids
      getInsureCont(Id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改保单登记再保合同信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.Id != null) {
            updateInsureCont(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addInsureCont(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const Ids = row.Id || this.ids;
      this.$modal.confirm('是否确认删除保单登记再保合同信息编号为"' + Ids + '"的数据项？').then(function() {
        return delInsureCont(Ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        'dws/prp/insure/cont/export',
        {
          ...this.queryParams,
        },
        `insure_cont_${new Date().getTime()}.xlsx`
      );
    },
    /** 推送按钮操作 */
    handlePush() {
      if (this.ids.length === 0) {
        this.$modal.msgError("请选择要推送的数据");
        return;
      }
      this.$modal.confirm('是否确认推送选中的数据？').then(function() {
        return pushInsureCont(this.ids);
      }.bind(this)).then(() => {
        this.getList();
        this.$modal.msgSuccess("推送成功");
      }).catch(() => {});
    },
    /** 导入模板操作 */
    importTemplate() {
      this.download('dws/prp/insure/cont/importTemplate', {}, `insure_cont_template_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.importConfig.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.importConfig.visible = false;
      this.importConfig.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    }
  }
};
</script>
