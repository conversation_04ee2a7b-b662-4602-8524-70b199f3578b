<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="130px">
        <el-form-item label="险种编码" prop="riskCode" label-width="70px">
          <el-select v-model="queryParams.riskCode" placeholder="请选择险种编码" filterable clearable>
            <el-option v-for="item in dict.type.core_insurance_type"
              :key="item.value" :label="item.value + '/' + item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        </el-form-item>
      </el-form>

     <timeline :list="Schedule" :riskCode="queryParams.riskCode"></timeline>
  </div>
</template>

<script>
import {listSchedule} from '@/api/reinsurance/staging'
import timeline from '@/components/TimeLine/timeline.vue'
export default {
  name:'Staging',
  components: {
    timeline
  },
  dicts:['core_insurance_type'],
  data() {
    return {
      queryParams:{},
      loading:false,
      Schedule:[]
    }
  },
  methods: {
    handleQuery(){
      this.getList()
    },  
    getList() {
      this.loading = true;
      listSchedule(this.queryParams).then(response => {
        this.Schedule = response.data;
      });
    },
  }
}
</script>

<style>

</style>