<template>
  <div>
    <el-table v-loading="loading" :data="tableList">
      <el-table-column v-for="column in tableColumns" :label="column.label" :prop="column.prop"
        :key="column.prop" :align="column.align" :min-width="column.width" v-if="column.visible" show-overflow-tooltip>
        <template slot-scope="{ row }">
          <template v-if="column.dict">
            <dict-tag :options="dict.type[column.dict]" :value="row[column.prop]"/>
          </template>
          <template v-else>{{ row[column.prop] }}</template>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>
<script>
import {listConfigLogDetail} from "@/api/reinsurance/importDataConfig";

export default  {
  name: 'ImportDataConfigDetail',
  dicts: ['import_data_config_data_status'],
  props: {
    params: {
      type: Object,
      default: () => {}
    },
  },
  data() {
    return {
      loading: false,
      tableList: [],
      tableColumns: [
        { visible: true, align: 'center', width: '150px', key: 0, prop: 'batchNo', label: '版本号' },
        { visible: true, align: 'center', width: '150px', key: 1, prop: 'tableEn', label: '涉及表' },
        { visible: true, align: 'center', width: '150px', key: 2, prop: 'tableName', label: '表名称' },
        { visible: true, align: 'center', width: '100px', key: 3, dict: 'import_data_config_data_status', prop: 'dataStatus', label: '状态' },
      ],
      total: 0,
      queryParams: {
        batchNo: null,
        pageNum: 1,
        pageSize: 10
      }
    }
  },
  created() {
    this.queryParams.batchNo = this.params.batchNo
    this.getList()
  },
  methods: {
    getList() {
      listConfigLogDetail(this.queryParams).then(res => {
        this.tableList = res.rows
        this.total = res.total
      })
    },
  }
}
</script>
<style scoped>
::v-deep .el-upload {
  width: 100%;
  padding-left: 20px;
}
::v-deep .el-upload-dragger {
  width: 100%;
}
::v-deep .el-upload-list--text {
  padding-left: 20px;
}
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  padding: 20px 20px 0 0;
}
</style>
