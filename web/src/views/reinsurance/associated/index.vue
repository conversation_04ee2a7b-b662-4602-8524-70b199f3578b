<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="130px">
      <el-form-item label="险种编码" prop="riskCode">
        <el-select v-model="queryParams.riskCode" placeholder="请选择险种编码" clearable filterable size="small">
          <el-option v-for="i in dict.type.core_insurance_type" :label="i.value+'-'+i.label" :value="i.value" :key="i.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="责任编码" prop="liabilityCode">
        <el-select v-model="queryParams.liabilityCode"  placeholder="请选择责任编码" clearable value-key='dictValue' filterable size="small">
          <el-option v-for="i in LiabilitySelectObj[queryParams.riskCode]" :label="i.dictValue+'-'+i.dictLabel" :value="i.dictValue" :key="i.dictLabel"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增关联算法</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="formulaLiability" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="险种编码" align="left" prop="riskCode" min-width="150px">
        <template slot-scope="scope">
          {{scope.row.riskCode}}-{{scope.row.riskName}}
        </template>
      </el-table-column>
      <el-table-column label="责任编码" align="left" prop="liabilityCode">
        <template slot-scope="scope">
          {{scope.row.liabilityCode}}-{{scope.row.liabilityName}}
        </template>
      </el-table-column>
      <el-table-column label="已关联算法类型" align="center" prop="formulaType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.formula_type" :value="scope.row.formulaType"/>
        </template>
      </el-table-column>
      <el-table-column label="已关联公式编码" align="center" prop="formulaCode"></el-table-column>
      <el-table-column label="关联算法条件" align="center" prop="formulatj" min-width="200px">
        <template slot-scope="scope">
          <div>
            <i v-if="isTrue(scope.row.maxAge)&&isTrue(scope.row.minAge)">年龄上下限:{{scope.row.minAge}}-{{scope.row.maxAge}}岁<br></i>
            <!-- <i v-if="isTrue(scope.row.maxAge)&&!isTrue(scope.row.minAge)">年龄上下限:{{scope.row.maxAge}}岁以下<br></i>  -->
            <!-- <i v-if="!isTrue(scope.row.maxAge)&&isTrue(scope.row.minAge)">年龄上下限:{{scope.row.minAge}}岁以上 <br></i> -->
            <i v-if="isTrue(scope.row.maxPolicyYear)&&isTrue(scope.row.minPolicyYear)">保单年度上下限:{{scope.row.minPolicyYear}}-{{scope.row.maxPolicyYear}}年<br></i>
            <!-- <i v-if="isTrue(scope.row.maxPolicyYear)&&!isTrue(scope.row.minPolicyYear)">保单年度上下限:{{scope.row.maxPolicyYear}}岁以下<br></i>  -->
            <!-- <i v-if="!isTrue(scope.row.maxPolicyYear)&&isTrue(scope.row.minPolicyYear)">保单年度上下限:{{scope.row.minPolicyYear}}岁以上<br></i>  -->
          </div>
        </template>
      </el-table-column>
      <el-table-column label="已关联算法公式" align="left" prop="formulaText" min-width="150px" show-overflow-tooltip/>
      <el-table-column label="保障计划" align="left" prop="supportPlan" min-width="150px" show-overflow-tooltip/>

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleUpdate(scope.row,false)"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="deleteItem(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <el-dialog :title="title" :visible.sync="open" append-to-body :close-on-click-modal="false" :close-on-press-escape="false" width="850px">
      <el-divider content-position="left">关联算法条件</el-divider>
      <el-form ref="form" :model="form" :rules="rules" inline size="mini"   label-width="123px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="险种编码" prop="riskObj">
              <el-select v-model="form.riskObj" :disabled="!isAdd" placeholder="请选择险种编码" value-key='value' filterable size="small" style="width:254px">
                <el-option v-for="i in dict.type.core_insurance_type" :label="i.value+ '-'+ i.label" :value="i" :key="i.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="责任编码" prop="liabilityObj">
              <el-select v-model="form.liabilityObj" :disabled="!isAdd"  placeholder="请选择责任编码" value-key='dictValue' filterable size="small" style="width:254px">
                <el-option v-for="i in LiabilitySelectObj[form.riskObj.value]" :label="i.dictValue + '-' +i.dictLabel" :value="i" :key="i.dictValue"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="年龄上下限"  prop="Age">
              <div class="inputgroup">
                <el-input v-model="form.minAge" :disabled="!isAdd" placeholder="年龄下限" clearable sizi="small"/>
                <span>至</span>
                <el-input v-model="form.maxAge" :disabled="!isAdd" placeholder="年龄上限" clearable sizi="small"/>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="保单年度上下限"  prop="PolicyYear" >
              <div class="inputgroup">
                <el-input v-model="form.minPolicyYear" :disabled="!isAdd" placeholder="年度下限" clearable sizi="small"/>
                <span>至</span>
                <el-input v-model="form.maxPolicyYear" :disabled="!isAdd" placeholder="年度上限" clearable sizi="small"/>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="是否缴费期满"  prop="payendExpire">
          <el-select v-model="form.payendExpire" :disabled="!isAdd" placeholder="请选择是否缴费期满" style="width:254px">
            <el-option v-for="i in dict.type.liability_formula_payout_period_full" :label="i.label" :value="+i.value" :key="i.value"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="保障计划" prop="supportPlan">
          <el-input v-model="form.supportPlan"  :disabled="!isAdd" placeholder="请输入保障计划" style="width:254px"/>
        </el-form-item>

      </el-form>
      <el-divider content-position="left">关联算法公式</el-divider>
      <el-form ref="Fform" :model="FqueryParams" inline size="mini">
        <el-form-item label="算法类型">
          <el-select v-model="FqueryParams.formulaType" disabled  @change="getFormulaList" placeholder="风险保额">
            <el-option v-for="item in dict.type.formula_type"
            :key="Number(item.value)" :label="item.label" :value="Number(item.value)">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="公式名称">
          <el-input v-model="FqueryParams.formulaName" @blur="getFormulaList" :disabled="!isAdd" placeholder="请输入公式名称" clearable sizi="small"/>
        </el-form-item>
        <el-form-item label="公式编码">
          <el-input v-model="FqueryParams.formulaCode" @blur="getFormulaList" :disabled="!isAdd" placeholder="请输入公式编码" clearable sizi="small"/>
        </el-form-item>
      </el-form>
      <el-table v-loading="loading" ref="multipleTable" :data="formulaList" @selection-change="handleSelectionChange" border :header-cell-class-name="setClassName">
        <el-table-column type="selection" width="55" align="center" v-if="isAdd" />
        <el-table-column label="公式名称" align="center" prop="formulaName" width="200px"></el-table-column>
        <el-table-column label="公式" align="center" prop="formulaText"></el-table-column>
      </el-table>
      <pagination
        style="margin-bottom:25px"
        v-show="Ftotal>0"
        layout="total, prev, pager, next, jumper"
        :total="Ftotal"
        :page.sync="FqueryParams.pageNum"
        :limit.sync="FqueryParams.pageSize"
        @pagination="getFormulaList"
      />

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" v-if="isAdd" size="small">确 定</el-button>
        <el-button @click="cancel" size="small">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getLiabilitySelect } from "@/api/reinsurance/riskLiability";
import {listFormula,} from "@/api/reinsurance/formula";
import { updateLiability, addLiability, detailLiability,  deleteLiability, listLiability,} from "@/api/reinsurance/associated";

export default {
  name:"Formula",
  components: {
  },
  dicts: [
    'core_insurance_type',
    'formula_type',
    'liability_formula_payout_period_full',
  ],
  data(){
    const AgeComp = (rule, value, callback) => {
      if (!this.isTrue(this.form.minAge)) {
        callback(new Error("请输入年龄下限"));
      } else if (!this.isTrue(this.form.maxAge)) {
        callback(new Error("请输入年龄上限"));
      } else {
        callback();
      }
    };
    const PolicyComp = (rule, value, callback) => {
      if (!this.isTrue(this.form.minPolicyYear)) {
        callback(new Error("请输入保单年度下限"));
      } else if (!this.isTrue(this.form.maxPolicyYear)) {
        callback(new Error("请输入保单年度上限"));
      } else {
        callback();
      }
    };
    return{
      LiabilitySelectObj:{},

      formulaList:[],
      isAdd: false,
      title: '',
      loading: false,
      showSearch: true,
      open: false,
      total:0,
      queryParams:{
        formulaType:null,
        riskCode:null,
        liabilityCode:null,
        pageNum:1,
        pageSize:10
      },
      Ftotal:0,
      FqueryParams:{
        formulaType:null,
        formulaName:null,
        formulaType:10,
        pageNum:1,
        pageSize:5
      },
      form:{
        riskObj:'',
        liabilityObj:'',
      },
      rules:{
        liabilityObj: [{ required: true, trigger: "change", message: "请选择责任编码" }],
        riskObj: [{ required: true, trigger: "change", message: "请选择险种编码" }],
        Age: [
          { required: true, validator: AgeComp, trigger: "blur" }
        ],
        PolicyYear: [
          { required: true, validator: PolicyComp, trigger: "blur" }
        ],
        // payendExpire: [{ required: true, trigger: "change", message: "请选择分保方式" }],
      },
      formulaLiability:[],
      selectedFormula:{}
    }
  },
  created(){
    if(this.$route.params&&this.$route.params.riskCode){
      this.queryParams.riskCode = this.$route.params.riskCode
    }
    this.LiabilitySelect();
    this.queryParams.formulaType = 10;
    this.getList()
  },
  activated(){
    if(this.$route.params&&this.$route.params.riskCode){
      this.queryParams.riskCode = this.$route.params.riskCode
    }
    this.getList();
  },
  methods:{
    setClassName({ column }) {
      // 若为选择框,且数据皆为不可选时
      if (column.type == 'selection') {
        return 'all-disabled'
      }
    },
    LiabilitySelect() {
      getLiabilitySelect([]).then(response => {
        this.LiabilitySelectObj = response.data;
        console.log(this.LiabilitySelectObj[511119])
      });
    },
    /** 查询公式列表 */
    getFormulaList() {
      listFormula(this.FqueryParams).then(response => {
        this.formulaList = response.rows;
        this.Ftotal = response.total;
        this.loading = false;
        if(this.form.id){
          this.selectedFormula = this.formulaList[0]
          setTimeout(() => {
              this.$refs.multipleTable.toggleRowSelection(this.formulaList[0],true)
          }, 10);
        }
      });
    },
    getList(){
      listLiability(this.queryParams).then(response => {
        this.formulaLiability = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.queryParams.formulaType = 10;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleAdd(){
      this.title ="新增算法关联"
      this.open = true
      this.isAdd =true
      this.formulaList = this.getFormulaList()
      this.reset();
    },
    handleUpdate(row,bol){
      this.title =bol?"修改算法关联":"查看算法关联"
      this.isAdd =bol
      this.reset();
      detailLiability(row.id).then(res=>{
        this.open = true
        this.form = res.data
        this.form.riskObj={
          value: this.form.riskCode,
          label: this.form.riskName
        }
        this.form.liabilityObj={
          dictValue:this.form.liabilityCode,
          dictLabel:this.form.liabilityName,
        }
        this.FqueryParams.formulaName = this.form.formulaName
        this.FqueryParams.formulaType = this.form.formulaType
        this.FqueryParams.formulaCode = this.form.formulaCode
        this.getFormulaList()
      })
    },
    // 表单重置
    reset() {
      this.form = {
        riskObj:'',
        liabilityObj:'',
        minAge: null,
        maxAge: null,
        minPolicyYear: 1,
        maxPolicyYear: 106,
        payendExpire: null,
        status:0
      };
      this.FqueryParams={
        formulaType:'',
        formulaName:'',
        formulaType:10,
        pageNum:1,
        pageSize:5
      }
      this.Ftotal = 0
      this.resetForm("form");
    },
    cancel() {
      this.open = false;
      this.reset();
    },
    submitForm(){
      this.$refs["form"].validate(valid => {
        if(!this.selectedFormula.formulaCode){
          this.$message.error('请选择公式')
          return
        }
        if (valid) {
          let form = JSON.parse(JSON.stringify(this.form))
          let {formulaName,formulaCode,formulaType,formulaText} = JSON.parse(JSON.stringify(this.selectedFormula))
          form.riskCode = form.riskObj.value
          form.riskName = form.riskObj.label
          form.liabilityCode = form.liabilityObj.dictValue
          form.liabilityName = form.liabilityObj.dictLabel
          form.formulaName = formulaName
          form.formulaCode = formulaCode
          form.formulaType = formulaType
          form.formulaText = formulaText
          delete form.riskObj
          delete form.liabilityObj
          if (this.form.id != null) {
            updateLiability(form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addLiability(form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    deleteItem(row,bol){
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除公式编码为"' + row.id + '"的数据项？').then(function () {
        return deleteLiability(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    handleSelectionChange(rows){
      if(rows.length>1){
        const item = rows.shift()
        this.$refs.multipleTable.toggleRowSelection(item,false) //设置这一行取消选中
      }
      console.log(rows)
      this.selectedFormula = rows[0]
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('huida-reinsurance/reinsurance/formula/liability/export', {
        ...this.queryParams
      }, `formula_${new Date().getTime()}.xlsx`)
    },
    isTrue(val){
      return val!==undefined&&val!==null&&val!==''
    }
  }
}
</script>

<style lang="scss" scoped>
.inputgroup{
    display: flex;
    justify-content: center;
    align-items: center;
  span{
    margin: 0px 10px;
  }
  .el-input{
    width: 110px;
  }
}
::v-deep .all-disabled {
  .cell{
    &:before{
      content: '选择';
    }
  }
  .el-checkbox__input .el-checkbox__inner{
    display: none;
  }
}
</style>>
