<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      label-width="100px"
    >
      <el-form-item label="推送状态" prop="pushStatus">
        <el-select
          v-model="queryParams.pushStatus"
          placeholder="推送状态"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.regulator_report_push_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="推送日期" prop="pushDate">
        <el-date-picker
          v-model="queryParams.pushDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="date"
          placeholder="推送日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="small"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="small" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <div style="margin-bottom: 10px">
      <el-button
        type="success"
        plain
        icon="el-icon-upload2"
        size="small"
        @click="importConfig.visible = true"
        >人工导入再保合同信息表</el-button
      >
      <el-button
        type="warning"
        plain
        icon="el-icon-download"
        size="small"
        @click="handleExport"
        >导出再保合同信息表</el-button
      >
      <el-button type="primary" plain size="small" @click="handlePush"
        >推送</el-button
      >
    </div>

    <el-table
      v-loading="loading"
      :data="reportList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" align="center" fixed="left" />
      <el-table-column label="序号" type="index" width="80" align="center">
        <template slot-scope="scope">
          <span>{{
            queryParams.pageSize * (queryParams.pageNum - 1) + scope.$index + 1
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="报表名称"
        align="center"
        prop="reportCode"
        width="150px"
      >
        再保合同信息表
      </el-table-column>
      <el-table-column
        label="流水号"
        align="center"
        prop="lsh"
        width="260px"
        show-overflow-tooltips
      />
      <el-table-column
        label="保险机构代码"
        align="center"
        prop="bxjgdm"
        width="120px"
      />
      <el-table-column
        label="保险机构名称"
        align="center"
        prop="bxjgmc"
        width="200px"
        show-overflow-tooltips
      />
      <el-table-column
        label="再保险合同号码"
        align="center"
        prop="zbxhthm"
        width="150px"
      />
      <el-table-column
        label="再保险合同名称"
        align="center"
        prop="zbxhtmc"
        width="200px"
        show-overflow-tooltip
      />
      <el-table-column
        label="再保险附约主合同号"
        align="center"
        prop="zbxfyzhth"
        width="200px"
        show-overflow-tooltip
      />
      <el-table-column
        label="再保险公司代码"
        align="center"
        prop="zbxgsdm"
        width="150px"
      />
      <el-table-column
        label="再保险公司名称"
        align="center"
        prop="zbxgsmc"
        width="250px"
        show-overflow-tooltip
      />
      <el-table-column
        label="原保险公司代码"
        align="center"
        prop="ybxgsdm"
        width="150px"
      />
      <el-table-column
        label="原保险公司名称"
        align="center"
        prop="ybxgsmc"
        width="200px"
      />
      <el-table-column
        label="合同分类"
        align="center"
        prop="htfl"
        width="120px"
      />
      <el-table-column
        label="合同附约类型"
        align="center"
        prop="htfylx"
        width="120px"
      />
      <el-table-column
        label="合同状态"
        align="center"
        prop="htzt"
        width="120px"
      />
      <el-table-column
        label="合同签署日期"
        align="center"
        prop="htqsrq"
        width="120px"
      />
      <el-table-column
        label="合同生效起期"
        align="center"
        prop="htsxqq"
        width="120px"
      />
      <el-table-column
        label="合同生效止期"
        align="center"
        prop="htsxzq"
        width="120px"
      />
      <el-table-column
        label="合同类型"
        align="center"
        prop="htlx"
        width="120px"
      />
      <el-table-column
        label="巨灾再保合同标志"
        align="center"
        prop="jzzbhtbz"
        width="150px"
      >
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.regulator_report_column_flag"
            :value="scope.row.jzzbhtbz"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="临分合同标志"
        align="center"
        prop="lfhtbz"
        width="150px"
      >
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.regulator_report_column_flag"
            :value="scope.row.lfhtbz"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="再保险经纪人"
        align="center"
        prop="zbxjjr"
        width="150px"
      />
      <el-table-column
        label="采集日期"
        align="center"
        prop="cjrq"
        width="120px"
      />
      <el-table-column
        label="所属年份"
        align="center"
        prop="reportYear"
        width="120px"
      />
      <el-table-column
        label="所属月份"
        align="center"
        prop="reportMonth"
        width="120px"
      />
      <el-table-column
        label="数据来源"
        align="center"
        prop="dataSource"
        width="120px"
      >
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.regulator_report_data_source"
            :value="scope.row.dataSource"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="推送状态"
        align="center"
        prop="pushStatus"
        width="120px"
      >
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.regulator_report_push_status"
            :value="scope.row.pushStatus"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="推送日期"
        align="center"
        prop="pushDate"
        width="120px"
      />
      <el-table-column
        label="推送人"
        align="center"
        prop="pushBy"
        width="120px"
      />
      <el-table-column label="操作" align="center" width="80px" fixed="right">
        <template slot-scope="scope">
          <el-popconfirm
            title="确认删除该条数据？"
            @confirm="handleDelete(scope.row)"
          >
            <el-button
              :disabled="scope.row.pushStatus == 1"
              type="text"
              style="color: #f56c6c"
              size="small"
              slot="reference"
              >删除</el-button
            >
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <ImportExcel
      :config="importConfig"
      @close="importConfig.visible = false"
      @uploadEnd="uploadEnd"
    />
  </div>
</template>

<script>
import {
  listEastContract,
  deleteEastData,
  pushEastData,
} from "@/api/reinsurance/regulatoryReport.js";
import ImportExcel from "@/components/ImportExcel";

export default {
  name: "ContractInformation",
  dicts: [
    "regulator_report_push_status",
    "regulator_report_column_flag",
    "regulator_report_data_source",
  ],
  components: { ImportExcel },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 字典表格数据
      reportList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        pushStatus: undefined,
        pushDate: undefined,
        reportYear: undefined,
      },
      ids: [],
      importVisible: true,
      importConfig: {
        accept: ".xls, .xlsx",
        tips: "仅允许导入xls 、xlsx格式文件。",
        title: "导入再保合同信息",
        visible: false,
        importUrl: "/huida-reinsurance/regulatory/report/import/east/0",
      },
    };
  },
  created() {
    this.queryParams.reportYear = this.$route.query.reportYear;
    this.handleQuery();
  },
  activated() {
    if (this.$route.query.reportYear !== this.queryParams.reportYear) {
      this.queryParams.reportYear = this.$route.query.reportYear;
      this.handleQuery();
    }
  },
  methods: {
    handlePush() {
      if (this.ids.length === 0) {
        this.$message({
          message: "请选择要推送的数据",
          type: "error",
        });
        return;
      }
      let arr = [];
      for (let item of this.ids) {
        if (item.pushStatus === 1) {
          this.$message({
            message: "存在已推送的数据，请重新选择！",
            type: "error",
          });
          return;
        } else {
          arr.push(item.id);
        }
      }
      pushEastData(0, arr.join(",")).then((res) => {
        this.$message({
          message: "推送成功",
          type: "success",
        });
        this.getList();
      });
    },
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item);
    },
    uploadEnd(res) {
      this.handleQuery();
      this.importConfig.visible = false;
    },
    handleDelete(data) {
      deleteEastData(0, data.id).then((response) => {
        this.$message({
          message: "删除成功",
          type: "success",
        });
        this.getList();
      });
    },
    /** 查询字典类型列表 */
    getList() {
      this.loading = true;
      listEastContract(this.queryParams).then((response) => {
        this.reportList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    handleExport() {
      this.download(
        "huida-reinsurance/regulatory/report/export/east/0",
        {
          ...this.queryParams,
        },
        `再保合同信息表_${new Date().getTime()}.xlsx`
      );
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
  },
};
</script>
