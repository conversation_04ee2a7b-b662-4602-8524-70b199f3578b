<template>
  <div>
    <el-scrollbar style="height: 70vh">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="135px"
        style="padding: 20px 40px 10px 20px"
      >
        <el-form-item label="险种编码" prop="riskCode">
          <el-select
            v-model="form.riskCode"
            class="w-100"
            placeholder="请选择险种编码"
            filterable
            clearable
            :disabled="params.optionType === UPDATE"
            @change="
              selectChange(
                dict.type.core_insurance_type,
                'riskCode',
                'riskName'
              )
            "
          >
            <el-option
              v-for="item in dict.type.core_insurance_type"
              :key="item.value"
              :label="item.value + '/' + item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="再保责任编码" prop="liabilityCode">
          <el-select
            v-model="form.liabilityCode"
            class="w-100"
            placeholder="请选择再保责任编码"
            filterable
            clearable
            :disabled="params.optionType === UPDATE"
            @change="
              selectChange(
                dict.type.reinsurance_liability,
                'liabilityCode',
                'liabilityName'
              )
            "
          >
            <el-option
              v-for="item in dict.type.reinsurance_liability"
              :key="item.value"
              :label="item.value + '/' + item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否主责任" prop="mainDuty">
          <el-select
            v-model="form.mainDuty"
            class="w-100"
            placeholder="请选择是否主责任"
            filterable
            clearable
          >
            <el-option
              v-for="item in dict.type.sys_yes_no"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="团个性质" prop="saleChnl">
          <el-select
            v-model="form.saleChnl"
            class="w-100"
            placeholder="请选择团个性质"
            filterable
            clearable
          >
            <el-option
              v-for="item in dict.type.risk_liability_insurance_channel"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否分出" prop="isCedeOut">
          <span slot="label">
            是否分出
            <el-tooltip
              content="是(计算分出)；否(不计算分出(全部自留))"
              placement="top"
            >
              <i class="el-icon-question"></i>
            </el-tooltip>
          </span>
          <el-select
            v-model="form.isCedeOut"
            class="w-100"
            placeholder="请选择是否分出"
            filterable
            clearable
          >
            <el-option
              v-for="item in dict.type.risk_liability_is_cede_out"
              :key="Number(item.value)"
              :label="item.label"
              :value="Number(item.value)"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否纳入巨灾" prop="intoCalamity">
          <span slot="label">
            是否纳入巨灾
            <el-tooltip
              content="是(纳入巨灾计算)；否(不纳入巨灾计算)"
              placement="top"
            >
              <i class="el-icon-question"></i>
            </el-tooltip>
          </span>
          <el-select
            v-model="form.intoCalamity"
            class="w-100"
            placeholder="请选择是否纳入巨灾"
            filterable
            clearable
          >
            <el-option
              v-for="item in dict.type.risk_liability_into_calamity"
              :key="Number(item.value)"
              :label="item.label"
              :value="Number(item.value)"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="准备金类型" prop="reserveType">
          <el-select
            v-model="form.reserveType"
            class="w-100"
            placeholder="请选择准备金类型"
            filterable
            clearable
          >
            <el-option
              v-for="item in dict.type.risk_liability_reserve_type"
              :key="Number(item.value)"
              :label="item.label"
              :value="Number(item.value)"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="期限类型" prop="periodType">
          <el-select
            v-model="form.periodType"
            class="w-100"
            placeholder="请选择期限类型"
            filterable
            clearable
          >
            <el-option
              v-for="item in dict.type.risk_liability_period_type"
              :key="Number(item.value)"
              :label="item.label"
              :value="Number(item.value)"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="产品类型" prop="productType">
          <el-select
            v-model="form.productType"
            class="w-100"
            placeholder="请选择产品类型"
            filterable
            clearable
          >
            <el-option
              v-for="item in dict.type.risk_liability_product_type"
              :key="Number(item.value)"
              :label="item.label"
              :value="Number(item.value)"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="豁免类型" prop="exemptType">
          <el-select
            v-model="form.exemptType"
            class="w-100"
            placeholder="请选择豁免类型"
            filterable
            clearable
          >
            <el-option
              v-for="item in dict.type.risk_liability_exempt_type"
              :key="Number(item.value)"
              :label="item.label"
              :value="Number(item.value)"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="主附类型" prop="riskType">
          <el-select
            v-model="form.riskType"
            class="w-100"
            placeholder="请选择主附类型"
            filterable
            clearable
          >
            <el-option
              v-for="item in dict.type.risk_liability_risk_type"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="业务类型" prop="businessType">
          <el-select
            v-model="form.businessType"
            class="w-100"
            placeholder="请选择业务类型"
            filterable
            clearable
          >
            <el-option
              v-for="item in dict.type.risk_liability_business_type"
              :key="Number(item.value)"
              :label="item.label"
              :value="Number(item.value)"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="再保计算频率" prop="rsCalcFrequency">
          <el-select
            v-model="form.rsCalcFrequency"
            class="w-100"
            placeholder="请选择再保计算频率"
            filterable
            clearable
          >
            <el-option
              v-for="item in dict.type.risk_liability_rs_calc_frequency"
              :key="Number(item.value)"
              :label="item.label"
              :value="Number(item.value)"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="税率" prop="taxRate">
          <el-input v-model="form.taxRate" placeholder="请输入税率" clearable />
        </el-form-item>
        <!-- <el-form-item label="再保合同类型" prop="rsContractType">
          <el-select
            v-model="form.rsContractType"
            class="w-100"
            placeholder="请选择再保合同类型"
            filterable
            clearable
          >
            <el-option
              v-for="item in dict.type.risk_liability_rs_contract_type"
              :key="Number(item.value)"
              :label="item.label"
              :value="Number(item.value)"
            >
            </el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="免赔类型" prop="deductibleType">
          <el-select
            v-model="form.deductibleType"
            class="w-100"
            placeholder="请选择免赔类型"
            filterable
            clearable
          >
            <el-option
              v-for="item in dict.type.risk_liability_deductible_type"
              :key="Number(item.value)"
              :label="item.label"
              :value="Number(item.value)"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="产品长短险标识" prop="periodFlag">
          <el-select
            v-model="form.periodFlag"
            class="w-100"
            placeholder="请选择产品长短险标识"
            filterable
            clearable
          >
            <el-option
              v-for="item in dict.type.risk_liability_period_flag"
              :key="Number(item.value)"
              :label="item.label"
              :value="Number(item.value)"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="理赔通知限额" prop="claimNotifyLimit">
          <el-input
            v-model="form.claimNotifyLimit"
            placeholder="请输入理赔通知限额"
            clearable
          />
        </el-form-item>
        <el-form-item label="理赔参与限额" prop="claimInvolvedLimit">
          <el-input
            v-model="form.claimInvolvedLimit"
            placeholder="请输入理赔参与限额"
            clearable
          />
        </el-form-item>
        <el-form-item label="理赔次数" prop="claimCount">
          <el-input
            v-model="form.claimCount"
            placeholder="请输入理赔次数"
            clearable
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select
            v-model="form.status"
            class="w-100"
            placeholder="请选择状态"
            filterable
            clearable
          >
            <el-option
              v-for="item in dict.type.risk_liability_status"
              :key="Number(item.value)"
              :label="item.label"
              :value="Number(item.value)"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="保险产品大类-E" prop="insProductType">
          <el-select
            v-model="form.insProductType"
            class="w-100"
            placeholder="请选择保险产品大类-E"
            filterable
            clearable
          >
            <el-option
              v-for="item in dict.type.regulator_product_type"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="责任分类-E" prop="insLiabilityType">
          <el-select
            v-model="form.insLiabilityType"
            class="w-100"
            placeholder="请选择责任分类-E"
            filterable
            clearable
          >
            <el-option
              v-for="item in dict.type.regulator_liability_type"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="保险期限类型-E" prop="periodTypeList">
          <el-select
            v-model="form.periodTypeList"
            class="w-100"
            placeholder="请选择保险期限类型-E"
            filterable
            clearable
            multiple
          >
            <el-option
              v-for="item in dict.type.regulator_period_type"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="政保合作业务标志" prop="insGovFlag">
          <el-select
            v-model="form.insGovFlag"
            class="w-100"
            placeholder="请选择政保合作业务标志"
            filterable
            clearable
          >
            <el-option
              v-for="item in dict.type.regulator_ins_gov_flag"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="保险产品大类-B" prop="prpProductType">
          <el-select
            v-model="form.prpProductType"
            class="w-100"
            placeholder="请选择保险产品大类-B"
            filterable
            clearable
          >
            <el-option
              v-for="item in dict.type.regulator_product_type"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="责任分类-B" prop="prpLiabilityType">
          <el-select
            v-model="form.prpLiabilityType"
            class="w-100"
            placeholder="请选择责任分类-B"
            filterable
            clearable
          >
            <el-option
              v-for="item in dict.type.prp_liability_type"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="保险期限类型-B" prop="prpInsuPeriod">
          <el-select
            v-model="form.prpInsuPeriod"
            class="w-100"
            placeholder="请选择保险期限类型-B"
            filterable
            clearable
          >
            <el-option
              v-for="item in dict.type.prp_period_type"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <div class="dialog-footer">
      <el-button @click="cancel">取 消</el-button>
      <el-button type="primary" @click="submit" :loading="submitLoading"
        >确 定</el-button
      >
    </div>
  </div>
</template>
<script>
import {
  addRiskLiability,
  updateRiskLiability,
} from "../../../api/reinsurance/riskLiability";
import { ADD, UPDATE } from "@/utils/systemUtil";

export default {
  name: "RiskLiabilityEdit",
  dicts: [
    "core_insurance_type",
    "reinsurance_liability",
    "risk_liability_insurance_channel",
    "risk_liability_is_cede_out",
    "risk_liability_into_calamity",
    "risk_liability_reserve_type",
    "risk_liability_period_type",
    "risk_liability_product_type",
    "risk_liability_exempt_type",
    "risk_liability_risk_type",
    "risk_liability_business_type",
    "risk_liability_rs_calc_frequency",
    "risk_liability_rs_contract_type",
    "risk_liability_deductible_type",
    "risk_liability_period_flag",
    "risk_liability_status",
    "sys_yes_no",
    "regulator_product_type",
    "regulator_liability_type",
    "regulator_period_type",
    "regulator_ins_gov_flag",
    "prp_liability_type",
    "prp_period_type",
  ],
  props: {
    params: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      UPDATE,
      // 提交加载
      submitLoading: false,
      // 表单参数
      form: {
        id: null,
        riskCode: null,
        riskName: null,
        liabilityCode: null,
        liabilityName: null,
        saleChnl: null,
        mainDuty: null,
        isCedeOut: 1,
        intoCalamity: null,
        reserveType: null,
        periodType: null,
        productType: null,
        exemptType: null,
        riskType: null,
        businessType: null,
        rsCalcFrequency: null,
        taxRate: null,
        rsContractType: null,
        deductibleType: null,
        periodFlag: null,
        claimNotifyLimit: null,
        claimInvolvedLimit: null,
        claimCount: null,
        status: 0,
        insProductType: null,
        insLiabilityType: null,
        periodTypeList: [],
        insGovFlag: null,
        prpProductType: null,
        prpLiabilityType: null,
        prpProductType: null,
      },
      // 表单校验
      rules: {
        riskCode: [
          { required: true, message: "险种编码不能为空", trigger: "change" },
        ],
        riskName: [
          { required: true, message: "险种名称不能为空", trigger: "change" },
        ],
        liabilityCode: [
          { required: true, message: "责任编码不能为空", trigger: "change" },
        ],
        liabilityName: [
          { required: true, message: "责任名称不能为空", trigger: "change" },
        ],
        mainDuty: [
          { required: true, message: "是否主责任不能为空", trigger: "change" },
        ],
        saleChnl: [
          { required: true, message: "团个性质不能为空", trigger: "change" },
        ],
        isCedeOut: [
          { required: true, message: "是否分出不能为空", trigger: "change" },
        ],
        intoCalamity: [
          {
            required: true,
            message: "是否纳入巨灾不能为空",
            trigger: "change",
          },
        ],
        reserveType: [
          { required: true, message: "准备金类型不能为空", trigger: "change" },
        ],
        periodType: [
          { required: true, message: "期限类型不能为空", trigger: "change" },
        ],
        productType: [
          { required: true, message: "产品类型不能为空", trigger: "change" },
        ],
        exemptType: [
          { required: true, message: "豁免类型不能为空", trigger: "change" },
        ],
        riskType: [
          { required: true, message: "主附类型不能为空", trigger: "change" },
        ],
        businessType: [
          { required: true, message: "业务类型不能为空", trigger: "change" },
        ],
        rsCalcFrequency: [
          {
            required: true,
            message: "再保计算频率不能为空",
            trigger: "change",
          },
        ],
        taxRate: [
          { required: true, message: "税率不能为空", trigger: "change" },
        ],
        // rsContractType: [
        //   { required: true, message: "再保合同类型不能为空", trigger: "change" }
        // ],
        deductibleType: [
          { required: true, message: "免赔类型不能为空", trigger: "change" },
        ],
        periodFlag: [
          {
            required: true,
            message: "产品长短险标识不能为空",
            trigger: "change",
          },
        ],
        claimNotifyLimit: [
          {
            required: true,
            message: "理赔通知限额不能为空",
            trigger: "change",
          },
        ],
        claimInvolvedLimit: [
          {
            required: true,
            message: "理赔参与限额不能为空",
            trigger: "change",
          },
        ],
        claimCount: [
          { required: true, message: "理赔次数不能为空", trigger: "change" },
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" },
        ],
        insProductType: [
          {
            required: true,
            message: "保险产品大类-E不能为空",
            trigger: "change",
          },
        ],
        insLiabilityType: [
          {
            required: true,
            message: "责任分类-E不能为空",
            trigger: "change",
          },
        ],
        periodTypeList: [
          {
            required: true,
            message: "保险期限类型-E不能为空",
            trigger: "change",
          },
        ],
        insGovFlag: [
          {
            required: true,
            message: "政保合作业务标志不能为空",
            trigger: "change",
          },
        ],
        prpProductType: [
          {
            required: true,
            message: "保险产品大类-B不能为空",
            trigger: "change",
          },
        ],
        prpLiabilityType: [
          {
            required: true,
            message: "责任分类-B不能为空",
            trigger: "change",
          },
        ],
        prpInsuPeriod: [
          {
            required: true,
            message: "保险期限类型-B不能为空",
            trigger: "change",
          },
        ],
      },
    };
  },
  created() {
    //修改时数据回显
    this.updateEcho();
  },
  methods: {
    // 取消
    cancel() {
      this.$emit("cancel");
    },
    // 提交
    submit() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.submitLoading = true;
          let arr = [];
          this.form.periodTypeList.forEach((val) => {
            for (let item of this.dict.type.regulator_period_type) {
              if (item.value == val) {
                arr.push({
                  periodTypeCode: val,
                  periodTypeName: item.label,
                });
                break;
              }
            }
          });
          this.form.periodTypeList = arr;
          //新增
          if (this.params && this.params.optionType === ADD) {
            addRiskLiability(this.form)
              .then((res) => {
                this.$message.success("添加成功");
                this.$emit("ok");
              })
              .finally(() => {
                this.submitLoading = false;
              });
          }
          //修改
          if (this.params && this.params.optionType === UPDATE) {
            updateRiskLiability(this.form)
              .then((res) => {
                this.$message.success("修改成功");
                this.$emit("ok");
              })
              .finally(() => {
                this.submitLoading = false;
              });
          }
        }
      });
    },
    /**
     * 选择变更
     * @param dictArr 字典数组
     * @param columnCode form表单字段编码
     * @param columnLabel form表单字段名称
     */
    selectChange(dictArr, columnCode, columnLabel) {
      const selectedOption = dictArr.find(
        (option) => option.value === this.form[columnCode]
      );
      if (selectedOption) {
        this.form[columnLabel] = selectedOption.label;
      } else {
        this.form[columnLabel] = null;
      }
    },
    // 修改数据回显
    updateEcho() {
      if (this.params && this.params.optionType === UPDATE) {
        for (const key in this.form) {
          if (this.params[key] !== undefined && this.params[key] !== null) {
            this.form[key] = this.params[key];
          }
        }
      }
    },
  },
};
</script>
<style scoped>
::v-deep .el-scrollbar__wrap {
  overflow-x: hidden !important;
}
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  padding: 10px 40px 10px 0;
  box-shadow: 0 -1px 5px rgba(0, 0, 0, 0.2);
}
</style>
