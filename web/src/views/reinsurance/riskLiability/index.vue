<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="险种编码" prop="riskCode" label-width="70px">
        <el-select
          v-model="queryParams.riskCode"
          placeholder="请选择险种编码"
          filterable
          clearable
        >
          <el-option
            v-for="item in dict.type.core_insurance_type"
            :key="item.value"
            :label="item.value + '/' + item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="再保责任编码"
        prop="liabilityCode"
        label-width="100px"
      >
        <el-select
          v-model="queryParams.liabilityCode"
          placeholder="请选择再保责任编码"
          filterable
          clearable
        >
          <el-option
            v-for="item in dict.type.risk_liability_reinsurance_duty"
            :key="item.value"
            :label="item.value + '/' + item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status" label-width="50px">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择状态"
          filterable
          clearable
        >
          <el-option
            v-for="item in dict.type.risk_liability_status"
            :key="Number(item.value)"
            :label="item.label"
            :value="Number(item.value)"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['reinsurance:riskLiability:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          @click="handleDelete"
          :disabled="multiple"
          v-hasPermi="['reinsurance:riskLiability:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['reinsurance:riskLiability:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
        :table-name="'riskLiabilityTable'"
        :columns="riskLiabilityTableColumns"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="riskLiabilityList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column width="60" align="center" label="序号">
        <template slot-scope="scope">
          {{
            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
          }}
        </template>
      </el-table-column>
      <el-table-column
        v-for="column in riskLiabilityTableColumns"
        :label="column.label"
        :prop="column.prop"
        :key="column.prop"
        :align="column.align"
        :min-width="column.width"
        v-if="column.visible"
        show-overflow-tooltip
      >
        <template slot-scope="{ row }">
          <template v-if="column.dict">
            <dict-tag
              :options="dict.type[column.dict]"
              :value="row[column.prop]"
            />
          </template>
          <template v-else>
            <span v-if="column.prop === 'periodTypeList'"
              >{{ multipleContent(row, column.prop, "periodTypeName") }}
            </span>
            <span v-else>{{ row[column.prop] }}</span>
          </template>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        min-width="150px"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['reinsurance:riskLiability:update']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['reinsurance:riskLiability:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :auto-scroll="false"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改险种责任对话框 -->
    <el-dialog
      :title="dialog.title"
      :visible.sync="dialog.visible"
      :close-on-click-modal="false"
      width="500px"
    >
      <component
        :is="dialog.componentsName"
        v-if="dialog.visible"
        :params="dialog.params"
        @ok="dialogOk"
        @cancel="dialogCancel"
      />
    </el-dialog>
  </div>
</template>

<script>
import {
  listRiskLiability,
  delRiskLiability,
} from "@/api/reinsurance/riskLiability";
import RiskLiabilityEdit from "@/views/reinsurance/riskLiability/edit.vue";
import { ADD, UPDATE } from "@/utils/systemUtil";

export default {
  name: "RiskLiability",
  components: {
    RiskLiabilityEdit,
  },
  dicts: [
    "core_insurance_type",
    "risk_liability_reinsurance_duty",
    "risk_liability_insurance_channel",
    "risk_liability_is_cede_out",
    "risk_liability_into_calamity",
    "risk_liability_reserve_type",
    "risk_liability_period_type",
    "risk_liability_product_type",
    "risk_liability_exempt_type",
    "risk_liability_risk_type",
    "risk_liability_business_type",
    "risk_liability_rs_calc_frequency",
    "risk_liability_rs_contract_type",
    "risk_liability_deductible_type",
    "risk_liability_period_flag",
    "risk_liability_status",
    "sys_yes_no",
    "sys_user",
    "regulator_product_type",
    "regulator_liability_type",
    "regulator_ins_gov_flag",
    "regulator_period_type",
    "prp_liability_type",
    "prp_period_type",
  ],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        riskCode: null,
        liabilityCode: null,
        status: 0,
      },
      // 公共模态框
      dialog: {
        title: "",
        visible: false,
        params: null,
        componentsName: null,
      },
      // 总条数
      total: 0,
      // 险种责任表格数据
      riskLiabilityList: [],
      // 表格列配置
      riskLiabilityTableColumns: [
        {
          visible: true,
          align: "center",
          width: "100px",
          prop: "riskCode",
          label: "险种编码",
        },
        {
          visible: true,
          align: "center",
          width: "250px",
          prop: "riskName",
          label: "险种名称",
        },
        {
          visible: true,
          align: "center",
          width: "150px",
          prop: "liabilityCode",
          label: "再保责任编码",
        },
        {
          visible: true,
          align: "center",
          width: "150px",
          prop: "liabilityName",
          label: "再保责任名称",
        },
        {
          visible: true,
          align: "center",
          width: "100px",
          dict: "sys_yes_no",
          prop: "mainDuty",
          label: "是否主责任",
        },
        {
          visible: true,
          align: "center",
          width: "150px",
          dict: "risk_liability_insurance_channel",
          prop: "saleChnl",
          label: "团个性质",
        },
        {
          visible: true,
          align: "center",
          width: "150px",
          dict: "risk_liability_is_cede_out",
          prop: "isCedeOut",
          label: "是否分出",
        },
        {
          visible: true,
          align: "center",
          width: "150px",
          dict: "risk_liability_into_calamity",
          prop: "intoCalamity",
          label: "是否纳入巨灾",
        },
        {
          visible: true,
          align: "center",
          width: "150px",
          dict: "risk_liability_reserve_type",
          prop: "reserveType",
          label: "准备金类型",
        },
        {
          visible: true,
          align: "center",
          width: "150px",
          dict: "risk_liability_period_type",
          prop: "periodType",
          label: "期限类型",
        },
        {
          visible: true,
          align: "center",
          width: "150px",
          dict: "risk_liability_product_type",
          prop: "productType",
          label: "产品类型",
        },
        {
          visible: true,
          align: "center",
          width: "150px",
          dict: "risk_liability_exempt_type",
          prop: "exemptType",
          label: "豁免类型",
        },
        {
          visible: true,
          align: "center",
          width: "150px",
          dict: "risk_liability_risk_type",
          prop: "riskType",
          label: "主附类型",
        },
        {
          visible: true,
          align: "center",
          width: "150px",
          dict: "risk_liability_business_type",
          prop: "businessType",
          label: "业务类型",
        },
        {
          visible: true,
          align: "center",
          width: "150px",
          dict: "risk_liability_rs_calc_frequency",
          prop: "rsCalcFrequency",
          label: "再保计算频率",
        },
        {
          visible: true,
          align: "center",
          width: "150px",
          prop: "taxRate",
          label: "税率",
        },
        // { visible: true, align: 'center', width: '150px', dict: 'risk_liability_rs_contract_type', prop: 'rsContractType', label: '再保合同类型' },
        {
          visible: true,
          align: "center",
          width: "150px",
          dict: "risk_liability_deductible_type",
          prop: "deductibleType",
          label: "免赔类型",
        },
        {
          visible: true,
          align: "center",
          width: "150px",
          dict: "risk_liability_period_flag",
          prop: "periodFlag",
          label: "产品长短险标识",
        },
        {
          visible: true,
          align: "center",
          width: "150px",
          prop: "claimNotifyLimit",
          label: "理赔通知限额",
        },
        {
          visible: true,
          align: "center",
          width: "150px",
          prop: "claimInvolvedLimit",
          label: "理赔参与限额",
        },
        {
          visible: true,
          align: "center",
          width: "150px",
          prop: "claimCount",
          label: "理赔次数",
        },
        {
          visible: true,
          align: "center",
          width: "150px",
          dict: "regulator_product_type",
          prop: "insProductType",
          label: "保险产品大类-E",
        },
        {
          visible: true,
          align: "center",
          width: "150px",
          dict: "regulator_liability_type",
          prop: "insLiabilityType",
          label: "责任分类-E",
        },
        {
          visible: true,
          align: "center",
          width: "150px",
          prop: "periodTypeList",
          label: "保险期限类型-E",
        },
        {
          visible: true,
          align: "center",
          width: "150px",
          dict: "regulator_ins_gov_flag",
          prop: "insGovFlag",
          label: "政保合作业务标志",
        },
        {
          visible: true,
          align: "center",
          width: "150px",
          dict: "regulator_product_type",
          prop: "prpProductType",
          label: "保险产品大类-B",
        },
        {
          visible: true,
          align: "center",
          width: "150px",
          dict: "prp_liability_type",
          prop: "prpLiabilityType",
          label: "责任分类-B",
        },
        {
          visible: true,
          align: "center",
          width: "150px",
          dict: "prp_period_type",
          prop: "prpInsuPeriod",
          label: "保险期限类型-B",
        },
        {
          visible: true,
          align: "center",
          width: "150px",
          dict: "risk_liability_status",
          prop: "status",
          label: "状态",
        },
        {
          visible: true,
          align: "center",
          width: "150px",
          dict: "sys_user",
          prop: "updateBy",
          label: "操作人",
        },
        {
          visible: true,
          align: "center",
          width: "180px",
          prop: "updateTime",
          label: "操作时间",
        },
      ],
    };
  },
  created() {
    if (this.$route.params && this.$route.params.riskCode) {
      this.queryParams.riskCode = this.$route.params.riskCode;
    }
    this.getList();
  },
  activated() {
    if (this.$route.params && this.$route.params.riskCode) {
      this.queryParams.riskCode = this.$route.params.riskCode;
    }
    this.getList();
  },
  methods: {
    multipleContent(data, prop, name) {
      if (Array.isArray(data[prop])) {
        let str = [];
        data[prop].forEach((item) => {
          str.push(item[name]);
        });
        return str.join("、");
      } else {
        return "";
      }
    },
    /** 查询险种责任列表 */
    getList() {
      this.loading = true;
      listRiskLiability(this.queryParams).then((response) => {
        this.riskLiabilityList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 模态框确定
    dialogOk() {
      this.dialogCancel();
      this.getList();
      //新增和修改后，需要刷一下字典数据
      this.dict.reloadDict("risk_liability_reinsurance_duty");
    },
    // 模态框取消
    dialogCancel() {
      this.dialog = {
        title: "",
        visible: false,
        params: null,
        componentsName: null,
      };
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.dialog = {
        title: "添加险种责任",
        visible: true,
        params: { optionType: ADD },
        componentsName: "RiskLiabilityEdit",
      };
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      let data = JSON.parse(JSON.stringify(row));
      let arr = [];
      if (Array.isArray(data.periodTypeList)) {
        data.periodTypeList.forEach((item) => {
          arr.push(item.periodTypeCode);
        });
      }
      data.periodTypeList = arr;
      this.dialog = {
        title: "修改险种责任",
        visible: true,
        params: { ...data, optionType: UPDATE },
        componentsName: "RiskLiabilityEdit",
      };
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm("是否确认删除？")
        .then(function () {
          return delRiskLiability(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "/huida-reinsurance/reinsurance/riskLiability/export",
        {
          ...this.queryParams,
        },
        `险种责任信息_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
<style scoped>
::v-deep .el-dialog__body {
  padding: 0;
}
</style>
