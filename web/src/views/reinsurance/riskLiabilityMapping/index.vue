<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="险种编码" prop="riskCode" label-width="70px">
        <el-select v-model="queryParams.riskCode" placeholder="请选择险种编码" filterable clearable>
          <el-option v-for="item in dict.type.core_insurance_type"
            :key="item.value" :label="item.value + '/' + item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="再保责任编码" prop="rsLiabilityCode" label-width="100px">
        <el-select v-model="queryParams.rsLiabilityCode" placeholder="请选择再保责任编码" filterable clearable>
          <el-option v-for="item in dict.type.risk_liability_reinsurance_duty"
            :key="item.value" :label="item.value + '/' + item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status" label-width="50px">
        <el-select v-model="queryParams.status" placeholder="请选择状态" filterable clearable>
          <el-option v-for="item in dict.type.rlm_status"
            :key="Number(item.value)" :label="item.label" :value="Number(item.value)">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['reinsurance:riskLiabilityMapping:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
          @click="handleDelete" v-hasPermi="['reinsurance:riskLiabilityMapping:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['reinsurance:riskLiabilityMapping:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"
        :table-name="'riskLiabilityMappingTable'" :columns="riskLiabilityMappingTableColumns"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="riskLiabilityMappingList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column width="60" align="center" label="序号">
        <template slot-scope="scope">
          {{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column v-for="column in riskLiabilityMappingTableColumns" :label="column.label" :prop="column.prop"
        :key="column.prop" :align="column.align" :min-width="column.width" v-if="column.visible" show-overflow-tooltip>
        <template slot-scope="{ row }">
          <!-- 自定义处理 -->
          <template v-if="column.custom">
            <!-- 险种编码 -->
            <template v-if="column.prop === 'riskCode'">
              <div style="display: flex;align-items: center;">
                {{row[column.prop]}}/<dict-tag :options="dict.type[column.dict]" :value="row[column.prop]"/>
              </div>
            </template>
          </template>
          <template v-else-if="column.dict">
            <dict-tag :options="dict.type[column.dict]" :value="row[column.prop]"/>
          </template>
          <template v-else>{{ row[column.prop] }}</template>
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="150px" align="center" class-name="small-padding fixed-width" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['reinsurance:riskLiabilityMapping:update']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['reinsurance:riskLiabilityMapping:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :auto-scroll="false"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改再保责任映射对话框 -->
    <el-dialog :title="dialog.title" :visible.sync="dialog.visible"
      :close-on-click-modal="false" width="500px">
      <component :is="dialog.componentsName" v-if="dialog.visible"
      :params="dialog.params" @ok="dialogOk" @cancel="dialogCancel" />
    </el-dialog>

  </div>
</template>

<script>
import { listRiskLiabilityMapping, delRiskLiabilityMapping } from "@/api/reinsurance/riskLiabilityMapping";
import {ADD, UPDATE} from "@/utils/systemUtil";
import RiskLiabilityMappingEdit from "@/views/reinsurance/riskLiabilityMapping/edit.vue";

export default {
  name: "RiskLiabilityMapping",
  components: {
    RiskLiabilityMappingEdit
  },
  dicts: [
    'core_insurance_type',
    'risk_liability_reinsurance_duty',
    'rlm_reinsurance_liability',
    'rlm_status',
  ],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 公共模态框
      dialog: {
        title: '',
        visible: false,
        params: null,
        componentsName: null,
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        riskCode: null,
        rsLiabilityCode: null,
        status: 0
      },
      // 总条数
      total: 0,
      // 再保责任映射表格数据
      riskLiabilityMappingList: [],
      // 表格列配置
      riskLiabilityMappingTableColumns: [
        { visible: true, align: 'center', width: '250px', key: 0, dict: 'core_insurance_type', prop: 'riskCode', label: '险种编码', custom: true},
        { visible: true, align: 'center', width: '150px', key: 1, prop: 'rsLiabilityCode', label: '再保责任编码' },
        { visible: true, align: 'center', width: '150px', key: 2, prop: 'rsLiabilityName', label: '再保责任名称' },
        { visible: true, align: 'center', width: '150px', key: 3, prop: 'lisLiabilityCode', label: '核心责任编码' },
        { visible: true, align: 'center', width: '200px', key: 4, prop: 'lisLiabilityName', label: '核心责任名称' },
        { visible: true, align: 'center', width: '150px', key: 5, prop: 'clLiabilityCode', label: '理赔责任编码' },
        { visible: true, align: 'center', width: '200px', key: 6, prop: 'clLiabilityName', label: '理赔责任名称' },
        { visible: true, align: 'center', width: '150px', key: 7, prop: 'supLiabilityCode', label: '监管责任编码' },
        { visible: true, align: 'center', width: '150px', key: 8, prop: 'supLiabilityName', label: '监管责任名称' },
        { visible: true, align: 'center', width: '150px', key: 9, dict: 'rlm_status', prop: 'status', label: '状态' },
      ]
    };
  },
  created() {
    if(this.$route.params&&this.$route.params.riskCode){
      this.queryParams.riskCode = this.$route.params.riskCode
    }
    this.getList();
  },
  activated(){
    if(this.$route.params&&this.$route.params.riskCode){
      this.queryParams.riskCode = this.$route.params.riskCode
    }
    this.getList();
  },
  methods: {
    /** 查询再保责任映射列表 */
    getList() {
      this.loading = true;
      listRiskLiabilityMapping(this.queryParams).then(response => {
        this.riskLiabilityMappingList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 模态框确定
    dialogOk() {
      this.dialogCancel()
      this.getList()
    },
    // 模态框取消
    dialogCancel() {
      this.dialog = {
        title: '',
        visible: false,
        params: null,
        componentsName: null,
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.dialog = {
        title: '添加再保责任映射',
        visible: true,
        params: {optionType: ADD},
        componentsName: 'RiskLiabilityMappingEdit',
      }
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.dialog = {
        title: '修改再保责任映射',
        visible: true,
        params: {...row, optionType: UPDATE},
        componentsName: 'RiskLiabilityMappingEdit',
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除？').then(function() {
        return delRiskLiabilityMapping(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('/huida-reinsurance/reinsurance/riskLiability/mapping/export', {
        ...this.queryParams
      }, `再保责任映射_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
