<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="105px"
    >
      <el-form-item label="原始合同名称" prop="contractCode">
        <el-select
          v-model="queryParams.contractCode"
          placeholder="请输入原始合同名称"
          clearable
          filterable
        >
          <el-option
            v-for="item in contractCodeList"
            :key="item.contractCode"
            :label="item.contractCode + ' ' + item.contractName"
            :value="item.contractCode"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="再保公司" prop="companyCode">
        <el-select
          v-model="queryParams.companyCode"
          placeholder="请输入再保公司名称"
          clearable
          filterable
        >
          <el-option
            v-for="item in companyCodeList"
            :key="item.companyCode"
            :label="item.companyCode + ' ' + item.companyName"
            :value="item.companyCode"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="状态:" prop="status">
        <el-select
          clearable
          v-model="queryParams.status"
          placeholder="请选择状态"
        >
          <el-option
            v-for="(item, index) in dict.type.cedeout_contract_status"
            :label="item.label"
            :value="+item.value"
            :key="index + 'a'"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="typeList">
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <!-- <el-table-column label="序号" align="center" prop="id" width="80px" /> -->
      <el-table-column
        label="原始合同编码"
        align="center"
        prop="contractCode"
        :show-overflow-tooltip="true"
        width="140"
      />
      <el-table-column
        label="原始合同名称"
        align="center"
        prop="contractName"
        :show-overflow-tooltip="true"
        width="100"
      />
      <el-table-column
        label="原始合同简称"
        align="center"
        prop="contractAbbr"
        :show-overflow-tooltip="true"
        width="100"
      />
      <el-table-column
        label="再保公司编码"
        align="center"
        prop="companyCode"
        :show-overflow-tooltip="true"
        width="140"
      />
      <el-table-column
        label="合同签订时间"
        align="center"
        prop="signDate"
        width="120px"
      />
      <el-table-column
        label="合同生效日期"
        align="center"
        prop="effectiveDate"
        width="110px"
      />
      <el-table-column
        label="合同终止日期"
        align="center"
        prop="expiredDate"
        width="110px"
      />
      <el-table-column
        label="合同附约类型"
        align="center"
        prop="contractType"
        width="100"
      >
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.re_contract_type"
            :value="scope.row.contractType"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="临分合同标志"
        align="center"
        prop="cedeoutType"
        width="100"
      >
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.cedeout_type"
            :value="scope.row.cedeoutType"
          />
        </template>
      </el-table-column>

      <el-table-column
        label="再保合同类型"
        align="center"
        prop="contractClass"
        width="100"
      >
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.prp_reinsu_class"
            :value="scope.row.contractClass"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="合同属性"
        align="center"
        prop="contractAttr"
        width="100"
      >
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.prp_contract_attr"
            :value="scope.row.contractAttr"
          />
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.cedeout_contract_status"
            :value="scope.row.status"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="关联主约"
        align="center"
        prop="mainContractCode"
        width="110px"
      />
      <el-table-column label="操作人" align="center" prop="updateBy" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding"
        fixed="right"
        width="300px"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="navLiability(scope.row)"
            >险种责任关联</el-button
          >
          <!-- <el-button size="mini" type="text"  @click="showConfirmation(scope.row)">查看附件</el-button> -->
          <!-- <el-button size="mini" type="text" icon="el-icon-download" @click="fileView(scope.row)">下载附件</el-button> -->
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="850px" append-to-body>
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="130px"
        size="mini"
      >
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="再保公司名称：" prop="companyCode">
              <el-select
                v-model="form.companyCode"
                placeholder="请输入再保公司名称"
                clearable
                filterable
                style="width: 270px"
              >
                <el-option
                  v-for="item in companyCodeList"
                  :key="item.companyCode"
                  :label="item.companyCode + ' ' + item.companyName"
                  :value="item.companyCode"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="原始合同编码：" prop="contractCode">
              <el-input
                v-model="form.contractCode"
                placeholder="请输入原始合同编码"
                :disabled="form.id"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="原始合同名称：" prop="contractName">
              <el-input
                v-model="form.contractName"
                placeholder="请输入原始合同名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="原始合同简称：" prop="contractAbbr">
              <el-input
                v-model="form.contractAbbr"
                placeholder="请输入原始合同简称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合同签订时间：" prop="signDate">
              <el-date-picker
                style="width: 100%"
                v-model="form.signDate"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择合同签订时间"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合同生效日期：" prop="effectiveDate">
              <el-date-picker
                style="width: 100%"
                v-model="form.effectiveDate"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择合同生效日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合同终止日期：" prop="expiredDate">
              <el-date-picker
                style="width: 100%"
                v-model="form.expiredDate"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择合同终止日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合同附约类型：" prop="contractType">
              <el-select
                v-model="form.contractType"
                placeholder="请选择合同附约类型"
                style="width: 100%"
              >
                <el-option
                  v-for="dict in dict.type.re_contract_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="+dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="关联主约："
              prop="mainContractId"
              v-if="form.contractType == 1"
            >
              <el-select
                v-model="form.mainContractId"
                placeholder="请选择关联主约"
                style="width: 100%"
              >
                <el-option
                  v-for="item in mainContractCodeList"
                  :key="item.contractCode + 'a'"
                  :label="item.contractCode"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="临分合同标志：" prop="cedeoutType">
              <el-select
                v-model="form.cedeoutType"
                placeholder="请选择临分合同标志"
                style="width: 100%"
              >
                <el-option
                  v-for="item in dict.type.cedeout_type"
                  :key="item.value"
                  :label="item.label"
                  :value="Number(item.value)"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="再保合同类型：" prop="contractClass">
              <el-select
                v-model="form.contractClass"
                placeholder="请选择再保合同类型"
                style="width: 100%"
              >
                <el-option
                  v-for="item in dict.type.prp_reinsu_class"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合同属性：" prop="contractAttr">
              <el-select
                v-model="form.contractAttr"
                placeholder="请选择合同属性"
                style="width: 100%"
              >
                <el-option
                  v-for="item in dict.type.prp_contract_attr"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <file-list
            :params="{ fileType: 'CCT', businessCode: form.contractCode }"
            v-if="form.contractCode"
          />
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <update-warp
      :config="importConfig"
      @close="importConfig.visible = false"
      @uploadEnd="uploadEnd"
    />
  </div>
</template>

<script>
import {
  contractList,
  contractDetail,
  delContract,
  addContract,
  editContract,
  getMainContractCodeList,
  getContractCodeList,
  getCompanyCodeList,
  contractLiabilityList,
  contractLiabilityDetail,
  delContractLiability,
  addContractLiability,
  editContractLiability,
} from "../../../api/reinsurance/contract";
import { companyList } from "../../../api/reinsurance/company";
import UpdateWarp from "../../../components/ImportExcel";
import FileList from "../../../components/FileList/index.vue";

export default {
  name: "Contract",
  dicts: ["re_contract_type", "cedeout_contract_status", "cedeout_type", "prp_reinsu_class", "prp_contract_attr"],
  components: {
    UpdateWarp,
    FileList,
  },
  data() {
    return {
      file: "",
      fileList: [],
      companyList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      mainContractCodeList: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 字典表格数据
      typeList: [],
      contractCodeList: [],
      companyCodeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        contractCode: null,
        status: 0,
      },
      // 表单参数
      form: {
        companyCode: null,
        contractCode: null,
        contractName: null,
        contractAbbr: null,
        signDate: null,
        effectiveDate: null,
        expiredDate: null,
        contractType: null,
        mainContractId: null,
        cedeoutType: null,
        contractClass: null,
        contractAttr: null,
      },
      importConfig: {
        // 传入文件格式
        accept: ".xls, .xlsx, .doc, .docx",
        tips: "仅允许导入xls 、xlsx、doc、docx格式文件。",
        title: "导入合同",
        visible: false,
        name: "合同",
        url: "/",
        importUrl: "/huida-reinsurance/reinsurance/contract/importData",
      },
      // 表单校验
      rules: {
        companyCode: [
          { required: true, message: "再保公司编码不能为空", trigger: "blur" },
        ],
        contractCode: [
          { required: true, message: "原始合同编码不能为空", trigger: "blur" },
        ],
        contractName: [
          { required: true, message: "原始合同名称不能为空", trigger: "blur" },
        ],
        contractAbbr: [
          { required: true, message: "原始合同简称不能为空", trigger: "blur" },
        ],
        signDate: [
          { required: true, message: "签订时间不能为空", trigger: "blur" },
        ],
        effectiveDate: [
          { required: true, message: "生效日期不能为空", trigger: "blur" },
        ],
        expiredDate: [
          { required: true, message: "终止日期不能为空", trigger: "blur" },
        ],
        contractType: [
          { required: true, message: "合同附约类型不能为空", trigger: "blur" },
        ],
        cedeoutType: [
          { required: true, message: "临分合同标志不能为空", trigger: "blur" },
        ],
        contractClass: [
          { required: true, message: "再保合同类型不能为空", trigger: "blur" },
        ],
        contractAttr: [
          { required: true, message: "合同属性不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
    this.getContractCodeList();
    this.getCompanyCodeList();
  },
  methods: {
    // 跳转
    navLiability(row) {
      this.$router.push({
        path: "/cedeoutInfo/contractLiability",
        query: {
          id: row.id,
          contractCode: row.contractCode,
        },
      });
    },

    getContractCodeList() {
      getContractCodeList().then((response) => {
        this.contractCodeList = response.data;
      });
    },

    getCompanyCodeList() {
      getCompanyCodeList().then((response) => {
        this.companyCodeList = response.data;
      });
    },

    /** 查询字典类型列表 */
    getList() {
      this.loading = true;
      contractList(this.queryParams).then((response) => {
        this.typeList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getCompanyList() {
      companyList({ pageNum: 1, pageSize: 9999 }).then((response) => {
        this.companyList = response.rows;
      });
    },
    getMainContractCodeList() {
      getMainContractCodeList().then((response) => {
        this.mainContractCodeList = response.data;
        console.log("this.mainContractCodeList");
        console.log(this.mainContractCodeList);
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        contractCode: null,
        companyCode: null,
        contractAbbr: null,
        contractName: null,
        signDate: null,
        effectiveDate: null,
        contractType: null,
        mainContractCode: null,
        filePath: null,
        fileName: null,
        status: 0,
        cedeoutType: null,
        contractClass: null,
        contractAttr: null,
      };
      this.file = "";
      this.fileList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.getMainContractCodeList();
      this.getCompanyList();
      this.reset();
      this.open = true;
      this.title = "添加合同";
    },
    // // 多选框选中数据
    // handleSelectionChange(selection) {
    //   this.ids = selection.map(item => item.id)
    //   this.single = selection.length != 1
    //   this.multiple = !selection.length
    // },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.getMainContractCodeList();
      this.reset();
      const id = row.id || this.ids;
      contractDetail(id).then((response) => {
        this.form = response.data;
        // this.form.contYears = response.data.contYears.toString()
        // this.fileList = [{ name: this.form.srcFileName, url: this.form.contFileKey || '' }]
        this.open = true;
        this.title = "修改合同信息";
      });
    },
    async handleDownload(row) {
      if (!row.filePath) {
        return;
      }
      try {
        let response = await fetch(row.filePath);
        if (response.status !== 200) {
          this.$message.error("下载错误，请稍后重试");
          return;
        }
        // 内容转变成blob地址
        let blob = await response.blob();
        // 创建隐藏的可下载链接
        let objectUrl = window.URL.createObjectURL(blob);
        let a = document.createElement("a");
        a.style.display = "none";
        a.href = objectUrl;
        a.download = row.fileName;
        document.body.appendChild(a);
        a.click();
        //移除
        URL.revokeObjectURL(a.href);
        document.body.removeChild(a);
      } catch (error) {
        console.log(error);
      }
    },
    fileView(row) {
      let a = document.createElement("a");
      a.style.display = "none";
      a.href = row.filePath;
      a.target = "_blank";
      document.body.appendChild(a);
      a.click();
      //移除
      document.body.removeChild(a);
    },
    uploadChange(file, fileList) {
      this.file = file.raw;
      console.log(file);
      if (fileList.length > 1) {
        fileList.splice(0, 1);
      }
      this.fileList = fileList;
    },
    uploadRemove() {
      this.file = null;
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          const form = JSON.parse(JSON.stringify(this.form));
          // const formData = new FormData()
          //delete form.params
          // formData.append('file', this.file)
          // Object.keys(form).forEach(i => {
          //   if (i == 'contYears') {
          //     formData.append(i, +form[i])
          //   } else {
          //     formData.append(i, form[i])
          //   }
          // })
          if (form.id != undefined) {
            editContract(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addContract(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    reinsurerCodeChange(val) {
      this.form.reinsurerName = this.companyList.find((i) => {
        return (val = i.comCode);
      }).comName;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除合同编码为"' + row.contractCode + '"的数据项？')
        .then(function () {
          return delContract(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "huida-reinsurance/system/dict/type/export",
        {
          ...this.queryParams,
        },
        `type_${new Date().getTime()}.xlsx`
      );
    },
    uploadEnd(data) {
      this.importConfig.visible = false;
      this.form.fileName = data.fileName;
      this.form.filePath = data.fileUrl;

      // this.getList();
    },
    showConfirmation(row) {
      this.dialog = {
        title: "查看合同文件",
        visible: true,
        width: "650px",
        params: { fileType: "CCT", businessCode: `${row.contractCode}` },
        componentsName: "FileList",
      };
    },
  },
};
</script>
