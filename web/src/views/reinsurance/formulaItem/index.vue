<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="计算项名称" prop="itemName" label-width="90px">
        <el-autocomplete v-model="queryParams.itemName" value-key="label" :debounce="100"
          :fetch-suggestions="querySearch" placeholder="请输入计算项名称" clearable />
      </el-form-item>
<!--      <el-form-item label="算法类型" prop="formulaType" label-width="70px">-->
<!--        <el-select v-model="queryParams.formulaType" placeholder="请选择" filterable clearable>-->
<!--          <el-option v-for="item in dict.type.formula_type"-->
<!--            :key="Number(item.value)" :label="item.label" :value="Number(item.value)">-->
<!--          </el-option>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
      <el-form-item label="状态" prop="status" label-width="50px">
        <el-select v-model="queryParams.status" placeholder="请选择状态" filterable clearable>
          <el-option v-for="item in dict.type.formula_item_status"
            :key="Number(item.value)" :label="item.label" :value="Number(item.value)">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
<!--      <el-col :span="1.5">-->
<!--        <el-button type="primary" plain icon="el-icon-plus" size="mini"-->
<!--          @click="handleAdd" v-hasPermi="['reinsurance:item:add']">新增</el-button>-->
<!--      </el-col>-->
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
          @click="handleDelete" v-hasPermi="['reinsurance:item:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini"
          @click="handleExport" v-hasPermi="['reinsurance:item:export']" >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"
        :table-name="'formulaItemTable'" :columns="formulaItemTableColumns"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="formulaItemList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />\
      <el-table-column width="60" align="center" label="序号">
        <template slot-scope="scope">
          {{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column v-for="column in formulaItemTableColumns" :label="column.label" :prop="column.prop"
        :key="column.prop" :align="column.align" :min-width="column.width" v-if="column.visible" show-overflow-tooltip>
        <template slot-scope="{ row }">
          <template v-if="column.dict">
            <dict-tag :options="dict.type[column.dict]" :value="row[column.prop]"/>
          </template>
          <template v-else>{{ row[column.prop] }}</template>
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="150px" align="center" class-name="small-padding fixed-width" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['reinsurance:item:update']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['reinsurance:item:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改再保责任映射对话框 -->
    <el-dialog :title="dialog.title" :visible.sync="dialog.visible"
      :close-on-click-modal="false" width="500px">
      <component :is="dialog.componentsName" v-if="dialog.visible"
        :params="dialog.params" @ok="dialogOk" @cancel="dialogCancel" />
    </el-dialog>

  </div>
</template>

<script>
import { listItem, delItem } from "@/api/reinsurance/formulaItem";
import {ADD, UPDATE} from "@/utils/systemUtil";
import FormulaItemEdit from '@/views/reinsurance/formulaItem/edit.vue'

export default {
  name: "FormulaItem",
  dicts: [
    'formula_item_name',
    'formula_type',
    'formula_item_status',
  ],
  components: {
    FormulaItemEdit
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 公共模态框
      dialog: {
        title: '',
        visible: false,
        params: null,
        componentsName: null,
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        itemName: null,
        //formulaType: null,
        status: 0
      },
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 公式项表格数据
      formulaItemList: [],
      // 表格列配置
      formulaItemTableColumns: [
        { visible: true, align: 'center', width: '150px', key: 0, prop: 'itemName', label: '计算项名称' },
        { visible: true, align: 'center', width: '150px', key: 1, prop: 'itemEn', label: '计算项英文名称' },
        { visible: true, align: 'center', width: '150px', key: 2, prop: 'itemCode', label: '计算项编码' },
        //{ visible: true, align: 'center', width: '150px', key: 3, dict: 'formula_type', prop: 'formulaType', label: '算法类型' },
        { visible: true, align: 'center', width: '150px', key: 4, prop: 'itemExplain', label: '计算项解释' },
        { visible: true, align: 'center', width: '150px', key: 5, dict: 'formula_item_status', prop: 'status', label: '状态' },
      ]
    }
  },
  created() {
    //查询公式项列表
    this.getList();
  },
  methods: {
    /** 输入联想，字典取值 */
    querySearch(queryString, cb) {
      let data = this.dict.type.formula_item_name
      let results = queryString ? data.filter(item => item.value.toLowerCase().indexOf(queryString.toLocaleString()) >= 0) : data;
      cb(results);
    },
    /** 查询公式项列表 */
    getList() {
      this.loading = true;
      listItem(this.queryParams).then(response => {
        this.formulaItemList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 模态框确定
    dialogOk() {
      this.dialogCancel()
      this.getList()
      //新增和修改后，需要刷一下字典数据
      this.dict.reloadDict('formula_item_name')
    },
    // 模态框取消
    dialogCancel() {
      this.dialog = {
        title: '',
        visible: false,
        params: null,
        componentsName: null,
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除？').then(function() {
        return delItem(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('/huida-reinsurance/reinsurance/formula/item/export', {
        ...this.queryParams
      }, `计算项配置_${new Date().getTime()}.xlsx`)
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.dialog = {
        title: '添加计算项配置',
        visible: true,
        params: {optionType: ADD},
        componentsName: 'FormulaItemEdit',
      }
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.dialog = {
        title: '修改计算项配置',
        visible: true,
        params: {...row, optionType: UPDATE},
        componentsName: 'FormulaItemEdit',
      }
    },
  }
}
</script>
