<template>
  <div class="app-container">
    <el-card header="报表生成">
      <el-form :model="reportParams" ref="reportForm" size="small" :inline="true" v-show="showSearch" label-width="75px">
        <!-- <el-form-item label="报表类型" prop="reportType">
          <el-select v-model="reportParams.reportType" @change="typechange" placeholder="请选择报表类型" filterable clearable>
            <el-option v-for="item in dict.type.reinsurance_report_type"
              :key="item.value" :label="item.label" :value="+item.value">
            </el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="报表名称" prop="reportCode">
          <el-select v-model="reportParams.reportCode" @change="getList" placeholder="请选择报表名称" filterable clearable>
            <el-option v-for="item in totalList"
              :key="item.value" :label="item.reportName" :value="item.reportCode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="账单日期" prop="billingDate">
          <el-date-picker v-model="reportParams.billingDate" @change="getList" style="width:220px" type="daterange" range-separator="至"
            start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="险种编码" prop="riskCode" label-width="70px">
        <el-select v-model="reportParams.riskCodeStr" @change="getList" placeholder="请选择险种编码" filterable clearable>
          <el-option v-for="item in dict.type.core_insurance_type"
            :key="item.value" :label="item.value + '/' + item.label" :value="item.value + '|' + item.label">
          </el-option>
        </el-select>
      </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" icon="el-icon-search" size="mini" @click="checkParams"
             :loading="reportStatisticsLoading">报表统计</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="reportStatisticsQueryLoading" :data="tableList.data">
        <el-table-column align="center" label="序号" width="60">
          <template slot-scope="scope">
            {{ (reportParams.pageNum - 1) * reportParams.pageSize + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="报表类型" align="center" prop="reportType" show-overflow-tooltip>
          <template slot-scope="{row}">
            <dict-tag :options="dict.type.reinsurance_report_type" :value="row.reportType"/>
          </template>
        </el-table-column>
        <el-table-column label="报表名称" align="center" prop="reportName" show-overflow-tooltip />
        <el-table-column label="账单业务时间" align="center" prop="startDate" width="100px" show-overflow-tooltip>
          <template slot-scope="{row}">
            {{`${row.startDate} ~ ${row.endDate}`}}
          </template>
        </el-table-column>
        <el-table-column label="险种编码" align="center" prop="riskCode" show-overflow-tooltip />
        <el-table-column label="险种名称" align="center" prop="riskName" show-overflow-tooltip />
        <el-table-column label="状态" align="center" prop="status" show-overflow-tooltip>
          <template slot-scope="{row}">
            <dict-tag :options="dict.type.business_report_status" :value="row.status"/>
          </template>
        </el-table-column>
        <el-table-column label="操作人" align="center" prop="status" show-overflow-tooltip>
          <template slot-scope="{row}">
            <dict-tag :options="dict.type.sys_user" :value="row.createBy"/>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" show-overflow-tooltip/>
        <el-table-column label="修改时间" align="center" prop="updateTime" show-overflow-tooltip/>
        <el-table-column label="操作" align="center" width="120" fixed="right">
          <template slot-scope="{row}">
            <el-button size="mini" type="text" icon="el-icon-download"  @click="downloadReport(row)">下载</el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="deleteReport(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="tableList.total > 0"
        :total="tableList.total"
        :page.sync="reportParams.pageNum"
        :limit.sync="reportParams.pageSize"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>
<script>
import {deleteReinsuranceReportData,ReportStatistics,listReportReinsurance} from "@/api/reinsurance/reinsuranceReport";
import {downloadFileByPathApi} from "@/api/reinsurance/file";
import {downloadFileHandler} from "@/api/tool/file";
import {Loading } from 'element-ui';
export default {
  dicts: ['reinsurance_report_type', 'business_report_status', 'sys_user','core_insurance_type'],
  props: {
    templateType:{
      type:Number,
      default:''
    },
    reportCode:{
      type:String,
      default:''
    },
    totalList:{
      type:Array,
      default:''
    },
  },
  computed: {
  },
  data() {
    return {
      //显示搜索条件
      showSearch: true,
      //报表查询loading
      reportStatisticsQueryLoading: false,
      //报表统计参数
      reportParams: {
        pageNum: 1,
        pageSize: 10,
        reportType: null,
        billingDate: null,
        reportCode: null,
        riskCodeStr: null
      },
      //报表统计loading
      reportStatisticsLoading: false,
      //表格数据
      tableList: {
        data: [],
        total: 0
      }
    }
  },
  created() {
    this.reportParams.reportType = this.templateType
    this.reportParams.reportCode = this.reportCode
    this.getList()
  },
  methods: {
    //报表统计
    checkParams(){
      if (!this.reportParams.billingDate || this.reportParams.billingDate.length !== 2) {
        this.$message.error('请选择账单日期')
        return
      }
      if (!this.reportParams.reportType) {
        this.$message.error('请选择报表类型')
        return
      }
      if(!this.reportParams.riskCodeStr){
        this.$confirm('确定导出' + this.reportParams.billingDate[0] + '至' + this.reportParams.billingDate[1] + '期间的所有险种账单数据吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.reportParams.riskCodeStr = ''
          this.reportParams.riskCode = ''
          this.reportParams.riskName = ''
          this.reportStatistics()
        }).catch(() => {});
      }else{
        this.reportStatistics()
      }
    },
    reportStatistics() {
      console.log('this.reportParams.riskCodeStr.split('|')[0]',this.reportParams.riskCodeStr.split('|')[0])
      this.reportStatisticsLoading = true
      this.reportParams.startDate = this.reportParams.billingDate[0]
      this.reportParams.endDate = this.reportParams.billingDate[1]
      this.reportParams.riskCode = this.reportParams.riskCodeStr.split('|')[0]
      this.reportParams.riskName = this.reportParams.riskCodeStr.split('|')[1]
      let data = JSON.parse(JSON.stringify(this.reportParams))
      delete data.billingDate
      delete data.pageNum
      delete data.pageSize
      console.log("sssssssss",data)
      ReportStatistics(data).then(res => {
        this.$message.success('操作成功，请耐心等待')
        //统计完成后，清空查询条件
        //this.resetQuery()
        this.handleQuery();
      }).finally(() => {
        this.reportStatisticsLoading = false
      })
    },
    typechange(){
      this.reportParams.reportName =''
      this.getList()
    },
    //报表记录查询
    getList() {
      this.reportStatisticsQueryLoading = true
      if (this.reportParams.billingDate && this.reportParams.billingDate.length === 2) {
        this.reportParams.startDate = this.reportParams.billingDate[0]
        this.reportParams.endDate = this.reportParams.billingDate[1]
      }else{
        this.reportParams.billingDate=[]
        this.reportParams.startDate = ''
        this.reportParams.endDate = ''
      }
      if(this.reportParams.riskCodeStr){
        this.reportParams.riskCode = this.reportParams.riskCodeStr.split('|')[0]
        this.reportParams.riskName = this.reportParams.riskCodeStr.split('|')[1]
      }else{
        this.reportParams.riskCode = ''
        this.reportParams.riskName = ''
      }
      listReportReinsurance(this.reportParams).then(res => {
        console.info('报表：', res)
        this.tableList.data = res.rows
        this.tableList.total = res.total
      }).finally(() => {
        this.reportStatisticsQueryLoading = false
      })
    },
    //重置按钮操作
    resetQuery() {
      this.resetForm("reportForm");
      this.reportParams.billingDate = null
      this.reportParams.startDate = null
      this.reportParams.endDate = null
      this.reportParams.riskCodeStr = null
      this.handleQuery();
    },
    //搜索按钮操作
    handleQuery() {
      this.reportParams.pageNum = 1;
      this.getList();
    },
    //下载
    downloadReport(row) {
      if (!row.reportDataPath) {
        this.$message.error('文件路径失效')
        return
      }
      let downloadLoadingInstance = Loading.service({ text: "正在下载数据，请稍候", spinner: "el-icon-loading", background: "rgba(0, 0, 0, 0.7)", })
      let paths = row.reportDataPath.split('/')
      downloadFileByPathApi(row.reportDataPath).then(res => {
        downloadFileHandler(res, paths[paths.length - 1])
        downloadLoadingInstance.close();
      }).catch((r) => {
          Message.error('下载文件出现错误，请联系管理员！')
          downloadLoadingInstance.close();
      })
    },
    //删除
    deleteReport(row) {
      let $this = this
      this.$modal.confirm('是否确认删除？')
        .then(function() {
          deleteReinsuranceReportData(row.id).then(res => {
            $this.$message.success('删除成功')
            $this.handleQuery()
          })
        }).catch(e => console.info(e))
    },
    //导出
    exportReport() {
      this.download('huida-reinsurance/reinsurance/businessReport/export', {
        ...this.queryParams
      }, `业务报表统计记录_${new Date().getTime()}.xlsx`)
    }
  },
  watch: {
    reportCode(){
      this.reportParams.pageNum = 1;
      this.reportParams.reportType = this.templateType
      this.reportParams.reportCode = this.reportCode
      this.getList()
    },
    templateType(){
      this.reportParams.reportType = this.templateType
      this.reportParams.reportName = this.reportCode
      this.getList()
    },

  }
}
</script>
<style scoped>
::v-deep .el-upload {
  width: 100%;
  padding-left: 20px;
  padding-right: 20px;
}
::v-deep .el-upload-dragger {
  width: 100%;
}
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  padding: 20px 20px 0 0;
}
</style>
