package com.reinsurance.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.poi.ExcelUtil;

import com.reinsurance.dto.DwsPrpAccountDTO;
import com.reinsurance.query.DwsPrpAccountQuery;
import com.reinsurance.service.IDwsPrpAccountService;

import lombok.extern.slf4j.Slf4j;

/**
 * 保单登记再保账单信息Controller
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Slf4j
@RestController
@RequestMapping("/dws/prp/account")
public class DwsPrpAccountController extends BaseController {
    
    @Autowired
    private IDwsPrpAccountService dwsPrpAccountService;

    /**
     * 查询保单登记再保账单信息列表
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:account:list')")
    @GetMapping("/list")
    public TableDataInfo list(DwsPrpAccountQuery dwsPrpAccountQuery) {
        startPage();
        List<DwsPrpAccountDTO> list = dwsPrpAccountService.selectDwsPrpAccountList(dwsPrpAccountQuery);
        return getDataTable(list);
    }

    /**
     * 导出保单登记再保账单信息列表
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:account:export')")
    @Log(title = "保单登记再保账单信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody DwsPrpAccountQuery dwsPrpAccountQuery) {
        dwsPrpAccountService.exportDwsPrpAccount(response, dwsPrpAccountQuery);
    }

    /**
     * 获取保单登记再保账单信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:account:query')")
    @GetMapping(value = "/{Id}")
    public Result getInfo(@PathVariable("Id") Long Id) {
        return Result.success(dwsPrpAccountService.selectDwsPrpAccountById(Id));
    }

    /**
     * 新增保单登记再保账单信息
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:account:add')")
    @Log(title = "保单登记再保账单信息", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@RequestBody DwsPrpAccountDTO dwsPrpAccountDTO) {
        return toAjax(dwsPrpAccountService.insertDwsPrpAccount(dwsPrpAccountDTO));
    }

    /**
     * 修改保单登记再保账单信息
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:account:edit')")
    @Log(title = "保单登记再保账单信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@RequestBody DwsPrpAccountDTO dwsPrpAccountDTO) {
        return toAjax(dwsPrpAccountService.updateDwsPrpAccount(dwsPrpAccountDTO));
    }

    /**
     * 删除保单登记再保账单信息
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:account:remove')")
    @Log(title = "保单登记再保账单信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{Ids}")
    public Result remove(@PathVariable Long[] Ids) {
        return toAjax(dwsPrpAccountService.deleteDwsPrpAccountByIds(Ids));
    }

    /**
     * 导入保单登记再保账单信息
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:account:import')")
    @Log(title = "保单登记再保账单信息", businessType = BusinessType.IMPORT)
    @PostMapping("/import")
    public Result importData(@RequestParam("file") MultipartFile file, 
                           @RequestParam(value = "companyCode", defaultValue = "000166") String companyCode,
                           @RequestParam(value = "companyName", defaultValue = "弘康人寿保险股份有限公司") String companyName,
                           @RequestParam(value = "manageCom", defaultValue = "000166") String manageCom) {
        return dwsPrpAccountService.importDwsPrpAccount(companyCode, companyName, manageCom, file);
    }

    /**
     * 获取导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DwsPrpAccountDTO> util = new ExcelUtil<>(DwsPrpAccountDTO.class);
        util.importTemplateExcel(response, "保单登记再保账单信息数据");
    }

    /**
     * 推送保单登记再保账单信息
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:account:push')")
    @Log(title = "推送保单登记再保账单信息", businessType = BusinessType.UPDATE)
    @PostMapping("/push")
    public Result push(@RequestBody Long[] Ids) {
        return dwsPrpAccountService.updateDwsPrpAccountPushStatus(Ids);
    }

    /**
     * 检查保单登记再保账单信息是否存在
     */
    @PostMapping("/exists")
    public Result exists(@RequestBody DwsPrpAccountQuery dwsPrpAccountQuery) {
        int count = dwsPrpAccountService.selectDwsPrpAccountExists(dwsPrpAccountQuery);
        return Result.success(count > 0);
    }

    /**
     * 生成再保账单信息表数据
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:account:generate')")
    @Log(title = "生成再保账单信息表数据", businessType = BusinessType.INSERT)
    @PostMapping("/generate")
    public Result generateData(@RequestParam("startDate") String startDate,
                              @RequestParam("endDate") String endDate,
                              @RequestParam("reportYear") Integer reportYear,
                              @RequestParam("reportMonth") Integer reportMonth) {
        return dwsPrpAccountService.generatePrpAccountData(startDate, endDate, reportYear, reportMonth);
    }
}
