package com.reinsurance.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;

import com.alibaba.excel.EasyExcel;
import com.jd.lightning.common.exception.ServiceException;
import com.reinsurance.dto.ReservesFactorDTO;
import com.reinsurance.query.ReservesFactorQuery;
import com.reinsurance.service.IReservesFactorService;
import com.reinsurance.dto.ReservesFactorImportDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;

/**
 * 准备金因子Controller
 * 
 * <AUTHOR>
 * @date 2024-03-20
 */
@Api(tags = "准备金因子管理")
@Slf4j
@Validated
@RestController
@RequestMapping("/reinsurance/reservesFactor")
public class ReservesFactorController extends BaseController
{
    @Autowired
    private IReservesFactorService reservesFactorService;

    @ApiOperation(value = "查询准备金因子列表（分页）", response = ReservesFactorDTO.class)
    @PreAuthorize("@ss.hasPermi('reinsurance:reservesFactor:list')")
    @GetMapping("/list")
    public TableDataInfo list(ReservesFactorQuery reservesFactorQuery)
    {
        startPage();
        List<ReservesFactorDTO> list = reservesFactorService.selectReservesFactorList(reservesFactorQuery);
        return getDataTable(list);
    }

    @ApiOperation(value = "导出准备金因子列表", response = ReservesFactorDTO.class)
    @PreAuthorize("@ss.hasPermi('reinsurance:reservesFactor:export')")
    @Log(title = "准备金因子", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ReservesFactorQuery reservesFactorQuery)
    {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        List<ReservesFactorDTO> list = reservesFactorService.selectReservesFactorList(reservesFactorQuery);
        try {
            EasyExcel.write(response.getOutputStream(), ReservesFactorDTO.class).sheet("准备金因子").doWrite(list);
        } catch (Exception e) {
            log.warn("导出准备金因子失败：", e);
            throw new ServiceException("导出准备金因子失败，请联系管理员");
        }
    }

    @ApiOperation(value = "获取准备金因子详细信息", response = ReservesFactorDTO.class)
    @PreAuthorize("@ss.hasPermi('reinsurance:reservesFactor:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id)
    {
        return Result.success(reservesFactorService.selectReservesFactorById(id));
    }

    @ApiOperation(value = "新增准备金因子", response = Integer.class)
    @PreAuthorize("@ss.hasPermi('reinsurance:reservesFactor:add')")
    @Log(title = "准备金因子", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@RequestBody ReservesFactorDTO reservesFactorDTO)
    {
        return toAjax(reservesFactorService.insertReservesFactor(reservesFactorDTO));
    }

    @ApiOperation(value = "修改准备金因子", response = Integer.class)
    @PreAuthorize("@ss.hasPermi('reinsurance:reservesFactor:update')")
    @Log(title = "准备金因子", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@RequestBody ReservesFactorDTO reservesFactorDTO)
    {
        return toAjax(reservesFactorService.updateReservesFactor(reservesFactorDTO));
    }

    @ApiOperation(value = "删除准备金因子（批量删除）", response = Integer.class)
    @PreAuthorize("@ss.hasPermi('reinsurance:reservesFactor:remove')")
    @Log(title = "准备金因子", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids)
    {
        return toAjax(reservesFactorService.deleteReservesFactorByIds(ids));
    }

    @ApiOperation(value = "下载准备金因子导入模板")
    @PreAuthorize("@ss.hasPermi('reinsurance:reservesFactor:import')")
    @PostMapping({"/importTemplate"})
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<ReservesFactorImportDTO> util = new ExcelUtil<>(ReservesFactorImportDTO.class);
        util.importTemplateExcel(response, "准备金因子导入模板");
    }

    @ApiOperation(value = "导入准备金因子数据")
    @PreAuthorize("@ss.hasPermi('reinsurance:reservesFactor:import')")
    @PostMapping("/importReservesFactor")
    public Result importReservesFactor(@RequestParam("files") MultipartFile [] files,
                                       @RequestParam("riskCode") @NotBlank(message = "险种编码不能为空") String riskCode,
                                       @RequestParam("factorType") @NotBlank(message = "因子类型不能为空") String factorType) {
        return reservesFactorService.importReservesFactor(files, riskCode, factorType, this.getUsername());
    }

}
