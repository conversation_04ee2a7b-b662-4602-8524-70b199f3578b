package com.reinsurance.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.poi.ExcelUtil;

import com.reinsurance.dto.DwsPrpProductDTO;
import com.reinsurance.query.DwsPrpProductQuery;
import com.reinsurance.service.IDwsPrpProductService;

import lombok.extern.slf4j.Slf4j;

/**
 * 保单登记再保产品信息Controller
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Slf4j
@RestController
@RequestMapping("/dws/prp/product")
public class DwsPrpProductController extends BaseController {
    
    @Autowired
    private IDwsPrpProductService dwsPrpProductService;

    /**
     * 查询保单登记再保产品信息列表
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:product:list')")
    @GetMapping("/list")
    public TableDataInfo list(DwsPrpProductQuery dwsPrpProductQuery) {
        startPage();
        List<DwsPrpProductDTO> list = dwsPrpProductService.selectDwsPrpProductList(dwsPrpProductQuery);
        return getDataTable(list);
    }

    /**
     * 导出保单登记再保产品信息列表
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:product:export')")
    @Log(title = "保单登记再保产品信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DwsPrpProductQuery dwsPrpProductQuery) {
        dwsPrpProductService.exportDwsPrpProduct(response, dwsPrpProductQuery);
    }

    /**
     * 获取保单登记再保产品信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:product:query')")
    @GetMapping(value = "/{Id}")
    public Result getInfo(@PathVariable("Id") Long Id) {
        return Result.success(dwsPrpProductService.selectDwsPrpProductById(Id));
    }

    /**
     * 新增保单登记再保产品信息
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:product:add')")
    @Log(title = "保单登记再保产品信息", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@RequestBody DwsPrpProductDTO dwsPrpProductDTO) {
        return toAjax(dwsPrpProductService.insertDwsPrpProduct(dwsPrpProductDTO));
    }

    /**
     * 修改保单登记再保产品信息
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:product:edit')")
    @Log(title = "保单登记再保产品信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@RequestBody DwsPrpProductDTO dwsPrpProductDTO) {
        return toAjax(dwsPrpProductService.updateDwsPrpProduct(dwsPrpProductDTO));
    }

    /**
     * 删除保单登记再保产品信息
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:product:remove')")
    @Log(title = "保单登记再保产品信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{Ids}")
    public Result remove(@PathVariable Long[] Ids) {
        return toAjax(dwsPrpProductService.deleteDwsPrpProductByIds(Ids));
    }

    /**
     * 导入保单登记再保产品信息
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:product:import')")
    @Log(title = "保单登记再保产品信息", businessType = BusinessType.IMPORT)
    @PostMapping("/import")
    public Result importData(@RequestParam("file") MultipartFile file, 
                           @RequestParam(value = "companyCode", defaultValue = "000166") String companyCode,
                           @RequestParam(value = "companyName", defaultValue = "弘康人寿保险股份有限公司") String companyName,
                           @RequestParam(value = "manageCom", defaultValue = "000166") String manageCom) {
        return dwsPrpProductService.importDwsPrpProduct(companyCode, companyName, manageCom, file);
    }

    /**
     * 获取导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DwsPrpProductDTO> util = new ExcelUtil<DwsPrpProductDTO>(DwsPrpProductDTO.class);
        util.importTemplateExcel(response, "保单登记再保产品信息数据");
    }

    /**
     * 推送保单登记再保产品信息
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:product:push')")
    @Log(title = "保单登记再保产品信息推送", businessType = BusinessType.UPDATE)
    @GetMapping("/push/{Ids}")
    public Result push(@PathVariable Long[] Ids) {
        return dwsPrpProductService.updateDwsPrpProductPushStatus(Ids);
    }

    /**
     * 检查保单登记再保产品信息是否存在
     */
    @PostMapping("/exists")
    public Result exists(@RequestBody DwsPrpProductQuery dwsPrpProductQuery) {
        int count = dwsPrpProductService.selectDwsPrpProductExists(dwsPrpProductQuery);
        return Result.success(count > 0);
    }

    /**
     * 生成再保产品信息表数据
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:product:generate')")
    @Log(title = "生成再保产品信息表数据", businessType = BusinessType.INSERT)
    @PostMapping("/generate")
    public Result generateData(@RequestParam("startDate") String startDate,
                              @RequestParam("endDate") String endDate,
                              @RequestParam("reportYear") Integer reportYear,
                              @RequestParam("reportMonth") Integer reportMonth) {
        return dwsPrpProductService.generatePrpProductData(startDate, endDate, reportYear, reportMonth);
    }
}
