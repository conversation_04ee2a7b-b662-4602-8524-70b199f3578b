package com.reinsurance.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.reinsurance.dto.LiabilityMappingDTO;
import com.reinsurance.dto.RiskLiabilityDTO;
import com.reinsurance.query.LiabilityMappingQuery;
import com.reinsurance.query.RiskLiabilityQuery;
import com.reinsurance.service.IRiskLiabilityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.poi.ExcelUtil;

/**
 * 险种责任Controller
 * 
 * <AUTHOR>
 * @date 2024-03-20
 */
@Api(tags = "险种责任管理")
@RestController
@RequestMapping("/reinsurance/riskLiability")
public class RiskLiabilityController extends BaseController
{
    @Autowired
    private IRiskLiabilityService riskLiabilityService;

    @ApiOperation(value = "根据险种编码查询责任", response = RiskLiabilityDTO.class)
    @GetMapping(value = {"/getLiabilityByRiskCode/{riskCodes}", "/getLiabilityByRiskCode/"})
    public Result getLiabilityByRiskCode(@PathVariable(value = "riskCodes", required = false) List<String> riskCodes) {
        return Result.success(riskLiabilityService.getLiabilityByRiskCodes(riskCodes));
    }

    @ApiOperation(value = "根据险种编码查询核心", response = RiskLiabilityDTO.class)
    @GetMapping(value = {"/getCoreLiabilityByRiskCode/{riskCodes}", "/getCoreLiabilityByRiskCode/"})
    public Result getCoreLiabilityByRiskCode(@PathVariable(value = "riskCodes", required = false) List<String> riskCodes) {
        return Result.success(riskLiabilityService.getCoreLiabilityByRiskCodes(riskCodes));
    }

    @ApiOperation(value = "查询险种责任信息列表（分页）", response = RiskLiabilityDTO.class)
    @PreAuthorize("@ss.hasPermi('reinsurance:riskLiability:list')")
    @GetMapping("/list")
    public TableDataInfo list(RiskLiabilityQuery riskLiabilityQuery)
    {
        startPage();
        List<RiskLiabilityDTO> list = riskLiabilityService.selectRiskLiabilityList(riskLiabilityQuery);
        return getDataTable(list);
    }

    @ApiOperation(value = "导出险种责任信息列表", response = RiskLiabilityDTO.class)
    @PreAuthorize("@ss.hasPermi('reinsurance:riskLiability:export')")
    @Log(title = "险种责任", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RiskLiabilityQuery riskLiabilityQuery)
    {
        List<RiskLiabilityDTO> list = riskLiabilityService.selectRiskLiabilityList(riskLiabilityQuery);
        ExcelUtil<RiskLiabilityDTO> util = new ExcelUtil<>(RiskLiabilityDTO.class);
        util.exportExcel(response, list, "险种责任数据");
    }

    @ApiOperation(value = "获取险种责任详细信息", response = RiskLiabilityDTO.class)
    @PreAuthorize("@ss.hasPermi('reinsurance:riskLiability:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id)
    {
        return Result.success(riskLiabilityService.selectRiskLiabilityById(id));
    }

    @ApiOperation(value = "新增险种责任信息", response = Integer.class)
    @PreAuthorize("@ss.hasPermi('reinsurance:riskLiability:add')")
    @Log(title = "险种责任", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@RequestBody RiskLiabilityDTO riskLiabilityDTO)
    {
        return toAjax(riskLiabilityService.insertRiskLiability(riskLiabilityDTO));
    }

    @ApiOperation(value = "修改险种责任信息", response = Integer.class)
    @PreAuthorize("@ss.hasPermi('reinsurance:riskLiability:update')")
    @Log(title = "险种责任", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@RequestBody RiskLiabilityDTO riskLiabilityDTO)
    {
        return toAjax(riskLiabilityService.updateRiskLiability(riskLiabilityDTO));
    }

    @ApiOperation(value = "删除险种责任信息（批量删除）", response = Integer.class)
    @PreAuthorize("@ss.hasPermi('reinsurance:riskLiability:remove')")
    @Log(title = "险种责任", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids)
    {
        return toAjax(riskLiabilityService.deleteRiskLiabilityByIds(ids));
    }
    
    @ApiOperation(value = "查询再保责任映射列表（分页）", response = LiabilityMappingDTO.class)
    @PreAuthorize("@ss.hasPermi('reinsurance:riskLiabilityMapping:list')")
    @GetMapping("/mapping/list")
    public TableDataInfo listMapping(LiabilityMappingQuery liabilityMappingQuery)
    {
        startPage();
        List<LiabilityMappingDTO> list = riskLiabilityService.selectLiabilityMappingList(liabilityMappingQuery);
        return getDataTable(list);
    }

    @ApiOperation(value = "导出再保责任映射列表", response = LiabilityMappingDTO.class)
    @PreAuthorize("@ss.hasPermi('reinsurance:riskLiabilityMapping:export')")
    @Log(title = "再保责任映射", businessType = BusinessType.EXPORT)
    @PostMapping("/mapping/export")
    public void exportMapping(HttpServletResponse response, LiabilityMappingQuery liabilityMappingQuery)
    {
        List<LiabilityMappingDTO> list = riskLiabilityService.selectLiabilityMappingList(liabilityMappingQuery);
        ExcelUtil<LiabilityMappingDTO> util = new ExcelUtil<>(LiabilityMappingDTO.class);
        util.exportExcel(response, list, "再保责任映射数据");
    }

    @ApiOperation(value = "获取再保责任映射详细信息", response = LiabilityMappingDTO.class)
    @PreAuthorize("@ss.hasPermi('reinsurance:riskLiabilityMapping:query')")
    @GetMapping(value = "/mapping/{id}")
    public Result getMappingInfo(@PathVariable("id") Long id)
    {
        return Result.success(riskLiabilityService.selectLiabilityMappingById(id));
    }

    @ApiOperation(value = "新增再保责任映射", response = Integer.class)
    @PreAuthorize("@ss.hasPermi('reinsurance:riskLiabilityMapping:add')")
    @Log(title = "再保责任映射", businessType = BusinessType.INSERT)
    @PostMapping("/mapping")
    public Result addMapping(@RequestBody LiabilityMappingDTO liabilityMappingDTO)
    {
        return toAjax(riskLiabilityService.insertLiabilityMapping(liabilityMappingDTO));
    }

    @ApiOperation(value = "修改再保责任映射", response = Integer.class)
    @PreAuthorize("@ss.hasPermi('reinsurance:riskLiabilityMapping:update')")
    @Log(title = "再保责任映射", businessType = BusinessType.UPDATE)
    @PutMapping("/mapping")
    public Result editMapping(@RequestBody LiabilityMappingDTO liabilityMappingDTO)
    {
        return toAjax(riskLiabilityService.updateLiabilityMapping(liabilityMappingDTO));
    }

    @ApiOperation(value = "删除再保责任映射（批量删除）", response = Integer.class)
    @PreAuthorize("@ss.hasPermi('reinsurance:riskLiabilityMapping:remove')")
    @Log(title = "再保责任映射", businessType = BusinessType.DELETE)
	@DeleteMapping("/mapping/{ids}")
    public Result removeMapping(@PathVariable Long[] ids)
    {
        return toAjax(riskLiabilityService.deleteLiabilityMappingByIds(ids));
    }
}
