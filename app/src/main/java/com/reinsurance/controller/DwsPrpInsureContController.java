package com.reinsurance.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.poi.ExcelUtil;

import com.reinsurance.dto.DwsPrpInsureContDTO;
import com.reinsurance.query.DwsPrpInsureContQuery;
import com.reinsurance.service.IDwsPrpInsureContService;

import lombok.extern.slf4j.Slf4j;

/**
 * 保单登记再保合同信息Controller
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Slf4j
@RestController
@RequestMapping("/dws/prp/insure/cont")
public class DwsPrpInsureContController extends BaseController {
    
    @Autowired
    private IDwsPrpInsureContService dwsPrpInsureContService;

    /**
     * 查询保单登记再保合同信息列表
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:insure:cont:list')")
    @GetMapping("/list")
    public TableDataInfo list(DwsPrpInsureContQuery dwsPrpInsureContQuery) {
        startPage();
        List<DwsPrpInsureContDTO> list = dwsPrpInsureContService.selectDwsPrpInsureContList(dwsPrpInsureContQuery);
        return getDataTable(list);
    }

    /**
     * 导出保单登记再保合同信息列表
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:insure:cont:export')")
    @Log(title = "保单登记再保合同信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody DwsPrpInsureContQuery dwsPrpInsureContQuery) {
        dwsPrpInsureContService.exportDwsPrpInsureCont(response, dwsPrpInsureContQuery);
    }

    /**
     * 获取保单登记再保合同信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:insure:cont:query')")
    @GetMapping(value = "/{Id}")
    public Result getInfo(@PathVariable("Id") Long Id) {
        return Result.success(dwsPrpInsureContService.selectDwsPrpInsureContById(Id));
    }

    /**
     * 新增保单登记再保合同信息
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:insure:cont:add')")
    @Log(title = "保单登记再保合同信息", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@RequestBody DwsPrpInsureContDTO dwsPrpInsureContDTO) {
        return toAjax(dwsPrpInsureContService.insertDwsPrpInsureCont(dwsPrpInsureContDTO));
    }

    /**
     * 修改保单登记再保合同信息
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:insure:cont:edit')")
    @Log(title = "保单登记再保合同信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@RequestBody DwsPrpInsureContDTO dwsPrpInsureContDTO) {
        return toAjax(dwsPrpInsureContService.updateDwsPrpInsureCont(dwsPrpInsureContDTO));
    }

    /**
     * 删除保单登记再保合同信息
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:insure:cont:remove')")
    @Log(title = "保单登记再保合同信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{Ids}")
    public Result remove(@PathVariable Long[] Ids) {
        return toAjax(dwsPrpInsureContService.deleteDwsPrpInsureContByIds(Ids));
    }

    /**
     * 导入保单登记再保合同信息
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:insure:cont:import')")
    @Log(title = "保单登记再保合同信息", businessType = BusinessType.IMPORT)
    @PostMapping("/import")
    public Result importData(@RequestParam("file") MultipartFile file, 
                           @RequestParam(value = "companyCode", defaultValue = "000166") String companyCode,
                           @RequestParam(value = "companyName", defaultValue = "弘康人寿保险股份有限公司") String companyName,
                           @RequestParam(value = "manageCom", defaultValue = "000166") String manageCom) {
        return dwsPrpInsureContService.importDwsPrpInsureCont(companyCode, companyName, manageCom, file);
    }

    /**
     * 获取导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DwsPrpInsureContDTO> util = new ExcelUtil<>(DwsPrpInsureContDTO.class);
        util.importTemplateExcel(response, "保单登记再保合同信息数据");
    }

    /**
     * 推送保单登记再保合同信息
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:insure:cont:push')")
    @Log(title = "推送保单登记再保合同信息", businessType = BusinessType.UPDATE)
    @PostMapping("/push")
    public Result push(@RequestBody Long[] Ids) {
        return dwsPrpInsureContService.updateDwsPrpInsureContPushStatus(Ids);
    }

    /**
     * 检查保单登记再保合同信息是否存在
     */
    @PostMapping("/exists")
    public Result exists(@RequestBody DwsPrpInsureContQuery dwsPrpInsureContQuery) {
        int count = dwsPrpInsureContService.selectDwsPrpInsureContExists(dwsPrpInsureContQuery);
        return Result.success(count > 0);
    }

    /**
     * 生成再保合同信息表数据
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:insure:cont:generate')")
    @Log(title = "生成再保合同信息表数据", businessType = BusinessType.INSERT)
    @PostMapping("/generate")
    public Result generateData(@RequestParam("startDate") String startDate,
                              @RequestParam("endDate") String endDate,
                              @RequestParam("reportYear") Integer reportYear,
                              @RequestParam("reportMonth") Integer reportMonth) {
        return dwsPrpInsureContService.generatePrpInsureContData(startDate, endDate, reportYear, reportMonth);
    }
}
