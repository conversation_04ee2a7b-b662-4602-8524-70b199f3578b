package com.reinsurance.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.reinsurance.dto.CedeoutBatchLogDTO;
import com.reinsurance.query.CedeoutBatchLogQuery;
import com.reinsurance.service.ICedeoutBatchLogService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.poi.ExcelUtil;

/**
 * 再保分出日志Controller
 * 
 * <AUTHOR>
 * @date 2024-03-21
 */
@RestController
@RequestMapping("/reinsurance/cedeoutBatchLog")
public class CedeoutBatchLogController extends BaseController
{
    @Autowired
    private ICedeoutBatchLogService cedeoutBatchLogService;

    /**
     * 查询再保分出日志列表
     */
    @PreAuthorize("@ss.hasPermi('reinsurance:cedeoutBatchLog:list')")
    @GetMapping("/list")
    public TableDataInfo list(CedeoutBatchLogQuery cedeoutBatchLogQuery)
    {
        startPage();
        List<CedeoutBatchLogDTO> list = cedeoutBatchLogService.selectCedeoutBatchLogList(cedeoutBatchLogQuery);
        return getDataTable(list);
    }

    /**
     * 导出再保分出日志列表
     */
    @PreAuthorize("@ss.hasPermi('reinsurance:cedeoutBatchLog:export')")
    @Log(title = "再保分出日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CedeoutBatchLogQuery cedeoutBatchLogQuery)
    {
        List<CedeoutBatchLogDTO> list = cedeoutBatchLogService.selectCedeoutBatchLogList(cedeoutBatchLogQuery);
        ExcelUtil<CedeoutBatchLogDTO> util = new ExcelUtil<CedeoutBatchLogDTO>(CedeoutBatchLogDTO.class);
        util.exportExcel(response, list, "再保分出日志数据");
    }

    /**
     * 获取再保分出日志详细信息
     */
    @PreAuthorize("@ss.hasPermi('reinsurance:cedeoutBatchLog:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id)
    {
        return Result.success(cedeoutBatchLogService.selectCedeoutBatchLogById(id));
    }

    /**
     * 新增再保分出日志
     */
    @PreAuthorize("@ss.hasPermi('reinsurance:cedeoutBatchLog:add')")
    @Log(title = "再保分出日志", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@RequestBody CedeoutBatchLogDTO cedeoutBatchLogDTO)
    {
        return toAjax(cedeoutBatchLogService.insertCedeoutBatchLog(cedeoutBatchLogDTO));
    }

    /**
     * 修改再保分出日志
     */
    @PreAuthorize("@ss.hasPermi('reinsurance:cedeoutBatchLog:edit')")
    @Log(title = "再保分出日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@RequestBody CedeoutBatchLogDTO cedeoutBatchLogDTO)
    {
        return toAjax(cedeoutBatchLogService.updateCedeoutBatchLog(cedeoutBatchLogDTO));
    }

    /**
     * 删除再保分出日志
     */
    @PreAuthorize("@ss.hasPermi('reinsurance:cedeoutBatchLog:remove')")
    @Log(title = "再保分出日志", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids)
    {
        return toAjax(cedeoutBatchLogService.deleteCedeoutBatchLogByIds(ids));
    }
}
