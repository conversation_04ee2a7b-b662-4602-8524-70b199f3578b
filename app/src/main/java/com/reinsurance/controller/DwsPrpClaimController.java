package com.reinsurance.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.poi.ExcelUtil;

import com.reinsurance.dto.DwsPrpClaimDTO;
import com.reinsurance.query.DwsPrpClaimQuery;
import com.reinsurance.service.IDwsPrpClaimService;

import lombok.extern.slf4j.Slf4j;

/**
 * 再保理赔险种明细Controller
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Slf4j
@RestController
@RequestMapping("/dws/prp/claim")
public class DwsPrpClaimController extends BaseController {
    
    @Autowired
    private IDwsPrpClaimService dwsPrpClaimService;

    /**
     * 查询再保理赔险种明细列表
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:claim:list')")
    @GetMapping("/list")
    public TableDataInfo list(DwsPrpClaimQuery dwsPrpClaimQuery) {
        startPage();
        List<DwsPrpClaimDTO> list = dwsPrpClaimService.selectDwsPrpClaimList(dwsPrpClaimQuery);
        return getDataTable(list);
    }

    /**
     * 导出再保理赔险种明细列表
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:claim:export')")
    @Log(title = "再保理赔险种明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody DwsPrpClaimQuery dwsPrpClaimQuery) {
        dwsPrpClaimService.exportDwsPrpClaim(response, dwsPrpClaimQuery);
    }

    /**
     * 获取再保理赔险种明细详细信息
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:claim:query')")
    @GetMapping(value = "/{Id}")
    public Result getInfo(@PathVariable("Id") Long Id) {
        return Result.success(dwsPrpClaimService.selectDwsPrpClaimById(Id));
    }

    /**
     * 新增再保理赔险种明细
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:claim:add')")
    @Log(title = "再保理赔险种明细", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@RequestBody DwsPrpClaimDTO dwsPrpClaimDTO) {
        return toAjax(dwsPrpClaimService.insertDwsPrpClaim(dwsPrpClaimDTO));
    }

    /**
     * 修改再保理赔险种明细
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:claim:edit')")
    @Log(title = "再保理赔险种明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@RequestBody DwsPrpClaimDTO dwsPrpClaimDTO) {
        return toAjax(dwsPrpClaimService.updateDwsPrpClaim(dwsPrpClaimDTO));
    }

    /**
     * 删除再保理赔险种明细
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:claim:remove')")
    @Log(title = "再保理赔险种明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{Ids}")
    public Result remove(@PathVariable Long[] Ids) {
        return toAjax(dwsPrpClaimService.deleteDwsPrpClaimByIds(Ids));
    }

    /**
     * 导入再保理赔险种明细
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:claim:import')")
    @Log(title = "再保理赔险种明细", businessType = BusinessType.IMPORT)
    @PostMapping("/import")
    public Result importData(@RequestParam("file") MultipartFile file, 
                           @RequestParam(value = "companyCode", defaultValue = "000166") String companyCode,
                           @RequestParam(value = "companyName", defaultValue = "弘康人寿保险股份有限公司") String companyName,
                           @RequestParam(value = "manageCom", defaultValue = "000166") String manageCom) {
        return dwsPrpClaimService.importDwsPrpClaim(companyCode, companyName, manageCom, file);
    }

    /**
     * 获取导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DwsPrpClaimDTO> util = new ExcelUtil<>(DwsPrpClaimDTO.class);
        util.importTemplateExcel(response, "再保理赔险种明细数据");
    }

    /**
     * 推送再保理赔险种明细
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:claim:push')")
    @Log(title = "推送再保理赔险种明细", businessType = BusinessType.UPDATE)
    @PostMapping("/push")
    public Result push(@RequestBody Long[] Ids) {
        return dwsPrpClaimService.updateDwsPrpClaimPushStatus(Ids);
    }

    /**
     * 检查再保理赔险种明细是否存在
     */
    @PostMapping("/exists")
    public Result exists(@RequestBody DwsPrpClaimQuery dwsPrpClaimQuery) {
        int count = dwsPrpClaimService.selectDwsPrpClaimExists(dwsPrpClaimQuery);
        return Result.success(count > 0);
    }

    /**
     * 生成再保理赔险种明细数据
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:claim:generate')")
    @Log(title = "生成再保理赔险种明细数据", businessType = BusinessType.INSERT)
    @PostMapping("/generate")
    public Result generateData(@RequestParam("startDate") String startDate,
                              @RequestParam("endDate") String endDate,
                              @RequestParam("reportYear") Integer reportYear,
                              @RequestParam("reportMonth") Integer reportMonth) {
        return dwsPrpClaimService.generatePrpClaimData(startDate, endDate, reportYear, reportMonth);
    }
}
