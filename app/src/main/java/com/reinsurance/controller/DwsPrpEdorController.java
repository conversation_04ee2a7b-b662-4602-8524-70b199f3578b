package com.reinsurance.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.poi.ExcelUtil;

import com.reinsurance.dto.DwsPrpEdorDTO;
import com.reinsurance.query.DwsPrpEdorQuery;
import com.reinsurance.service.IDwsPrpEdorService;

import lombok.extern.slf4j.Slf4j;

/**
 * 再保保全险种明细Controller
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Slf4j
@RestController
@RequestMapping("/dws/prp/edor")
public class DwsPrpEdorController extends BaseController {
    
    @Autowired
    private IDwsPrpEdorService dwsPrpEdorService;

    /**
     * 查询再保保全险种明细列表
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:edor:list')")
    @GetMapping("/list")
    public TableDataInfo list(DwsPrpEdorQuery dwsPrpEdorQuery) {
        startPage();
        List<DwsPrpEdorDTO> list = dwsPrpEdorService.selectDwsPrpEdorList(dwsPrpEdorQuery);
        return getDataTable(list);
    }

    /**
     * 导出再保保全险种明细列表
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:edor:export')")
    @Log(title = "再保保全险种明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody DwsPrpEdorQuery dwsPrpEdorQuery) {
        dwsPrpEdorService.exportDwsPrpEdor(response, dwsPrpEdorQuery);
    }

    /**
     * 获取再保保全险种明细详细信息
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:edor:query')")
    @GetMapping(value = "/{Id}")
    public Result getInfo(@PathVariable("Id") Long Id) {
        return Result.success(dwsPrpEdorService.selectDwsPrpEdorById(Id));
    }

    /**
     * 新增再保保全险种明细
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:edor:add')")
    @Log(title = "再保保全险种明细", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@RequestBody DwsPrpEdorDTO dwsPrpEdorDTO) {
        return toAjax(dwsPrpEdorService.insertDwsPrpEdor(dwsPrpEdorDTO));
    }

    /**
     * 修改再保保全险种明细
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:edor:edit')")
    @Log(title = "再保保全险种明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@RequestBody DwsPrpEdorDTO dwsPrpEdorDTO) {
        return toAjax(dwsPrpEdorService.updateDwsPrpEdor(dwsPrpEdorDTO));
    }

    /**
     * 删除再保保全险种明细
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:edor:remove')")
    @Log(title = "再保保全险种明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{Ids}")
    public Result remove(@PathVariable Long[] Ids) {
        return toAjax(dwsPrpEdorService.deleteDwsPrpEdorByIds(Ids));
    }

    /**
     * 导入再保保全险种明细
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:edor:import')")
    @Log(title = "再保保全险种明细", businessType = BusinessType.IMPORT)
    @PostMapping("/import")
    public Result importData(@RequestParam("file") MultipartFile file, 
                           @RequestParam(value = "companyCode", defaultValue = "000166") String companyCode,
                           @RequestParam(value = "companyName", defaultValue = "弘康人寿保险股份有限公司") String companyName,
                           @RequestParam(value = "manageCom", defaultValue = "000166") String manageCom) {
        return dwsPrpEdorService.importDwsPrpEdor(companyCode, companyName, manageCom, file);
    }

    /**
     * 获取导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DwsPrpEdorDTO> util = new ExcelUtil<>(DwsPrpEdorDTO.class);
        util.importTemplateExcel(response, "再保保全险种明细数据");
    }

    /**
     * 推送再保保全险种明细
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:edor:push')")
    @Log(title = "推送再保保全险种明细", businessType = BusinessType.UPDATE)
    @PostMapping("/push")
    public Result push(@RequestBody Long[] Ids) {
        return dwsPrpEdorService.updateDwsPrpEdorPushStatus(Ids);
    }

    /**
     * 检查再保保全险种明细是否存在
     */
    @PostMapping("/exists")
    public Result exists(@RequestBody DwsPrpEdorQuery dwsPrpEdorQuery) {
        int count = dwsPrpEdorService.selectDwsPrpEdorExists(dwsPrpEdorQuery);
        return Result.success(count > 0);
    }

    /**
     * 生成再保保全险种明细数据
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:edor:generate')")
    @Log(title = "生成再保保全险种明细数据", businessType = BusinessType.INSERT)
    @PostMapping("/generate")
    public Result generateData(@RequestParam("startDate") String startDate,
                              @RequestParam("endDate") String endDate,
                              @RequestParam("reportYear") Integer reportYear,
                              @RequestParam("reportMonth") Integer reportMonth) {
        return dwsPrpEdorService.generatePrpEdorData(startDate, endDate, reportYear, reportMonth);
    }
}
