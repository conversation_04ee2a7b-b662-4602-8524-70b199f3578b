package com.reinsurance.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;

import com.jd.lightning.common.core.domain.R;
import com.jd.lightning.common.exception.ServiceException;
import com.jd.lightning.common.utils.DateUtils;
import com.jd.lightning.system.service.ISysConfigService;
import com.reinsurance.constant.RsConstant;
import com.reinsurance.domain.DwsContHistoryEntity;
import com.reinsurance.dto.CedeoutBatchLogDTO;
import com.reinsurance.dto.DwsReinsuTradeDTO;
import com.reinsurance.dto.JobParamDTO;
import com.reinsurance.dto.RiskLiabilityDTO;
import com.reinsurance.query.DwsReinsuTradeQuery;
import com.reinsurance.service.ICedeoutBatchLogService;
import com.reinsurance.service.IDwsContHistoryService;
import com.reinsurance.service.IDwsReinsuTradeService;
import com.reinsurance.service.IFormulaService;
import com.reinsurance.service.IRiskLiabilityService;
import com.reinsurance.enums.BasicDataEnums.DataTrackType;
import com.reinsurance.enums.BasicDataEnums.FormulaType;
import com.reinsurance.enums.BasicDataEnums.HandleStatus;
import com.reinsurance.enums.CedeoutEnums;
import com.reinsurance.utils.CedeoutUtils;
import com.reinsurance.utils.ReinsuJsonUtil;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 理赔生成摊回记录<br/>
 * 1、查询所有待处理的理赔记录<br/>
 * 2、获取保单未摊回/已终止的分出记录<br/>
 * 3、以以事故日期为分隔点将分出账单分隔为事故日期前、后分出<br/>
 * 4、生成摊回记录事故日期后的分出退还再保费，事故日期前的分出摊回理赔款<br/>
 * 5、保存摊回记录，更新分出记录的摊回状态<br/>
 * 6、更新理赔记录的处理状态
 * <AUTHOR>
 * @version 1.1
 * @date 2025-01-09
 */
@Slf4j
@Component("dwsClaim2ReinsuTradeHandler")
public class DwsClaim2ReinsuTradeHandler {
	
	@Autowired
    private IFormulaService formulaService;
	
    @Autowired
    private ISysConfigService sysConfigService;
    
    @Autowired
	private IRiskLiabilityService riskLiabilityService;
    
    @Autowired
    private IDwsContHistoryService dwsContHistoryService;

    @Autowired
    private IDwsReinsuTradeService dwsReinsuTradeService;

    @Autowired
    private ICedeoutBatchLogService cedeoutBatchLogService;

    public void handler(String batchNo, JobParamDTO jobParam, CedeoutBatchLogDTO cedeoutBatchLog) {
    	try {
    		log.info("再保理赔生成摊回批处理开始, batchNo:{}, jobParam:{}", batchNo, ReinsuJsonUtil.toJsonString(jobParam));
    		String mysqlBaseName = sysConfigService.selectConfigByKey(RsConstant.mysqlBaseName);
    		int limit = Integer.parseInt(sysConfigService.selectConfigByKey(RsConstant.jobOnceMaxRows));
    		Map<String, RiskLiabilityDTO> riskLiabilityMap = riskLiabilityService.selectRiskLiabilityHash();
    		while(true) {
    			List<DwsContHistoryEntity> claimList = dwsContHistoryService.selectHistoryListByBusiType(mysqlBaseName, CedeoutEnums.业务类型_理赔.getValue(), HandleStatus.未处理.getCode(), jobParam.getEndDate(), limit);
    			if (CollUtil.isEmpty(claimList)) {
                    log.info("再保理赔生成摊回批处理执行结束，未查询到理赔保单");
                    return;
                }
    			List<DwsReinsuTradeDTO> returnReinsuTradeList = new ArrayList<>();//摊回数据
                List<DwsReinsuTradeDTO> cedeoutReinsuTradeList = new ArrayList<>();//需要释放风险保额的原有分出数据
                List<Map<String, Object>> updateHistoryList = new ArrayList<Map<String, Object>>();
                for (DwsContHistoryEntity claim : claimList) {
                	Map<String, Object> claimResultMap = this.handlerClaim(cedeoutBatchLog, claim, cedeoutReinsuTradeList, returnReinsuTradeList, riskLiabilityMap);
                	updateHistoryList.add(claimResultMap);
                }
                try {//新增数据
	                if(CollUtil.isNotEmpty(cedeoutReinsuTradeList)) {
	        			dwsReinsuTradeService.updateBatchDwsReinsuTrade(cedeoutReinsuTradeList);
	        		}
	                if(CollUtil.isNotEmpty(returnReinsuTradeList)) {
                		int passCount = dwsReinsuTradeService.insertBatchDwsReinsuTrade(returnReinsuTradeList);
                		cedeoutBatchLog.setFailCount(null);
                		cedeoutBatchLog.setPassCount(passCount);
                    	cedeoutBatchLogService.updateCedeoutBatchLogCount(cedeoutBatchLog);//更新日志成功数
	                }
                }catch(Exception e) {
            		log.error("再保理赔生成摊回批处理异常, batchNo:{}, jobParam:{}", batchNo, ReinsuJsonUtil.toJsonString(jobParam), e);
            	}
                dwsContHistoryService.updateBatchHistoryHandleStatus(updateHistoryList);
                if(claimList.size() < limit) {//已处理完成
					break;
				}
    		}
            log.error("再保理赔生成摊回批处理完成, batchNo:{}, jobParam:{}", batchNo, ReinsuJsonUtil.toJsonString(jobParam));
    	}catch(Exception e) {
    		log.error("再保理赔生成摊回批处理异常, batchNo:{}, jobParam:{}", batchNo, ReinsuJsonUtil.toJsonString(jobParam), e);
    	}
    }
    
    
    /**
     * 逐条理赔处理
     * @param cedeoutBatchLog
     * @param claim
     * @param cedeoutReinsuTradeList
     * @param returnReinsuTradeList
     * @param riskLiabilityMap
     * @return
     */
    private Map<String, Object> handlerClaim(CedeoutBatchLogDTO cedeoutBatchLog, DwsContHistoryEntity claim, List<DwsReinsuTradeDTO> cedeoutReinsuTradeList, List<DwsReinsuTradeDTO> returnReinsuTradeList, Map<String, RiskLiabilityDTO> riskLiabilityMap){
		Map<String, Object> claimResultMap = MapUtil.newHashMap();
		claimResultMap.put("busi_type", claim.getBusiType());
		claimResultMap.put("unique_key", claim.getUniqueKey());
        claimResultMap.put("handle_error_num", claim.getHandleErrorNum());
        claimResultMap.put("handle_error_msg", claim.getHandleErrorMsg());
        claimResultMap.put("handle_status", HandleStatus.处理成功.getCode());
    	try {
            List<DwsReinsuTradeDTO> dwsReinsuTradeList = this.queryCedeoutTradeList(claim);
            if (CollUtil.isEmpty(dwsReinsuTradeList)) {//查询分出记录
                log.info("再保理赔生成摊回批处理执行中，未查询到分出记录, dwsClaim: {}", ReinsuJsonUtil.toJsonString(claim));
                claimResultMap.put("handle_date", DateUtils.getNowDate());
                claimResultMap.put("handle_status", HandleStatus.处理失败.getCode());
                claimResultMap.put("handle_error_msg", "未查询到分出记录, 无法产生摊回记录");
                claimResultMap.put("handle_error_num", claim.getHandleErrorNum() + 1);
                return claimResultMap;
            }
            List<DwsReinsuTradeDTO> accidentAfterTradeList = null;//事故日期及之后产生的分出，需要返回再保费，返回佣金，增值税
            List<DwsReinsuTradeDTO> accidentBeforeLastTradeList = null;//事故日期之前产生的分出（最后一次），需要摊回理赔款
            
        	List<DwsReinsuTradeDTO> accidentBeforeTradeList = dwsReinsuTradeList.stream().filter(trade -> DateUtil.compare(trade.getBusiOccurDate(), claim.getClmAccidentDate()) < 0).collect(Collectors.toList());
        	if(CollUtil.isNotEmpty(accidentBeforeTradeList)) {//事故之前产生了分出
        		Date maxBusiOccurDate = accidentBeforeTradeList.stream().max(Comparator.comparing(DwsReinsuTradeDTO::getBusiOccurDate)).get().getBusiOccurDate();
        		accidentBeforeLastTradeList = accidentBeforeTradeList.stream().filter(trade -> DateUtil.compare(trade.getBusiOccurDate(), maxBusiOccurDate) == 0).collect(Collectors.toList());
        	}
        	accidentAfterTradeList = dwsReinsuTradeList.stream().filter(trade -> DateUtil.compare(trade.getBusiOccurDate(), claim.getClmAccidentDate()) >= 0).collect(Collectors.toList());
            
            if(CollUtil.isNotEmpty(accidentAfterTradeList)) {//事故日期及之后产生的分出，需要退还再保费，返还佣金，返还增值税
                for(DwsReinsuTradeDTO cedeoutReinsuTrade : accidentAfterTradeList) {
                	if(CedeoutEnums.计算状态_忽略.getValue() == cedeoutReinsuTrade.getCalcStatus()) {//未达溢额线的分出记录，在原记录上释放风险保额
                		if(cedeoutReinsuTrade.getReturnStatus() == CedeoutEnums.摊回状态_未摊回.getValue() && cedeoutReinsuTrade.getOccupyRiskAmount() != null) {
                			cedeoutReinsuTrade.setReleaseRiskAmount(cedeoutReinsuTrade.getOccupyRiskAmount().negate());//有可能之前续期已释放，不能重复释放；只有未摊回状态才释放
                    	}
                		cedeoutReinsuTrade.setReturnDate(DateUtils.getNowDate());
                		cedeoutReinsuTrade.setReturnStatus(CedeoutEnums.摊回状态_已摊回.getValue());
                		cedeoutReinsuTrade.setReturnReason("理赔追溯摊回(未达溢额线),id:" + claim.getId());
                		cedeoutReinsuTradeList.add(cedeoutReinsuTrade);
                		continue;
                	}
                	DwsReinsuTradeDTO returnReinsuTrade = new DwsReinsuTradeDTO();
                	BeanUtils.copyProperties(claim, returnReinsuTrade);
                    BeanUtils.copyProperties(cedeoutReinsuTrade, returnReinsuTrade, RsConstant.return_copy_cedeout_ignore_fields);
                    
                    returnReinsuTrade.setBatchNo(cedeoutBatchLog.getBatchNo());
                    returnReinsuTrade.setSrcOutTradeId(cedeoutReinsuTrade.getId());
                    returnReinsuTrade.setGetDutyCodes(claim.getGetDutyCode());
                    returnReinsuTrade.setGetDutyNames(claim.getGetDutyName());
                    returnReinsuTrade.setContType(cedeoutReinsuTrade.getContType());
                    returnReinsuTrade.setDataCopy(CedeoutEnums.数据_原始.getValue());
                    returnReinsuTrade.setBusiType(CedeoutEnums.业务类型_理赔.getValue());
                    returnReinsuTrade.setDataType(CedeoutEnums.数据类型_摊回.getValue());
                    returnReinsuTrade.setCalcStatus(CedeoutEnums.计算状态_成功.getValue());
                    returnReinsuTrade.setCalcType(cedeoutBatchLog.getCalcType());
                    returnReinsuTrade.setAdjustStatus(CedeoutEnums.否.getValue());
                    returnReinsuTrade.setCreateBy(cedeoutBatchLog.getExecutor());
                    returnReinsuTrade.setCreateTime(DateUtils.getNowDate());
                    returnReinsuTrade.setUpdateBy(cedeoutBatchLog.getExecutor());
                    returnReinsuTrade.setUpdateTime(DateUtils.getNowDate());
                	if(cedeoutReinsuTrade.getCedeoutPremium() != null) {//退还基础再保费
                		returnReinsuTrade.setReturnPremium(cedeoutReinsuTrade.getCedeoutPremium());
                	}
                	if(cedeoutReinsuTrade.getCedeoutAddPremium() != null) {//退还加费分保费
                		returnReinsuTrade.setReturnCbPremium(cedeoutReinsuTrade.getCedeoutAddPremium());
                	}
                	if(cedeoutReinsuTrade.getCedeoutTotalPremium() != null) {//退还总再保费
                		returnReinsuTrade.setReturnTotalPremium(cedeoutReinsuTrade.getCedeoutTotalPremium());
                	}
                	if(cedeoutReinsuTrade.getCedeoutCommission() != null) {//返还分保佣金
                		returnReinsuTrade.setReturnCommission(cedeoutReinsuTrade.getCedeoutCommission());
                	}
                	if(cedeoutReinsuTrade.getAddedTax() != null) {//返还增值税
                		returnReinsuTrade.setAddedTax(cedeoutReinsuTrade.getAddedTax());
                	}
                	if(cedeoutReinsuTrade.getReturnStatus() == CedeoutEnums.摊回状态_未摊回.getValue() && cedeoutReinsuTrade.getOccupyRiskAmount() != null) {
                		returnReinsuTrade.setReleaseRiskAmount(cedeoutReinsuTrade.getOccupyRiskAmount().negate());//有可能之前续期已释放，不能重复释放；只有未摊回状态才释放
                	}
                    returnReinsuTradeList.add(returnReinsuTrade);
                    
                    cedeoutReinsuTrade.setReturnDate(DateUtils.getNowDate());
            		cedeoutReinsuTrade.setReturnStatus(CedeoutEnums.摊回状态_已摊回.getValue());
            		cedeoutReinsuTrade.setReturnReason("理赔追溯摊回退还再保费,id:" + claim.getId());
            		cedeoutReinsuTradeList.add(cedeoutReinsuTrade);
                }
            }
            
            if(CollUtil.isNotEmpty(accidentBeforeLastTradeList)) {//事故日期之前产生分出
            	claim.getParams().put(RsConstant.paramsEndDate, claim.getBusiOccurDate());
                int alreadyClamCount = dwsContHistoryService.selectContAlreadyClamCountByExpiryDate(claim);//查询已发生理赔次数
                
                int clmCountLimit = 1;
	            RiskLiabilityDTO riskLiability = riskLiabilityMap.get(claim.getRiskCode() + RsConstant.splicing + claim.getLiabilityCode());
	            if(riskLiability != null) {
	            	clmCountLimit = riskLiability.getClaimCount();
	            }
                //事故日期之前产生的分出（最后一次），需要摊回理赔款
                for (DwsReinsuTradeDTO cedeoutReinsuTrade : accidentBeforeLastTradeList) {
                	if(CedeoutEnums.计算状态_忽略.getValue() == cedeoutReinsuTrade.getCalcStatus()) {//未达溢额线的分出记录，在原记录上释放风险保额
                		if (alreadyClamCount >= clmCountLimit) {//判断已发生理赔次数是否等于最大理赔次数，原分出记录修改为已摊回
                			if(cedeoutReinsuTrade.getReturnStatus() == CedeoutEnums.摊回状态_未摊回.getValue() && cedeoutReinsuTrade.getOccupyRiskAmount() != null) {
                    			cedeoutReinsuTrade.setReleaseRiskAmount(cedeoutReinsuTrade.getOccupyRiskAmount().negate());//有可能之前续期已释放，不能重复释放；只有未摊回状态才释放
	                    	}
                			cedeoutReinsuTrade.setReturnDate(DateUtils.getNowDate());
                    		cedeoutReinsuTrade.setReturnStatus(CedeoutEnums.摊回状态_已摊回.getValue());
                    		cedeoutReinsuTrade.setReturnReason("理赔摊回(未达溢额线),id:" + claim.getId());
                    		cedeoutReinsuTradeList.add(cedeoutReinsuTrade);
                		}
                		continue;
                	}
                    DwsReinsuTradeDTO returnReinsuTrade = new DwsReinsuTradeDTO();
                    BeanUtils.copyProperties(claim, returnReinsuTrade);
                    BeanUtils.copyProperties(cedeoutReinsuTrade, returnReinsuTrade, RsConstant.return_copy_cedeout_ignore_fields);
                    
                    
                    returnReinsuTrade.setBatchNo(cedeoutBatchLog.getBatchNo());
                    returnReinsuTrade.setSrcOutTradeId(cedeoutReinsuTrade.getId());
                    returnReinsuTrade.setGetDutyCodes(claim.getGetDutyCode());
                    returnReinsuTrade.setGetDutyNames(claim.getGetDutyName());
                    returnReinsuTrade.setContType(cedeoutReinsuTrade.getContType());
                    returnReinsuTrade.setDataCopy(CedeoutEnums.数据_原始.getValue());
                    returnReinsuTrade.setBusiType(CedeoutEnums.业务类型_理赔.getValue());
                    returnReinsuTrade.setDataType(CedeoutEnums.数据类型_摊回.getValue());
                    returnReinsuTrade.setCalcStatus(CedeoutEnums.计算状态_成功.getValue());
                    returnReinsuTrade.setBackTrackData(DataTrackType.常规数据.getCode());
                    returnReinsuTrade.setCalcType(cedeoutBatchLog.getCalcType());
                    returnReinsuTrade.setAdjustStatus(CedeoutEnums.否.getValue());
                    returnReinsuTrade.setCreateBy(cedeoutBatchLog.getExecutor());
                    returnReinsuTrade.setCreateTime(DateUtils.getNowDate());
                    returnReinsuTrade.setUpdateBy(cedeoutBatchLog.getExecutor());
                    returnReinsuTrade.setUpdateTime(DateUtils.getNowDate());
                    try {
						if(returnReinsuTrade.getCedeoutMode() == CedeoutEnums.分出模式_毛保费.getValue()){
							returnReinsuTrade.setReturnClaimAmount(CedeoutUtils.getReturnClaimAmount(returnReinsuTrade));
						}else {
							returnReinsuTrade.setK1(CedeoutUtils.queryK(returnReinsuTrade.getInsuredAppAge(), returnReinsuTrade.getContYear(), BigDecimal.ZERO));
							returnReinsuTrade.setK2(CedeoutUtils.queryK(returnReinsuTrade.getInsuredAppAge(), returnReinsuTrade.getContYear(), BigDecimal.ONE));
							R<BigDecimal> formulaCalcResult = formulaService.getFormulaCalcResultValue(CedeoutEnums.计算大类_理赔摊回.getValue(), FormulaType.理赔摊回.getCode(), returnReinsuTrade);
							if(formulaCalcResult.getCode() == R.SUCCESS) {
								returnReinsuTrade.setReturnClaimAmount(formulaCalcResult.getData());
							}else {
							    //如果计算失败 判断开关如果为true，则调用计算公式逻辑
                                boolean flag = Boolean.parseBoolean(sysConfigService.selectConfigByKey(RsConstant.claimReturnFixedFormulaSwitch));
							    if(flag){
                                    returnReinsuTrade.setReturnClaimAmount(CedeoutUtils.getReturnClaimAmountNet(cedeoutReinsuTrade.getReserves(),returnReinsuTrade));
                                }else{
                                    returnReinsuTrade.setCalcStatus(CedeoutEnums.计算状态_失败.getValue());
                                    returnReinsuTrade.setCalcFailCode(Integer.valueOf(formulaCalcResult.getMsg()));
                                }
							}
						}
                    } catch (ServiceException e) {
                    	returnReinsuTrade.setCalcStatus(CedeoutEnums.计算状态_失败.getValue());
                    	returnReinsuTrade.setCalcFailCode(CedeoutEnums.计算失败原因_系统异常.getValue());
                    }
                    if (alreadyClamCount >= clmCountLimit) {//判断已发生理赔次数是否等于最大理赔次数，原分出记录修改为已摊回
                        if(cedeoutReinsuTrade.getReturnStatus() == CedeoutEnums.摊回状态_未摊回.getValue() && cedeoutReinsuTrade.getOccupyRiskAmount() != null) {//释放风险保额
                        	returnReinsuTrade.setReleaseRiskAmount(cedeoutReinsuTrade.getOccupyRiskAmount().negate());
                        }
                        cedeoutReinsuTrade.setReturnDate(DateUtils.getNowDate());
                        cedeoutReinsuTrade.setReturnStatus(CedeoutEnums.摊回状态_已摊回.getValue());
                        cedeoutReinsuTrade.setReturnReason("理赔摊回,id:" + claim.getId());
                        cedeoutReinsuTradeList.add(cedeoutReinsuTrade);
                    }
                    returnReinsuTradeList.add(returnReinsuTrade);
                }
            }
            claimResultMap.put("handle_date", DateUtils.getNowDate());
    	}catch(Exception e) {
            claimResultMap.put("handle_date", DateUtils.getNowDate());
            claimResultMap.put("handle_status", HandleStatus.处理失败.getCode());
            claimResultMap.put("handle_error_msg", "系统异常" + e.getMessage());
            claimResultMap.put("handle_error_num", claim.getHandleErrorNum() + 1);
    		log.error("再保理赔生成摊回批处理执行中，生成摊回数据出错, dwsClaim: {}, 错误原因:", ReinsuJsonUtil.toJsonString(claim), e);
    	}
    	return claimResultMap;
    }
    
    /**
     * 查询分出记录
     * @param claimEntity 理赔信息
     * @return 分出记录
     */
    private List<DwsReinsuTradeDTO> queryCedeoutTradeList(DwsContHistoryEntity claim) {
    	DwsReinsuTradeQuery dwsReinsuTradeQuery = new DwsReinsuTradeQuery();
        dwsReinsuTradeQuery.setContNo(claim.getContNo());
        dwsReinsuTradeQuery.setLiabilityCode(claim.getLiabilityCode());
        dwsReinsuTradeQuery.setStatus(CedeoutEnums.状态_有效.getValue());
        dwsReinsuTradeQuery.setDataType(CedeoutEnums.数据类型_分出.getValue());
        dwsReinsuTradeQuery.getParams().put("returnStatus", Arrays.asList(CedeoutEnums.摊回状态_未摊回.getValue(), CedeoutEnums.摊回状态_已终止.getValue()));
        dwsReinsuTradeQuery.getParams().put("calcStatus", Arrays.asList(CedeoutEnums.计算状态_成功.getValue(), CedeoutEnums.计算状态_忽略.getValue()));
        if (StrUtil.isNotBlank(claim.getPolNo()) && !RsConstant.ALL_POLNO.equals(claim.getPolNo())) {//险种号
        	dwsReinsuTradeQuery.setPolNo(claim.getPolNo());
        }
        if (StrUtil.isNotBlank(claim.getInsuredNo()) && !RsConstant.ALL_POLNO.equals(claim.getInsuredNo())) {//被保险人号
        	dwsReinsuTradeQuery.setInsuredNo(claim.getInsuredNo());
        }
        return dwsReinsuTradeService.selectWaitReturnOnCedeoutReinsuTrade(dwsReinsuTradeQuery);
    }
    
    
}
