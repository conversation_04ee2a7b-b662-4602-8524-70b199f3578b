package com.reinsurance.handler;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import com.jd.lightning.common.utils.DateUtils;
import com.jd.lightning.system.service.ISysConfigService;
import com.reinsurance.domain.DwsContHistoryEntity;
import com.reinsurance.dto.JobParamDTO;
import com.reinsurance.dto.CedeoutBatchLogDTO;
import com.reinsurance.dto.DwsReinsuTradeDTO;
import com.reinsurance.query.DwsReinsuTradeQuery;
import com.reinsurance.service.IDwsReinsuTradeService;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.reinsurance.constant.RsConstant;
import com.reinsurance.enums.BasicDataEnums.DataTrackType;
import com.reinsurance.enums.BasicDataEnums.HandleStatus;
import com.reinsurance.enums.BasicDataEnums.InvalidStateReason;
import com.reinsurance.enums.BasicDataEnums.InvalidStateType;
import com.reinsurance.enums.CedeoutEnums;
import com.reinsurance.service.ICedeoutBatchLogService;
import com.reinsurance.service.IDwsContHistoryService;
import com.reinsurance.utils.CedeoutUtils;
import com.reinsurance.utils.ReinsuJsonUtil;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 失效保单生成摊回记录批处理<br/>
 * 1、查询所有未处理的失效保单<br/>
 * 2、查询对应的分出记录<br/>
 * 3、根据分出记录复制为对应的摊回记录<br/>
 * 4、计算摊回金额<br/>
 * 5、批量插入摊回记录表、分出日志<br/>
 * 6、更新失效记录的状态
 * <AUTHOR>
 * @version 1.1
 * @date 2025-01-10 16:50:12
 */
@Slf4j
@Component("invalid2ReinsuTradeHandler")
public class Invalid2ReinsuTradeHandler {
	
	@Autowired
	private ISysConfigService sysConfigService;
	
	@Autowired
	private IDwsContHistoryService dwsContHistoryService;
	
    @Autowired
    private IDwsReinsuTradeService dwsReinsuTradeService;
    
    @Autowired
    private ICedeoutBatchLogService cedeoutBatchLogService;

	public void handler(String batchNo, JobParamDTO jobParam, CedeoutBatchLogDTO cedeoutBatchLog){
		try {
            log.info("再保失效保单生成摊回数据开始, batchNo:{}, jobParam:{}", batchNo, ReinsuJsonUtil.toJsonString(jobParam));
            Integer busiType = CedeoutEnums.业务类型_失效满期.getValue();
            if(jobParam.getCalcType() == CedeoutEnums.执行方式_手动.getValue()){//所有类型的失效
            	busiType = jobParam.getBusiType();
			}
            int limit = Integer.valueOf(sysConfigService.selectConfigByKey(RsConstant.jobOnceMaxRows));
            while(true) {//循环处理，查询到的数据小于limit时跳出循环
            	List<DwsContHistoryEntity> invalidHistoryList = dwsContHistoryService.selectHistoryListByBusiType(null, busiType, HandleStatus.未处理.getCode(), jobParam.getEndDate(), limit);
            	if(CollUtil.isEmpty(invalidHistoryList)) {
					log.info("再保失效保单生成摊回数据结束, 未查询到失效保单, batchNo:{}", batchNo);
                    break;
				}
            	List<DwsReinsuTradeDTO> returnReinsuTradeList = new ArrayList<DwsReinsuTradeDTO>();
    			List<DwsReinsuTradeDTO> cedeoutReinsuTradeList = new ArrayList<DwsReinsuTradeDTO>();
            	List<Map<String, Object>> updateHistoryList = new ArrayList<Map<String, Object>>();
            	for(DwsContHistoryEntity invalidHistory : invalidHistoryList) {
            		Map<String, Object> invalidResultMap = this.handlerInvalid(batchNo, jobParam, invalidHistory, cedeoutReinsuTradeList, returnReinsuTradeList);
            		updateHistoryList.add(invalidResultMap);
            	}
				try {
					if(CollUtil.isNotEmpty(cedeoutReinsuTradeList)) {
	    				dwsReinsuTradeService.updateBatchDwsReinsuTrade(cedeoutReinsuTradeList);
	    			}
					if(CollUtil.isNotEmpty(returnReinsuTradeList)) {//保存摊回记录
    					int passCount = dwsReinsuTradeService.insertBatchDwsReinsuTrade(returnReinsuTradeList);
    					cedeoutBatchLog.setFailCount(null);
    					cedeoutBatchLog.setPassCount(passCount);
	                	cedeoutBatchLogService.updateCedeoutBatchLogCount(cedeoutBatchLog);//更新日志成功数
					}
				}catch(Exception e) {
					log.error("再保失效保单生成摊回数据出错, batchNo:{}", batchNo, e);
				}
				log.info("再保失效保单生成摊回数据执行, batchNo:{}, returnPolicyDwsList:{}", batchNo, returnReinsuTradeList.size());
            	dwsContHistoryService.updateBatchHistoryHandleStatus(updateHistoryList);
				if(invalidHistoryList.size() < limit) {//已处理完成
					break;
				}
            }
            log.info("再保失效保单生成摊回数据完成, batchNo:{}, jobParam:{}", batchNo, ReinsuJsonUtil.toJsonString(jobParam));
		}catch(Exception e) {
			log.error("再保失效保单生成摊回数据出错, batchNo:{}, jobParam:{}, 错误原因:", batchNo, ReinsuJsonUtil.toJsonString(jobParam), e);
		}
	}
	
	private Map<String, Object> handlerInvalid(String batchNo, JobParamDTO jobParam, DwsContHistoryEntity invalidHistory, List<DwsReinsuTradeDTO> cedeoutReinsuTradeList, List<DwsReinsuTradeDTO> returnReinsuTradeList){
		Map<String, Object> invalidResultMap = MapUtil.newHashMap();
		invalidResultMap.put("busi_type", invalidHistory.getBusiType());
		invalidResultMap.put("unique_key", invalidHistory.getUniqueKey());
        invalidResultMap.put("handle_status", HandleStatus.处理成功.getCode());
        invalidResultMap.put("handle_error_num", invalidHistory.getHandleErrorNum());
        invalidResultMap.put("handle_error_msg", invalidHistory.getHandleErrorMsg());
		try {
			DwsReinsuTradeQuery dwsReinsuTradeQuery = new DwsReinsuTradeQuery();
	        dwsReinsuTradeQuery.setContNo(invalidHistory.getContNo());
	        dwsReinsuTradeQuery.setStatus(CedeoutEnums.状态_有效.getValue());
	        dwsReinsuTradeQuery.setDataType(CedeoutEnums.数据类型_分出.getValue());
	        dwsReinsuTradeQuery.setBusiOccurDate(invalidHistory.getBusiOccurDate());
	        dwsReinsuTradeQuery.getParams().put("returnStatus", Arrays.asList(CedeoutEnums.摊回状态_未摊回.getValue()));
	        dwsReinsuTradeQuery.getParams().put("calcStatus", Arrays.asList(CedeoutEnums.计算状态_成功.getValue(), CedeoutEnums.计算状态_忽略.getValue()));
	        if (StrUtil.isNotBlank(invalidHistory.getPolNo()) && !RsConstant.ALL_POLNO.equals(invalidHistory.getPolNo())) {//险种号
	        	dwsReinsuTradeQuery.setPolNo(invalidHistory.getPolNo());
	        }
	        if (StrUtil.isNotBlank(invalidHistory.getInsuredNo()) && !RsConstant.ALL_POLNO.equals(invalidHistory.getInsuredNo())) {//被保险人号
	        	dwsReinsuTradeQuery.setInsuredNo(invalidHistory.getInsuredNo());
	        }
	        
			List<DwsReinsuTradeDTO> lastReinsuTradeList = dwsReinsuTradeService.selectWaitReturnOnCedeoutReinsuTrade(dwsReinsuTradeQuery);
			if(CollectionUtils.isEmpty(lastReinsuTradeList)){
				log.info("再保失效保单生成摊回数据执行, 未查询分出记录, 不需要生成摊回记录, batchNo:{}, dwsReinsuTradeQuery:{}", batchNo, ReinsuJsonUtil.toJsonString(dwsReinsuTradeQuery));
				invalidResultMap.put("handle_date", DateUtils.getNowDate());
                invalidResultMap.put("handle_status", HandleStatus.处理失败.getCode());
                invalidResultMap.put("handle_error_msg", "未查询到分出记录, 无法产生摊回记录");
                invalidResultMap.put("handle_error_num", invalidHistory.getHandleErrorNum() + 1);
                return invalidResultMap;
			}
			log.info("再保失效保单生成摊回数据执行, 查询失效保单分出记录完毕, batchNo:{}, lastReinsuTradeList:{}", batchNo, lastReinsuTradeList.size());
			for(DwsReinsuTradeDTO cedeoutReinsuTrade : lastReinsuTradeList) {
				if(CedeoutEnums.计算状态_忽略.getValue() == cedeoutReinsuTrade.getCalcStatus()) {//未达溢额线的分出记录，在原记录上释放风险保额
            		if(cedeoutReinsuTrade.getOccupyRiskAmount() != null) {
            			cedeoutReinsuTrade.setReleaseRiskAmount(cedeoutReinsuTrade.getOccupyRiskAmount().negate());//有可能之前续期已释放，不能重复释放；只有未摊回状态才释放
                	}
            		cedeoutReinsuTrade.setReturnDate(DateUtils.getNowDate());
            		cedeoutReinsuTrade.setReturnStatus(CedeoutEnums.摊回状态_已摊回.getValue());
            		cedeoutReinsuTrade.setReturnReason("失效摊回(未达溢额线),id:" + invalidHistory.getId());
            		continue;
            	}
				if(CedeoutEnums.缴费频率_月交.getValue() == cedeoutReinsuTrade.getRsPayIntv()){
					cedeoutReinsuTrade.setReturnDate(DateUtils.getNowDate());
					cedeoutReinsuTrade.setReturnStatus(CedeoutEnums.摊回状态_已摊回.getValue());
					if(cedeoutReinsuTrade.getOccupyRiskAmount() != null) {
						cedeoutReinsuTrade.setReleaseRiskAmount(cedeoutReinsuTrade.getOccupyRiskAmount().negate());//释放占用的风险保额
					}
					cedeoutReinsuTrade.setReturnReason("失效摊回(月缴),id:" + invalidHistory.getId());
					continue;
				}
				DwsReinsuTradeDTO returnReinsuTrade = new DwsReinsuTradeDTO();
				BeanUtils.copyProperties(invalidHistory, returnReinsuTrade);
				BeanUtils.copyProperties(cedeoutReinsuTrade, returnReinsuTrade, RsConstant.return_copy_cedeout_ignore_fields);

				returnReinsuTrade.setBatchNo(batchNo);
				returnReinsuTrade.setCalcType(jobParam.getCalcType());
				returnReinsuTrade.setCreateBy(jobParam.getExecutor());
				returnReinsuTrade.setCreateTime(DateUtils.getNowDate());
				returnReinsuTrade.setAdjustStatus(CedeoutEnums.否.getValue());
				returnReinsuTrade.setSrcOutTradeId(cedeoutReinsuTrade.getId());
				returnReinsuTrade.setBackTrackData(DataTrackType.常规数据.getCode());
				returnReinsuTrade.setInvalidStateType(invalidHistory.getStateType());
				returnReinsuTrade.setInvalidStateReason(invalidHistory.getStateReason());
				returnReinsuTrade.setBusiOccurDate(invalidHistory.getStateStartDate());
				returnReinsuTrade.setInvalidStartDate(invalidHistory.getStateStartDate());
				returnReinsuTrade.setDataType(CedeoutEnums.数据类型_摊回.getValue());
				returnReinsuTrade.setDataCopy(CedeoutEnums.数据_原始.getValue());
				returnReinsuTrade.setCalcStatus(CedeoutEnums.计算状态_成功.getValue());
				returnReinsuTrade.setContAppFlag(CedeoutEnums.保单状态_失效.getValue());
				returnReinsuTrade.setRiskAppFlag(CedeoutEnums.保单状态_失效.getValue());
				returnReinsuTrade.setReturnCommission(BigDecimal.ZERO);
				if(cedeoutReinsuTrade.getOccupyRiskAmount() != null) {
					returnReinsuTrade.setReleaseRiskAmount(cedeoutReinsuTrade.getOccupyRiskAmount().negate());//释放占用的风险保额
				}
				if (InvalidStateType.终止.getCode().equals(invalidHistory.getStateType()) &&
						InvalidStateReason.满期终止.getCode().equals(invalidHistory.getStateReason())) {
					returnReinsuTrade.setBusiType(CedeoutEnums.业务类型_满期.getValue());
					if(cedeoutReinsuTrade.getCedeoutMode() == CedeoutEnums.分出模式_毛保费.getValue()) {
						returnReinsuTrade.setReturnExpiredGold(CedeoutUtils.getReturnExpiredGold(cedeoutReinsuTrade));
					}
				}else {//计算退还分保费
					returnReinsuTrade.setReturnPremium(CedeoutUtils.getReturnPremium(returnReinsuTrade.getBusiOccurDate(), cedeoutReinsuTrade));
					returnReinsuTrade.setReturnCbPremium(CedeoutUtils.getReturnCbPremium(returnReinsuTrade.getBusiOccurDate(), cedeoutReinsuTrade));
					returnReinsuTrade.setReturnTotalPremium(CedeoutUtils.getReturnTotalPremium(returnReinsuTrade));
					returnReinsuTrade.setAddedTax(CedeoutUtils.getReturnAddedTax(returnReinsuTrade));//计算退还增值税
				}
				returnReinsuTradeList.add(returnReinsuTrade);
				
				cedeoutReinsuTrade.setReturnDate(DateUtils.getNowDate());
        		cedeoutReinsuTrade.setReturnStatus(CedeoutEnums.摊回状态_已摊回.getValue());
        		cedeoutReinsuTrade.setReturnReason("失效摊回,id:" + invalidHistory.getId());
			}
			cedeoutReinsuTradeList.addAll(lastReinsuTradeList);
			invalidResultMap.put("handle_date", DateUtils.getNowDate());
		}catch(Exception e) {
			invalidResultMap.put("handle_date", DateUtils.getNowDate());
            invalidResultMap.put("handle_status", HandleStatus.处理失败.getCode());
            invalidResultMap.put("handle_error_msg", "系统异常" + e.getMessage());
            invalidResultMap.put("handle_error_num", invalidHistory.getHandleErrorNum() + 1);
			log.error("再保失效保单生成摊回数据执行, 生成数据异常, batchNo:{}, invalidHistory:{}, 错误原因:", batchNo, ReinsuJsonUtil.toJsonString(invalidHistory));
		}
		return invalidResultMap;
	}
	
}
