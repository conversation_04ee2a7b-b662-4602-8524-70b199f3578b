package com.reinsurance;

import com.reinsurance.domain.DwsPrpProductEntity;
import com.reinsurance.dto.DwsPrpProductDTO;
import com.reinsurance.query.DwsPrpProductQuery;
import com.reinsurance.utils.ReinsuJsonUtil;
import com.reinsurance.utils.ReinsuObjectUtil;

public class Test {

    public static void main(String[] args) {
        DwsPrpProductDTO dto = new DwsPrpProductDTO();
        dto.setTransactionNo("1232131");
        dto.setGPFlag("1");
        dto.setId(1L);
        dto.setReportYear(2025);
        System.out.println(ReinsuJsonUtil.toJsonString(dto));

        DwsPrpProductEntity entity = ReinsuObjectUtil.convertModel(dto, DwsPrpProductEntity.class);
        System.out.println(ReinsuJsonUtil.toJsonString(entity));

        DwsPrpProductQuery query = ReinsuObjectUtil.convertModel(entity, DwsPrpProductQuery.class);
        System.out.println(ReinsuJsonUtil.toJsonString(query));

    }
}
