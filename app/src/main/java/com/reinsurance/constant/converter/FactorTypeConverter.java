package com.reinsurance.constant.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.ReadConverterContext;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.reinsurance.enums.BasicDataEnums;
import java.util.Optional;

/**
 * 导入导出，因子类型转换，默认 准备金因子
 */
public class FactorTypeConverter implements Converter<String> {

    @Override
    public Class<?> supportJavaTypeKey() {
        return String.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    /**
     * 将excel对象转换为Java对象 - 读
     * @param context read converter context
     * @return
     * @throws Exception
     */
    @Override
    public String convertToJavaData(ReadConverterContext<?> context) throws Exception {
        String stringValue = context.getReadCellData().getStringValue();
        return Optional.ofNullable(BasicDataEnums.FactorType.getCodeByDesc(stringValue)).orElse(BasicDataEnums.FactorType.RF.getCode());
    }

    /**
     * 将Java对象转换为excel对象 - 写
     * @param context write context
     * @return
     * @throws Exception
     */
    @Override
    public WriteCellData<?> convertToExcelData(WriteConverterContext<String> context) throws Exception {
        String value = context.getValue();
        return new WriteCellData<>(Optional.ofNullable(BasicDataEnums.FactorType.getDescByCode(value)).orElse(BasicDataEnums.FactorType.RF.getDesc()));
    }

}
