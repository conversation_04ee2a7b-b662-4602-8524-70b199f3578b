package com.reinsurance.mapper;

import java.util.List;
import com.reinsurance.domain.CedeoutLiabilityEntity;
import com.reinsurance.dto.CedeoutLiabilityDTO;
import com.reinsurance.query.CedeoutLiabilityQuery;

/**
 * 分保责任Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-20
 */
public interface CedeoutLiabilityMapper 
{
    /**
     * 查询分保责任
     * 
     * @param id 分保责任主键
     * @return 分保责任
     */
    CedeoutLiabilityEntity selectCedeoutLiabilityById(Long id);
    /**
     * 查询分保责任
     *
     * @param code 分保责任code
     * @return 分保责任
     */
    CedeoutLiabilityEntity selectCedeoutLiabilityByCode(String code);
    /**
     * 查询分保责任
     *
     * @param addupRiskCode 分保责任主键
     * @return 分保责任
     */
    CedeoutLiabilityEntity selectCedeoutLiabilityByRiskCode(String addupRiskCode);
    /**
     * 查询分保责任
     *
     * @param addupRiskName 分保责任主键
     * @return 分保责任
     */
    CedeoutLiabilityEntity selectCedeoutLiabilityByRiskName(String addupRiskName);
    /**
     * 查询分保责任
     *
     * @param cedeoutLiabilityDTO 分保责任主键
     * @return 分保责任
     */
    List<CedeoutLiabilityEntity> selectCedeoutLiabilityByRiskCodeAndName(CedeoutLiabilityDTO cedeoutLiabilityDTO);
    /**
     * 查询分保责任列表
     * 
     * @param cedeoutLiabilityQuery 分保责任
     * @return 分保责任集合
     */
    List<CedeoutLiabilityEntity> selectCedeoutLiabilityList(CedeoutLiabilityQuery cedeoutLiabilityQuery);

    /**
     * 新增分保责任
     * 
     * @param cedeoutLiability 分保责任
     * @return 结果
     */
    int insertCedeoutLiability(CedeoutLiabilityEntity cedeoutLiability);

    /**
     * 修改分保责任
     * 
     * @param cedeoutLiability 分保责任
     * @return 结果
     */
    int updateCedeoutLiability(CedeoutLiabilityEntity cedeoutLiability);

    /**
     * 删除分保责任
     * 
     * @param id 分保责任主键
     * @return 结果
     */
    int deleteCedeoutLiabilityById(Long id);

    /**
     * 批量删除分保责任
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteCedeoutLiabilityByIds(Long[] ids);
}
