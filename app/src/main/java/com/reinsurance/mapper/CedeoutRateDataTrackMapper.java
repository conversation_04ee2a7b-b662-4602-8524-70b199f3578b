package com.reinsurance.mapper;

import java.util.List;

import com.reinsurance.domain.CedeoutRateDataTrackEntity;
import com.reinsurance.query.CedeoutRateDataTrackQuery;

/**
 * 费率数据轨迹Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-20
 */
public interface CedeoutRateDataTrackMapper 
{
    /**
     * 查询费率数据轨迹
     * 
     * @param trackId 费率数据轨迹主键
     * @return 费率数据轨迹
     */
    public CedeoutRateDataTrackEntity selectCedeoutRateDataTrackByTrackId(Long trackId);

    /**
     * 查询费率数据轨迹列表
     * 
     * @param cedeoutRateDataTrackQuery 费率数据轨迹
     * @return 费率数据轨迹集合
     */
    public List<CedeoutRateDataTrackEntity> selectCedeoutRateDataTrackList(CedeoutRateDataTrackQuery cedeoutRateDataTrackQuery);

    /**
     * 新增费率数据轨迹
     * 
     * @param cedeoutRateDataTrack 费率数据轨迹
     * @return 结果
     */
    public int insertCedeoutRateDataTrack(CedeoutRateDataTrackEntity cedeoutRateDataTrack);

    /**
     * 修改费率数据轨迹
     * 
     * @param cedeoutRateDataTrack 费率数据轨迹
     * @return 结果
     */
    public int updateCedeoutRateDataTrack(CedeoutRateDataTrackEntity cedeoutRateDataTrack);

    /**
     * 删除费率数据轨迹
     * 
     * @param trackId 费率数据轨迹主键
     * @return 结果
     */
    public int deleteCedeoutRateDataTrackByTrackId(Long trackId);

    /**
     * 批量删除费率数据轨迹
     * 
     * @param trackIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCedeoutRateDataTrackByTrackIds(Long[] trackIds);
}
