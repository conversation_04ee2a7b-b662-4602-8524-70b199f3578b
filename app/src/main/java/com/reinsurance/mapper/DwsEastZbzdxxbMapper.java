package com.reinsurance.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.reinsurance.domain.DwsEastZbzdxxbEntity;
import com.reinsurance.query.DwsEastZbzdxxbQuery;

/**
 * East再保账单信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-05
 */
public interface DwsEastZbzdxxbMapper {
    /**
     * 查询East再保账单信息
     *
     * @param id East再保账单信息主键
     * @return East再保账单信息
     */
    DwsEastZbzdxxbEntity selectDwsEastZbzdxxbById(Long id);

    /**
     * 查询East再保账单信息列表
     *
     * @param dwsEastZbzdxxbQuery East再保账单信息
     * @return East再保账单信息集合
     */
    List<DwsEastZbzdxxbEntity> selectDwsEastZbzdxxbList(DwsEastZbzdxxbQuery dwsEastZbzdxxbQuery);

    /**
     * 查询East再保账单信息列表
     *
     * @param ids East再保账单信息
     * @return East再保账单信息集合
     */
    List<DwsEastZbzdxxbEntity> selectDwsEastZbzdxxbListByIds(Long [] ids);
    
    /**
     * East再保产品信息是不是存在
     * @param dwsEastZbcpxxbQuery
     * @return
     */
    int selectDwsEastZbzdxxbExists(DwsEastZbzdxxbQuery dwsEastZbzdxxbQuery);
    
    /**
     * 获取监管报表应该赋值的推送状态
     * @param reportYear	报表年份
     * @return
     */
    Integer selectAnnualReportShouldPushStatus(@Param("reportYear")int reportYear);
    
    /**
     * 推送前查询所有非空字段实际值为空的流水号
     * @param ids
     * @return
     */
    List<String> selectPushBeforeCheckEmpty(Long [] ids);
    
    /**
     * 新增East再保账单信息
     *
     * @param dwsEastZbzdxxbEntity East再保账单信息
     * @return 结果
     */
    int insertDwsEastZbzdxxb(DwsEastZbzdxxbEntity dwsEastZbzdxxbEntity);

    /**
     * 批量新增East再保账单信息
     *
     * @param dwsEastZbzdxxbList East再保账单信息
     * @return 结果
     */
    int insertBatchDwsEastZbzdxxb(List<DwsEastZbzdxxbEntity> dwsEastZbzdxxbList);
    
    /**
     * 修改East再保账单信息
     *
     * @param dwsEastZbzdxxbEntity East再保账单信息
     * @return 结果
     */
    int updateDwsEastZbzdxxb(DwsEastZbzdxxbEntity dwsEastZbzdxxbEntity);
    
    /**
     * 更新再保账单信息的明细数据导入状态
     * @param dwsEastZbzdxxbEntity
     * @return
     */
    int updateZbzdxxbImportStatusByLsh(DwsEastZbzdxxbEntity dwsEastZbzdxxbEntity);
    
    /**
     * 推送East再保账单信息
     * @param pushStatus
     * @param pushBy
     * @param ids
     * @return 结果
     */
    int updateDwsEastZbzdxxbPushStatus(@Param("pushStatus")Integer pushStatus, @Param("pushBy")String pushBy, @Param("ids")Long [] ids);

    /**
     * 删除East再保账单信息
     *
     * @param id East再保账单信息主键
     * @return 结果
     */
    int deleteDwsEastZbzdxxbById(Long id);

    /**
     * 批量删除East再保账单信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteDwsEastZbzdxxbByIds(Long[] ids);
}
