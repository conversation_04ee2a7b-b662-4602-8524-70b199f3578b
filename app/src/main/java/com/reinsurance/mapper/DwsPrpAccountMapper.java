package com.reinsurance.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;

import com.reinsurance.domain.DwsPrpAccountEntity;
import com.reinsurance.query.DwsPrpAccountQuery;

/**
 * 保单登记再保账单信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface DwsPrpAccountMapper {
    
    /**
     * 查询保单登记再保账单信息
     *
     * @param Id 保单登记再保账单信息主键
     * @return 保单登记再保账单信息
     */
    public DwsPrpAccountEntity selectDwsPrpAccountById(Long Id);

    /**
     * 查询保单登记再保账单信息列表
     *
     * @param dwsPrpAccountQuery 保单登记再保账单信息
     * @return 保单登记再保账单信息集合
     */
    public List<DwsPrpAccountEntity> selectDwsPrpAccountList(DwsPrpAccountQuery dwsPrpAccountQuery);

    /**
     * 根据主键数组查询保单登记再保账单信息列表
     *
     * @param Ids 主键数组
     * @return 保单登记再保账单信息集合
     */
    public List<DwsPrpAccountEntity> selectDwsPrpAccountByIds(Long[] Ids);

    /**
     * 新增保单登记再保账单信息
     *
     * @param dwsPrpAccountEntity 保单登记再保账单信息
     * @return 结果
     */
    public int insertDwsPrpAccount(DwsPrpAccountEntity dwsPrpAccountEntity);

    /**
     * 批量新增保单登记再保账单信息
     *
     * @param dwsPrpAccountList 保单登记再保账单信息列表
     * @return 结果
     */
    public int insertBatchDwsPrpAccount(List<DwsPrpAccountEntity> dwsPrpAccountList);

    /**
     * 修改保单登记再保账单信息
     *
     * @param dwsPrpAccountEntity 保单登记再保账单信息
     * @return 结果
     */
    public int updateDwsPrpAccount(DwsPrpAccountEntity dwsPrpAccountEntity);

    /**
     * 删除保单登记再保账单信息
     *
     * @param Id 保单登记再保账单信息主键
     * @return 结果
     */
    public int deleteDwsPrpAccountById(Long Id);

    /**
     * 批量删除保单登记再保账单信息
     *
     * @param Ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDwsPrpAccountByIds(Long[] Ids);

    /**
     * 检查保单登记再保账单信息是否存在
     *
     * @param dwsPrpAccountQuery 查询条件
     * @return 结果
     */
    public int selectDwsPrpAccountExists(DwsPrpAccountQuery dwsPrpAccountQuery);

    /**
     * 更新推送状态
     *
     * @param pushStatus 推送状态
     * @param pushBy 推送人
     * @param Ids 主键数组
     * @return 结果
     */
    public int updateDwsPrpAccountPushStatus(@Param("pushStatus") Integer pushStatus, @Param("pushBy") String pushBy, @Param("Ids") Long[] Ids);

    /**
     * 查询年度报表推送状态
     *
     * @param reportYear 报表年份
     * @return 结果
     */
    public Integer selectAnnualReportShouldPushStatus(@Param("reportYear") int reportYear);
}
