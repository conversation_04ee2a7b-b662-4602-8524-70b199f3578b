package com.reinsurance.mapper;

import java.util.List;
import com.reinsurance.domain.LiabilityFormulaTrackEntity;
import com.reinsurance.query.LiabilityFormulaTrackQuery;

/**
 * 险种公式轨迹Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-19
 */
public interface LiabilityFormulaTrackMapper 
{
    /**
     * 查询险种公式轨迹
     * 
     * @param trackId 险种公式轨迹主键
     * @return 险种公式轨迹
     */
    public LiabilityFormulaTrackEntity selectLiabilityFormulaTrackByTrackId(Long trackId);

    /**
     * 查询险种公式轨迹列表
     * 
     * @param liabilityFormulaTrack 险种公式轨迹
     * @return 险种公式轨迹集合
     */
    public List<LiabilityFormulaTrackEntity> selectLiabilityFormulaTrackList(LiabilityFormulaTrackQuery liabilityFormulaTrackQuery);

    /**
     * 新增险种公式轨迹
     * 
     * @param liabilityFormulaTrack 险种公式轨迹
     * @return 结果
     */
    public int insertLiabilityFormulaTrack(LiabilityFormulaTrackEntity liabilityFormulaTrack);

    /**
     * 修改险种公式轨迹
     * 
     * @param liabilityFormulaTrack 险种公式轨迹
     * @return 结果
     */
    public int updateLiabilityFormulaTrack(LiabilityFormulaTrackEntity liabilityFormulaTrack);

    /**
     * 删除险种公式轨迹
     * 
     * @param trackId 险种公式轨迹主键
     * @return 结果
     */
    public int deleteLiabilityFormulaTrackByTrackId(Long trackId);

    /**
     * 批量删除险种公式轨迹
     * 
     * @param trackIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteLiabilityFormulaTrackByTrackIds(Long[] trackIds);
}
