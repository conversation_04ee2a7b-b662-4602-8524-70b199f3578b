package com.reinsurance.mapper;

import java.util.List;
import com.reinsurance.domain.CedeoutCompanyBankTrackEntity;
import com.reinsurance.query.CedeoutCompanyBankTrackQuery;

/**
 * 再保公司银行信息轨迹Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-18
 */
public interface CedeoutCompanyBankTrackMapper 
{
    /**
     * 查询再保公司银行信息轨迹
     * 
     * @param trackId 再保公司银行信息轨迹主键
     * @return 再保公司银行信息轨迹
     */
    public CedeoutCompanyBankTrackEntity selectCedeoutCompanyBankTrackByTrackId(Long trackId);

    /**
     * 查询再保公司银行信息轨迹列表
     * 
     * @param cedeoutCompanyBankTrack 再保公司银行信息轨迹
     * @return 再保公司银行信息轨迹集合
     */
    public List<CedeoutCompanyBankTrackEntity> selectCedeoutCompanyBankTrackList(CedeoutCompanyBankTrackQuery cedeoutCompanyBankTrackQuery);

    /**
     * 新增再保公司银行信息轨迹
     * 
     * @param cedeoutCompanyBankTrack 再保公司银行信息轨迹
     * @return 结果
     */
    public int insertCedeoutCompanyBankTrack(CedeoutCompanyBankTrackEntity tCedeoutCompanyBankTrack);

    /**
     * 修改再保公司银行信息轨迹
     * 
     * @param cedeoutCompanyBankTrack 再保公司银行信息轨迹
     * @return 结果
     */
    public int updateCedeoutCompanyBankTrack(CedeoutCompanyBankTrackEntity tCedeoutCompanyBankTrack);

    /**
     * 删除再保公司银行信息轨迹
     * 
     * @param trackId 再保公司银行信息轨迹主键
     * @return 结果
     */
    public int deleteCedeoutCompanyBankTrackByTrackId(Long trackId);

    /**
     * 批量删除再保公司银行信息轨迹
     * 
     * @param trackIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCedeoutCompanyBankTrackByTrackIds(Long[] trackIds);
}
