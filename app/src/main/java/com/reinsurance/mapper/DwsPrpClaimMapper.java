package com.reinsurance.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;

import com.reinsurance.domain.DwsPrpClaimEntity;
import com.reinsurance.query.DwsPrpClaimQuery;

/**
 * 再保理赔险种明细Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface DwsPrpClaimMapper {
    
    /**
     * 查询再保理赔险种明细
     *
     * @param Id 再保理赔险种明细主键
     * @return 再保理赔险种明细
     */
    public DwsPrpClaimEntity selectDwsPrpClaimById(Long Id);

    /**
     * 查询再保理赔险种明细列表
     *
     * @param dwsPrpClaimQuery 再保理赔险种明细
     * @return 再保理赔险种明细集合
     */
    public List<DwsPrpClaimEntity> selectDwsPrpClaimList(DwsPrpClaimQuery dwsPrpClaimQuery);

    /**
     * 根据主键数组查询再保理赔险种明细列表
     *
     * @param Ids 主键数组
     * @return 再保理赔险种明细集合
     */
    public List<DwsPrpClaimEntity> selectDwsPrpClaimByIds(Long[] Ids);

    /**
     * 新增再保理赔险种明细
     *
     * @param dwsPrpClaimEntity 再保理赔险种明细
     * @return 结果
     */
    public int insertDwsPrpClaim(DwsPrpClaimEntity dwsPrpClaimEntity);

    /**
     * 批量新增再保理赔险种明细
     *
     * @param dwsPrpClaimList 再保理赔险种明细列表
     * @return 结果
     */
    public int insertBatchDwsPrpClaim(List<DwsPrpClaimEntity> dwsPrpClaimList);

    /**
     * 修改再保理赔险种明细
     *
     * @param dwsPrpClaimEntity 再保理赔险种明细
     * @return 结果
     */
    public int updateDwsPrpClaim(DwsPrpClaimEntity dwsPrpClaimEntity);

    /**
     * 删除再保理赔险种明细
     *
     * @param Id 再保理赔险种明细主键
     * @return 结果
     */
    public int deleteDwsPrpClaimById(Long Id);

    /**
     * 批量删除再保理赔险种明细
     *
     * @param Ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDwsPrpClaimByIds(Long[] Ids);

    /**
     * 检查再保理赔险种明细是否存在
     *
     * @param dwsPrpClaimQuery 查询条件
     * @return 结果
     */
    public int selectDwsPrpClaimExists(DwsPrpClaimQuery dwsPrpClaimQuery);

    /**
     * 更新推送状态
     *
     * @param pushStatus 推送状态
     * @param pushBy 推送人
     * @param Ids 主键数组
     * @return 结果
     */
    public int updateDwsPrpClaimPushStatus(@Param("pushStatus") Integer pushStatus, @Param("pushBy") String pushBy, @Param("Ids") Long[] Ids);

    /**
     * 查询年度报表推送状态
     *
     * @param reportYear 报表年份
     * @return 结果
     */
    public Integer selectAnnualReportShouldPushStatus(@Param("reportYear") int reportYear);
}
