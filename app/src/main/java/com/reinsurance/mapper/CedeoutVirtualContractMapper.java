package com.reinsurance.mapper;

import java.util.List;
import com.reinsurance.domain.CedeoutVirtualContractEntity;
import com.reinsurance.query.CedeoutVirtualContractQuery;

/**
 * 虚拟合同Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-19
 */
public interface CedeoutVirtualContractMapper 
{
    /**
     * 查询虚拟合同
     * 
     * @param id 虚拟合同主键
     * @return 虚拟合同
     */
    public CedeoutVirtualContractEntity selectCedeoutVirtualContractById(Long id);

    CedeoutVirtualContractEntity selectCedeoutVirtualContractByCode(String virtualCode);
    /**
     * 查询虚拟合同列表
     * 
     * @param cedeoutVirtualContractQuery 虚拟合同
     * @return 虚拟合同集合
     */
    public List<CedeoutVirtualContractEntity> selectCedeoutVirtualContractList(CedeoutVirtualContractQuery cedeoutVirtualContractQuery);

    /**
     * 新增虚拟合同
     * 
     * @param cedeoutVirtualContract 虚拟合同
     * @return 结果
     */
    public int insertCedeoutVirtualContract(CedeoutVirtualContractEntity cedeoutVirtualContract);

    /**
     * 修改虚拟合同
     * 
     * @param cedeoutVirtualContract 虚拟合同
     * @return 结果
     */
    public int updateCedeoutVirtualContract(CedeoutVirtualContractEntity cedeoutVirtualContract);

    /**
     * 删除虚拟合同
     * 
     * @param id 虚拟合同主键
     * @return 结果
     */
    public int deleteCedeoutVirtualContractById(Long id);

    /**
     * 批量删除虚拟合同
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCedeoutVirtualContractByIds(Long[] ids);
}
