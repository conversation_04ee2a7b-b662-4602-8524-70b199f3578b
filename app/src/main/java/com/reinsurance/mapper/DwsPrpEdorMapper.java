package com.reinsurance.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;

import com.reinsurance.domain.DwsPrpEdorEntity;
import com.reinsurance.query.DwsPrpEdorQuery;

/**
 * 再保保全险种明细Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface DwsPrpEdorMapper {
    
    /**
     * 查询再保保全险种明细
     *
     * @param Id 再保保全险种明细主键
     * @return 再保保全险种明细
     */
    public DwsPrpEdorEntity selectDwsPrpEdorById(Long Id);

    /**
     * 查询再保保全险种明细列表
     *
     * @param dwsPrpEdorQuery 再保保全险种明细
     * @return 再保保全险种明细集合
     */
    public List<DwsPrpEdorEntity> selectDwsPrpEdorList(DwsPrpEdorQuery dwsPrpEdorQuery);

    /**
     * 根据主键数组查询再保保全险种明细列表
     *
     * @param Ids 主键数组
     * @return 再保保全险种明细集合
     */
    public List<DwsPrpEdorEntity> selectDwsPrpEdorByIds(Long[] Ids);

    /**
     * 新增再保保全险种明细
     *
     * @param dwsPrpEdorEntity 再保保全险种明细
     * @return 结果
     */
    public int insertDwsPrpEdor(DwsPrpEdorEntity dwsPrpEdorEntity);

    /**
     * 批量新增再保保全险种明细
     *
     * @param dwsPrpEdorList 再保保全险种明细列表
     * @return 结果
     */
    public int insertBatchDwsPrpEdor(List<DwsPrpEdorEntity> dwsPrpEdorList);

    /**
     * 修改再保保全险种明细
     *
     * @param dwsPrpEdorEntity 再保保全险种明细
     * @return 结果
     */
    public int updateDwsPrpEdor(DwsPrpEdorEntity dwsPrpEdorEntity);

    /**
     * 删除再保保全险种明细
     *
     * @param Id 再保保全险种明细主键
     * @return 结果
     */
    public int deleteDwsPrpEdorById(Long Id);

    /**
     * 批量删除再保保全险种明细
     *
     * @param Ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDwsPrpEdorByIds(Long[] Ids);

    /**
     * 检查再保保全险种明细是否存在
     *
     * @param dwsPrpEdorQuery 查询条件
     * @return 结果
     */
    public int selectDwsPrpEdorExists(DwsPrpEdorQuery dwsPrpEdorQuery);

    /**
     * 更新推送状态
     *
     * @param pushStatus 推送状态
     * @param pushBy 推送人
     * @param Ids 主键数组
     * @return 结果
     */
    public int updateDwsPrpEdorPushStatus(@Param("pushStatus") Integer pushStatus, @Param("pushBy") String pushBy, @Param("Ids") Long[] Ids);

    /**
     * 查询年度报表推送状态
     *
     * @param reportYear 报表年份
     * @return 结果
     */
    public Integer selectAnnualReportShouldPushStatus(@Param("reportYear") int reportYear);
}
