package com.reinsurance.mapper;

import org.apache.ibatis.annotations.Param;

public interface DeleteJobTradeDataMapper {
	
	/**
	 * 删除某个批次的日志
	 * @param batchNo	批次号
	 * @return
	 */
	public int deleteCedeoutBatchLogByBatchNo(@Param("batchNo") String batchNo);
	
	/**
	 * 删除某个批次的分出数据
	 * @param batchNo	批次号
	 * @return
	 */
	public int deleteDwsReinsuTradeByBatchNo(@Param("batchNo") String batchNo);
	
	/**
	 * 删除某个批次的再保保单数据
	 * @param batchNo	批次号
	 * @return
	 */
	public int deleteDwsReinsuPolicyLiabilityByBatchNo(@Param("batchNo") String batchNo);
}
