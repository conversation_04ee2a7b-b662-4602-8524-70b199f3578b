package com.reinsurance.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;

import com.reinsurance.domain.DwsPrpInsureContEntity;
import com.reinsurance.query.DwsPrpInsureContQuery;

/**
 * 保单登记再保合同信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface DwsPrpInsureContMapper {
    
    /**
     * 查询保单登记再保合同信息
     *
     * @param Id 保单登记再保合同信息主键
     * @return 保单登记再保合同信息
     */
    public DwsPrpInsureContEntity selectDwsPrpInsureContById(Long Id);

    /**
     * 查询保单登记再保合同信息列表
     *
     * @param dwsPrpInsureContQuery 保单登记再保合同信息
     * @return 保单登记再保合同信息集合
     */
    public List<DwsPrpInsureContEntity> selectDwsPrpInsureContList(DwsPrpInsureContQuery dwsPrpInsureContQuery);

    /**
     * 根据主键数组查询保单登记再保合同信息列表
     *
     * @param Ids 主键数组
     * @return 保单登记再保合同信息集合
     */
    public List<DwsPrpInsureContEntity> selectDwsPrpInsureContByIds(Long[] Ids);

    /**
     * 新增保单登记再保合同信息
     *
     * @param dwsPrpInsureContEntity 保单登记再保合同信息
     * @return 结果
     */
    public int insertDwsPrpInsureCont(DwsPrpInsureContEntity dwsPrpInsureContEntity);

    /**
     * 批量新增保单登记再保合同信息
     *
     * @param dwsPrpInsureContList 保单登记再保合同信息列表
     * @return 结果
     */
    public int insertBatchDwsPrpInsureCont(List<DwsPrpInsureContEntity> dwsPrpInsureContList);

    /**
     * 修改保单登记再保合同信息
     *
     * @param dwsPrpInsureContEntity 保单登记再保合同信息
     * @return 结果
     */
    public int updateDwsPrpInsureCont(DwsPrpInsureContEntity dwsPrpInsureContEntity);

    /**
     * 删除保单登记再保合同信息
     *
     * @param Id 保单登记再保合同信息主键
     * @return 结果
     */
    public int deleteDwsPrpInsureContById(Long Id);

    /**
     * 批量删除保单登记再保合同信息
     *
     * @param Ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDwsPrpInsureContByIds(Long[] Ids);

    /**
     * 检查保单登记再保合同信息是否存在
     *
     * @param dwsPrpInsureContQuery 查询条件
     * @return 结果
     */
    public int selectDwsPrpInsureContExists(DwsPrpInsureContQuery dwsPrpInsureContQuery);

    /**
     * 更新推送状态
     *
     * @param pushStatus 推送状态
     * @param pushBy 推送人
     * @param Ids 主键数组
     * @return 结果
     */
    public int updateDwsPrpInsureContPushStatus(@Param("pushStatus") Integer pushStatus, @Param("pushBy") String pushBy, @Param("Ids") Long[] Ids);

    /**
     * 查询年度报表推送状态
     *
     * @param reportYear 报表年份
     * @return 结果
     */
    public Integer selectAnnualReportShouldPushStatus(@Param("reportYear") int reportYear);
}
