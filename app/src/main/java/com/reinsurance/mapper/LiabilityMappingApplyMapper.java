package com.reinsurance.mapper;

import java.util.List;
import com.reinsurance.domain.LiabilityMappingApplyEntity;
import com.reinsurance.query.LiabilityMappingApplyQuery;

/**
 * 再保责任映射申请Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-20
 */
public interface LiabilityMappingApplyMapper 
{
    /**
     * 查询再保责任映射申请
     * 
     * @param id 再保责任映射申请主键
     * @return 再保责任映射申请
     */
    public LiabilityMappingApplyEntity selectLiabilityMappingApplyById(Long id);

    /**
     * 查询再保责任映射申请列表
     * 
     * @param liabilityMappingApplyQuery 再保责任映射申请
     * @return 再保责任映射申请集合
     */
    public List<LiabilityMappingApplyEntity> selectLiabilityMappingApplyList(LiabilityMappingApplyQuery liabilityMappingApplyQuery);

    /**
     * 新增再保责任映射申请
     * 
     * @param liabilityMappingApply 再保责任映射申请
     * @return 结果
     */
    public int insertLiabilityMappingApply(LiabilityMappingApplyEntity liabilityMappingApply);

    /**
     * 修改再保责任映射申请
     * 
     * @param liabilityMappingApply 再保责任映射申请
     * @return 结果
     */
    public int updateLiabilityMappingApply(LiabilityMappingApplyEntity liabilityMappingApply);

    /**
     * 删除再保责任映射申请
     * 
     * @param id 再保责任映射申请主键
     * @return 结果
     */
    public int deleteLiabilityMappingApplyById(Long id);

    /**
     * 批量删除再保责任映射申请
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteLiabilityMappingApplyByIds(Long[] ids);
}
