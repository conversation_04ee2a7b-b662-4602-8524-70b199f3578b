package com.reinsurance.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.reinsurance.domain.DwsEastZbcpxxbEntity;
import com.reinsurance.query.DwsEastZbcpxxbQuery;

/**
 * East再保产品信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-05
 */
public interface DwsEastZbcpxxbMapper {
    /**
     * 查询East再保产品信息
     *
     * @param id East再保产品信息主键
     * @return East再保产品信息
     */
    DwsEastZbcpxxbEntity selectDwsEastZbcpxxbById(Long id);

    /**
     * 查询East再保产品信息列表
     *
     * @param dwsEastZbcpxxbQuery East再保产品信息
     * @return East再保产品信息集合
     */
    List<DwsEastZbcpxxbEntity> selectDwsEastZbcpxxbList(DwsEastZbcpxxbQuery dwsEastZbcpxxbQuery);

    /**
     * 查询East再保产品信息
     *
     * @param ids East再保产品信息主键
     * @return East再保合同信息
     */
    List<DwsEastZbcpxxbEntity> selectDwsEastZbcpxxbListByIds(Long [] ids);
    
    /**
     * East再保产品信息是不是存在
     * @param dwsEastZbcpxxbQuery
     * @return
     */
    int selectDwsEastZbcpxxbExists(DwsEastZbcpxxbQuery dwsEastZbcpxxbQuery);
    
    /**
     * 获取监管报表应该赋值的推送状态
     * @param reportYear	报表年份
     * @return
     */
    Integer selectAnnualReportShouldPushStatus(@Param("reportYear")int reportYear);
    
    /**
     * 推送前查询所有非空字段实际值为空的流水号
     * @param ids
     * @return
     */
    List<String> selectPushBeforeCheckEmpty(Long [] ids);
    
    /**
     * 新增East再保产品信息
     *
     * @param dwsEastZbcpxxbEntity East再保产品信息
     * @return 结果
     */
    int insertDwsEastZbcpxxb(DwsEastZbcpxxbEntity dwsEastZbcpxxbEntity);
    
    /**
     * 新增East再保产品信息
     *
     * @param dwsEastZbcpxxbEntity East再保产品信息
     * @return 结果
     */
    int insertBatchDwsEastZbcpxxb(List<DwsEastZbcpxxbEntity> dwsEastZbcpxxbList);
    
    /**
     * 修改East再保产品信息
     *
     * @param dwsEastZbcpxxbEntity East再保产品信息
     * @return 结果
     */
    int updateDwsEastZbcpxxb(DwsEastZbcpxxbEntity dwsEastZbcpxxbEntity);
    
    /**
     * 推送East再保产品信息
     * @param pushStatus
     * @param pushBy
     * @param ids
     * @return 结果
     */
    int updateDwsEastZbcpxxbPushStatus(@Param("pushStatus")Integer pushStatus, @Param("pushBy")String pushBy, @Param("ids")Long [] ids);

    /**
     * 删除East再保产品信息
     *
     * @param id East再保产品信息主键
     * @return 结果
     */
    int deleteDwsEastZbcpxxbById(Long id);

    /**
     * 批量删除East再保产品信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteDwsEastZbcpxxbByIds(Long[] ids);
}
