package com.reinsurance.mapper;

import java.util.List;
import com.reinsurance.domain.ReservesFactorTrackEntity;
import com.reinsurance.query.ReservesFactorTrackQuery;

/**
 * 准备金因子轨迹Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-20
 */
public interface ReservesFactorTrackMapper 
{
    /**
     * 查询准备金因子轨迹
     * 
     * @param trackId 准备金因子轨迹主键
     * @return 准备金因子轨迹
     */
    public ReservesFactorTrackEntity selectReservesFactorTrackByTrackId(Long trackId);

    /**
     * 查询准备金因子轨迹列表
     * 
     * @param reservesFactorTrack 准备金因子轨迹
     * @return 准备金因子轨迹集合
     */
    public List<ReservesFactorTrackEntity> selectReservesFactorTrackList(ReservesFactorTrackQuery reservesFactorTrackQuery);

    /**
     * 新增准备金因子轨迹
     * 
     * @param reservesFactorTrack 准备金因子轨迹
     * @return 结果
     */
    public int insertReservesFactorTrack(ReservesFactorTrackEntity reservesFactorTrack);

    /**
     * 修改准备金因子轨迹
     * 
     * @param reservesFactorTrack 准备金因子轨迹
     * @return 结果
     */
    public int updateReservesFactorTrack(ReservesFactorTrackEntity reservesFactorTrack);

    /**
     * 删除准备金因子轨迹
     * 
     * @param trackId 准备金因子轨迹主键
     * @return 结果
     */
    public int deleteReservesFactorTrackByTrackId(Long trackId);

    /**
     * 批量删除准备金因子轨迹
     * 
     * @param trackIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteReservesFactorTrackByTrackIds(Long[] trackIds);
}
