package com.reinsurance.mapper;

import java.util.List;

import com.reinsurance.dto.DwsPrpAccountDTO;
import org.apache.ibatis.annotations.Param;

import com.reinsurance.domain.DwsPrpContEntity;
import com.reinsurance.query.DwsPrpContQuery;

/**
 * 再保首续期险种明细Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface DwsPrpContMapper {
    
    /**
     * 查询再保首续期险种明细
     *
     * @param Id 再保首续期险种明细主键
     * @return 再保首续期险种明细
     */
    public DwsPrpContEntity selectDwsPrpContById(Long Id);

    /**
     * 查询再保首续期险种明细列表
     *
     * @param dwsPrpContQuery 再保首续期险种明细
     * @return 再保首续期险种明细集合
     */
    public List<DwsPrpContEntity> selectDwsPrpContList(DwsPrpContQuery dwsPrpContQuery);

    /**
     * 根据主键数组查询再保首续期险种明细列表
     *
     * @param Ids 主键数组
     * @return 再保首续期险种明细集合
     */
    public List<DwsPrpContEntity> selectDwsPrpContByIds(Long[] Ids);

    /**
     * 新增再保首续期险种明细
     *
     * @param dwsPrpContEntity 再保首续期险种明细
     * @return 结果
     */
    public int insertDwsPrpCont(DwsPrpContEntity dwsPrpContEntity);

    /**
     * 批量新增再保首续期险种明细
     *
     * @param dwsPrpContList 再保首续期险种明细列表
     * @return 结果
     */
    public int insertBatchDwsPrpCont(List<DwsPrpContEntity> dwsPrpContList);

    /**
     * 修改再保首续期险种明细
     *
     * @param dwsPrpContEntity 再保首续期险种明细
     * @return 结果
     */
    public int updateDwsPrpCont(DwsPrpContEntity dwsPrpContEntity);

    /**
     * 删除再保首续期险种明细
     *
     * @param Id 再保首续期险种明细主键
     * @return 结果
     */
    public int deleteDwsPrpContById(Long Id);

    /**
     * 批量删除再保首续期险种明细
     *
     * @param Ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDwsPrpContByIds(Long[] Ids);

    /**
     * 查询再保首续期险种明细总数
     * @param accTransNo 账单交易编码
     * @return
     */
    int selectDwsPrpContCountByAccTransNo(String accTransNo);

    /**
     * 查询再保首续期险种明细列表
     * @param accTransNo 账单交易编码
     * @param pageSize
     * @param startRows
     * @return 再保首续期险种明细列表
     */
    List<DwsPrpContEntity> selectDwsPrpContListByAccTransNo(@Param("accTransNo")String accTransNo, @Param("pageSize")int pageSize, @Param("startRows")int startRows);

    /**
     * 删除再保首续期险种明细
     * @param accTransNo 账单交易编码
     * @return 结果
     */
    public int deleteDwsPrpContByAccTransNo(String accTransNo);

    /**
     * 批量插入首续期险种明细数据（已结算）
     * @param account
     * @return
     */
    public int insertDwsPrpContFormTrade(DwsPrpAccountDTO account);

    /**
     * 批量插入首续期险种明细数据（未结算）
     * @param account
     * @return
     */
    public int insertPreDwsPrpContFormTrade(DwsPrpAccountDTO account);

    /**
     * 查询再保首续期险种明细交易编码为空的数据
     * @param accTransNo
     * @param limit
     * @return East比例再保保单明细DTO 集合
     */
    public List<Long> selectWaitSetTransNoDwsPrpContListByAccTransNo(String accTransNo, int limit);

    /**
     * 更新再保首续期险种明细推送状态
     * @param pushStatus 推送状态
     * @param pushBy 推送人
     * @param accTransNos 账单交易编码集合
     * @return 结果
     */
    public int updateDwsPrpContPushStatus(@Param("pushStatus") Integer pushStatus, @Param("pushBy") String pushBy, @Param("accTransNos") List<String> accTransNos);
}
