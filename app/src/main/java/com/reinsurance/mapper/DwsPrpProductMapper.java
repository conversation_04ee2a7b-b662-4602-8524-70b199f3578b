package com.reinsurance.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;

import com.reinsurance.domain.DwsPrpProductEntity;
import com.reinsurance.query.DwsPrpProductQuery;

/**
 * 保单登记再保产品信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
public interface DwsPrpProductMapper {
    
    /**
     * 查询保单登记再保产品信息
     *
     * @param Id 保单登记再保产品信息主键
     * @return 保单登记再保产品信息
     */
    public DwsPrpProductEntity selectDwsPrpProductById(Long Id);

    /**
     * 查询保单登记再保产品信息列表
     *
     * @param dwsPrpProductQuery 保单登记再保产品信息
     * @return 保单登记再保产品信息集合
     */
    public List<DwsPrpProductEntity> selectDwsPrpProductList(DwsPrpProductQuery dwsPrpProductQuery);

    /**
     * 根据主键数组查询保单登记再保产品信息列表
     *
     * @param Ids 主键数组
     * @return 保单登记再保产品信息集合
     */
    public List<DwsPrpProductEntity> selectDwsPrpProductByIds(Long[] Ids);

    /**
     * 新增保单登记再保产品信息
     *
     * @param dwsPrpProductEntity 保单登记再保产品信息
     * @return 结果
     */
    public int insertDwsPrpProduct(DwsPrpProductEntity dwsPrpProductEntity);

    /**
     * 批量新增保单登记再保产品信息
     *
     * @param dwsPrpProductList 保单登记再保产品信息列表
     * @return 结果
     */
    public int insertBatchDwsPrpProduct(List<DwsPrpProductEntity> dwsPrpProductList);

    /**
     * 修改保单登记再保产品信息
     *
     * @param dwsPrpProductEntity 保单登记再保产品信息
     * @return 结果
     */
    public int updateDwsPrpProduct(DwsPrpProductEntity dwsPrpProductEntity);

    /**
     * 删除保单登记再保产品信息
     *
     * @param Id 保单登记再保产品信息主键
     * @return 结果
     */
    public int deleteDwsPrpProductById(Long Id);

    /**
     * 批量删除保单登记再保产品信息
     *
     * @param Ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDwsPrpProductByIds(Long[] Ids);

    /**
     * 检查保单登记再保产品信息是否存在
     *
     * @param dwsPrpProductQuery 查询条件
     * @return 结果
     */
    public int selectDwsPrpProductExists(DwsPrpProductQuery dwsPrpProductQuery);

    /**
     * 更新推送状态
     *
     * @param pushStatus 推送状态
     * @param pushBy 推送人
     * @param Ids 主键数组
     * @return 结果
     */
    public int updateDwsPrpProductPushStatus(@Param("pushStatus") Integer pushStatus, @Param("pushBy") String pushBy, @Param("Ids") Long[] Ids);

    /**
     * 查询年度报表推送状态
     *
     * @param reportYear 报表年份
     * @return 结果
     */
    public Integer selectAnnualReportShouldPushStatus(@Param("reportYear") int reportYear);

    /**
     * 从合同责任表查询再保产品信息
     *
     * @param dwsPrpProductQuery 保单登记再保产品信息查询对象
     * @return 结果
     */
    public List<DwsPrpProductEntity> selectContractLiabilityAsPrpProduct(DwsPrpProductQuery dwsPrpProductQuery);
}
