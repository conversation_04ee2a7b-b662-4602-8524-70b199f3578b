package com.reinsurance.mapper;

import java.util.List;
import com.reinsurance.domain.CedeoutContractLiabilityTrackEntity;
import com.reinsurance.query.CedeoutContractLiabilityTrackQuery;

/**
 * 再保合同责任轨迹Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-18
 */
public interface CedeoutContractLiabilityTrackMapper 
{
    /**
     * 查询再保合同责任轨迹
     * 
     * @param trackId 再保合同责任轨迹主键
     * @return 再保合同责任轨迹
     */
    public CedeoutContractLiabilityTrackEntity selectCedeoutContractLiabilityTrackByTrackId(Long trackId);

    /**
     * 查询再保合同责任轨迹列表
     * 
     * @param cedeoutContractLiabilityTrackQuery 再保合同责任轨迹
     * @return 再保合同责任轨迹集合
     */
    public List<CedeoutContractLiabilityTrackEntity> selectCedeoutContractLiabilityTrackList(CedeoutContractLiabilityTrackQuery cedeoutContractLiabilityTrackQuery);

    /**
     * 新增再保合同责任轨迹
     * 
     * @param cedeoutContractLiabilityTrack 再保合同责任轨迹
     * @return 结果
     */
    public int insertCedeoutContractLiabilityTrack(CedeoutContractLiabilityTrackEntity cedeoutContractLiabilityTrack);

    /**
     * 修改再保合同责任轨迹
     * 
     * @param cedeoutContractLiabilityTrack 再保合同责任轨迹
     * @return 结果
     */
    public int updateCedeoutContractLiabilityTrack(CedeoutContractLiabilityTrackEntity cedeoutContractLiabilityTrack);

    /**
     * 删除再保合同责任轨迹
     * 
     * @param trackId 再保合同责任轨迹主键
     * @return 结果
     */
    public int deleteCedeoutContractLiabilityTrackByTrackId(Long trackId);

    /**
     * 批量删除再保合同责任轨迹
     * 
     * @param trackIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCedeoutContractLiabilityTrackByTrackIds(Long[] trackIds);
}
