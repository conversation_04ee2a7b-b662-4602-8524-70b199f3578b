package com.reinsurance.mapper;

import java.util.List;

import com.reinsurance.domain.DwsReinsuPolicyLiabilityEntity;

/**
 * 再保方案保单责任对象Mapper接口
 *
 * <AUTHOR>
 * @date 2024-08-26
 */
public interface DwsReinsuPolicyLiabilityMapper {
	
	/**
     * 查询待处理的再保方案保单责任对象列表
     * @param dwsReinsuPolicyLiabilityQuery 再保方案保单责任对象
     * @return 再保方案保单责任对象集合
     */
    List<DwsReinsuPolicyLiabilityEntity> selectWaitReinsuPolicyLiabilityList(DwsReinsuPolicyLiabilityEntity dwsReinsuPolicyLiabilityQuery);

	/**
     * 查询一个再保方案保单责任对象
     * @param dwsReinsuPolicyLiabilityQuery 再保方案保单责任对象
     * @return 再保方案保单责任对象集合
     */
    DwsReinsuPolicyLiabilityEntity selectOneReinsuPolicyLiability(DwsReinsuPolicyLiabilityEntity dwsReinsuPolicyLiabilityQuery);
    
    /**
     * 批量新增再保方案保单责任对象新单数据（数据来源v_dws_policy）
     *
     * @param dwsReinsuPolicyLiabilityEntity
     * @return 结果
     */
    int insertBatchDwsReinsuPolicyLiabilityNew(DwsReinsuPolicyLiabilityEntity dwsReinsuPolicyLiabilityEntity);

    /**
     * 批量新增再保方案保单责任对象续期数据（数据来源v_dws_policy）
     *
     * @param dwsReinsuPolicyLiabilityEntity
     * @return 结果
     */
    int insertBatchDwsReinsuPolicyLiabilityRenewal(DwsReinsuPolicyLiabilityEntity dwsReinsuPolicyLiabilityEntity);
    
    /**
     * 批量插入回溯方案产生的再保方案保单责任（数据来源t_dws_cont_history）
     * @param dwsReinsuPolicyLiabilityEntity
     * @return
     */
    int insertBatchReinsuPolicyLiabilityFromHistory(DwsReinsuPolicyLiabilityEntity dwsReinsuPolicyLiabilityEntity);

}
