package com.reinsurance.mapper;

import java.util.List;

import com.reinsurance.domain.DwsPolicyEntity;
import com.reinsurance.query.DwsReinsuTradeQuery;

/**
 * 保单物化视图Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-04-19
 */
public interface DwsPolicyMapper{
	
	/**
	 * 获取一条保单给付责任信息
	 * @param dwsPolicyEntity
	 * @return
	 */
	DwsPolicyEntity selectOneDwsPolicy(DwsPolicyEntity dwsPolicyEntity);
	
	/**
	 * 获取一条保单给付责任信息
	 * @param dwsPolicyEntity
	 * @return
	 */
	DwsPolicyEntity selectOneDwsPolicyFormDwsContLiability(DwsPolicyEntity dwsPolicyEntity);
	
    /**
     * 保单列表
     *
     * @param dwsPolicyEntity 保单
     * @return 保单列表集合
     */
    List<DwsPolicyEntity> selectDwsPolicyList(DwsPolicyEntity dwsPolicyEntity);
    
    /**
     * 查询视图未分出数据
     *
     * @param dwsReinsuTradeQuery 保单
     * @return 保单列表集合
     */
    List<DwsPolicyEntity> selectDwsPolicyNotCedeoutList(DwsReinsuTradeQuery dwsReinsuTradeQuery);
}