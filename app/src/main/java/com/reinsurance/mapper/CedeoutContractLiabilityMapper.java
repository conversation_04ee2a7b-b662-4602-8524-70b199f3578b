package com.reinsurance.mapper;

import java.util.List;
import com.reinsurance.domain.CedeoutContractLiabilityEntity;
import com.reinsurance.dto.DwsEastZbcpxxbDTO;
import com.reinsurance.dto.DwsPrpProductDTO;
import com.reinsurance.query.CedeoutContractLiabilityQuery;

/**
 * 再保合同责任Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-18
 */
public interface CedeoutContractLiabilityMapper 
{
    /**
     * 查询再保合同责任
     * 
     * @param id 再保合同责任主键
     * @return 再保合同责任
     */
    public CedeoutContractLiabilityEntity selectCedeoutContractLiabilityById(Long id);

    /**
     * 查询再保合同责任列表
     * 
     * @param cedeoutContractLiabilityQuery 再保合同责任
     * @return 再保合同责任集合
     */
    public List<CedeoutContractLiabilityEntity> selectCedeoutContractLiabilityList(CedeoutContractLiabilityQuery cedeoutContractLiabilityQuery);

    /**
     * 新增再保合同责任
     * 
     * @param cedeoutContractLiability 再保合同责任
     * @return 结果
     */
    public int insertCedeoutContractLiability(CedeoutContractLiabilityEntity cedeoutContractLiability);

    /**
     * 修改再保合同责任
     * 
     * @param cedeoutContractLiability 再保合同责任
     * @return 结果
     */
    public int updateCedeoutContractLiability(CedeoutContractLiabilityEntity cedeoutContractLiability);

    /**
     * 删除再保合同责任
     * 
     * @param id 再保合同责任主键
     * @return 结果
     */
    public int deleteCedeoutContractLiabilityById(Long id);

    /**
     * 批量删除再保合同责任
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCedeoutContractLiabilityByIds(Long[] ids);
    
    /**
     * 查询原始合同的再保责任转换为为East再保产品DTO对象
     * @param cedeoutContractLiabilityQuery
     * @return
     */
    public List<DwsEastZbcpxxbDTO> selectContractLiabilityAsEastZbcpxxb(CedeoutContractLiabilityQuery cedeoutContractLiabilityQuery);

    /**
     * 从合同责任表查询再保产品信息
     *
     * @param cedeoutContractLiabilityQuery 再保合同产品查询对象
     * @return 结果
     */
    public List<DwsPrpProductDTO> selectContractLiabilityAsPrpProduct(CedeoutContractLiabilityQuery cedeoutContractLiabilityQuery);
}
