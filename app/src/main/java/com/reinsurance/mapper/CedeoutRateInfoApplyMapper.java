package com.reinsurance.mapper;

import java.util.List;

import com.reinsurance.domain.CedeoutRateInfoApplyEntity;
import com.reinsurance.query.CedeoutRateInfoApplyQuery;

/**
 * 费率信息申请Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-20
 */
public interface CedeoutRateInfoApplyMapper 
{
    /**
     * 查询费率信息申请
     * 
     * @param id 费率信息申请主键
     * @return 费率信息申请
     */
    public CedeoutRateInfoApplyEntity selectCedeoutRateInfoApplyById(Long id);

    /**
     * 查询费率信息申请列表
     * 
     * @param cedeoutRateInfoApplyQuery 费率信息申请
     * @return 费率信息申请集合
     */
    public List<CedeoutRateInfoApplyEntity> selectCedeoutRateInfoApplyList(CedeoutRateInfoApplyQuery cedeoutRateInfoApplyQuery);

    /**
     * 新增费率信息申请
     * 
     * @param cedeoutRateInfoApply 费率信息申请
     * @return 结果
     */
    public int insertCedeoutRateInfoApply(CedeoutRateInfoApplyEntity cedeoutRateInfoApply);

    /**
     * 修改费率信息申请
     * 
     * @param cedeoutRateInfoApply 费率信息申请
     * @return 结果
     */
    public int updateCedeoutRateInfoApply(CedeoutRateInfoApplyEntity cedeoutRateInfoApply);

    /**
     * 删除费率信息申请
     * 
     * @param id 费率信息申请主键
     * @return 结果
     */
    public int deleteCedeoutRateInfoApplyById(Long id);

    /**
     * 批量删除费率信息申请
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCedeoutRateInfoApplyByIds(Long[] ids);
}
