package com.reinsurance.mapper;

import java.util.List;
import com.reinsurance.domain.CedeoutContractEntity;
import com.reinsurance.dto.CedeoutContractDTO;
import com.reinsurance.dto.DwsEastZbhtxxbDTO;
import com.reinsurance.query.CedeoutContractQuery;

/**
 * 再保合同Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-18
 */
public interface CedeoutContractMapper 
{

    List<CedeoutContractDTO> selectCedeoutContractCodeList();

    /**
     * 查询再保合同
     * 
     * @param id 再保合同主键
     * @return 再保合同
     */
    CedeoutContractEntity selectCedeoutContractById(Long id);

    /**
     * 查询再保合同列表
     * 
     * @param cedeoutContractQuery 再保合同
     * @return 再保合同集合
     */
    List<CedeoutContractEntity> selectCedeoutContractList(CedeoutContractQuery cedeoutContractQuery);

    /**
     * 查询再保合同
     *
     * @param contractCode
     * @return 再保合同
     */
    CedeoutContractEntity selectCedeoutContractByCode(String contractCode);
    /**
     * 新增再保合同
     * 
     * @param cedeoutContract 再保合同
     * @return 结果
     */
    public int insertCedeoutContract(CedeoutContractEntity cedeoutContract);

    /**
     * 修改再保合同
     * 
     * @param cedeoutContract 再保合同
     * @return 结果
     */
    public int updateCedeoutContract(CedeoutContractEntity cedeoutContract);

    /**
     * 删除再保合同
     * 
     * @param id 再保合同主键
     * @return 结果
     */
    public int deleteCedeoutContractById(Long id);

    /**
     * 批量删除再保合同
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCedeoutContractByIds(Long[] ids);
    
    /**
     * 查询原始再保合同转换为East再保合同DTO对象
     * @param cedeoutContractQuery
     * @return
     */
    public List<DwsEastZbhtxxbDTO> selectOriginalContractAsEastZbhtxxb(CedeoutContractQuery cedeoutContractQuery);
}
