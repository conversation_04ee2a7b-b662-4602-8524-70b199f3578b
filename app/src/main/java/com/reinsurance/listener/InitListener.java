package com.reinsurance.listener;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import com.reinsurance.service.ICacheService;
import com.reinsurance.track.ContTrackAdapter;
import com.reinsurance.track.factory.ContTrackAdapterFactory;

import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Order(1)
@Component
public class InitListener implements ApplicationRunner {

	@Autowired
	private ICacheService cacheService;
	
	@Override
	public void run(ApplicationArguments args) throws Exception {
		long start = System.currentTimeMillis();
		log.info("初始化监听开始执行");
		ContTrackAdapterFactory.init(SpringUtil.getBeansOfType(ContTrackAdapter.class));
		//cacheService.initCacheData();
		log.info("初始化监听执行完成, 耗时(秒):{}", (System.currentTimeMillis() - start)/1000);
	}
	
}
