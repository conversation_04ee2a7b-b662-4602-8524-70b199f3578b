package com.reinsurance.enums;

import cn.hutool.core.collection.CollectionUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Calendar;
import java.util.List;

public class BasicDataEnums {

    /** 因子类型 */
    @Getter
    @AllArgsConstructor
    public enum FactorType {

        RF("RF", "准备金因子"),
        ;

        private final String code;
        private final String desc;

        public static String getDescByCode(String code) {
            for (FactorType value : values()) {
                if (value.getCode().equals(code)) {
                    return value.getDesc();
                }
            }
            return null;
        }

        public static String getCodeByDesc(String desc) {
            for (FactorType value : values()) {
                if (value.getDesc().equals(desc)) {
                    return value.getCode();
                }
            }
            return null;
        }
    }

    /** 是否优选体 */
    @Getter
    @AllArgsConstructor
    public enum InusredMrtclst {

        IM_0(0, "次标体"),
        IM_1(1, "标准体"),
        IM_2(2, "优选体"),
        ;

        private final Integer code;
        private final String desc;

        public static String getDescByCode(Integer code) {
            for (InusredMrtclst value : values()) {
                if (value.getCode().equals(code)) {
                    return value.getDesc();
                }
            }
            return null;
        }

        public static Integer getCodeByDesc(String desc) {
            for (InusredMrtclst value : values()) {
                if (value.getDesc().equals(desc)) {
                    return value.getCode();
                }
            }
            return null;
        }
    }

    /** 交费频率 */
    @Getter
    @AllArgsConstructor
    public enum PayIntv {

        PI_0(0, "趸交"),
        PI_1(1, "月交"),
        PI_2(12, "年交"),
        ;

        private final Integer code;
        private final String desc;

        public static String getDescByCode(Integer code) {
            for (PayIntv value : values()) {
                if (value.getCode().equals(code)) {
                    return value.getDesc();
                }
            }
            return null;
        }

        public static Integer getCodeByDesc(String desc) {
            for (PayIntv value : values()) {
                if (value.getDesc().equals(desc)) {
                    return value.getCode();
                }
            }
            return null;
        }
    }

    /** 期间单位 */
    @Getter
    @AllArgsConstructor
    public enum PayUnit {

        YEAR("Y", "年"),
        AGE("A", "岁"),
        ;

        private final String code;
        private final String desc;

        public static String getDescByCode(String code) {
            for (PayUnit value : values()) {
                if (value.getCode().equals(code)) {
                    return value.getDesc();
                }
            }
            return null;
        }

        public static String getCodeByDesc(String desc) {
            for (PayUnit value : values()) {
                if (value.getDesc().equals(desc)) {
                    return value.getCode();
                }
            }
            return null;
        }
    }

    /** 状态 */
    @Getter
    @AllArgsConstructor
    public enum Status {

        STATUS_0(0, "有效"),
        STATUS_1(1, "无效"),
        ;

        private final Integer code;
        private final String desc;
        public static String getDescByCode(Integer code) {
            for (Status value : values()) {
                if (value.getCode().equals(code)) {
                    return value.getDesc();
                }
            }
            return null;
        }

        public static Integer getCodeByDesc(String desc) {
            for (Status value : values()) {
                if (value.getDesc().equals(desc)) {
                    return value.getCode();
                }
            }
            return null;
        }
    }

    /** 配置数据状态 */
    @Getter
    @AllArgsConstructor
    public enum DataConfigStatus {

        STATUS_0(0, "待生效"),
        STATUS_1(1, "已生效"),
        STATUS_2(2, "已回退"),
        ;

        private final Integer code;
        private final String desc;
    }

    /** 险种类型 */
    @Getter
    @AllArgsConstructor
    public enum CoreRiskType {
    	TYPE_1("1", "传统险"),
    	TYPE_2("2", "分红险"),
        TYPE_3("3", "投连险"),
        TYPE_4("4", "万能险"),
        ;

        private final String code;
        private final String desc;

    }

    /** 报表类型 */
    @Getter
    @AllArgsConstructor
    public enum ReportType {

        TYPE_1("1", "财务接口报表"),
        TYPE_2("2", "i17报表"),
        ;

        private final String code;
        private final String desc;

        /** 业务报表集合 */
        public static List<String> businessReport() {
            return CollectionUtil.list(false, TYPE_1.getCode(), TYPE_2.getCode());
        }

    }

    /** 业务报表处理状态 */
    @Getter
    @AllArgsConstructor
    public enum BusinessReportStatus {
        STATUS_0(0, "有效"),
        STATUS_1(1, "无效"),
        STATUS_2(2, "处理中"),
        STATUS_3(3, "处理失败"),
        ;

        private final Integer code;
        private final String desc;

    }

    /** 算法类型 */
    @Getter
    @AllArgsConstructor
    public enum FormulaType {

        风险保额("10","风险保额"),
        理赔摊回("11","理赔摊回"),
        /************************* 新单 ************************/
        新单_自留额("NP-RL", "新单_自留额"),
        新单_自留比例("NP-RR", "新单_自留比例"),
        新单_分保比例("NP-R", "新单_分保比例"),
        新单_分保保费("NP-RP", "新单_分保保费"),
        新单_分出保额("NP-CedeAmt", "新单_分出保额"),
        新单_次标再保费("NP-SBR", "新单_次标再保费"),
        新单_分保佣金("NP-RC", "新单_分保佣金"),
        /************************* 续期 ************************/
        续期_自留额("Renewal-RL", "续期_自留额"),
        续期_自留比例("Renewal-RR", "续期_自留比例"),
        续期_分保比例("Renewal-R", "续期_分保比例"),
        续期_分保保费("Renewal-RP", "续期_分保保费"),
        续期_分出保额("Renewal-CedeAmt", "续期_分出保额"),
        续期_次标再保费("Renewal-SBR", "续期_次标再保费"),
        续期_分保佣金("Renewal-RC", "续期_分保佣金"),
        /************************* 保全-退保 ************************/
        保全_退保_自留额("PE-RI-RL", "保全_退保_自留额"),
        保全_退保_分出保额("PE-RI-CedeAmt", "保全_退保_分出保额"),
        保全_退保_分保保费("PE-RI-RP", "保全_退保_分保保费"),
        保全_退保_次标再保费("PE-RI-SBR", "保全_退保_次标再保费"),
        保全_退保_分保佣金("PE-RI-RC", "保全_退保_分保佣金"),
        /************************* 保全 ************************/
        保全_自留额("PE-RL", "保全_自留额"),
        保全_自留比例("PE-RR", "保全_自留比例"),
        保全_分保比例("PE-R", "保全_分保比例"),
        保全_分出保额("PE-CedeAmt", "保全_分出保额"),
        保全_分保保费("PE-RP", "保全_分保保费"),
        保全_次标再保费("PE-SBR", "保全_次标再保费"),
        保全_分保佣金("PE-RC", "保全_分保佣金"),
        /************************* 保全-复效 ************************/
        保全_复效_自留额("PE-R-RL", "保全_复效_自留额"),
        保全_复效_自留比例("PE-R-RR", "保全_复效_自留比例"),
        保全_复效_分保比例("PE-R-R", "保全_复效_分保比例"),
        保全_复效_分出保额("PE-R-CedeAmt", "保全_复效_分出保额"),
        保全_复效_分保保费("PE-R-RP", "保全_复效_分保保费"),
        保全_复效_次标再保费("PE-R-SBR", "保全_复效_次标再保费"),
        保全_复效_分保佣金("PE-R-RC", "保全_复效_分保佣金"),
        /************************* 理赔 ************************/
        理赔_传统险_摊回赔款("CLAIM-C-RCA", "理赔_传统险_摊回赔款"),
        理赔_万能$投连险_摊回赔款("CLAIM-W-T-RCA", "理赔_万能/投连险_摊回赔款"),
        /************************* 满期 ************************/
        满期_摊回满期金("EXPIRE-REG", "满期_摊回满期金"),

        ;

        private final String code;
        private final String desc;

    }

    /** 临分标记 */
    @Getter
    @AllArgsConstructor
    public enum CedeoutFacultative {
        STATUS_0(0, "未临分"),
        STATUS_1(1, "临分"),
        ;

        private final Integer code;
        private final String desc;

    }

    /** 临分数据状态 */
    @Getter
    @AllArgsConstructor
    public enum CedeoutFacultativeDataStatus {
        STATUS_1(1, "待处理"),
        STATUS_2(2, "处理完毕"),
        ;

        private final Integer code;
        private final String desc;

    }

    /** 临分计算状态 */
    @Getter
    @AllArgsConstructor
    public enum CedeoutFacultativeCalcStatus {
        STATUS_1(1, "未计算"),
        ;

        private final Integer code;
        private final String desc;

    }
    
    /** 各个模块生成唯一编码枚举类（定义编码的规则） */
    @Getter
    @AllArgsConstructor
    public enum RedisKeyModule {
        REPORT("report", "R", Calendar.YEAR, 5, 1L, "报表模板"),
        CEDEOUT_LIABILITY("cedeoutLiability", "L", Calendar.ERA, 4, 100L, "分保责任"),
        PROGRAMME("programme", "P", Calendar.ERA, 8, 500L, "再保方案"),
        RATE("rate", "R", Calendar.ERA, 8, 100L, "费率"),
        VIRTUAL_CONTRACT("virtualContract", "V", Calendar.ERA, 8, 100L, "虚拟合约"),
        BILL("bill", "S", Calendar.YEAR, 8, 100L, "账单"),
        EAST("east", "", Calendar.DAY_OF_MONTH, 10, 1L, "报表"),
        PRP("prp", "", Calendar.DAY_OF_MONTH, 14, 1L, "报表"),
        ;
    	/**模块编码*/
        private final String code;
        /**编码前缀，拼接到UniqueCode起始位置*/
        private final String prefix;
        /**计数方式（Calendar.ERA=持久,Calendar.YEAR=年,Calendar.MONTH=月,Calendar.DAY_OF_MONTH=日）*/
        private final Integer recountMethod;
        /**序列号的长度*/
        private final Integer length;
        /**起始值*/
        private final Long initValue;
        /**枚举说明*/
        private final String desc;

        public static RedisKeyModule getModuleByCode(String code) {
            for (RedisKeyModule module : values()) {
                if (module.getCode().equals(code)) {
                    return module;
                }
            }
            return null;
        }
    }

    /** 临分分保类型 */
    @Getter
    @AllArgsConstructor
    public enum CedeoutFacultativeCedeoutType {
        STATUS_1(1, "自留分保"),
        STATUS_2(2, "合同分保"),
        STATUS_3(3, "自定义方案"),
        ;

        private final Integer code;
        private final String desc;

    }
    
    /** 再保报表统计方式 */
    @Getter
    @AllArgsConstructor
    public enum ReportStatMethod {
    	聚合(0, "聚合"),
        明细(1, "明细"),
        ;

        private final Integer code;
        private final String desc;

    }

    
    /** 再保报表字段输出逻辑 */
    @Getter
    @AllArgsConstructor
    public enum ReportStatLogic {
        普通(0, "明细列"),
        分组(1, "分组列"),
        聚合(2, "聚合列"),
        ;

        private final Integer code;
        private final String desc;

    }

    /** 文件类型 */
    @Getter
    @AllArgsConstructor
    public enum FileType {

        /** 临分确认书 - 文件存储平台(本地) */
        CFC("CFC", "local"),
        /** 再保合同文件 - 文件存储平台(本地) */
        CCT("CCT", "local"),
        REPORT("REPORT","local"),
        ;

        private final String code;
        private final String platform;

        public static String getPlatform(String code) {
            for (FileType fileType : values()) {
                if (fileType.getCode().equals(code)) {
                    return fileType.getPlatform();
                }
            }
            return null;
        }
    }

    /** 文件存储路径 */
    @Getter
    @AllArgsConstructor
    public enum FileStoragePath {

        /** 临分确认书 */
        CFC("CFC", "cedeoutFacultative/confirmation/"),

        /** 再保合同文件 */
        CCT("CCT", "cedeoutContract/contract/"),

        REPORT("REPORT", "report/"),
        ;

        private final String code;
        private final String path;

        public static String getPath(String code) {
            for (FileStoragePath storagePath : values()) {
                if (storagePath.getCode().equals(code)) {
                    return storagePath.getPath();
                }
            }
            return null;
        }
    }
    
    /** 文件存储格式 */
    @Getter
    @AllArgsConstructor
    public enum FileSuffix {
        EXCEL("EXCEL", ".xlsx"),
        ;

        private final String code;
        private final String suffix;

    }
    
    /** 保单终止类型 */
    @Getter
    @AllArgsConstructor
    public enum InvalidStateType {
        终止("Terminate", "终止");

        private final String code;
        private final String suffix;

    }
    
    /** 保单终止原因 */
    @Getter
    @AllArgsConstructor
    public enum InvalidStateReason {
    	满期终止("01", "满期终止"),
    	退保终止("02", "退保终止"),
    	解约终止("03", "解约终止"),
    	理赔终止("04", "理赔终止"),
    	协退终止("05", "协退终止"),
    	犹退终止("06", "犹退终止"),
    	失效终止("07", "失效终止"),
    	其他终止("08", "其他终止"),
        贷款终止("09", "贷款终止"),
        合规挂起失效("11", "合规挂起失效"),
        客权中止失效("13", "客权中止失效"),
        合规解挂("14", "合规解挂"),
        ;

        private final String code;
        private final String suffix;

    }
    
    /** 再保方案保单责任对象数据处理状态 */
    @Getter
    @AllArgsConstructor
    public enum HandleStatus {
    	未处理(0, "未处理"),
    	处理成功(1, "处理成功"),
    	处理失败(2, "处理失败"),
        无需处理(3, "无需处理"),
        ;

        private final Integer code;
        private final String desc;

    }
    
    /** 再保方案回溯类型 */
    @Getter
    @AllArgsConstructor
    public enum DataTrackType {
    	常规数据(0, "常规数据"),
    	回溯数据(1, "回溯数据"),
        ;

        private final Integer code;
        private final String desc;

    }
    
    /** 确认状态 */
    @Getter
    @AllArgsConstructor
    public enum ConfirmStatus {
    	未确认(0, "未确认", "未结算"),
    	已确认(1, "已确认", "已结算"),
        ;

        private final Integer code;
        private final String desc;
        private final String settle;
        
        public static String getDescByCode(Integer code) {
            for (ConfirmStatus value : values()) {
                if (value.getCode().equals(code)) {
                    return value.getDesc();
                }
            }
            return String.valueOf(code);
        }
        
        public static String getSettleByCode(Integer code) {
            for (ConfirmStatus value : values()) {
                if (value.getCode().equals(code)) {
                    return value.getSettle();
                }
            }
            return null;
        }
        
        public static Integer getCodeBySettle(String settle) {
            for (ConfirmStatus value : values()) {
                if (value.getSettle().equals(settle)) {
                    return value.getCode();
                }
            }
            return null;
        }
    }
    
    /**监管报表类型*/
    @Getter
    @AllArgsConstructor
    public enum RegulatorReport{
    	统信数据报送(0, "统信数据报送"),
        保单登记数据报送(1, "保单登记数据报送"),
    	East数据报送(2, "East数据报送"),
        ;

        private final Integer code;
        private final String desc;
        
        public static String check(Integer code) {
    		for (RegulatorReport cedeoutEnum : RegulatorReport.values()) {
    			if (cedeoutEnum.getCode() == code) {
    				return cedeoutEnum.getDesc();
    			}
    		}
    		return null;
    	}
    }

    /**保单登记报表名称*/
    @Getter
    @AllArgsConstructor
    public enum PrpReport{
        LRInsureCont(0, "LRInsureCont"),
        LRProduct(1, "LRProduct"),
        LRAccount(2, "LRAccount"),
        ;

        private final Integer code;
        private final String desc;

        public static String check(Integer code) {
            for (PrpReport item : PrpReport.values()) {
                if (item.getCode() == code) {
                    return item.getDesc();
                }
            }
            return null;
        }
    }

    /**East报表名称*/
    @Getter
    @AllArgsConstructor
    public enum EastReport{
    	再保合同信息表(0, "再保合同信息表"),
    	再保产品信息表(1, "再保产品信息表"),
    	再保账单信息表(2, "再保账单信息表"),
        ;

        private final Integer code;
        private final String desc;
        
        public static String check(Integer code) {
    		for (EastReport cedeoutEnum : EastReport.values()) {
    			if (cedeoutEnum.getCode() == code) {
    				return cedeoutEnum.getDesc();
    			}
    		}
    		return null;
    	}
    }
    
    
    /**报表推送状态*/
    @Getter
    @AllArgsConstructor
    public enum ReportPushStatus{
    	未推送(0, "未推送"),
    	部分推送(1, "部分推送"),
    	全部推送(2, "全部推送"),
    	已推送(1, "已推送"),
        ;

        private final Integer code;
        private final String desc;
    }
    
    /**报表数据来源*/
    @Getter
    @AllArgsConstructor
    public enum ReportDataSource{
    	系统(0, "系统"),
    	人工(1, "人工"),
        ;

        private final Integer code;
        private final String desc;
    }

    /**保单登记账单类型*/
    @Getter
    @AllArgsConstructor
    public enum PrpBillType{
        未结算(1, "未结算", "是"),
        已结算(0, "已结算", "否"),
        ;

        private final Integer code;
        private final String name;
        private final String desc;

        public static String getName(Integer code) {
            for (PrpBillType billType : PrpBillType.values()) {
                if (billType.getCode() == code) {
                    return billType.getName();
                }
            }
            return null;
        }
    }

    /**East账单类型*/
    @Getter
    @AllArgsConstructor
    public enum EastBillType{
    	预提(1, "预提", "是"),
    	实际(0, "实际", "否"),
        ;

        private final Integer code;
        private final String name;
        private final String desc;
        
        public static String getName(Integer code) {
    		for (EastBillType billType : EastBillType.values()) {
    			if (billType.getCode() == code) {
    				return billType.getName();
    			}
    		}
    		return null;
    	}
    }
    
    /**East合同分类*/
    @Getter
    @AllArgsConstructor
    public enum EastContractType{
    	分出合同(0, "分出合同"),
    	比例合同(0, "比例合同"),
        ;

        private final Integer code;
        private final String desc;
    }
    
    /**East报表是否标识*/
    @Getter
    @AllArgsConstructor
    public enum EastReportEitherFlag{
    	否(0, "否"),
    	是(1, "是"),
        ;

        private final Integer code;
        private final String desc;
        
        public static String getCodeByDesc(String desc) {
            for (EastReportEitherFlag value : values()) {
                if (value.getDesc().equals(desc)) {
                    return String.valueOf(value.getCode());
                }
            }
            return desc;
        }
        public static String getDescByCode(String code) {
            for (EastReportEitherFlag value : values()) {
                if (String.valueOf(value.getCode()).equals(code)) {
                    return value.getDesc();
                }
            }
            return null;
        }
    }

    /**East产品团个性质*/
    @Getter
    @AllArgsConstructor
    public enum EastContType{
    	个人(10, "个人"),
    	团体(20, "团体"),
        ;

        private final Integer code;
        private final String desc;
        
        public static String getDescByCode(Integer code) {
            for (EastContType value : values()) {
                if (value.getCode().equals(code)) {
                    return value.getDesc();
                }
            }
            return String.valueOf(code);
        }
    }
    
    /**East产品分保方式*/
    @Getter
    @AllArgsConstructor
    public enum EastCedeoutWay{
    	共保(3, "共保"),
    	超赔(4, "超赔"),
    	超额赔付率(5, "超额赔付率"),
    	溢额(CedeoutEnums.分出方式_溢额.getValue(), "溢额（YRT）"),
    	成数(CedeoutEnums.分出方式_成数.getValue(), "成数（YRT）"),
    	混合(CedeoutEnums.分出方式_混合.getValue(), "成数溢额混合（YRT）"),
        ;

        private final Integer code;
        private final String desc;
        
        public static String getDescByCode(Integer code) {
            for (EastCedeoutWay value : values()) {
                if (value.getCode().equals(code)) {
                    return value.getDesc();
                }
            }
            return String.valueOf(code);
        }
        
        public static Integer getCodeByDesc(String desc){
        	for (EastCedeoutWay value : values()) {
                if (value.getDesc().equals(desc)) {
                    return value.getCode();
                }
            }
            return null;
        }
    }
    
    /**East产品分保方式*/
    @Getter
    @AllArgsConstructor
    public enum EastZdfl{
    	分出账单(0, "分出账单"),
        ;

        private final Integer code;
        private final String desc;
        
        public static String getDescByCode(Integer code) {
            for (EastZdfl value : values()) {
                if (value.getCode().equals(code)) {
                    return value.getDesc();
                }
            }
            return null;
        }
        
        public static Integer getCodeByDesc(String desc) {
            for (EastZdfl value : values()) {
                if (value.getDesc().equals(desc)) {
                    return value.getCode();
                }
            }
            return null;
        }
    }


    /**保単登记合同分类*/
    @Getter
    @AllArgsConstructor
    public enum PrpContractAmendmentType{
        主合同("1", "主合同", CedeoutEnums.合同类型_主合同.getValue().toString()),
        附约("2", "附约", CedeoutEnums.合同类型_附约.getValue().toString()),
        ;

        private final String code;
        private final String desc;
        private final String mappingCode;

        public static String getCodeByMapingCode(String mappingCode) {
            for (PrpContractAmendmentType value : values()) {
                if (value.getMappingCode().equals(mappingCode)) {
                    return value.getCode();
                }
            }
            return null;
        }
    }
    
}
