package com.reinsurance.enums;

public enum CedeoutStrEnums {
	
	期间单位_年("Y", "年"), 期间单位_岁("A", "岁"),
	
	主附类型_主险("M", "主险"), 主附类型_附加险("S", "附加险"),

	批次号_新增("-1","批次号_新增"),

	累计风险保额_编码自增("10","累计风险保额_编码自增"),费率_编码自增("1000","费率_编码自增"),再保虚拟合约_编码自增("1000","再保虚拟合约_编码自增"),再保方案_编码自增("1000","再保方案_编码自增"),
	
	保单终止原因_满期("01", "满期"), 保单终止原因_退保("02", "退保"), 保单终止原因_解约("03", "解约"), 保单终止原因_理赔("04", "理赔"), 
	保单终止原因_协退("05", "协退"), 保单终止原因_犹退("06", "犹退"), 保单终止原因_失效("07", "失效"), 保单终止原因_其他("08", "其他"),
	
	保全_加保("AI", "加保"), 保全_复效("RE", "复效"), 保全_新增险种("NS", "新增险种"), 保全_回退("RB", "回退"), 保全_满期("FG", "满期"), 
	保全_新增被保险人("NI", "新增被保险人"), 保全_减少被保险人("ZT", "减少被保险人"), 保全_犹退("WT", "犹退"), 保全_退保("CT", "退保"), 保全_协退("XT", "协退"),
	
	再保方案_虚拟合同("virtual", "虚拟合同"), 再保方案_再保公司("company", "再保公司"), 再保方案_方案信息("programme", "方案信息"), 
	再保方案_方案责任("liability", "再保责任"), 再保方案_费率信息("rate", " 费率信息"), 再保方案_责任映射("mapping", "责任映射"), 
	再保方案_合同信息("contract", "再保合同"), 再保责任基本配置("reLiability", "再保责任配置"), 保额复利险种("interestAmountRiskCode", "保额复利险种"),
	匹配准备金险种("mustHaveReservesRiskCode", "匹配准备金险种"), 不匹配再保费率给付责任("ignoreRateGetDutyCode", "不匹配再保费率给付责任"),
	未来应缴保费险种("unpayPremiumRiskCode", "未来应缴保费险种"),
	
	准备金因子类型("RF", "准备金因子"), 
	
	缓存_准备金("reserve", "准备金"), 缓存_费率("rate", "费率"), 缓存_公式("formula", "公式"), 缓存_险种关联公式("liabilityFormula", "险种关联公式"),


	算法类型_风险保额("10","算法类型_风险保额"),算法类型_理赔摊回("11","理赔摊回"),
	;
	
	private String value;
	
	private String name;

	public static String getValueByKey(String value) {
		for (CedeoutStrEnums cedeoutEnum : CedeoutStrEnums.values()) {
			if (cedeoutEnum.getValue().equals(value)) {
				return cedeoutEnum.getName();
			}
		}
		return value;
	}

	CedeoutStrEnums(String value, String name) {
        this.value = value;
        this.name = name;
    }

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
}
