package com.reinsurance.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;
import lombok.EqualsAndHashCode;

import com.jd.lightning.common.annotation.Excel;

/**
 * 需要分出的保全成功后保单给付责任信息快照表
 * <AUTHOR>
 * @date 2024-12-25
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class DwsEdorContLiabilityEntity extends VirtualFieldEntity{
	
	private static final long serialVersionUID = -6323494890212153665L;

	/** 主键自增 */
    private Long id;
    
    /**保全视图的唯一键*/
    private String uniqueKey;

    /** 保单类型(1=个险,2=团险) */
    private Integer contType;
    
    /** 团单号 */
    private String grpContNo;

    /** 团单险种号 */
    private String grpPolNo;

    /** 个单合同号码 */
    private String contNo;

    /** 险种号 */
    private String polNo;

    /** 主险保单险种号 */
    private String mainPolNo;

    /** 销售渠道编码 */
    private String saleChnl;

    /** 销售渠道名称 */
    private String saleChnlName;

    /** 销售方式 */
    private String sellType;

    /** 销售方式名称 */
    private String sellTypeName;

    /** 销售机构编码 */
    private String saleComCode;

    /** 销售机构名称 */
    private String saleComName;

    /** 代理机构编码 */
    private String agentComCode;

    /** 代理机构名称 */
    private String agentComName;

    /** 管理机构编码 */
    private String manageComCode;

    /** 管理机构名称 */
    private String manageComName;
    
    /**银行网点名称*/
    private String bankBranchName;

    /** 签单日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date signDate;

    /** 签单时间 */
    private String signTime;

    /** 生效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date riskValiDate;

    /** 保险终止日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date riskEndDate;

    /** 保单创建日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contMakeDate;

    /** 保单创建时间 */
    private String contMakeTime;

    /** 保单年度 */
    private Integer contYear;

    /** 保单周年日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contAnniversary;

    /** 上一保单周年日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date previousContAnniversary;

    /** 保单状态(1=有效,4=失效) */
    private Integer contAppFlag;

    /** 险种编码 */
    private String riskCode;

    /** 险种名称 */
    private String riskName;

    /** 险种类别(1=传统险,2=分红险,3=投连险,4=万能险) */
    private String riskType3;

    /** 保障计划 */
    @Excel(name = "保障计划")
    private String planCode;

    /** 风险类型 */
    private String polRiskType;

    /** 险种状态(1=有效,4=失效) */
    private Integer riskAppFlag;

    /** 主附险标识(M=主险,S=附加险) */
    private String subRiskFlag;

    /** 一年期险种标志(L=长险,M=一年期险,S=极短期险) */
    private String riskPeriod;

    /** 保险期间 */
    private Integer insuYear;

    /** 保险期间单位(Y=年, M=月, D=日, A=岁) */
    private String insuYearFlag;

    /** 缴费频率(0=趸交, 1=月交, 2=不定期交, 3=季交, 6=半年交, 12=年交) */
    private Integer payIntv;

    /** 缴费期间 */
    private Integer payendYear;

    /** 缴费期间单位(Y=年, M=月, D=日, A=岁) */
    private String payendYearFlag;

    /** 交费终止日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date payEndDate;

    /** 保费交至日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date payToDate;

    /** 责任编码 */
    private String dutyCode;

    /** 责任名称 */
    private String dutyName;

    /** 给付责任编码 */
    private String getDutyCode;

    /** 给付责任名称 */
    private String getDutyName;

    /** 给付责任状态 */
    private String getDutyState;

    /** 保额 */
    private BigDecimal amount;

    /** 累交保费 */
    private BigDecimal sumPayMoney;

    /** 累计加费 */
    private BigDecimal sumAddMoney;

    /** 累计领取金额 */
    private BigDecimal sumGetMoney;

    /** 基础保费 */
    private BigDecimal basePremium;

    /** 加费 */
    private BigDecimal addPremium;

    /** 加费评点 */
    private BigDecimal addScale;

    /** 险种是否已豁免(0=未豁免,1=已豁免) */
    private Integer riskFreeFlag;

    /** 投保人客户号 */
    private String appntNo;

    /** 投保人姓名 */
    private String appntName;

    /** 投保人证件类型 */
    private String appntIdType;

    /** 投保人证件号码 */
    private String appntIdNo;

    /** 投保人性别(0=男,1=女) */
    private Integer appntSex;

    /** 投保人出生日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private java.sql.Date appntBirthday;

    /** 投保人职业等级 */
    private String appntOccType;

    /** 投保人职业代码 */
    private String appntOccCode;

    /** 被保险人数量 */
    private Integer insuredPeoples;
    
    /**险种的主被保险人号*/
    private String mainInsuredNo;
    
    /**被保险人序号*/
    private String insuredSequenceNo;	
    
    /** 被保险人投保年龄 */
    private Integer insuredAppAge;
    
    /** 被保险人客户号 */
    private String insuredNo;

    /** 被保险人姓名 */
    private String insuredName;

    /** 被保险人证件类型 */
    private String insuredIdType;

    /** 被保险人证件号码 */
    private String insuredIdNo;

    /** 被保险人性别(0=男,1=女) */
    private Integer insuredSex;

    /** 被保险人出生日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private java.sql.Date insuredBirthday;

    /** 被保险人职业等级 */
    private String insuredOccType;

    /** 被保险人职业代码 */
    private String insuredOccCode;
    
    /** 被保险人健康状况(4=次标体, 9=标体) */
    private Integer insuredPassFlag;
    
    /** 是否临分(0=否,1=是) */
    private Integer cedeoutType;
    
    /** 核心临分结论 */
    private String coreConclusion;

    /** 再保加费评点 */
    private String reinsuAddScale;
    
    /**分出次数（冗余字段，无实际作用）*/
    private Integer cedeoutCount;

    /** 账户价值 */
    private BigDecimal insuaccValue;

    /** 现金价值 */
    private BigDecimal cashValue;

    /** 是否删除(0=未删除,1=已删除) */
    private Integer isDel;
    
    /** 再保责任编码（表中不存在该字段） */
    private String liabilityCode;

    /** 再保责任名称（表中不存在该字段） */
    private String liabilityName;
}

