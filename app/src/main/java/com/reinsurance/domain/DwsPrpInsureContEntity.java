package com.reinsurance.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 保单登记再保合同信息对象 t_dws_prp_insure_cont
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class DwsPrpInsureContEntity extends PrpBaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 自增主键 */
    @JsonProperty("Id")
    private Long Id;

    /** 流水号 */
    @JsonProperty("TransactionNo")
    private String TransactionNo;

    /** 保险机构代码（唯一固定值：000166） */
    @JsonProperty("CompanyCode")
    private String CompanyCode;

    /** 再保险合同号码 */
    @JsonProperty("ReInsuranceContNo")
    private String ReInsuranceContNo;

    /** 再保险合同名称 */
    @JsonProperty("ReInsuranceContName")
    private String ReInsuranceContName;

    /** 再保险合同简称 */
    @JsonProperty("ReInsuranceContTitle")
    private String ReInsuranceContTitle;

    /** 再保险附约主合同号 */
    @JsonProperty("MainReInsuranceContNo")
    private String MainReInsuranceContNo;

    /** 合同附约类型（1=主合同,2=附约） */
    @JsonProperty("ContOrAmendmentType")
    private String ContOrAmendmentType;

    /** 合同属性（1=保险合同,2=混合合同,3=非保险合同） */
    @JsonProperty("ContAttribute")
    private String ContAttribute;

    /** 合同状态（1=有效,2=终止） */
    @JsonProperty("ContStatus")
    private String ContStatus;

    /** 合同/临分标志（0=否,1=是） */
    @JsonProperty("TreatyOrFacultativeFlag")
    private String TreatyOrFacultativeFlag;

    /** 合同签署日期 */
    @JsonProperty("ContSigndate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date ContSigndate;

    /** 合同生效起期 */
    @JsonProperty("PeriodFrom")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date PeriodFrom;

    /** 合同生效止期 */
    @JsonProperty("PeriodTo")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date PeriodTo;

    /** 合同类型（1=比例合同,2=非比例合同） */
    @JsonProperty("ContType")
    private String ContType;

    /** 再保险公司代码 */
    @JsonProperty("ReinsurerCode")
    private String ReinsurerCode;

    /** 再保险公司名称 */
    @JsonProperty("ReinsurerName")
    private String ReinsurerName;

    /** 佣金核算方式（1=业务年度，2=财务年度） */
    @JsonProperty("ChargeType")
    private String ChargeType;

    /** 所属年份 */
    @JsonProperty("ReportYear")
    private Integer ReportYear;

    /** 所属月份 */
    @JsonProperty("ReportMonth")
    private Integer ReportMonth;

    /** 所属账期 */
    @JsonProperty("AccountPeriod")
    private String AccountPeriod;

    /** 数据来源（0=系统,1=人工） */
    @JsonProperty("DataSource")
    private Integer DataSource;

    /** 推送状态（0=未推送,1=已推送） */
    @JsonProperty("PushStatus")
    private Integer PushStatus;

    /** 推送日期 */
    @JsonProperty("PushDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date PushDate;

    /** 推送人 */
    @JsonProperty("PushBy")
    private String PushBy;

    /** 备注 */
    @JsonProperty("Remark")
    private String Remark;

    /** 是否删除(0=未删除,1=已删除) */
    @JsonProperty("IsDel")
    private Integer IsDel;

}
