package com.reinsurance.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 保单变化轨迹快照表
 * <AUTHOR>
 * @date 2024-12-25
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class DwsContHistoryEntity extends VirtualFieldEntity{

	private static final long serialVersionUID = -5569900087773273184L;

	/** 主键自增 */
    private Long id;

    /** 批次号 */
    private String batchNo;

    /** 批次执行日期 */
    private Date batchDate;
    
    /**唯一键*/
    private String uniqueKey;

    /** 业务类型 (业务类型(0=新单,1=续期,2=保全,3=理赔,4=满期,5=失效) */
    private Integer busiType;
    
    /**业务发生时间*/
    private Date busiOccurTime;
    
    /**业务发生日*/
    private Date busiOccurDate;

    /** 保单类型(1=个险,2=团险) */
    private Integer contType;
    
    /** 团单号 */
    private String grpContNo;

    /** 团单险种号 */
    private String grpPolNo;

    /** 个单合同号码 */
    private String contNo;

    /** 险种号 */
    private String polNo;

    /** 主险保单险种号 */
    private String mainPolNo;

    /** 销售渠道编码 */
    private String saleChnl;

    /** 销售渠道名称 */
    private String saleChnlName;

    /** 销售方式 */
    private String sellType;

    /** 销售方式名称 */
    private String sellTypeName;

    /** 销售机构编码 */
    private String saleComCode;

    /** 销售机构名称 */
    private String saleComName;

    /** 代理机构编码 */
    private String agentComCode;

    /** 代理机构名称 */
    private String agentComName;

    /** 管理机构编码 */
    private String manageComCode;

    /** 管理机构名称 */
    private String manageComName;
    
    /**银行网点名称*/
    private String bankBranchName;

    /** 签单日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date signDate;

    /** 签单时间 */
    private String signTime;

    /** 生效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date riskValiDate;

    /** 保险终止日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date riskEndDate;

    /** 保单创建日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contMakeDate;

    /** 保单创建时间 */
    private String contMakeTime;

    /** 保单年度 */
    private Integer contYear;

    /** 保单周年日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contAnniversary;

    /** 上一保单周年日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date previousContAnniversary;

    /** 保单状态(1=有效,4=失效) */
    private Integer contAppFlag;

    /** 险种编码 */
    private String riskCode;

    /** 险种名称 */
    private String riskName;

    /** 险种类别(1=传统险,2=分红险,3=投连险,4=万能险) */
    private String riskType3;

    /** 保障计划 */
    private String planCode;

    /** 风险类型 */
    private String polRiskType;

    /** 险种状态(1=有效,4=失效) */
    private Integer riskAppFlag;

    /** 主附险标识(M=主险,S=附加险) */
    private String subRiskFlag;

    /** 一年期险种标志(L=长险,M=一年期险,S=极短期险) */
    private String riskPeriod;

    /** 保险期间 */
    private Integer insuYear;

    /** 保险期间单位(Y=年, M=月, D=日, A=岁) */
    private String insuYearFlag;

    /** 缴费频率(0=趸交, 1=月交, 2=不定期交, 3=季交, 6=半年交, 12=年交) */
    private Integer payIntv;

    /** 缴费期间 */
    private Integer payendYear;

    /** 缴费期间单位(Y=年, M=月, D=日, A=岁) */
    private String payendYearFlag;

    /** 交费终止日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date payEndDate;

    /** 保费交至日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date payToDate;

    /** 责任编码 */
    private String dutyCode;

    /** 责任名称 */
    private String dutyName;

    /** 给付责任编码 */
    private String getDutyCode;

    /** 给付责任名称 */
    private String getDutyName;

    /** 给付责任状态 */
    private String getDutyState;

    /** 保额 */
    private BigDecimal amount;

    /** 累交保费 */
    private BigDecimal sumPayMoney;

    /** 累计加费 */
    private BigDecimal sumAddMoney;

    /** 累计领取金额 */
    private BigDecimal sumGetMoney;

    /** 基础保费 */
    private BigDecimal basePremium;

    /** 加费 */
    private BigDecimal addPremium;

    /** 加费评点 */
    private BigDecimal addScale;

    /** 险种是否已豁免(0=未豁免,1=已豁免) */
    private Integer riskFreeFlag;

    /** 投保人客户号 */
    private String appntNo;

    /** 投保人姓名 */
    private String appntName;

    /** 投保人证件类型 */
    private String appntIdType;

    /** 投保人证件号码 */
    private String appntIdNo;

    /** 投保人性别(0=男,1=女) */
    private Integer appntSex;

    /** 投保人出生日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private java.sql.Date appntBirthday;

    /** 投保人职业等级 */
    private String appntOccType;

    /** 投保人职业代码 */
    private String appntOccCode;

    /** 被保险人数量 */
    private Integer insuredPeoples;
    
    /**险种的主被保险人号*/
    private String mainInsuredNo;
    
    /**被保险人序号*/
    private String insuredSequenceNo;	
    
    /** 被保险人投保年龄 */
    private Integer insuredAppAge;
    
    /** 被保险人客户号 */
    private String insuredNo;

    /** 被保险人姓名 */
    private String insuredName;

    /** 被保险人证件类型 */
    private String insuredIdType;

    /** 被保险人证件号码 */
    private String insuredIdNo;

    /** 被保险人性别(0=男,1=女) */
    private Integer insuredSex;

    /** 被保险人出生日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private java.sql.Date insuredBirthday;

    /** 被保险人职业等级 */
    private String insuredOccType;

    /** 被保险人职业代码 */
    private String insuredOccCode;
    
    /** 被保险人健康状况(4=次标体, 9=标体) */
    private Integer insuredPassFlag;
    
    /** 是否临分(0=否,1=是) */
    private Integer cedeoutType;
    
    /** 核心临分结论 */
    private String coreConclusion;

    /** 再保加费评点 */
    private String reinsuAddScale;
    
    /**分出次数（冗余字段，无实际作用）*/
    private Integer cedeoutCount;

    /** 账户价值 */
    private BigDecimal insuaccValue;

    /** 现金价值 */
    private BigDecimal cashValue;

    /** 保全受理号 */
    private String edorAcceptNo;

    /** 保全号 */
    private String edorNo;

    /** 保全项目编码 */
    private String edorType;

    /** 保全状态(0=有效) */
    private Long edorState;

    /** 保全号 */
    private String edorAppType;

    /** 保全创建日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date edorMakeDate;

    /** 保全创建时间 */
    private String edorMakeTime;

    /** 保全补退金额 */
    private BigDecimal edorGetMoney;

    /** 保全补退利息 */
    private BigDecimal edorGetInterest;

    /** 保全申请日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date edorAppDate;

    /** 保全生效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date edorValidate;

    /** 保全确认日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date edorConfDate;

    /** 保全确认时间 */
    private String edorConfTime;

    /** 保全申请人 */
    private String edorAppName;

    /**保全备用字段1，edorType=RB（保全回退）时存储为回退的保全受理号*/
    private String edorStandbyFlag1;

    /**保全备用字段2*/
    private String edorStandbyFlag2;
    
    /**保全备用字段3，edorType=RB（保全回退）时存储为回退的保全项目编码*/
    private String edorStandbyFlag3;
    
    /**保全备用字段4*/
    private String edorStandbyFlag4;
    
    /**保全备用字段5*/
    private String edorStandbyFlag5;

    /** 赔案号 */
    private String clmNo;

    /** 立案号 */
    private String clmRgtNo;

    /** 结案号 */
    private String clmCaseNo;

    /** 案件状态(10=报案,20=立案,30=审核,35=预付,40=审批,50=结案,60=完成,70=关闭) */
    private String clmState;

    /** 理算金额 */
    private BigDecimal clmStandpay;
    
    /** 预付金额 */
    private BigDecimal clmBeforepay;
    
    /** 结算金额 */
    private BigDecimal clmBalancepay;

    /** 赔付金额 */
    private BigDecimal clmRealpay;
    
    /** 理赔结论代码(0=正常给付,1=部分给付,2=拒付,3=公司撤案,4=客户撤案) */
    private String clmGiveType;
    
    /** 理赔结论名称 */
    private String clmGiveTypeDesc;
    
    /** 事故发生日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date clmAccidentDate;
    
    /** 出险日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date clmAccDate;

    /** 报案日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date clmRptDate;

    /** 立案日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date clmRgtDate;

    /** 结案日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date clmCaseEndDate;

    /** 赔款给付日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date clmEnterAccDate;

    /** 调查费 */
    private BigDecimal clmFeeSum;
    
    /** 出险原因代码(1=意外,2=疾病,9=其它) */
    private String clmAccidentReason;
    
    /** 出险结果1 */
    private String clmAccresult1;
    
    /** 出险结果2 */
    private String clmAccresult2;
    
    /** 出险结果1名称 */
    private String clmAccresult1Name;
    
    /** 出险结果2名称 */
    private String clmAccresult2Name;

    /**  */
    private String clmFeeFinaType;
    
    /** 伤残程度编码 */
    private String clmDefoType;
    
    /** 伤残等级编码 */
    private String clmDefoGrade;

    /** 伤残程度 */
    private String clmDefoName;
    
    /** 伤残等级 */
    private String clmDefoGradeName;
    
    /** 结算业务类型 */
    private String clmFeeType;

    /** 结算业务类型名称 */
    private String clmBalTypeDesc;

    /** 结算业务子类型 */
    private String clmSubFeeType;

    /** 结算业务子类型名称 */
    private String clmSubBalTypeDesc;

    /** 医院编码 */
    private String clmHospitalCode;
    
    /** 医院名称 */
    private String clmHospitalName;

    /** 住院日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date clmInHospitalDate;

    /** 出院日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date clmOutHospitalDate;

    /** 理赔创建日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date clmMakeDate;

    /** 理赔创建时间 */
    private String clmMakeTime;

    /**状态类别（Terminate=终止, Available=中止）*/
    private String stateType;
    
    /**状态记录是否有效(0=有效,1=失效)*/
    private String stateState;
    
    /**终止原因（01=满期终止,02=退保终止,03=解约终止,04=理赔终止,05=协退终止,06=犹退终止,07=失效终止,08=其他终止,09=贷款终止,11=合规挂起失效,13=客权中止失效,14=合规解挂）*/
    private String stateReason;
    
    /**状态开始时间*/
    private Date stateStartDate;

    /**状态结束时间*/
    private Date stateEndDate;

    /**状态创建日期*/
    private Date stateMakeDate;

    /**状态创建时间*/
    private String stateMakeTime;
    
    /**关联数据快照状态(0=未处理,1=成功,2=失败)*/
    private Integer subTrackStatus;
    
    /** 处理状态(0=未处理,1=成功,2=失败)<font color='red'>只适用于保全、理赔、失效、满期4种业务，新单/续期不要使用该状态做业务判断</font> */
    private Integer handleStatus;

    /** 处理时间 */
    private Date handleDate;

    /** 错误次数 */
    private Integer handleErrorNum;

    /** 错误原因 */
    private String handleErrorMsg;

    /** 是否删除(0=未删除,1=已删除) */
    private Integer isDel;
    
    public DwsContHistoryEntity() {
    	
    }
    
    public DwsContHistoryEntity(Integer busiType, Integer subTrackStatus, Integer pageSize, Integer startRows) {
    	this.busiType = busiType;
    	this.subTrackStatus = subTrackStatus;
    	this.pageSize = pageSize;
    	this.startRows = startRows;
    }
}

