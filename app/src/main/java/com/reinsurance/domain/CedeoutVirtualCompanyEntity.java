package com.reinsurance.domain;

import java.math.BigDecimal;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import com.jd.lightning.common.core.domain.BaseEntity;

/**
 * 虚拟合同再保公司关系对象 t_cedeout_virtual_company
 * 
 * <AUTHOR>
 * @date 2024-04-10
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper=true)
public class CedeoutVirtualCompanyEntity extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键自增 */
    private Long id;

    /** 批次号 */
    private String batchNo;

    /** 版本号 */
    private Long version;

    /** 虚拟合同Id */
    private Long virtualId;

    /** 虚拟合同编码 */
    private String virtualCode;

    /** 再保公司id */
    private Long companyId;

    /** 再保公司编码 */
    private String companyCode;
    /** 再保公司编码 */
    private String companyName;
    /** 分保比例 */
    private BigDecimal cedeoutScale;

    /** 状态（0=有效,1=无效） */
    private Integer status;

    /** 是否删除（0=未删除,1=已删除） */
    private Integer isDel;
}
