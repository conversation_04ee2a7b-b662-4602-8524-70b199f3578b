package com.reinsurance.domain;

import java.math.BigDecimal;

import com.jd.lightning.common.core.domain.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 险种责任对象 t_risk_liability
 * 
 * <AUTHOR>
 * @date 2024-03-20
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class RiskLiabilityEntity extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键自增 */
    private Long id;

    /** 批次号 */
    private String batchNo;

    /** 版本号 */
    private Long version;

    /** 险种编码 */
    private String riskCode;

    /** 险种名称 */
    private String riskName;

    /** 责任编码 */
    private String liabilityCode;

    /** 责任名称 */
    private String liabilityName;

    /** 险种渠道 */
    private String saleChnl;

    /** 是否分出（0=否,1=是） */
    private Integer isCedeOut;

    /** 是否纳入巨灾（0=否,1=是） */
    private Integer intoCalamity;

    /** 准备金类型（0=无,1=寿险准备金,2=未到期责任准备金,3=长期健康险准备金） */
    private Integer reserveType;

    /** 期限类型（1=短险,2=长险） */
    private Integer periodType;

    /** 产品类型（0=重疾险,1=健康险,2=寿险,3=意外险） */
    private Integer productType;

    /** 豁免类型（0=被保人豁免,1=投保人豁免） */
    private Integer exemptType;

    /** 主附类型（M=主附,S=附加险） */
    private String riskType;

    /** 业务类型（0=巨灾业务,1=财务再业务-DD,2=财务再业务-LTPA,3=财务再业务-YRT,4=保证费率再业务,5=传统再业务） */
    private Integer businessType;

    /** 再保计算率 */
    private Integer rsCalcFrequency;

    /** 税率 */
    private BigDecimal taxRate;

    /** 再保合同类型（0=寿险溢额合同,1=医疗险合同,2=重疾合同） */
    private Integer rsContractType;

    /** 产品长短险标识（0=一年期,1=一年期及以内,2=一年其以上） */
    private Integer periodFlag;

    /** 免赔类型（0=年,1=次） */
    private Integer deductibleType;

    /** 理赔通知限额 */
    private Integer claimNotifyLimit;

    /** 理赔参与限额 */
    private Integer claimInvolvedLimit;

    /** 理赔次数 */
    private Integer claimCount;

    /** 是否主责任 */
    private String mainDuty;
    
    /**政保合作业务标志(0=否, 1=是)*/
    private String insGovFlag;
    
    /**保险产品大类编码-E*/
    private String insProductType;
    
    /**保险产品大类名称-E*/
    private String insProductTypeName;
    
    /**责任分类编码-E*/
    private String insLiabilityType;
    
    /**责任分类名称-E*/
    private String insLiabilityTypeName;

    /**责任分类-B*/
    private String prpLiabilityType;

    /**保险产品大类-B*/
    private String prpProductType;

    /**保险期限-B*/
    private String prpInsuPeriod;

    /** 状态（0=有效,1=无效） */
    private Integer status;

    /** 是否删除（0=未删除,1=已删除） */
    private Integer isDel;

}
