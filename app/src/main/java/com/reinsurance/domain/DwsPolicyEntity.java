package com.reinsurance.domain;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.lightning.common.core.domain.BaseEntity;
import com.jd.lightning.common.utils.DateUtils;
import com.reinsurance.enums.CedeoutEnums;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper=true)
public class DwsPolicyEntity extends BaseEntity {
	
	private static final long serialVersionUID = 8817369744674088873L;
	
	/** 业务类型(0=新单,1=续期,2=保全,3=理赔,4=满期,5=失效) */
    private Integer busiType = CedeoutEnums.业务类型_新单.getValue();
    
    /** 数据类型(0=分出,1=摊回) */
    private Integer dataType = CedeoutEnums.数据类型_分出.getValue();

    /** 保单类型(1=个险,2=团险) */
    private Integer contType;

    /** 提数日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date getDate = DateUtils.getNowDate();

    /** 提数时间 */
    private String getTime = DateUtils.getTime().substring(11);

    /** 所属账期 */
    private Integer accountPeriod = Integer.valueOf(DateUtils.dateTime().substring(0, 6));

    /** 账单日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date accountDate = DateUtils.getNowDate();
    
    /**唯一键*/
    private String uniqueKey;
    
    /** 保单年度 */
    private Integer contYear;

    /** 保单周年日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contAnniversary;
    
    /** 上一保单周年日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date previousContAnniversary;
    
    /**团单号*/
    private String grpContNo;

    /** 团单险种号 */
    private String grpPolNo;
    
    /** 个单合同号码 */
    private String contNo;

    /** 险种号 */
    private String polNo;

    /** 主险保单险种号 */
    private String mainPolNo;

    /** 销售渠道编码 */
    private String saleChnl;
    
    /** 销售渠道名称 */
    private String saleChnlName;

    /** 销售方式编码 */
    private String sellType;
    
    /** 销售方式名称 */
    private String sellTypeName;

    /** 销售机构编码 */
    private String saleComCode;
    
    /** 销售机构名称 */
    private String saleComName;
    
    /** 代理机构编码 */
    private String agentComCode;
    
    /** 代理机构名称 */
    private String agentComName;
    
    /** 管理机构编码 */
    private String manageComCode;
    
    /** 管理机构名称 */
    private String manageComName;

    /** 签单日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date signDate;

    /** 签单时间 */
    private String signTime;
    
    /** 保单状态(1=有效,4=失效) */
    private Integer contAppFlag;

    /** 保单创建日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contMakeDate;

    /** 保单创建时间 */
    private String contMakeTime;

    /** 险种编码 */
    private String riskCode;
    
    /** 险种名称 */
    private String riskName;
    
    /** 险种状态(1=有效,4=失效) */
    private Integer riskAppFlag;
    
    /** 生效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date riskValiDate;

    /** 保险终止日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date riskEndDate;
    
    /** 险种类别(1=传统险,2=分红险,3=投连险,4=万能险)*/
    private String riskType3;
    
    /** 保障计划*/
    private String planCode;
    
    /**风险类型(0=一般体,1=标准体,2=优选体,3=优选+体)*/
    private String polRiskType;
    
    /** 主附险标识(M=主险,S=附加险) */
    private String subRiskFlag;

    /** 一年期险种标志(L=长险,M=一年期险,S=极短期险) */
    private String riskPeriod;

    /** 核心责任编码 */
    private String dutyCode;

    /** 核心责任名称 */
    private String dutyName;
    
    /** 给付责任编码 */
    private String getDutyCode;

    /** 给付责任名称 */
    private String getDutyName;

    /** 给付责任状态 */
    private String getDutyState;
    
    /** 保险期间 */
    private Integer insuYear;

    /** 保险期间单位(Y=年, M=月, D=日, A=岁) */
    private String insuYearFlag;

    /** 缴费频率(0=趸交, 1=月交, 2=不定期交, 3=季交, 6=半年交, 12=年交) */
    private String payIntv;

    /** 缴费期间 */
    private Integer payendYear;

    /** 缴费期间单位(Y=年, M=月, D=日, A=岁) */
    private String payendYearFlag;

    /** 保费交至日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date payToDate;

    /** 交费终止日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date payEndDate;
    
    /** 业务发生日 max(signDate, riskValiDate) */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date busiOccurDate;
    
    /** 保额 */
    private BigDecimal amount;

    /** 总保费 */
    private BigDecimal totalPremium;

    /** 基础保费 */
    private BigDecimal basePremium;

    /** 加费 */
    private BigDecimal addPremium;

    /** 加费评点 */
    private BigDecimal addScale;
    
    /** 累交保费 */
    private BigDecimal sumPayMoney;

    /** 累计加费 */
    private BigDecimal sumAddMoney;

    /** 累计领取金额 */
    private BigDecimal sumGetMoney;
    
    /** 险种是否已豁免(0=未豁免,1=已豁免) */
    private Integer riskFreeFlag;
    
    /** 投保人客户号 */
    private String appntNo;

    /** 投保人姓名 */
    private String appntName;

    /** 投保人证件类型 */
    private String appntIdType;

    /** 投保人证件号码 */
    private String appntIdNo;

    /** 投保人性别(0=男,1=女) */
    private Integer appntSex;

    /** 投保人出生日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private java.sql.Date appntBirthday;

    /** 投保人职业等级 */
    private String appntOccType;

    /** 投保人职业代码 */
    private String appntOccCode;
    
    /** 被保险人数量 */
    private Integer insuredPeoples;
    
    /**被保险人序号*/
    private Integer insuredSequenceNo;
    
    /**险种的主被保险人号*/
    private String mainInsuredNo;

    /** 被保险人客户号 */
    private String insuredNo;

    /** 被保险人姓名 */
    private String insuredName;

    /** 被保险人证件类型 */
    private String insuredIdType;

    /** 被保险人证件号码 */
    private String insuredIdNo;

    /** 被保险人性别(0=男,1=女) */
    private Integer insuredSex;

    /** 被保险人出生日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private java.sql.Date insuredBirthday;

    /** 被保险人职业等级 */
    private String insuredOccType;

    /** 被保险人职业代码 */
    private String insuredOccCode;

    /** 被保人是否有医保(0否,1=是) */
    private Integer insuredSocisec;

    /** 被保险人健康状况(4=次标体, 9=标体) */
    private Integer insuredPassFlag;

    /** 被保险人投保年龄 */
    private Integer insuredAppAge;

    /**再保责任编码*/
    private String liabilityCode;

    /**再保责任名称*/
    private String liabilityName;
    
    /**已分出次数*/
    private Integer cedeoutCount;

    /** 临分标记 0未临分 1临分 */
    private Integer cedeoutType;

    /** 核心临分结论 */
    private String coreConclusion;
    
    /**再保加费评点*/
    private BigDecimal reinsuAddScale;
    
    /**账户价值*/
    private BigDecimal insuaccValue;
    
    /**现金价值*/
    private BigDecimal cashValue;
}
