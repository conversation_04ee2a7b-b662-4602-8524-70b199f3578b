package com.reinsurance.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.jd.lightning.common.core.domain.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 虚拟合同申请对象 t_cedeout_virtual_contract_apply
 * 
 * <AUTHOR>
 * @date 2024-03-19
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class CedeoutVirtualContractApplyEntity extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键自增 */
    private Long id;

    /** 批次号 */
    private String batchNo;

    /** 版本号 */
    private Long version;

    /** 虚拟合同编码 */
    private String virtualCode;

    /** 虚拟合同名称 */
    private String virtualName;

    /** 签订日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date signDate;

    /** 生效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date effectiveDate;

    /** 失效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expiredDate;

    /** 状态（0=有效,1=无效） */
    private Integer status;

    /** 是否删除（0=未删除,1=已删除） */
    private Integer isDel;

}
