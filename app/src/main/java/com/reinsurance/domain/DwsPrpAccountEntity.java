package com.reinsurance.domain;

import java.util.Date;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 保单登记再保账单信息对象 t_dws_prp_account
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class DwsPrpAccountEntity extends PrpBaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 自增主键 */
    @JsonProperty("Id")
    private Long Id;

    /** 流水号 */
    @JsonProperty("TransactionNo")
    private String TransactionNo;

    /** 保险机构代码（唯一固定值：000166） */
    @JsonProperty("CompanyCode")
    private String CompanyCode;

    /** 账单编号 */
    @JsonProperty("AccountID")
    private String AccountID;

    /** 账单起期 */
    @JsonProperty("AccountingPeriodfrom")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date AccountingPeriodfrom;

    /** 账单止期 */
    @JsonProperty("AccountingPeriodto")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date AccountingPeriodto;

    /** 再保险公司代码 */
    @JsonProperty("ReinsurerCode")
    private String ReinsurerCode;

    /** 再保险公司名称 */
    @JsonProperty("ReinsurerName")
    private String ReinsurerName;

    /** 再保险合同号码 */
    @JsonProperty("ReInsuranceContNo")
    private String ReInsuranceContNo;

    /** 再保险合同名称 */
    @JsonProperty("ReInsuranceContName")
    private String ReInsuranceContName;

    /** 分保费 */
    @JsonProperty("ReinsurancePremium")
    private BigDecimal ReinsurancePremium;

    /** 分保佣金率 */
    @JsonProperty("ReinsuranceCommssionRate")
    private BigDecimal ReinsuranceCommssionRate;

    /** 分保佣金 */
    @JsonProperty("ReinsuranceCommssion")
    private BigDecimal ReinsuranceCommssion;

    /** 退回分保费 */
    @JsonProperty("ReturnReinsurancePremium")
    private BigDecimal ReturnReinsurancePremium;

    /** 退回分保佣金 */
    @JsonProperty("ReturnReinsuranceCommssion")
    private BigDecimal ReturnReinsuranceCommssion;

    /** 摊回退保金 */
    @JsonProperty("ReturnSurrenderPay")
    private BigDecimal ReturnSurrenderPay;

    /** 摊回理赔款 */
    @JsonProperty("ReturnClaimPay")
    private BigDecimal ReturnClaimPay;

    /** 摊回满期金 */
    @JsonProperty("ReturnMaturity")
    private BigDecimal ReturnMaturity;

    /** 摊回年金 */
    @JsonProperty("ReturnAnnuity")
    private BigDecimal ReturnAnnuity;

    /** 摊回生存金 */
    @JsonProperty("ReturnLivBene")
    private BigDecimal ReturnLivBene;

    /** 账单状态（1=有效,2=无效） */
    @JsonProperty("AccountStatus")
    private String AccountStatus;

    /** 结算状态（1=未结算,2=已结算） */
    @JsonProperty("PairingStatus")
    private String PairingStatus;

    /** 结算日期 */
    @JsonProperty("PairingDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date PairingDate;

    /** 货币代码 */
    @JsonProperty("Currency")
    private String Currency;

    /** 结算汇率 */
    @JsonProperty("CurrentRate")
    private BigDecimal CurrentRate;

    /** 结算账单号 */
    @JsonProperty("SettleBillNo")
    private String SettleBillNo;

    /** 首续期明细导入状态（0=未导入,1=已导入,2=不需要导入） */
    @JsonProperty("ContImportStatus")
    private Integer ContImportStatus;

    /** 首续期明细导入描述 */
    @JsonProperty("ContImportRemark")
    private String ContImportRemark;

    /** 保全明细导入状态（0=未导入,1=已导入,2=不需要导入） */
    @JsonProperty("EdorImportStatus")
    private Integer EdorImportStatus;

    /** 保全明细导入描述 */
    @JsonProperty("EdorImportRemark")
    private String EdorImportRemark;

    /** 理赔明细导入状态（0=未导入,1=已导入,2=不需要导入） */
    @JsonProperty("ClaimImportStatus")
    private Integer ClaimImportStatus;

    /** 理赔明细导入描述 */
    @JsonProperty("ClaimImportRemark")
    private String ClaimImportRemark;

    /** 生存金明细导入状态（0=未导入,1=已导入,2=不需要导入） */
    @JsonProperty("BenefitImportStatus")
    private Integer BenefitImportStatus;

    /** 生存金明细导入描述 */
    @JsonProperty("BenefitImportRemark")
    private String BenefitImportRemark;

    /** 所属年份 */
    @JsonProperty("ReportYear")
    private Integer ReportYear;

    /** 所属月份 */
    @JsonProperty("ReportMonth")
    private Integer ReportMonth;

    /** 所属账期 */
    @JsonProperty("AccountPeriod")
    private String AccountPeriod;

    /** 数据来源（0=系统,1=人工） */
    @JsonProperty("DataSource")
    private Integer DataSource;

    /** 推送状态（0=未推送,1=已推送） */
    @JsonProperty("PushStatus")
    private Integer PushStatus;

    /** 推送日期 */
    @JsonProperty("PushDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date PushDate;

    /** 推送人 */
    @JsonProperty("PushBy")
    private String PushBy;

    /** 备注 */
    @JsonProperty("Remark")
    private String Remark;

    /** 是否删除(0=未删除,1=已删除) */
    @JsonProperty("IsDel")
    private Integer IsDel;

}
