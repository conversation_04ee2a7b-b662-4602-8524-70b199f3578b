package com.reinsurance.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 保单登记再保产品信息对象 t_dws_prp_product
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class DwsPrpProductEntity extends PrpBaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 自增主键 */
    @JsonProperty("Id")
    private Long Id;

    /** 交易编码 */
    @JsonProperty("TransactionNo")
    private String TransactionNo;

    /** 保险机构代码（唯一固定值：000166） */
    @JsonProperty("CompanyCode")
    private String CompanyCode;

    /** 再保险合同号码 */
    @JsonProperty("ReInsuranceContNo")
    private String ReInsuranceContNo;

    /** 再保险合同名称 */
    @JsonProperty("ReInsuranceContName")
    private String ReInsuranceContName;

    /** 再保险合同简称 */
    @JsonProperty("ReInsuranceContTitle")
    private String ReInsuranceContTitle;

    /** 再保险附约主合同号 */
    @JsonProperty("MainReInsuranceContNo")
    private String MainReInsuranceContNo;

    /** 合同附约类型（1=主合同,2=附约） */
    @JsonProperty("ContOrAmendmentType")
    private String ContOrAmendmentType;

    /** 产品编码 */
    @JsonProperty("ProductCode")
    private String ProductCode;

    /** 产品名称 */
    @JsonProperty("ProductName")
    private String ProductName;

    /** 团个性质（01=个险,02=团险,99=其他） */
    @JsonProperty("GPFlag")
    private String GPFlag;

    /** 险类代码 */
    @JsonProperty("ProductType")
    private String ProductType;

    /** 责任代码 */
    @JsonProperty("LiabilityCode")
    private String LiabilityCode;

    /** 责任名称 */
    @JsonProperty("LiabilityName")
    private String LiabilityName;

    /** 再保险公司代码 */
    @JsonProperty("ReinsurerCode")
    private String ReinsurerCode;

    /** 再保险公司名称 */
    @JsonProperty("ReinsurerName")
    private String ReinsurerName;

    /** 再保人参与份额比例 */
    @JsonProperty("ReinsuranceShare")
    private String ReinsuranceShare;

    /** 分保方式（1=溢额,2=成数,3=成数溢额混合,4=超赔） */
    @JsonProperty("ReinsurMode")
    private String ReinsurMode;

    /** 再保类型（01=事故超赔,02=修正共保方式,03=共保方式,04=风险保费方式,05=赔付率超赔,06=损失终止,07=险位超赔） */
    @JsonProperty("ReInsuranceType")
    private String ReInsuranceType;

    /** 保险期限类型（10=长期险,11=定期(年),12=定期(岁),13=定期(两可),14=终身,20=短期险,21=短期,22=极短期,30=主险缴费期,90=未知） */
    @JsonProperty("TermType")
    private String TermType;

    /** 自留额 */
    @JsonProperty("RetentionAmount")
    private String RetentionAmount;

    /** 自留比例 */
    @JsonProperty("RetentionPercentage")
    private String RetentionPercentage;

    /** 分保比例 */
    @JsonProperty("QuotaSharePercentage")
    private String QuotaSharePercentage;

    /** 所属年份 */
    @JsonProperty("ReportYear")
    private Integer ReportYear;

    /** 所属月份 */
    @JsonProperty("ReportMonth")
    private Integer ReportMonth;

    /** 所属账期 */
    @JsonProperty("AccountPeriod")
    private String AccountPeriod;

    /** 数据来源（0=系统,1=人工） */
    @JsonProperty("DataSource")
    private Integer DataSource;

    /** 推送状态（0=未推送,1=已推送） */
    @JsonProperty("PushStatus")
    private Integer PushStatus;

    /** 推送日期 */
    @JsonProperty("PushDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date PushDate;

    /** 推送人 */
    @JsonProperty("PushBy")
    private String PushBy;

    /** 备注 */
    @JsonProperty("Remark")
    private String Remark;

    /** 是否删除(0=未删除,1=已删除) */
    @JsonProperty("IsDel")
    private Integer IsDel;

}
