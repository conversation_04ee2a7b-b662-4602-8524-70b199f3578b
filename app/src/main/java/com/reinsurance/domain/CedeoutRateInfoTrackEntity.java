package com.reinsurance.domain;

import com.jd.lightning.common.core.domain.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 费率信息轨迹对象 t_cedeout_rate_info_track
 * 
 * <AUTHOR>
 * @date 2024-03-20
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class CedeoutRateInfoTrackEntity extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 轨迹Id */
    private Long trackId;

    /** 业务表Id */
    private Long id;

    /** 批次号 */
    private String batchNo;

    /** 版本号 */
    private Long version;

    /** 费率表编码 */
    private String rateCode;

    /** 费率表名称 */
    private String rateName;

    /** 费率表类型（0=分保费率表,1=分保佣金率表,2=折扣率,3=生命表） */
    private Integer rateType;

    /** 导入状态（0=未导入,1=已导入） */
    private Integer importStatus;

    /** 状态（0=有效,1=无效） */
    private Integer status;

    /** 是否删除（0=未删除,1=已删除） */
    private Integer isDel;

}
