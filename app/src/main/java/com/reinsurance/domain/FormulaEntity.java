package com.reinsurance.domain;

import com.jd.lightning.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 计算公式对象 t_formula
 * 
 * <AUTHOR>
 * @date 2024-03-19
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class FormulaEntity extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键自增 */
    private Long id;

    /** 批次号 */
    private String batchNo;

    /** 版本号 */
    private Long version;

    /** 公式编码 */
    private String formulaCode;

    /** 公式名称 */
    private String formulaName;

    /** 算法大类 */
    private Integer formulaBigType;

    /** 算法类型 */
    private String formulaType;

    /** 分出方式（0=溢额,1=成数,2=混合） */
    private Integer cedeoutWay;

    /** 缴费方式（12=年交） */
    private String payIntv;

    /** 业务类型（0=新单,1=续期,2=保全,3=理赔,4=满期） */
    private Integer busiType;

    /** 计算公式 */
    private String formulaValue;

    /** 公式中文 */
    private String formulaText;

    /** 公式富文本 */
    private String formulaRichText;

    /** 公式描述 */
    private String formulaDesc;

    /** 分出模式 */
    private Integer cedeoutMode;

    /** 再保项目 */
    private Integer reinsuranceProject;

    /** 状态（0=有效,1=无效） */
    private Integer status;

    /** 是否删除（0=未删除,1=已删除） */
    private Integer isDel;

}
