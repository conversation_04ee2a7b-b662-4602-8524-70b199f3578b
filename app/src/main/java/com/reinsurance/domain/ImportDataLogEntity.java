package com.reinsurance.domain;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import com.jd.lightning.common.core.domain.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 导入配置数据操作日志对象 t_import_data_log
 * 
 * <AUTHOR>
 * @date 2024-03-21
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class ImportDataLogEntity extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键自增 */
    private Long id;

    /** 批次号 */
    private String batchNo;

    /** 数据状态（0=待生效,1=已生效,2=已回退） */
    private Integer dataStatus;

    /** 导入人姓名 */
    private String importName;

    /** 生效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date effectiveDate;

    /** 是否删除（0=未删除,1=已删除） */
    private Integer isDel;

}
