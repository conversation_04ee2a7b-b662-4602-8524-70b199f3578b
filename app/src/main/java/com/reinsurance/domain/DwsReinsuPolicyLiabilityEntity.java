package com.reinsurance.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 再保方案保单责任对象对象 t_dws_reinsu_policy_liability
 *
 * <AUTHOR>
 * @date 2024-08-26
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class DwsReinsuPolicyLiabilityEntity extends VirtualFieldEntity
{
	
	private static final long serialVersionUID = -5825078134791374742L;

	/** 自增 */
    private Long id;

    /** 批次号 */
    private String batchNo;
    
    /** 业务类型(0=新单,1=续期)*/
    private Integer busiType;
    
    /**历史表id集合*/
    private String tdchIds;
    
    /**唯一键*/
    private String uniqueKey; 

    /** 再保方案编码 */
    private String programmeCode;

    /** 保单类型(1=个险,2=团险) */
    private Integer contType;

    /** 集体合同号码 */
    private String grpContNo;

    /** 团单险种号 */
    private String grpPolNo;

    /** 个单合同号码 */
    private String contNo;

    /** 险种号 */
    private String polNo;

    /** 主险保单险种号 */
    private String mainPolNo;

    /** 销售渠道编码 */
    private String saleChnl;

    /** 销售渠道名称 */
    private String saleChnlName;

    /** 销售方式 */
    private String sellType;

    /** 销售方式名称 */
    private String sellTypeName;

    /** 销售机构编码 */
    private String saleComCode;

    /** 销售机构名称 */
    private String saleComName;

    /** 代理机构编码 */
    private String agentComCode;

    /** 代理机构名称 */
    private String agentComName;

    /** 管理机构编码 */
    private String manageComCode;

    /** 管理机构名称 */
    private String manageComName;

    /** 银保支行名称 */
    private String bankBranchName;
    
    /**业务发生时间*/
    private Date busiOccurTime;
    
    /** 业务发生日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date busiOccurDate;

    /** 签单日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date signDate;

    /** 签单时间 */
    private String signTime;

    /** 险种生效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date riskValiDate;

    /** 险种终止日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date riskEndDate;

    /** 保单创建日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contMakeDate;

    /** 保单创建时间 */
    private String contMakeTime;

    /** 保单年度 */
    private Integer contYear;
    
    /** 保单月度（年交存0，月缴存具体月份） */
    private Integer contMonth;

    /** 保单周年日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contAnniversary;

    /** 上一保单周年日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date previousContAnniversary;

    /** 保单状态 */
    private Integer contAppFlag;

    /** 险种编码 */
    private String riskCode;

    /** 险种名称 */
    private String riskName;

    /** 险种类别(1=传统险,2=分红险,3=投连险,4=万能险) */
    private String riskType3;

    /** 保障计划 */
    private String planCode;

    /** 风险类型 */
    private String polRiskType;

    /** 险种状态 */
    private Integer riskAppFlag;

    /** 主附险标识 */
    private String subRiskFlag;

    /** 一年期险种标志(L=长险,M=一年期险,S=极短期险) */
    private String riskPeriod;

    /** 保险期间 */
    private Integer insuYear;

    /** 保险期间单位 */
    private String insuYearFlag;

    /** 缴费方式 */
    private Integer payIntv;

    /** 缴费期间 */
    private Integer payendYear;

    /** 缴费期间单位 */
    private String payendYearFlag;

    /** 保费交至日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date payToDate;

    /** 终交日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date payEndDate;

    /** 给付责任编码(多个之间用逗号分隔) */
    private String getDutyCodes;

    /** 给付责任名称(多个之间用逗号分隔) */
    private String getDutyNames;

    /** 再保责任编码 */
    private String liabilityCode;

    /** 再保责任名称 */
    private String liabilityName;

    /** 保额 */
    private BigDecimal amount;

    /** 基础保费 */
    private BigDecimal basePremium;

    /** 加费 */
    private BigDecimal addPremium;

    /** EM加点 */
    private BigDecimal addScale;

    /** 累交保费 */
    private BigDecimal sumPayMoney;

    /** 累计追加保费 */
    private BigDecimal sumAddMoney;

    /** 累计部分领取金额 */
    private BigDecimal sumGetMoney;

    /** 险种是否已豁免 */
    private Integer riskFreeFlag;

    /** 投保人号 */
    private String appntNo;

    /** 投保人姓名 */
    private String appntName;

    /** 投保人证件类型 */
    private String appntIdType;

    /** 投保人证件号码 */
    private String appntIdNo;

    /** 投保人性别 */
    private Integer appntSex;

    /** 投保人出生日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private java.sql.Date appntBirthday;

    /** 投保人职业等级 */
    private String appntOccType;

    /** 投保人职业编码 */
    private String appntOccCode;

    /** 被保险人个数 */
    private Integer insuredPeoples;

    /** 被保险人投保年龄 */
    private Integer insuredAppAge;

    /** 被保险人客户号 */
    private String insuredNo;

    /** 被保险人姓名 */
    private String insuredName;

    /** 被保险人证件类型 */
    private String insuredIdType;

    /** 被保险人证件号码 */
    private String insuredIdNo;

    /** 被保险人性别 */
    private Integer insuredSex;

    /** 被保险人出生日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private java.sql.Date insuredBirthday;

    /** 被保险人职业等级 */
    private String insuredOccType;

    /** 被保险人职业编码 */
    private String insuredOccCode;

    /** 被保险人健康等级(4=次标体,9=标体) */
    private Integer insuredPassFlag;

    /** 现金价值 */
    private BigDecimal cashValue;

    /** 账户价值 */
    private BigDecimal insuaccValue;

    /** 是否临分(0=正常分保,1=临时分保) */
    private Integer cedeoutType;

    /** 核心临分结论 */
    private String coreConclusion;

    /** 再保加费评点 */
    private BigDecimal reinsuAddScale;

    /** 分出次数 */
    private Integer cedeoutCount;

    /** 处理状态(0=未处理,1=成功,2=失败) */
    private Integer handleStatus;

    /** 处理时间 */
    private Date handleDate;

    /** 错误次数 */
    private Integer handleErrorNum;

    /** 错误原因 */
    private String handleErrorMsg;

    /** 状态（0=有效,1=无效） */
    private Integer status;

    /**状态(0=正常,1=回溯数据)*/
    private Integer backTrackData;

    /** 是否删除(0=未删除,1=已删除) */
    private Integer isDel;

}
