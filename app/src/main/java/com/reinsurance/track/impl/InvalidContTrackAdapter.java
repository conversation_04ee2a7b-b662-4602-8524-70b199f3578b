package com.reinsurance.track.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.google.common.collect.ImmutableMap;
import com.jd.lightning.common.core.domain.R;
import com.jd.lightning.common.utils.DateUtils;
import com.reinsurance.constant.RsConstant;
import com.reinsurance.domain.DwsContHistoryEntity;
import com.reinsurance.dto.DwsReinsuTradeDTO;
import com.reinsurance.enums.CedeoutEnums;
import com.reinsurance.enums.BasicDataEnums.DataTrackType;
import com.reinsurance.service.IDwsReinsuTradeService;
import com.reinsurance.track.ContTrackAdapter;
import com.reinsurance.utils.CedeoutUtils;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class InvalidContTrackAdapter extends ContTrackAdapter {
	
	@Autowired
	private IDwsReinsuTradeService dwsReinsuTradeService;
	
	public Integer getBusiType() {
		return CedeoutEnums.业务类型_失效.getValue();
	}

	@Override
	public R<Map<String, Integer>> processor(String batchNo, Map<String, Object> cedeoutConfigMap, DwsContHistoryEntity contHistory, List<DwsContHistoryEntity> edorContReLiabilitys)throws Exception {
		int passCount = 0;
		int failCount = 0;
		try{
			List<DwsReinsuTradeDTO> cedeoutReinsuTradeList = super.getLastCedeoutTradeByParams(Arrays.asList(CedeoutEnums.摊回状态_未摊回.getValue()), contHistory);
			if(CollUtil.isEmpty(cedeoutReinsuTradeList)) {
				log.info("再保回溯方案失效摊回逻辑失败(未匹配到分出记录), busiType:{}, ids:{}", contHistory.getBusiType(), contHistory.getId());
				return R.fail(ImmutableMap.of(RsConstant.passCount, passCount, RsConstant.failCount, failCount), "未匹配到分出记录");
			}
			
			List<DwsReinsuTradeDTO> returnReinsuTradeList = new ArrayList<DwsReinsuTradeDTO>();
			for(DwsReinsuTradeDTO cedeoutReinsuTrade : cedeoutReinsuTradeList) {
				if(CedeoutEnums.计算状态_忽略.getValue() == cedeoutReinsuTrade.getCalcStatus()) {//未达溢额线的分出记录，在原记录上释放风险保额
					if(cedeoutReinsuTrade.getOccupyRiskAmount() != null) {
	        			cedeoutReinsuTrade.setReleaseRiskAmount(cedeoutReinsuTrade.getOccupyRiskAmount().negate());//有可能之前续期已释放，不能重复释放；只有未摊回状态才释放
	            	}
	        		cedeoutReinsuTrade.setReturnDate(DateUtils.getNowDate());
	        		cedeoutReinsuTrade.setReturnStatus(CedeoutEnums.摊回状态_已摊回.getValue());
	        		cedeoutReinsuTrade.setReturnReason("失效摊回(未达溢额线),id:" + contHistory.getId());
	        		continue;
	        	}
				DwsReinsuTradeDTO returnReinsuTrade = new DwsReinsuTradeDTO();
				BeanUtils.copyProperties(contHistory, returnReinsuTrade);
				BeanUtils.copyProperties(cedeoutReinsuTrade, returnReinsuTrade, RsConstant.return_copy_cedeout_ignore_fields);
				
				
				returnReinsuTrade.setBatchNo(batchNo);
				returnReinsuTrade.setSrcOutTradeId(cedeoutReinsuTrade.getId());
				returnReinsuTrade.setReturnCommission(BigDecimal.ZERO);
				returnReinsuTrade.setReturnDate(DateUtils.getNowDate());
				returnReinsuTrade.setAdjustStatus(CedeoutEnums.否.getValue());
				returnReinsuTrade.setDataCopy(CedeoutEnums.数据_原始.getValue());
				returnReinsuTrade.setCalcType(CedeoutEnums.执行方式_自动.getValue());
				returnReinsuTrade.setDataType(CedeoutEnums.数据类型_摊回.getValue());
				returnReinsuTrade.setCalcStatus(CedeoutEnums.计算状态_成功.getValue());
				returnReinsuTrade.setBackTrackData(DataTrackType.回溯数据.getCode());
				returnReinsuTrade.setContAppFlag(CedeoutEnums.保单状态_失效.getValue());
				returnReinsuTrade.setRiskAppFlag(CedeoutEnums.保单状态_失效.getValue());
				returnReinsuTrade.setInvalidStateType(contHistory.getStateType());
				returnReinsuTrade.setInvalidStateReason(contHistory.getStateReason());
				returnReinsuTrade.setInvalidStartDate(contHistory.getStateStartDate());
				if(cedeoutReinsuTrade.getOccupyRiskAmount() != null) {
					returnReinsuTrade.setReleaseRiskAmount(cedeoutReinsuTrade.getOccupyRiskAmount().negate());//释放占用的风险保额
				}
				returnReinsuTrade.setReturnPremium(CedeoutUtils.getReturnPremium(returnReinsuTrade.getBusiOccurDate(), cedeoutReinsuTrade));
				returnReinsuTrade.setReturnCbPremium(CedeoutUtils.getReturnCbPremium(returnReinsuTrade.getBusiOccurDate(), cedeoutReinsuTrade));
				returnReinsuTrade.setReturnTotalPremium(CedeoutUtils.getReturnTotalPremium(returnReinsuTrade));//计算退还再保费
				returnReinsuTrade.setAddedTax(CedeoutUtils.getReturnAddedTax(returnReinsuTrade));//计算退还增值税
				returnReinsuTradeList.add(returnReinsuTrade);
				
				cedeoutReinsuTrade.setReturnDate(DateUtils.getNowDate());
	    		cedeoutReinsuTrade.setReturnStatus(CedeoutEnums.摊回状态_已摊回.getValue());
	    		cedeoutReinsuTrade.setReturnReason("失效摊回,id:" + contHistory.getId());
			}
			int updateRows = 0;
			int insertRows = 0;
			if(CollUtil.isNotEmpty(cedeoutReinsuTradeList)){
				updateRows = dwsReinsuTradeService.updateBatchCedeoutTradeReturnStatus(cedeoutReinsuTradeList);
			}
			if(CollUtil.isNotEmpty(returnReinsuTradeList)){
				insertRows = dwsReinsuTradeService.insertBatchDwsReinsuTradeByPool(returnReinsuTradeList);
			}
			log.info("再保回溯方案失效摊回逻辑一笔完成, updateRows:{}, insertRows:{}, busiType:{}, id:{}", updateRows, insertRows, contHistory.getBusiType(), contHistory.getId());
			if(insertRows > 0) {
				passCount = Math.toIntExact(returnReinsuTradeList.stream().filter(t -> CedeoutEnums.计算状态_成功.getValue() == t.getCalcStatus()).count());
				failCount = insertRows - passCount;
			}
	        return R.ok(ImmutableMap.of(RsConstant.passCount, passCount, RsConstant.failCount, failCount));
		}catch(Exception e) {
			throw e;
		}
	}
	
}
