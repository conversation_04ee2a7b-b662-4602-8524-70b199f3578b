package com.reinsurance.query;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.lightning.common.core.domain.BaseQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 再保分出摊回明细对象 t_dws_reinsu_trade
 *
 * <AUTHOR>
 * @date 2024-04-18
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class DwsReinsuTradeQuery extends BaseQuery {
	
	private static final long serialVersionUID = 4331190293197172357L;    
    
    /** 主键自增 */
    private Long id;

    /** 批次号 */
    private String batchNo;

    /** 业务类型(0=新单,1=续期,2=保全,3=理赔,4=满期,5=保单失效) */
    private Integer busiType;

    /** 数据类型(0=分出,1=摊回) */
    private Integer dataType;

    /** 保单类型(1=个险,2=团险) */
    private Integer contType;
    
    /**数据复制标识（0=源数据,1=复制数据）*/
    private Integer dataCopy;
    
    /**数据组号（同一张保单、同一被险保人、同一再保责任）分出多家再保公司时的组号*/
    private String dataGroupNo;
    
    /**业务发生日*/
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date busiOccurDate;

    /** 提数日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date getDate;

    /** 提数时间 */
    private String getTime;

    /** 所属账期 */
    private Integer accountPeriod;

    /** 账单日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date accountDate;

    /** 集体合同号码 */
    private String grpContNo;

    /** 团单险种号 */
    private String grpPolNo;

    /** 个单合同号码 */
    private String contNo;

    /** 险种号 */
    private String polNo;

    /** 主险保单险种号 */
    private String mainPolNo;

    /** 销售渠道编码 */
    private String saleChnl;
    
    /** 销售渠道名称 */
    private String saleChnlName;

    /** 销售方式编码 */
    private String sellType;
    
    /** 销售方式名称 */
    private String sellTypeName;

    /** 销售机构编码 */
    private String saleComCode;
    
    /** 销售机构名称 */
    private String saleComName;
    
    /** 代理机构编码 */
    private String agentComCode;
    
    /** 代理机构名称 */
    private String agentComName;
    
    /** 管理机构编码 */
    private String manageComCode;
    
    /** 管理机构名称 */
    private String manageComName;
    
    /** 保单创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contMakeDate;
    
    /** 签单日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date signDate;

    /** 签单时间 */
    private String signTime;

    /** 保单年度 */
    private Integer contYear;
    
    /** 分出月份(年交为0，月交为具体月份) */
    private Integer contMonth;

    /** 保单周年日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contAnniversary;
    
    /** 上一保单周年日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date previousContAnniversary;

    /** 保单状态(1=有效,4=失效) */
    private Integer contAppFlag;

    /** 终止类型 */
    private String invalidStateType;
    
    /** 终止原因(01=满期终止,02=退保终止,03=解约终止,04=理赔终止,05=协退终止,06=犹退终止,07=失效终止,08=其他终止,09=贷款终止,11=合规挂起失效,13=客权中止失效,14=合规解挂) */
    private String invalidStateReason;

    /** 终止开始日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date invalidStartDate;

    /** 险种编码 */
    private String riskCode;

    /** 险种名称 */
    private String riskName;

    /** 险种状态(1=有效,4=失效) */
    private Integer riskAppFlag;

    /** 生效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date riskValiDate;

    /** 保险终止日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date riskEndDate;

    /** 主附险标识(M=主险,S=附加险) */
    private String subRiskFlag;

    /** 一年期险种标志(L=长险,M=一年期险,S=极短期险) */
    private String riskPeriod;

    /** 险种类别(1=传统险,2=分红险,3=投连险,4=万能险)*/
    private String riskType3;
    
    /** 保障计划*/
    private String planCode;
    
    /**风险类型(0=一般体,1=标准体,2=优选体,3=优选+体)*/
    private String polRiskType;
    
    /** 给付责任编码(多个之间用逗号分隔) */
    private String getDutyCodes;

    /** 给付责任名称(多个之间用逗号分隔) */
    private String getDutyNames;

    /** 再保责任编码 */
    private String liabilityCode;

    /** 再保责任名称 */
    private String liabilityName;

    /** 保险期间 */
    private Integer insuYear;

    /** 保险期间单位(Y=年, M=月, D=日, A=岁) */
    private String insuYearFlag;

    /** 缴费频率(0=趸交, 1=月交, 2=不定期交, 3=季交, 6=半年交, 12=年交) */
    private Integer payIntv;

    /** 缴费期间 */
    private Integer payendYear;

    /** 缴费期间单位(Y=年, M=月, D=日, A=岁) */
    private String payendYearFlag;

    /** 险种是否已豁免(0=未豁免,1=已豁免) */
    private Integer riskFreeFlag;

    /** 总缴费期数 */
    private Integer payPeriods;

    /** 已缴费期数 */
    private Integer inPayPeriods;

    /** 未缴费期数 */
    private Integer unPayPeriods;
    
    /** 应收未缴保费（未缴期数*单次保费） */
    private BigDecimal unPayPremium;

    /** 保额 */
    private BigDecimal amount;
    
    /**有效保额*/
    private BigDecimal availableAmount;
    
    /** 总保费 */
    private BigDecimal totalPremium;

    /** 基础保费 */
    private BigDecimal basePremium;

    /** 加费 */
    private BigDecimal addPremium;

    /** 加费评点 */
    private BigDecimal addScale;

    /** 保费交至日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date payToDate;

    /** 交费终止日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date payEndDate;

    /** 累交保费 */
    private BigDecimal sumPayMoney;

    /** 累计加费 */
    private BigDecimal sumAddMoney;

    /** 累计领取金额 */
    private BigDecimal sumGetMoney;

    /** 现金价值 */
    private BigDecimal cashValue;

    /** 账户价值 */
    private BigDecimal insuaccValue;

    /** 投保人客户号 */
    private String appntNo;

    /** 投保人姓名 */
    private String appntName;

    /** 投保人证件类型 */
    private String appntIdType;

    /** 投保人证件号码 */
    private String appntIdNo;

    /** 投保人性别(0=男,1=女) */
    private Integer appntSex;

    /** 投保人出生日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private java.sql.Date appntBirthday;

    /** 投保人职业等级 */
    private String appntOccType;

    /** 投保人职业代码 */
    private String appntOccCode;

    /** 被保险人数量 */
    private Integer insuredPeoples;

    /** 被保险人客户号 */
    private String insuredNo;

    /** 被保险人姓名 */
    private String insuredName;

    /** 被保险人证件类型 */
    private String insuredIdType;

    /** 被保险人证件号码 */
    private String insuredIdNo;

    /** 被保险人性别(0=男,1=女) */
    private Integer insuredSex;

    /** 被保险人出生日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private java.sql.Date insuredBirthday;

    /** 被保险人职业等级 */
    private String insuredOccType;

    /** 被保险人职业代码 */
    private String insuredOccCode;

    /** 被保人是否有医保(0否,1=是) */
    private Integer insuredSocisec;

    /** 被保险人健康状况(4=次标体, 9=标体) */
    private Integer insuredPassFlag;

    /** 被保险人投保年龄 */
    private Integer insuredAppAge;

    /** 保全受理号 */
    private String edorAcceptNo;

    /** 保全项目编码 */
    private String edorType;

    /** 保全状态(0=有效) */
    private Integer edorState;

    /** 保全补退金额 */
    private BigDecimal edorGetMoney;

    /** 保全补退利息 */
    private BigDecimal edorGetInterest;

    /** 保全申请日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date edorAppDate;

    /** 保全生效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date edorValidate;

    /** 保全确认日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date edorConfDate;

    /** 保全确认时间 */
    private String edorConfTime;
    
    /** 保全创建时间 */
    private Date edorMakeDate;

    /** 赔案号 */
    private String clmNo;

    /** 案件状态(10=报案,20=立案,30=审核,35=预付,40=审批,50=结案,60=完成,70=关闭) */
    private String clmState;

    /** 理赔结论代码(0=正常给付,1=部分给付,2=拒付,3=公司撤案,4=客户撤案) */
    private String clmGiveType;

    /** 理算金额 */
    private BigDecimal clmStandpay;

    /** 赔付金额 */
    private BigDecimal clmRealpay;
    
    /** 事故发生日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date clmAccidentDate;
    
    /** 出险日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date clmAccDate;

    /** 报案日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date clmRptDate;

    /** 立案日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date clmRgtDate;

    /** 结案日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date clmCaseEndDate;

    /** 赔款给付日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date clmEnterAccDate;

    /** 调查费 */
    private BigDecimal clmFeeSum;

    /** 结算业务类型 */
    private String clmFeeType;

    /** 结算业务类型名称 */
    private String clmBalTypeDesc;

    /** 结算业务子类型 */
    private String clmSubFeeType;

    /** 结算业务子类型名称 */
    private String clmSubBalTypeDesc;

    /** 伤残等级编码 */
    private String clmDefoGrade;

    /** 伤残等级 */
    private String clmDefoGradeName;

    /** 伤残程度编码 */
    private String clmDefoType;

    /** 伤残程度 */
    private String clmDefoName;

    /** 医院名称 */
    private String clmHospitalName;

    /** 住院日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date clmInHospitalDate;

    /** 出院日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date clmOutHospitalDate;

    /** 出险原因代码(1=意外,2=疾病,9=其它) */
    private String clmAccidentReason;
    
    /** 出险结果1 */
    private String clmAccresult1;
    
    /** 出险结果2 */
    private String clmAccresult2;
    
    /** 出险结果1名称 */
    private String clmAccresult1Name;
    
    /** 出险结果2名称 */
    private String clmAccresult2Name;
    
    /** 理赔创建日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date clmMakeDate;

    /** 再保缴费频率(12=年交) */
    private Integer rsPayIntv;
    
    /** 分出计算状态(0=未计算,1=成功,2=失败,3=忽略) */
    private Integer calcStatus;

    /** 分出计算方式(0=自动,1=手动) */
    private Integer calcType;

    /** 计算失败原因(0=缺少险种映射,1=缺少准备金,2=缺少费率,3=缺少合同,4=缺少方案,5=缺少公式) */
    private Integer calcFailCode;

    /** 是否临分(0=否,1=是) */
    private Integer cedeoutType;
    
    /**临分方式(1=自留分保,2=合同分保,3=自定义方案)*/
    private Integer facultativeWay;

    /** 临分通知书 */
    private String reNoticeUrl;

    /** 分出次数 */
    private Integer cedeoutCount;
    
    /** 再保公司编码 */
    private String companyCode;
    
    /** 再保公司名称 */
    private String companyName;
    
    /**再保合约号*/
    private String contractCode;
    
    /**再保主合约号*/
    private String mainContractCode;
    
    /**再保合约名称*/
    private String contractName;
    
    /**再保主合约名称*/
    private String mainContractName;

    /** 方案编码 */
    private String programmeCode;
    
    /** 方案名称 */
    private String programmeName;
    
    /** 自留额（方案配置的） */
    private BigDecimal programmeSelfAmount;
    
    /** 自留比例（方案配置的） */
    private BigDecimal programmeSelfScale;
    
    /** 分出模式（0=净保费,1=毛保费） */
    private Integer cedeoutMode;

    /** 分出方式（0=溢额,1=成数,2=混合） */
    private Integer cedeoutWay;
    
    /** 累计风险保额方式（0=不累计,1=责任层累计,2=险种层累计） */
    private Integer addupAmountType;
    
    /** 风险保额编码 */
    private String addupRiskCode;
    
    /** 风险保额名称 */
    private String addupRiskName;
    
    /**初始化风险保额*/
    private BigDecimal initRiskAmount;

    /** 占用风险保额 */
    private BigDecimal occupyRiskAmount;
    
    /** 释放风险保额 */
    private BigDecimal releaseRiskAmount;

    /** 分出保额 */
    private BigDecimal cedeoutAmount;
    
    /** 自留额 */
    private BigDecimal selfAmount;

    /**接受份额*/
    private BigDecimal acceptCopies;
    
    /** 分保比例 */
    private BigDecimal cedeoutScale;
    
    /** 自留比例 */
    private BigDecimal selfScale;

    /** 基础分保费 */
    private BigDecimal cedeoutPremium;
    
    /** 加费分保费 */
    private BigDecimal cedeoutAddPremium;
    
    /** 分出总保费 */
    private BigDecimal cedeoutTotalPremium;

    /** 分保佣金 */
    private BigDecimal cedeoutCommission;
    
    /** 增值税税率 */
    private BigDecimal taxRate;
    
    /** 增值税 */
    private BigDecimal addedTax;

    /** 分出费率编码 */
    private String rateCode;

    /** 分出费率Id */
    private Long rateDataId;

    /** 分出费率值 */
    private BigDecimal rateDataValue;

    /** 分出费率乘积 = 分出费率值 * 拆扣率值 */
    private BigDecimal cedeoutRateDataValue;

    /** 分保佣金费率编码 */
    private String comRateCode;

    /** 分保佣金费率Id */
    private Long comRateDataId;

    /** 分保佣金费率值 */
    private BigDecimal comRateDataValue;

    /** 折扣费率编码 */
    private String disRateCode;

    /** 折扣率Id */
    private Long disRateDataId;

    /** 拆扣率值 */
    private BigDecimal disRateDataValue;
    
    /** 准备金类型 */
    private Integer reservesType;
    
    /** 准备金因子Id */
    private Long reservesId;

    /** 准备金 */
    private BigDecimal reserves;
    
    /** 摊回状态（0=未摊回,1=已摊回,2=已终止） */
    private Integer returnStatus;
    
    /** 摊回日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date returnDate;

    /**摊回原因（适用于修改分出记录的摊回状态时进行描述）*/
    private String returnReason;
    
    /**原分出记录Id（适用于摊回记录生成的来源于哪条分出）*/
    private Long srcOutTradeId;
    
    /** 退回分保费 */
    private BigDecimal returnPremium;

    /** 退回次标再保费 */
    private BigDecimal returnCbPremium;
    
    /** 退回总再保费 */
    private BigDecimal returnTotalPremium;

    /** 摊回理赔金 */
    private BigDecimal returnClaimAmount;

    /** 摊回满期金 */
    private BigDecimal returnExpiredGold;

    /** 退还佣金 */
    private BigDecimal returnCommission;

    /** 是否调整过(0=否,1=是) */
    private Integer adjustStatus;

    /** 调整时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date adjustDate;

    /** 调整人 */
    private String adjuster;

    /** 调整原因 */
    private String adjustReason;

    /** 调整批次号 */
    private String adjustBatchNo;
    
    /**账单确认状态(0=未确认, 1=已确认)*/
    private Integer billConfirmStatus;
    
    /**账单确认日期*/
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date billConfirmDate;
    
    /**账单确认人*/
    private String billConfirmer;
    
    /**账单号*/
    private String billNo;

    /** 状态(0=有效,1=无效) */
    private Integer status;

    /**状态(0=正常,1=回溯数据)*/
    private Integer backTrackData;

    /** 是否删除(0=未删除,1=已删除) */
    private Integer isDel;
    
    /** 用于保单列表查询（区分正常分保记录/未达溢额线的记录） */
    private Integer insuredCedeoutType;
}
