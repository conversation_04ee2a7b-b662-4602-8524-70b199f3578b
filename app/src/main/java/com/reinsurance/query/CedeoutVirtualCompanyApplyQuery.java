package com.reinsurance.query;

import java.math.BigDecimal;

import com.jd.lightning.common.core.domain.BaseQuery;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 虚拟合同再保公司关系申请对象 t_cedeout_virtual_company_apply
 * 
 * <AUTHOR>
 * @date 2024-03-19
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class CedeoutVirtualCompanyApplyQuery extends BaseQuery
{
    private static final long serialVersionUID = 1L;

    /** 主键自增 */
    private Long id;

    /** 批次号 */
    private String batchNo;

    /** 版本号 */
    private Long version;

    /** 虚拟合同编码 */
    private String virtualCode;

    /** 再保公司编码 */
    private String companyCode;

    /** 分保比例 */
    private BigDecimal cedeoutScale;

    /** 状态（0=有效,1=无效） */
    private Integer status;

    /** 是否删除（0=未删除,1=已删除） */
    private Integer isDel;

}
