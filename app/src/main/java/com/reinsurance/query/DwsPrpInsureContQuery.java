package com.reinsurance.query;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

/**
 * 保单登记再保合同信息查询对象
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class DwsPrpInsureContQuery extends PrpBaseQuery {
    
    private static final long serialVersionUID = 1L;

    /** 自增主键 */
    @JsonProperty("Id")
    private Long Id;

    /** 流水号 */
    @JsonProperty("TransactionNo")
    @Size(max = 64, message = "流水号长度不能超过64个字符")
    private String TransactionNo;

    /** 保险机构代码（唯一固定值：000166） */
    @JsonProperty("CompanyCode")
    @Size(max = 64, message = "保险机构代码长度不能超过64个字符")
    private String CompanyCode;

    /** 再保险合同号码 */
    @JsonProperty("ReInsuranceContNo")
    @Size(max = 64, message = "再保险合同号码长度不能超过64个字符")
    private String ReInsuranceContNo;

    /** 再保险合同名称 */
    @JsonProperty("ReInsuranceContName")
    @Size(max = 256, message = "再保险合同名称长度不能超过256个字符")
    private String ReInsuranceContName;

    /** 再保险合同简称 */
    @JsonProperty("ReInsuranceContTitle")
    @Size(max = 256, message = "再保险合同简称长度不能超过256个字符")
    private String ReInsuranceContTitle;

    /** 再保险附约主合同号 */
    @JsonProperty("MainReInsuranceContNo")
    @Size(max = 64, message = "再保险附约主合同号长度不能超过64个字符")
    private String MainReInsuranceContNo;

    /** 合同附约类型（1=主合同,2=附约） */
    @JsonProperty("ContOrAmendmentType")
    @Size(max = 4, message = "合同附约类型长度不能超过4个字符")
    private String ContOrAmendmentType;

    /** 合同属性（1=保险合同,2=混合合同,3=非保险合同） */
    @JsonProperty("ContAttribute")
    @Size(max = 4, message = "合同属性长度不能超过4个字符")
    private String ContAttribute;

    /** 合同状态（1=有效,2=终止） */
    @JsonProperty("ContStatus")
    @Size(max = 4, message = "合同状态长度不能超过4个字符")
    private String ContStatus;

    /** 合同/临分标志（0=否,1=是） */
    @JsonProperty("TreatyOrFacultativeFlag")
    @Size(max = 4, message = "合同/临分标志长度不能超过4个字符")
    private String TreatyOrFacultativeFlag;

    /** 合同签署日期 */
    @JsonProperty("ContSigndate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date ContSigndate;

    /** 合同生效起期 */
    @JsonProperty("PeriodFrom")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date PeriodFrom;

    /** 合同生效止期 */
    @JsonProperty("PeriodTo")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date PeriodTo;

    /** 合同类型（1=比例合同,2=非比例合同） */
    @JsonProperty("ContType")
    @Size(max = 4, message = "合同类型长度不能超过4个字符")
    private String ContType;

    /** 再保险公司代码 */
    @JsonProperty("ReinsurerCode")
    @Size(max = 64, message = "再保险公司代码长度不能超过64个字符")
    private String ReinsurerCode;

    /** 再保险公司名称 */
    @JsonProperty("ReinsurerName")
    @Size(max = 256, message = "再保险公司名称长度不能超过256个字符")
    private String ReinsurerName;

    /** 佣金核算方式（1=业务年度，2=财务年度） */
    @JsonProperty("ChargeType")
    @Size(max = 4, message = "佣金核算方式长度不能超过4个字符")
    private String ChargeType;

    /** 所属年份 */
    @JsonProperty("ReportYear")
    private Integer ReportYear;

    /** 所属月份 */
    @JsonProperty("ReportMonth")
    @Min(value = 1, message = "所属月份最小值为1")
    @Max(value = 12, message = "所属月份最大值为12")
    private Integer ReportMonth;

    /** 所属账期 */
    @JsonProperty("AccountPeriod")
    @Size(max = 64, message = "所属账期长度不能超过64个字符")
    private String AccountPeriod;

    /** 数据来源（0=系统,1=人工） */
    @JsonProperty("DataSource")
    private Integer DataSource;

    /** 推送状态（0=未推送,1=已推送） */
    @JsonProperty("PushStatus")
    private Integer PushStatus;

    /** 推送日期 */
    @JsonProperty("PushDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date PushDate;

    /** 推送人 */
    @JsonProperty("PushBy")
    @Size(max = 64, message = "推送人长度不能超过64个字符")
    private String PushBy;

    /** 备注 */
    @JsonProperty("Remark")
    @Size(max = 128, message = "备注长度不能超过128个字符")
    private String Remark;

    /** 是否删除(0=未删除,1=已删除) */
    @JsonProperty("IsDel")
    private Integer IsDel;
}
