package com.reinsurance.query;

import java.math.BigDecimal;

import com.jd.lightning.common.core.domain.BaseQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 险种责任对象 t_risk_liability
 * 
 * <AUTHOR>
 * @date 2024-03-20
 */
@Data
@ApiModel("险种责任维护--查询参数")
@EqualsAndHashCode(callSuper=true)
public class RiskLiabilityQuery extends BaseQuery
{
    private static final long serialVersionUID = 1L;

    /** 主键自增 */
    private Long id;

    /** 批次号 */
    private String batchNo;

    /** 版本号 */
    private Long version;

    @ApiModelProperty("险种编码，字典：core_insurance_type")
    private String riskCode;

    @ApiModelProperty("险种名称")
    private String riskName;

    @ApiModelProperty("责任编码")
    private String liabilityCode;

    @ApiModelProperty("责任名称")
    private String liabilityName;

    @ApiModelProperty("险种渠道，字典：risk_liability_insurance_channel")
    private String saleChnl;

    @ApiModelProperty("是否分出，字典：risk_liability_is_cede_out")
    private Integer isCedeOut;

    @ApiModelProperty("是否纳入巨灾，字典：risk_liability_into_calamity")
    private Integer intoCalamity;

    @ApiModelProperty("准备金类型，字典：risk_liability_reserve_type")
    private Integer reserveType;

    @ApiModelProperty("期限类型，字典：risk_liability_period_type")
    private Integer periodType;

    @ApiModelProperty("产品类型，字典：risk_liability_product_type")
    private Integer productType;

    @ApiModelProperty("豁免类型，字典：risk_liability_exempt_type")
    private Integer exemptType;

    @ApiModelProperty("主附类型，字典：risk_liability_risk_type")
    private String riskType;

    @ApiModelProperty("业务类型，字典：risk_liability_business_type")
    private Integer businessType;

    @ApiModelProperty("再保计算频率，字典：risk_liability_rs_calc_frequency")
    private Integer rsCalcFrequency;

    @ApiModelProperty("税率")
    private BigDecimal taxRate;

    @ApiModelProperty("再保合同类型，字典：risk_liability_rs_contract_type")
    private Integer rsContractType;

    @ApiModelProperty("产品长短险标识，字典：risk_liability_period_flag")
    private Integer periodFlag;

    @ApiModelProperty("免赔类型，字典：risk_liability_deductible_type")
    private Integer deductibleType;

    @ApiModelProperty("理赔通知限额")
    private Integer claimNotifyLimit;

    @ApiModelProperty("理赔参与限额")
    private Integer claimInvolvedLimit;

    @ApiModelProperty("理赔次数")
    private Integer claimCount;

    @ApiModelProperty("是否主责任")
    private String mainDuty;

    /**政保合作业务标志(0=否, 1=是)*/
    @ApiModelProperty("政保合作业务标志，字典：regulator_gov_ins_flag")
    private Integer insGovFlag;
    
    /**保险产品大类编码-E*/
    @ApiModelProperty("保险产品大类，字典：regulator_product_type")
    private String insProductType;
    
    /**保险产品大类名称-E*/
    private String insProductTypeName;
    
    /**责任分类编码-E*/
    @ApiModelProperty("责任分类，字典：regulator_liability_type")
    private String insLiabilityType;

    /**责任分类名称-E*/
    private String insLiabilityTypeName;

    /**责任分类-B*/
    private String prpLiabilityType;

    /**保险产品大类-B*/
    private String prpProductType;

    /**保险期限-B*/
    private String prpInsuPeriod;

    @ApiModelProperty("状态，字典：risk_liability_status")
    private Integer status;

    /** 是否删除 */
    private Integer isDel;

}
