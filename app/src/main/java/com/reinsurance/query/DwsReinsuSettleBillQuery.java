package com.reinsurance.query;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import com.jd.lightning.common.core.domain.BaseQuery;

/**
 * 再保结算账单对象
 *
 * <AUTHOR>
 * @date 2025-03-05
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class DwsReinsuSettleBillQuery extends BaseQuery {

	private static final long serialVersionUID = 8102476826782616165L;

	/** 主键自增 */
    private Long id;

    /** 账单号 */
    private String billNo;

    /** 再保公司编码 */
    private String companyCode;

    /** 再保公司名称 */
    private String companyName;

    /** 原始合同编码 */
    private String contractCode;

    /** 原始合同名称 */
    private String contractName;

    /** 业务起始日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date accountStartDate;

    /** 业务截止日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date accountEndDate;

    /** 结算日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date settleDate;
    
    /**实际账单起始日期*/
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date actualStartDate;
    
    /**实际账单截止日期*/
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date actualEndDate;
    
    /** 结算流水号 */
    private String settleTradeNo;
    
    /**资金交易流水号*/
    private String fundTradeNo;
    
    /**记账凭证号*/
    private String accVoucherNo;
    
    /**记账凭证日期*/
    private Date accVoucherDate;

    /** 结算汇率 */
    private BigDecimal exchangeRate;

    /** 贷币代码 */
    private String currency;

    /** 确认状态(0=未确认, 1=已确认) */
    private Integer confirmStatus;

    /** 确认日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date confirmDate;

    /** 确认人 */
    private String confirmer;

    /** 是否删除(0=未删除,1=已删除) */
    private Integer isDel;

}
