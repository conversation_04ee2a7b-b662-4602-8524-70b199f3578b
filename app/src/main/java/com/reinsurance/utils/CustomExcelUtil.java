package com.reinsurance.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.util.ListUtils;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.AbstractColumnWidthStyleStrategy;
import com.jd.lightning.common.annotation.Excel;
import com.jd.lightning.common.annotation.Excels;
import com.jd.lightning.common.core.text.Convert;
import com.jd.lightning.common.exception.ServiceException;
import com.jd.lightning.common.utils.DateUtils;
import com.jd.lightning.common.utils.SecurityUtils;
import com.jd.lightning.common.utils.StringUtils;
import com.jd.lightning.common.utils.poi.ExcelHandlerAdapter;
import com.jd.lightning.common.utils.poi.ExcelUtil;
import com.reinsurance.domain.ImportDataConfigEntity;
import com.reinsurance.domain.ImportDataLogDetailEntity;
import com.reinsurance.domain.ImportDataLogEntity;
import com.reinsurance.enums.BasicDataEnums;
import com.reinsurance.mapper.EntityMapper;
import com.reinsurance.mapper.ImportDataConfigMapper;
import com.reinsurance.mapper.ImportDataLogDetailMapper;
import com.reinsurance.mapper.ImportDataLogMapper;
import com.reinsurance.dto.ExcelConfigDTO;
import com.reinsurance.dto.InsertBatchParamDTO;
import com.reinsurance.query.ImportDataLogDetailQuery;
import com.reinsurance.query.ImportDataLogQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RegExUtils;
import org.apache.poi.ss.usermodel.*;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;

public class CustomExcelUtil {

    /** excel对应实体位置 */
    public static final String EXCEL_DTO_POS_PREFIX = "com.reinsurance.dto.excel.";
    public static final String REPEAT = "repeat";
    /** 隔500条插入一次 */
    public static final int BATCH_COUNT = 500;


    /**
     * excel样式，与系统保持一致
     */
    public static HorizontalCellStyleStrategy excelStyle() {
        //表头样式
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_50_PERCENT.getIndex());
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        headWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headWriteCellStyle.setBorderRight(BorderStyle.THIN);
        headWriteCellStyle.setRightBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        headWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        headWriteCellStyle.setLeftBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        headWriteCellStyle.setBorderTop(BorderStyle.THIN);
        headWriteCellStyle.setTopBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        headWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        headWriteCellStyle.setBottomBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        //字体
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontName("Arial");
        headWriteFont.setFontHeightInPoints((short) 10);
        headWriteFont.setColor(IndexedColors.WHITE.getIndex());
        headWriteCellStyle.setWriteFont(headWriteFont);
        //内容字体
        WriteFont contentWriteFont = new WriteFont();
        contentWriteFont.setFontName("Arial");
        contentWriteFont.setFontHeightInPoints((short) 10);
        //内容样式
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        contentWriteCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        contentWriteCellStyle.setRightBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        contentWriteCellStyle.setLeftBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
        contentWriteCellStyle.setTopBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        contentWriteCellStyle.setBottomBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        return new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
    }

    /**
     * 获取实体的excel配置
     * @param tableName 表名
     * @param entity 实体
     * @param type 类型
     * @return 表头
     */
    public static <T> ExcelConfigDTO getExcelEntityConfig(String tableName, String tableNameCN, T entity, Excel.Type type) {
        List<Field> tempFields = new ArrayList<>();
        tempFields.addAll(Arrays.asList(entity.getClass().getSuperclass().getDeclaredFields()));
        tempFields.addAll(Arrays.asList(entity.getClass().getDeclaredFields()));
        List<List<String>> headList = CollectionUtil.list(false);
        Map<String, ExcelConfigDTO.ExcelHeadConfig> headMapCN = MapUtil.newHashMap();
        for (Field field : tempFields) {
            List<String> names = CollectionUtil.list(false);
            // 单注解
            if (field.isAnnotationPresent(Excel.class)) {
                Excel attr = field.getAnnotation(Excel.class);
                if (attr != null && (attr.type() == Excel.Type.ALL || attr.type() == type)) {
                    field.setAccessible(true);
                    names.add(attr.name());
                    headList.add(names);
                    String columnName = StrUtil.toSymbolCase(field.getName(), '_');
                    ExcelConfigDTO.ExcelHeadConfig excelHeadConfig =
                            new ExcelConfigDTO.ExcelHeadConfig(attr, attr.name(), field.getName(), columnName, field);
                    headMapCN.put(attr.name(), excelHeadConfig);
                }
            }
            // 多注解
            if (field.isAnnotationPresent(Excels.class)) {
                Excels attrs = field.getAnnotation(Excels.class);
                Excel[] excels = attrs.value();
                for (Excel attr : excels) {
                    if (attr != null && (attr.type() == Excel.Type.ALL || attr.type() == type)) {
                        field.setAccessible(true);
                        names.add(attr.name());
                        headList.add(names);
                        String columnName = StrUtil.toSymbolCase(field.getName(), '_');
                        ExcelConfigDTO.ExcelHeadConfig excelHeadConfig =
                                new ExcelConfigDTO.ExcelHeadConfig(attr, attr.name(), field.getName(), columnName, field);
                        headMapCN.put(attr.name(), excelHeadConfig);
                    }
                }
            }
        }
        ExcelConfigDTO excelConfig = new ExcelConfigDTO();
        excelConfig.setTableNameEN(tableName);
        excelConfig.setTableNameCN(tableNameCN);
        excelConfig.setHeadList(headList);
        excelConfig.setHeadMapCN(headMapCN);
        return excelConfig;
    }

    /**
     * 导出时填充导出数据
     */
    public static List<List<Object>> fillExportData(ExcelConfigDTO excelConfig, List<Map<String, Object>> dataList) {
        List<List<Object>> resultDataList = CollectionUtil.list(false);
        Map<String, ExcelConfigDTO.ExcelHeadConfig> headMapCN = excelConfig.getHeadMapCN();
        List<List<String>> headList = excelConfig.getHeadList();
        ExcelUtil<Object> excelUtil = new ExcelUtil<>(Object.class);
        //列数据转换
        for (List<String> head: headList) {
            ExcelConfigDTO.ExcelHeadConfig excelHeadConfig = headMapCN.get(head.get(0));
            Excel excel = excelHeadConfig.getExcel();
            String column = excelHeadConfig.getHeadColumnName();
            String dateFormat = excel.dateFormat();
            String readConverterExp = excel.readConverterExp();
            String separator = excel.separator();
            String dictType = excel.dictType();
            for (Map<String, Object> dataMap : dataList) {
                Object value = dataMap.get(column);
                Object v = null;
                if (StringUtils.isNotEmpty(dateFormat) && StringUtils.isNotNull(value)) {
                    v = excelUtil.parseDateToStr(dateFormat, value);
                } else if (StringUtils.isNotEmpty(readConverterExp) && StringUtils.isNotNull(value)) {
                    v = ExcelUtil.convertByExp(Convert.toStr(value), readConverterExp, separator);
                } else if (StringUtils.isNotEmpty(dictType) && StringUtils.isNotNull(value)) {
                    v = ExcelUtil.convertDictByExp(Convert.toStr(value), dictType, separator);
                } else if (value instanceof BigDecimal && -1 != excel.scale()) {
                    v = ((BigDecimal)value).setScale(excel.scale(), excel.roundingMode()).toString();
                } else if (!excel.handler().equals(ExcelHandlerAdapter.class)) {
                    v = excelUtil.dataFormatHandlerAdapter(value, excel);
                } else {
                    v = convertValue(value, excel);
                }
                dataMap.put(column, v);
            }
        }
        for (Map<String, Object> dataMap : dataList) {
            List<Object> dl = CollectionUtil.list(false);
            for (List<String> head : headList) {
                ExcelConfigDTO.ExcelHeadConfig excelHeadConfig = headMapCN.get(head.get(0));
                dl.add(dataMap.get(excelHeadConfig.getHeadColumnName()));
            }
            resultDataList.add(dl);
        }
        return resultDataList;
    }

    /**
     * 数据格式处理，不处理图片格式
     * @param value 值
     * @param attr 注解
     * @return 值
     */
    public static Object convertValue(Object value, Excel attr) {
        if (Excel.ColumnType.STRING == attr.cellType()) {
            String cellValue = Convert.toStr(value);
            if (StringUtils.startsWithAny(cellValue, ExcelUtil.FORMULA_STR)) {
                cellValue = RegExUtils.replaceFirst(cellValue, "=|-|\\+|@", "\t$0");
            }
            return StringUtils.isNull(cellValue) ? attr.defaultValue() : cellValue + attr.suffix();
        } else if (Excel.ColumnType.NUMERIC == attr.cellType()) {
            if (StringUtils.isNotNull(value)) {
                return StringUtils.contains(Convert.toStr(value), ".") ? Convert.toDouble(value) : (double) Convert.toInt(value);
            }
        }
        return value;
    }

    /**
     * 配置信息导入导出 - 自定义列宽
     */
    public static class CustomDataConfigColumnWidthStrategy extends AbstractColumnWidthStyleStrategy {

        /** 表头 */
        private final Map<String, ExcelConfigDTO.ExcelHeadConfig> headMapCN;

        public CustomDataConfigColumnWidthStrategy(Map<String, ExcelConfigDTO.ExcelHeadConfig> headMapCN) {
            if (ObjectUtil.isNull(headMapCN)) { throw new ServiceException("表头信息处理异常"); }
            this.headMapCN = headMapCN;
        }

        @Override
        protected void setColumnWidth(WriteSheetHolder writeSheetHolder, List<WriteCellData<?>> cellDataList, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
            final int rowIndex = cell.getRowIndex();
            if (rowIndex != 0) { return; }
            List<String> headNameList = head.getHeadNameList();
            if (CollectionUtil.isEmpty(headNameList)) {
                writeSheetHolder.getSheet().setColumnWidth(cell.getColumnIndex(), 16 * 256);
                return;
            }
            ExcelConfigDTO.ExcelHeadConfig excelHeadConfig = headMapCN.get(headNameList.get(0));
            if (ObjectUtil.isNull(excelHeadConfig)) {
                writeSheetHolder.getSheet().setColumnWidth(cell.getColumnIndex(), 16 * 256);
                return;
            }
            double width = excelHeadConfig.getExcel().width();
            writeSheetHolder.getSheet().setColumnWidth(cell.getColumnIndex(), ((int) width) * 256);
        }

    }

    /**
     * 配置信息导入导出 - 自定义数据处理
     */
    @Slf4j
    public static class CustomDataConfigDataListener extends AnalysisEventListener<Map<Integer, String>> {

        private ImportDataConfigMapper importDataConfigMapper;
        private EntityMapper entityMapper;
        private ImportDataLogMapper importDataLogMapper;
        private ImportDataLogDetailMapper importDataLogDetailMapper;
        private String batchNo;
        private Date date = new Date();
        private ExcelUtil<Object> excelUtil = new ExcelUtil<>(Object.class);
        /** 每个表的配置信息，key：表中文名称，value：配置 */
        private Map<String, ExcelConfigDTO> dataConfigMap = MapUtil.newHashMap();
        /** 待插入的数据 */
        private String currentTable = null;
        private List<String> columnNames = CollectionUtil.list(false);
        private List<List<Object>> columnValues = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

        public CustomDataConfigDataListener(ImportDataConfigMapper importDataConfigMapper, EntityMapper entityMapper,
                                            String batchNo, ImportDataLogMapper importDataLogMapper,
                                            ImportDataLogDetailMapper importDataLogDetailMapper) {
            this.importDataConfigMapper = importDataConfigMapper;
            this.entityMapper = entityMapper;
            this.batchNo = batchNo;
            this.importDataLogMapper = importDataLogMapper;
            this.importDataLogDetailMapper = importDataLogDetailMapper;
        }

        @Override
        public void invoke(Map<Integer, String> data, AnalysisContext context) {
            if (CollectionUtil.isEmpty(context.readSheetHolder().getHead())) {
                throw new ServiceException("导入配置信息失败，未获取到表头");
            }
            String sheetName = context.readSheetHolder().getSheetName();
            ExcelConfigDTO excelConfig = getExcelConfigByTableName(sheetName);
            //数据与表是否一致，不一致，清空缓存
            if (ObjectUtil.isNull(currentTable)) {
                currentTable = sheetName;
            }
            Map<String, ExcelConfigDTO.ExcelHeadConfig> headMapCN = excelConfig.getHeadMapCN();
            List<List<String>> currentHeadList = context.readSheetHolder().getHead();
            if (headMapCN.size() != currentHeadList.size()) {
                throw new ServiceException("导入配置信息失败，表头需要与导出时的表头一致");
            }
            List<Object> vals = CollectionUtil.list(false);
            for (int i = 0; i < currentHeadList.size(); i++) {
                ExcelConfigDTO.ExcelHeadConfig excelHeadConfig = headMapCN.get(currentHeadList.get(i).get(0));
                vals.add(dataConversion(data.get(i), excelHeadConfig.getField(), excelHeadConfig.getExcel()));
                //数据库字段名
                if (columnNames.size() <= currentHeadList.size()) {
                    columnNames.add(excelHeadConfig.getHeadColumnName());
                }
            }
            //字段添加批次号和修改人
            if (columnNames.size() <= currentHeadList.size()) {
                columnNames.add("batch_no");
                columnNames.add("update_by");
            }
            //数据添加批次号
            vals.add(batchNo);
            //数据添加修改人
            vals.add(SecurityUtils.getUsername());
            columnValues.add(vals);
            if (columnValues.size() >= BATCH_COUNT) {
                //保存数据
                saveData(excelConfig.getTableNameEN());
                columnValues = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
            }
        }

        /**
         * 每个sheet页解析完毕后调用
         */
        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            String sheetName = context.readSheetHolder().getSheetName();
            ExcelConfigDTO excelConfig = getExcelConfigByTableName(sheetName);
            //还有数据，需要继续插入
            if (CollectionUtil.isNotEmpty(columnValues)) {
                saveData(excelConfig.getTableNameEN());
            }
            saveLog();
            saveDetailLog(excelConfig);
            log.info("导入完成，{}", context.readSheetHolder().getSheetName());
            currentTable = null;
            columnNames = CollectionUtil.list(false);
            columnValues = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
        }

        @Override
        public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
            List<List<String>> headList = CollectionUtil.list(false);
            for (String name : headMap.values()) {
                List<String> head = CollectionUtil.list(false);
                head.add(name);
                headList.add(head);
            }
            context.readSheetHolder().setHead(headList);
        }

        /**
         * 获取Excel注解配置
         * @param tableName 表中文名
         * @return Excel注解配置
         */
        private ExcelConfigDTO getExcelConfigByTableName(String tableName) {
            if (dataConfigMap.containsKey(tableName)) {
                return dataConfigMap.get(tableName);
            }
            ImportDataConfigEntity dataConfigEntity = selectDataConfigByTableName(tableName);
            Object entity = ReflectUtil.newInstance(EXCEL_DTO_POS_PREFIX + dataConfigEntity.getTableEntity());
            ExcelConfigDTO excelConfig = CustomExcelUtil.getExcelEntityConfig(dataConfigEntity.getTableEn(),
                    dataConfigEntity.getTableName(), entity, Excel.Type.IMPORT);
            dataConfigMap.put(tableName, excelConfig);
            return excelConfig;
        }

        /**
         * 查询配置信息数据
         * @param tableName 表中文名
         * @return 配置信息数据
         */
        private ImportDataConfigEntity selectDataConfigByTableName(String tableName) {
            ImportDataConfigEntity dataConfigEntitie = importDataConfigMapper.selectImportDataConfigByTableName(tableName);
            if (ObjectUtil.isNull(dataConfigEntitie)) {
                throw new ServiceException("导入配置信息失败，请检查sheet页名称与配置表名称(中文)是否对应");
            }
            return dataConfigEntitie;
        }

        /**
         * 数据转换
         */
        private Object dataConversion(Object val, Field field, Excel attr) {
            Class<?> fieldType = field.getType();
            if (String.class == fieldType) {
                String s = Convert.toStr(val);
                if (StringUtils.endsWith(s, ".0")) {
                    val = StringUtils.substringBefore(s, ".0");
                } else {
                    String dateFormat = field.getAnnotation(Excel.class).dateFormat();
                    if (StringUtils.isNotEmpty(dateFormat)) {
                        val = excelUtil.parseDateToStr(dateFormat, val);
                    } else {
                        val = Convert.toStr(val);
                    }
                }
            } else if ((Integer.TYPE == fieldType || Integer.class == fieldType) && StringUtils.isNumeric(Convert.toStr(val))) {
                val = Convert.toInt(val);
            } else if ((Long.TYPE == fieldType || Long.class == fieldType) && StringUtils.isNumeric(Convert.toStr(val))) {
                val = Convert.toLong(val);
            } else if (Double.TYPE == fieldType || Double.class == fieldType) {
                val = Convert.toDouble(val);
            } else if (Float.TYPE == fieldType || Float.class == fieldType) {
                val = Convert.toFloat(val);
            } else if (BigDecimal.class == fieldType) {
                val = Convert.toBigDecimal(val);
            } else if (Date.class == fieldType) {
                if (val instanceof String) {
                    val = DateUtils.parseDate(val);
                } else if (val instanceof Double) {
                    val = DateUtil.getJavaDate((Double) val);
                }
            } else if (Boolean.TYPE == fieldType || Boolean.class == fieldType) {
                val = Convert.toBool(val, false);
            }
            if (StringUtils.isNotNull(fieldType)) {
                String propertyName = field.getName();
                if (StringUtils.isNotEmpty(attr.targetAttr())) {
                    propertyName = field.getName() + "." + attr.targetAttr();
                } else if (StringUtils.isNotEmpty(attr.readConverterExp())) {
                    val = ExcelUtil.reverseByExp(Convert.toStr(val), attr.readConverterExp(), attr.separator());
                } else if (StringUtils.isNotEmpty(attr.dictType())) {
                    val = ExcelUtil.reverseDictByExp(Convert.toStr(val), attr.dictType(), attr.separator());
                } else if (!attr.handler().equals(ExcelHandlerAdapter.class)) {
                    val = excelUtil.dataFormatHandlerAdapter(val, attr);
                }
            }
            return val;
        }

        /**
         * 保存数据
         * @param tableNameEN 表名
         */
        private void saveData(String tableNameEN) {
            //保存数据到申请表
            String applyTableName = tableNameEN + "_apply";
            InsertBatchParamDTO insertBatchParams = new InsertBatchParamDTO();
            insertBatchParams.setTableName(applyTableName);
            insertBatchParams.setColumnNames(columnNames);
            insertBatchParams.setColumnValues(columnValues);
            entityMapper.insertBatch(insertBatchParams);
        }

        /**
         * 保存日志
         */
        private void saveLog() {
            String username = SecurityUtils.getUsername();
            ImportDataLogQuery importDataLogQuery = new ImportDataLogQuery();
            importDataLogQuery.setBatchNo(batchNo);
            List<ImportDataLogEntity> importDataLogEntities = importDataLogMapper.selectImportDataLogList(importDataLogQuery);
            if (CollectionUtil.isEmpty(importDataLogEntities)) {
                ImportDataLogEntity importDataLog = new ImportDataLogEntity();
                importDataLog.setBatchNo(batchNo);
                importDataLog.setDataStatus(BasicDataEnums.DataConfigStatus.STATUS_0.getCode());
                importDataLog.setImportName(SecurityUtils.getLoginUser().getUser().getNickName());
                importDataLog.setCreateBy(username);
                importDataLog.setCreateTime(date);
                importDataLog.setUpdateBy(username);
                importDataLog.setUpdateTime(date);
                importDataLogMapper.insertImportDataLog(importDataLog);
            }
        }

        /**
         * 保存日志详细信息
         */
        private void saveDetailLog(ExcelConfigDTO excelConfig) {
            String username = SecurityUtils.getUsername();
            ImportDataLogDetailQuery importDataLogDetailQuery = new ImportDataLogDetailQuery();
            importDataLogDetailQuery.setBatchNo(batchNo);
            importDataLogDetailQuery.setTableEn(excelConfig.getTableNameEN());
            List<ImportDataLogDetailEntity> importDataLogDetailEntities = importDataLogDetailMapper.selectImportDataLogDetailList(importDataLogDetailQuery);
            if (CollectionUtil.isEmpty(importDataLogDetailEntities)) {
                ImportDataLogDetailEntity importDataLogDetail = new ImportDataLogDetailEntity();;
                importDataLogDetail.setBatchNo(batchNo);
                importDataLogDetail.setTableEn(excelConfig.getTableNameEN());
                importDataLogDetail.setTableName(excelConfig.getTableNameCN());
                importDataLogDetail.setDataStatus(BasicDataEnums.DataConfigStatus.STATUS_0.getCode());
                importDataLogDetail.setCreateBy(username);
                importDataLogDetail.setCreateTime(date);
                importDataLogDetail.setUpdateBy(username);
                importDataLogDetail.setUpdateTime(date);
                importDataLogDetailMapper.insertImportDataLogDetail(importDataLogDetail);
            }
        }
    }

}
