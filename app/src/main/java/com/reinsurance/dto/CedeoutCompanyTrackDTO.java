package com.reinsurance.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.lightning.common.annotation.Excel;
import com.jd.lightning.common.core.domain.BaseDTO;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 再保公司轨迹对象 t_cedeout_company_track
 * 
 * <AUTHOR>
 * @date 2024-03-18
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class CedeoutCompanyTrackDTO extends BaseDTO
{
    private static final long serialVersionUID = 1L;

    /** 轨迹Id */
    private Long trackId;

    /** 业务表Id */
    private Long id;

    /** 批次号 */
    @Excel(name = "批次号")
    private String batchNo;

    /** 版本号 */
    @Excel(name = "版本号")
    private Long version;

    /** 公司编码 */
    @Excel(name = "公司编码")
    private String companyCode;

    /** 公司简称 */
    @Excel(name = "公司简称")
    private String companyAbbr;

    /** 公司全称 */
    @Excel(name = "公司全称")
    private String companyName;

    /** 中保信编码 */
    @Excel(name = "中保信编码")
    private String cbiCode;

    /** 财务编码 */
    @Excel(name = "财务编码")
    private String financeCode;

    /** 信用评级 */
    @Excel(name = "信用评级")
    private String ratingValue;

    /** 评级机构 */
    @Excel(name = "评级机构")
    private String ratingOrg;

    /** 评级时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "评级时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date ratingDate;

    /** 是否境外（0=否,1=是） */
    @Excel(name = "是否境外", readConverterExp = "0=否,1=是")
    private Integer overseas;

    /** 起始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "起始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startDate;

    /** 截止时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "截止时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endDate;

    /** 状态（0=有效,1=无效） */
    @Excel(name = "状态", readConverterExp = "0=有效,1=无效")
    private Integer status;

    /** 是否删除（0=未删除,1=已删除） */
    @Excel(name = "是否删除", readConverterExp = "0=未删除,1=已删除")
    private Integer isDel;

}
