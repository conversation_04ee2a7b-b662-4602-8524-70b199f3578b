package com.reinsurance.dto;

import com.jd.lightning.common.annotation.Excel;
import com.jd.lightning.common.core.domain.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 险种责任保险期间关系对象
 *
 * <AUTHOR>
 * @date 2025-03-05
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class RiskLiabilityPeriodDTO extends BaseDTO {
    
	private static final long serialVersionUID = -1779068287486803482L;

	/** 主键自增 */
    private Long id;

    /** 险种编码 */
    @Excel(name = "险种编码")
    private String riskCode;

    /** 险种名称 */
    @Excel(name = "险种名称")
    private String riskName;

    /** 责任编码 */
    @Excel(name = "责任编码")
    private String liabilityCode;

    /** 责任名称 */
    @Excel(name = "责任名称")
    private String liabilityName;

    /** 保险期限类型编码 */
    @Excel(name = "保险期限类型编码")
    private String periodTypeCode;

    /** 保险期限类型名称 */
    @Excel(name = "保险期限类型名称")
    private String periodTypeName;

}
