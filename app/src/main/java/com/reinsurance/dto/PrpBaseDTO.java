package com.reinsurance.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.util.Date;

public class PrpBaseDTO implements Serializable {

    /** 创建人 */
    @JsonProperty("CreateBy")
    private String CreateBy;

    /** 创建时间 */
    @JsonProperty("CreateTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date CreateTime;

    /** 修改人 */
    @JsonProperty("UpdateBy")
    private String UpdateBy;

    /** 修改时间 */
    @JsonProperty("UpdateTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date UpdateTime;

    public String getCreateBy() {
        return CreateBy;
    }

    public void setCreateBy(String createBy) {
        CreateBy = createBy;
    }

    public Date getCreateTime() {
        return CreateTime;
    }

    public void setCreateTime(Date createTime) {
        CreateTime = createTime;
    }

    public String getUpdateBy() {
        return UpdateBy;
    }

    public void setUpdateBy(String updateBy) {
        UpdateBy = updateBy;
    }

    public Date getUpdateTime() {
        return UpdateTime;
    }

    public void setUpdateTime(Date updateTime) {
        UpdateTime = updateTime;
    }
}
