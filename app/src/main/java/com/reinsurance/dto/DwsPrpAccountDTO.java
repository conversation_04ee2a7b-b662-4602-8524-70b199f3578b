package com.reinsurance.dto;

import java.util.Date;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.jd.lightning.common.annotation.Excel;
import com.jd.lightning.common.annotation.Excel.Type;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

/**
 * 保单登记再保账单信息对象
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class DwsPrpAccountDTO extends PrpBaseDTO {
    
    private static final long serialVersionUID = 1L;

    /** 自增主键 */
    @JsonProperty("Id")
    private Long Id;

    /** 流水号 */
    @JsonProperty("TransactionNo")
    @Excel(name = "流水号", type=Type.EXPORT)
    @NotBlank(message = "流水号不能为空")
    @Size(max = 64, message = "流水号长度不能超过64个字符")
    private String TransactionNo;

    /** 保险机构代码（唯一固定值：000166） */
    @JsonProperty("CompanyCode")
    @Excel(name = "保险机构代码")
    @NotBlank(message = "保险机构代码不能为空")
    @Size(max = 64, message = "保险机构代码长度不能超过64个字符")
    private String CompanyCode;

    /** 账单编号 */
    @JsonProperty("AccountID")
    @Excel(name = "账单编号")
    @NotBlank(message = "账单编号不能为空")
    @Size(max = 64, message = "账单编号长度不能超过64个字符")
    private String AccountID;

    /** 账单起期 */
    @JsonProperty("AccountingPeriodfrom")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "账单起期", width = 30, dateFormat = "yyyy-MM-dd")
    @NotNull(message = "账单起期不能为空")
    private Date AccountingPeriodfrom;

    /** 账单止期 */
    @JsonProperty("AccountingPeriodto")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "账单止期", width = 30, dateFormat = "yyyy-MM-dd")
    @NotNull(message = "账单止期不能为空")
    private Date AccountingPeriodto;

    /** 再保险公司代码 */
    @JsonProperty("ReinsurerCode")
    @Excel(name = "再保险公司代码")
    @NotBlank(message = "再保险公司代码不能为空")
    @Size(max = 64, message = "再保险公司代码长度不能超过64个字符")
    private String ReinsurerCode;

    /** 再保险公司名称 */
    @JsonProperty("ReinsurerName")
    @Excel(name = "再保险公司名称")
    @NotBlank(message = "再保险公司名称不能为空")
    @Size(max = 256, message = "再保险公司名称长度不能超过256个字符")
    private String ReinsurerName;

    /** 再保险合同号码 */
    @JsonProperty("ReInsuranceContNo")
    @Excel(name = "再保险合同号码")
    @NotBlank(message = "再保险合同号码不能为空")
    @Size(max = 64, message = "再保险合同号码长度不能超过64个字符")
    private String ReInsuranceContNo;

    /** 再保险合同名称 */
    @JsonProperty("ReInsuranceContName")
    @Excel(name = "再保险合同名称")
    @NotBlank(message = "再保险合同名称不能为空")
    @Size(max = 256, message = "再保险合同名称长度不能超过256个字符")
    private String ReInsuranceContName;

    /** 分保费 */
    @JsonProperty("ReinsurancePremium")
    @Excel(name = "分保费")
    @NotNull(message = "分保费不能为空")
    @DecimalMin(value = "0", message = "分保费不能小于0")
    private BigDecimal ReinsurancePremium;

    /** 分保佣金率 */
    @JsonProperty("ReinsuranceCommssionRate")
    @Excel(name = "分保佣金率")
    @NotNull(message = "分保佣金率不能为空")
    @DecimalMin(value = "0", message = "分保佣金率不能小于0")
    private BigDecimal ReinsuranceCommssionRate;

    /** 分保佣金 */
    @JsonProperty("ReinsuranceCommssion")
    @Excel(name = "分保佣金")
    @NotNull(message = "分保佣金不能为空")
    @DecimalMin(value = "0", message = "分保佣金不能小于0")
    private BigDecimal ReinsuranceCommssion;

    /** 退回分保费 */
    @JsonProperty("ReturnReinsurancePremium")
    @Excel(name = "退回分保费")
    @NotNull(message = "退回分保费不能为空")
    @DecimalMin(value = "0", message = "退回分保费不能小于0")
    private BigDecimal ReturnReinsurancePremium;

    /** 退回分保佣金 */
    @JsonProperty("ReturnReinsuranceCommssion")
    @Excel(name = "退回分保佣金")
    @NotNull(message = "退回分保佣金不能为空")
    @DecimalMin(value = "0", message = "退回分保佣金不能小于0")
    private BigDecimal ReturnReinsuranceCommssion;

    /** 摊回退保金 */
    @JsonProperty("ReturnSurrenderPay")
    @Excel(name = "摊回退保金")
    @NotNull(message = "摊回退保金不能为空")
    @DecimalMin(value = "0", message = "摊回退保金不能小于0")
    private BigDecimal ReturnSurrenderPay;

    /** 摊回理赔款 */
    @JsonProperty("ReturnClaimPay")
    @Excel(name = "摊回理赔款")
    @NotNull(message = "摊回理赔款不能为空")
    @DecimalMin(value = "0", message = "摊回理赔款不能小于0")
    private BigDecimal ReturnClaimPay;

    /** 摊回满期金 */
    @JsonProperty("ReturnMaturity")
    @Excel(name = "摊回满期金")
    @NotNull(message = "摊回满期金不能为空")
    @DecimalMin(value = "0", message = "摊回满期金不能小于0")
    private BigDecimal ReturnMaturity;

    /** 摊回年金 */
    @JsonProperty("ReturnAnnuity")
    @Excel(name = "摊回年金")
    @NotNull(message = "摊回年金不能为空")
    @DecimalMin(value = "0", message = "摊回年金不能小于0")
    private BigDecimal ReturnAnnuity;

    /** 摊回生存金 */
    @JsonProperty("ReturnLivBene")
    @Excel(name = "摊回生存金")
    @NotNull(message = "摊回生存金不能为空")
    @DecimalMin(value = "0", message = "摊回生存金不能小于0")
    private BigDecimal ReturnLivBene;

    /** 账单状态（1=有效,2=无效） */
    @JsonProperty("AccountStatus")
    @Excel(name = "账单状态")
    @NotBlank(message = "账单状态不能为空")
    @Size(max = 4, message = "账单状态长度不能超过4个字符")
    private String AccountStatus;

    /** 结算状态（1=未结算,2=已结算） */
    @JsonProperty("PairingStatus")
    @Excel(name = "结算状态", dictType = "prp_pairing_status")
    @NotBlank(message = "结算状态不能为空")
    @Size(max = 4, message = "结算状态长度不能超过4个字符")
    private String PairingStatus;

    /** 结算日期 */
    @JsonProperty("PairingDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "结算日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date PairingDate;

    /** 货币代码 */
    @JsonProperty("Currency")
    @Excel(name = "货币代码")
    @NotBlank(message = "货币代码不能为空")
    @Size(max = 4, message = "货币代码长度不能超过4个字符")
    private String Currency;

    /** 结算汇率 */
    @JsonProperty("CurrentRate")
    @Excel(name = "结算汇率")
    @DecimalMin(value = "0", message = "结算汇率不能小于0")
    private BigDecimal CurrentRate;

    /** 结算账单号 */
    @JsonProperty("SettleBillNo")
    @Excel(name = "结算账单号")
    @Size(max = 64, message = "结算账单号长度不能超过64个字符")
    private String SettleBillNo;

    /** 首续期明细导入状态（0=未导入,1=已导入,2=不需要导入） */
    @JsonProperty("ContImportStatus")
    @Excel(name = "首续期明细导入状态", type=Type.EXPORT)
    @NotNull(message = "首续期明细导入状态不能为空")
    private Integer ContImportStatus;

    /** 首续期明细导入描述 */
    @JsonProperty("ContImportRemark")
    @Excel(name = "首续期明细导入描述", type=Type.EXPORT)
    @Size(max = 128, message = "首续期明细导入描述长度不能超过128个字符")
    private String ContImportRemark;

    /** 保全明细导入状态（0=未导入,1=已导入,2=不需要导入） */
    @JsonProperty("EdorImportStatus")
    @Excel(name = "保全明细导入状态", type=Type.EXPORT)
    @NotNull(message = "保全明细导入状态不能为空")
    private Integer EdorImportStatus;

    /** 保全明细导入描述 */
    @JsonProperty("EdorImportRemark")
    @Excel(name = "保全明细导入描述", type=Type.EXPORT)
    @Size(max = 128, message = "保全明细导入描述长度不能超过128个字符")
    private String EdorImportRemark;

    /** 理赔明细导入状态（0=未导入,1=已导入,2=不需要导入） */
    @JsonProperty("ClaimImportStatus")
    @Excel(name = "理赔明细导入状态", type=Type.EXPORT)
    @NotNull(message = "理赔明细导入状态不能为空")
    private Integer ClaimImportStatus;

    /** 理赔明细导入描述 */
    @JsonProperty("ClaimImportRemark")
    @Excel(name = "理赔明细导入描述", type=Type.EXPORT)
    @Size(max = 128, message = "理赔明细导入描述长度不能超过128个字符")
    private String ClaimImportRemark;

    /** 生存金明细导入状态（0=未导入,1=已导入,2=不需要导入） */
    @JsonProperty("BenefitImportStatus")
    @Excel(name = "生存金明细导入状态", type=Type.EXPORT)
    @NotNull(message = "生存金明细导入状态不能为空")
    private Integer BenefitImportStatus;

    /** 生存金明细导入描述 */
    @JsonProperty("BenefitImportRemark")
    @Excel(name = "生存金明细导入描述", type=Type.EXPORT)
    @Size(max = 128, message = "生存金明细导入描述长度不能超过128个字符")
    private String BenefitImportRemark;

    /** 所属年份 */
    @JsonProperty("ReportYear")
    @Excel(name = "所属年份")
    @NotNull(message = "所属年份不能为空")
    private Integer ReportYear;

    /** 所属月份 */
    @JsonProperty("ReportMonth")
    @Excel(name = "所属月份")
    @NotNull(message = "所属月份不能为空")
    @Min(value = 1, message = "所属月份最小值为1")
    @Max(value = 12, message = "所属月份最大值为12")
    private Integer ReportMonth;

    /** 所属账期 */
    @JsonProperty("AccountPeriod")
    @Excel(name = "所属账期")
    @NotBlank(message = "所属账期不能为空")
    @Size(max = 64, message = "所属账期长度不能超过64个字符")
    private String AccountPeriod;

    /** 数据来源（0=系统,1=人工） */
    @JsonProperty("DataSource")
    @Excel(name = "数据来源", type=Type.EXPORT, dictType = "regulator_report_data_source")
    @NotNull(message = "数据来源不能为空")
    private Integer DataSource;

    /** 推送状态（0=未推送,1=已推送） */
    @JsonProperty("PushStatus")
    @Excel(name = "推送状态", type=Type.EXPORT, dictType = "regulator_report_push_status")
    @NotNull(message = "推送状态不能为空")
    private Integer PushStatus;

    /** 推送日期 */
    @JsonProperty("PushDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "推送日期", type=Type.EXPORT, width = 30, dateFormat = "yyyy-MM-dd")
    private Date PushDate;

    /** 推送人 */
    @JsonProperty("PushBy")
    @Excel(name = "推送人", type=Type.EXPORT)
    @Size(max = 64, message = "推送人长度不能超过64个字符")
    private String PushBy;

    /** 备注 */
    @JsonProperty("Remark")
    @Excel(name = "备注")
    @Size(max = 128, message = "备注长度不能超过128个字符")
    private String Remark;

    /** 是否删除(0=未删除,1=已删除) */
    @JsonProperty("IsDel")
    private Integer IsDel;
}
