package com.reinsurance.dto;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import com.jd.lightning.common.annotation.Excel;
import com.jd.lightning.common.core.domain.BaseDTO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 再保分出日志对象 t_cedeout_batch_log
 * 
 * <AUTHOR>
 * @date 2024-03-21
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class CedeoutBatchLogDTO extends BaseDTO
{
    private static final long serialVersionUID = 1L;

    /** 主键自增 */
    private Long id;

    /** 批次号 */
    @Excel(name = "批次号")
    private String batchNo;

    /** 执行方式（0=自动,1=手动） */
    @Excel(name = "执行方式", readConverterExp = "0=自动,1=手动")
    private Integer calcType;

    /** 执行参数;手动执行时存储页面条件 */
    @Excel(name = "执行参数;手动执行时存储页面条件")
    private String inputParams;

    /** 成功数 */
    @Excel(name = "成功数")
    private Integer passCount;

    /** 失败数 */
    @Excel(name = "失败数")
    private Integer failCount;

    /** 计算结果 */
    private String calcResult;

    /** 执行人 */
    @Excel(name = "执行人")
    private String executor;

    /** 执行状态（0=未开始,1=执行中,2=执行完成） */
    @Excel(name = "执行状态", readConverterExp = "0=未开始,1=执行中,2=执行完成")
    private Integer progress;

    /** 执行日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "执行日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date executeDate;

    /** 状态（0=有效,1=无效） */
    @Excel(name = "状态", readConverterExp = "0=有效,1=无效")
    private Integer status;

    /** 回溯数据 （0:正常跑批数据，1：回溯产生的数据） */
    @Excel(name = "回溯数据", readConverterExp = "回溯数据 （0:正常跑批数据，1：回溯产生的数据）")
    private Integer isBackTrack;

    /** 是否删除（0=未删除,1=已删除） */
    @Excel(name = "是否删除", readConverterExp = "0=未删除,1=已删除")
    private Integer isDel;
}
