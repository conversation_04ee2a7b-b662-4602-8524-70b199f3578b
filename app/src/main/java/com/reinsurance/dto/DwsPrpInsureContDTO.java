package com.reinsurance.dto;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.jd.lightning.common.annotation.Excel;
import com.jd.lightning.common.annotation.Excel.Type;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

/**
 * 保单登记再保合同信息对象
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class DwsPrpInsureContDTO extends PrpBaseDTO {
    
    private static final long serialVersionUID = 1L;

    /** 自增主键 */
    @JsonProperty("Id")
    private Long Id;

    /** 流水号 */
    @JsonProperty("TransactionNo")
    @Excel(name = "流水号", type=Type.EXPORT)
    @NotBlank(message = "流水号不能为空")
    @Size(max = 64, message = "流水号长度不能超过64个字符")
    private String TransactionNo;

    /** 保险机构代码（唯一固定值：000166） */
    @JsonProperty("CompanyCode")
    @Excel(name = "保险机构代码")
    @NotBlank(message = "保险机构代码不能为空")
    @Size(max = 64, message = "保险机构代码长度不能超过64个字符")
    private String CompanyCode;

    /** 再保险合同号码 */
    @JsonProperty("ReInsuranceContNo")
    @Excel(name = "再保险合同号码")
    @NotBlank(message = "再保险合同号码不能为空")
    @Size(max = 64, message = "再保险合同号码长度不能超过64个字符")
    private String ReInsuranceContNo;

    /** 再保险合同名称 */
    @JsonProperty("ReInsuranceContName")
    @Excel(name = "再保险合同名称")
    @NotBlank(message = "再保险合同名称不能为空")
    @Size(max = 256, message = "再保险合同名称长度不能超过256个字符")
    private String ReInsuranceContName;

    /** 再保险合同简称 */
    @JsonProperty("ReInsuranceContTitle")
    @Excel(name = "再保险合同简称")
    @NotBlank(message = "再保险合同简称不能为空")
    @Size(max = 256, message = "再保险合同简称长度不能超过256个字符")
    private String ReInsuranceContTitle;

    /** 再保险附约主合同号 */
    @JsonProperty("MainReInsuranceContNo")
    @Excel(name = "再保险附约主合同号")
    @NotBlank(message = "再保险附约主合同号不能为空")
    @Size(max = 64, message = "再保险附约主合同号长度不能超过64个字符")
    private String MainReInsuranceContNo;

    /** 合同附约类型（1=主合同,2=附约） */
    @JsonProperty("ContOrAmendmentType")
    @Excel(name = "合同附约类型", dictType = "prp_contract_type")
    @NotBlank(message = "合同附约类型不能为空")
    @Size(max = 4, message = "合同附约类型长度不能超过4个字符")
    private String ContOrAmendmentType;

    /** 合同属性（1=保险合同,2=混合合同,3=非保险合同） */
    @JsonProperty("ContAttribute")
    @Excel(name = "合同属性", dictType = "prp_contract_attr")
    @NotBlank(message = "合同属性不能为空")
    @Size(max = 4, message = "合同属性长度不能超过4个字符")
    private String ContAttribute;

    /** 合同状态（1=有效,2=终止） */
    @JsonProperty("ContStatus")
    @Excel(name = "合同状态", dictType = "prp_cont_pol_duty_status")
    @NotBlank(message = "合同状态不能为空")
    @Size(max = 4, message = "合同状态长度不能超过4个字符")
    private String ContStatus;

    /** 合同/临分标志（0=否,1=是） */
    @JsonProperty("TreatyOrFacultativeFlag")
    @Excel(name = "合同/临分标志")
    @NotBlank(message = "合同/临分标志不能为空")
    @Size(max = 4, message = "合同/临分标志长度不能超过4个字符")
    private String TreatyOrFacultativeFlag;

    /** 合同签署日期 */
    @JsonProperty("ContSigndate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "合同签署日期", width = 30, dateFormat = "yyyy-MM-dd")
    @NotNull(message = "合同签署日期不能为空")
    private Date ContSigndate;

    /** 合同生效起期 */
    @JsonProperty("PeriodFrom")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "合同生效起期", width = 30, dateFormat = "yyyy-MM-dd")
    @NotNull(message = "合同生效起期不能为空")
    private Date PeriodFrom;

    /** 合同生效止期 */
    @JsonProperty("PeriodTo")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "合同生效止期", width = 30, dateFormat = "yyyy-MM-dd")
    @NotNull(message = "合同生效止期不能为空")
    private Date PeriodTo;

    /** 合同类型（1=比例合同,2=非比例合同） */
    @JsonProperty("ContType")
    @Excel(name = "合同类型")
    @NotBlank(message = "合同类型不能为空")
    @Size(max = 4, message = "合同类型长度不能超过4个字符")
    private String ContType;

    /** 再保险公司代码 */
    @JsonProperty("ReinsurerCode")
    @Excel(name = "再保险公司代码")
    @NotBlank(message = "再保险公司代码不能为空")
    @Size(max = 64, message = "再保险公司代码长度不能超过64个字符")
    private String ReinsurerCode;

    /** 再保险公司名称 */
    @JsonProperty("ReinsurerName")
    @Excel(name = "再保险公司名称")
    @NotBlank(message = "再保险公司名称不能为空")
    @Size(max = 256, message = "再保险公司名称长度不能超过256个字符")
    private String ReinsurerName;

    /** 佣金核算方式（1=业务年度，2=财务年度） */
    @JsonProperty("ChargeType")
    @Excel(name = "佣金核算方式")
    @NotBlank(message = "佣金核算方式不能为空")
    @Size(max = 4, message = "佣金核算方式长度不能超过4个字符")
    private String ChargeType;

    /** 所属年份 */
    @JsonProperty("ReportYear")
    @Excel(name = "所属年份")
    @NotNull(message = "所属年份不能为空")
    private Integer ReportYear;

    /** 所属月份 */
    @JsonProperty("ReportMonth")
    @Excel(name = "所属月份")
    @NotNull(message = "所属月份不能为空")
    @Min(value = 1, message = "所属月份最小值为1")
    @Max(value = 12, message = "所属月份最大值为12")
    private Integer ReportMonth;

    /** 所属账期 */
    @JsonProperty("AccountPeriod")
    @Excel(name = "所属账期")
    @NotBlank(message = "所属账期不能为空")
    @Size(max = 64, message = "所属账期长度不能超过64个字符")
    private String AccountPeriod;

    /** 数据来源（0=系统,1=人工） */
    @JsonProperty("DataSource")
    @Excel(name = "数据来源", type=Type.EXPORT, dictType = "regulator_report_data_source")
    @NotNull(message = "数据来源不能为空")
    private Integer DataSource;

    /** 推送状态（0=未推送,1=已推送） */
    @JsonProperty("PushStatus")
    @Excel(name = "推送状态", type=Type.EXPORT, dictType = "regulator_report_push_status")
    @NotNull(message = "推送状态不能为空")
    private Integer PushStatus;

    /** 推送日期 */
    @JsonProperty("PushDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "推送日期", type=Type.EXPORT, width = 30, dateFormat = "yyyy-MM-dd")
    private Date PushDate;

    /** 推送人 */
    @JsonProperty("PushBy")
    @Excel(name = "推送人", type=Type.EXPORT)
    @Size(max = 64, message = "推送人长度不能超过64个字符")
    private String PushBy;

    /** 备注 */
    @JsonProperty("Remark")
    @Excel(name = "备注")
    @Size(max = 128, message = "备注长度不能超过128个字符")
    private String Remark;

    /** 是否删除(0=未删除,1=已删除) */
    @JsonProperty("IsDel")
    private Integer IsDel;
}
