package com.reinsurance.dto;

import com.jd.lightning.common.annotation.Excel;
import com.jd.lightning.common.core.domain.BaseDTO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 再保公司银行信息申请对象 t_cedeout_company_bank_apply
 * 
 * <AUTHOR>
 * @date 2024-03-18
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class CedeoutCompanyBankApplyDTO extends BaseDTO
{
    private static final long serialVersionUID = 1L;

    /** 主键自增 */
    private Long id;

    /** 批次号 */
    @Excel(name = "批次号")
    private String batchNo;

    /** 版本号 */
    @Excel(name = "版本号")
    private Long version;

    /** 公司编码 */
    @Excel(name = "公司编码")
    private String companyCode;

    /** 交易方名称 */
    @Excel(name = "交易方名称")
    private String tradeName;

    /** 账户编码 */
    @Excel(name = "账户编码")
    private String accountCode;

    /** 交易方区域编码 */
    @Excel(name = "交易方区域编码")
    private String tradeRegionCode;

    /** 交易方银行编码 */
    @Excel(name = "交易方银行编码")
    private String tradeBankCode;

    /** 交易方开户银行名称 */
    @Excel(name = "交易方开户银行名称")
    private String tradeBankName;

    /** 联行号编码 */
    @Excel(name = "联行号编码")
    private String interbankCode;

    /** 状态（0=有效,1=无效） */
    @Excel(name = "状态", readConverterExp = "0=有效,1=无效")
    private Integer status;

    /** 是否删除（0=未删除,1=已删除） */
    @Excel(name = "是否删除", readConverterExp = "0=未删除,1=已删除")
    private Integer isDel;

}
