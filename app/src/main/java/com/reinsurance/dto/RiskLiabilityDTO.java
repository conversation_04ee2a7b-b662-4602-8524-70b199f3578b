package com.reinsurance.dto;

import java.math.BigDecimal;
import java.util.List;

import com.jd.lightning.common.annotation.Excel;
import com.jd.lightning.common.core.domain.BaseDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 险种责任对象 t_risk_liability
 * 
 * <AUTHOR>
 * @date 2024-03-20
 */
@Data
@ApiModel("险种责任维护--实体")
@EqualsAndHashCode(callSuper=true)
public class RiskLiabilityDTO extends BaseDTO
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("批次号")
    private String batchNo;

    /**  */
    @ApiModelProperty("版本号")
    private Long version;

    @ApiModelProperty("险种编码")
    @Excel(name = "险种编码")
    private String riskCode;

    @ApiModelProperty("险种名称")
    @Excel(name = "险种名称")
    private String riskName;

    @ApiModelProperty("再保责任编码")
    @Excel(name = "再保责任编码")
    private String liabilityCode;

    @ApiModelProperty("再保责任名称")
    @Excel(name = "再保责任名称")
    private String liabilityName;

    @ApiModelProperty("险种渠道，字典：risk_liability_insurance_channel")
    @Excel(name = "险种渠道", dictType = "risk_liability_insurance_channel")
    private String saleChnl;

    @ApiModelProperty("是否分出，字典：risk_liability_is_cede_out")
    @Excel(name = "是否分出", dictType = "risk_liability_is_cede_out")
    private Integer isCedeOut;

    @ApiModelProperty("是否纳入巨灾，字典：risk_liability_into_calamity")
    @Excel(name = "是否纳入巨灾", dictType = "risk_liability_into_calamity")
    private Integer intoCalamity;

    @ApiModelProperty("准备金类型，字典：risk_liability_reserve_type")
    @Excel(name = "准备金类型", dictType = "risk_liability_reserve_type")
    private Integer reserveType;

    @ApiModelProperty("期限类型，字典：risk_liability_period_type")
    @Excel(name = "期限类型", dictType = "risk_liability_period_type")
    private Integer periodType;

    @ApiModelProperty("产品类型，字典：risk_liability_product_type")
    @Excel(name = "产品类型", dictType = "risk_liability_product_type")
    private Integer productType;

    @ApiModelProperty("豁免类型，字典：risk_liability_exempt_type")
    @Excel(name = "豁免类型", dictType = "risk_liability_exempt_type")
    private Integer exemptType;

    @ApiModelProperty("主附类型，字典：risk_liability_risk_type")
    @Excel(name = "主附类型", dictType = "risk_liability_risk_type")
    private String riskType;

    @ApiModelProperty("业务类型，字典：risk_liability_business_type")
    @Excel(name = "业务类型", dictType = "risk_liability_business_type")
    private Integer businessType;

    @ApiModelProperty("再保计算频率，字典：risk_liability_rs_calc_frequency")
    @Excel(name = "再保计算频率", dictType = "risk_liability_rs_calc_frequency")
    private Integer rsCalcFrequency;

    @ApiModelProperty("税率")
    @Excel(name = "税率")
    private BigDecimal taxRate;

    @ApiModelProperty("再保合同类型，字典：risk_liability_rs_contract_type")
    @Excel(name = "再保合同类型", dictType = "risk_liability_rs_contract_type")
    private Integer rsContractType;

    @ApiModelProperty("免赔类型，字典：risk_liability_deductible_type")
    @Excel(name = "免赔类型", dictType = "risk_liability_deductible_type")
    private Integer deductibleType;

    @ApiModelProperty("产品长短险标识，字典：risk_liability_period_flag")
    @Excel(name = "产品长短险标识", dictType = "risk_liability_period_flag")
    private Integer periodFlag;

    @ApiModelProperty("理赔通知限额")
    @Excel(name = "理赔通知限额")
    private Integer claimNotifyLimit;

    @ApiModelProperty("理赔参与限额")
    @Excel(name = "理赔参与限额")
    private Integer claimInvolvedLimit;

    @ApiModelProperty("理赔次数")
    @Excel(name = "理赔次数")
    private Integer claimCount;

    @ApiModelProperty("状态，字典：risk_liability_status")
    @Excel(name = "状态", dictType = "risk_liability_status")
    private Integer status;

    @ApiModelProperty("是否主责任，字典：sys_yes_no")
    @Excel(name = "是否主责任", dictType = "sys_yes_no")
    private String mainDuty;

    /**政保合作业务标志(0=否, 1=是)*/
    @ApiModelProperty("政保合作业务标志，字典：regulator_ins_gov_flag")
    @Excel(name = "政保合作业务标志", dictType = "regulator_ins_gov_flag")
    private String insGovFlag;
    
    /**保险产品大类-E*/
    @ApiModelProperty("保险产品大类-E，字典：regulator_product_type")
    @Excel(name = "保险产品大类-E", dictType = "regulator_product_type")
    private String insProductType;
    
    /**保险产品大类名称-E*/
    private String insProductTypeName;
    
    /**责任分类-E*/
    @ApiModelProperty("责任分类-E，字典：regulator_liability_type")
    @Excel(name = "责任分类-E", dictType = "regulator_liability_type")
    private String insLiabilityType;
    
    /**责任分类名称-E*/
    private String insLiabilityTypeName;

    /**责任分类-B*/
    @ApiModelProperty("责任分类-B，字典：prp_liability_type")
    @Excel(name = "责任分类-B", dictType = "prp_liability_type")
    private String prpLiabilityType;

    /**保险产品大类-B*/
    @ApiModelProperty("保险产品大类-B，字典：regulator_product_type")
    @Excel(name = "保险产品大类-B", dictType = "regulator_product_type")
    private String prpProductType;

    /**保险期限-B*/
    @ApiModelProperty("保险期限类型-B，字典：prp_period_type")
    @Excel(name = "保险期限类型-B", dictType = "prp_period_type")
    private String prpInsuPeriod;

    /** 是否删除 */
    private Integer isDel;
    
    /**险种保险期限类型-E关系集合*/
    private List<RiskLiabilityPeriodDTO> periodTypeList;

}
