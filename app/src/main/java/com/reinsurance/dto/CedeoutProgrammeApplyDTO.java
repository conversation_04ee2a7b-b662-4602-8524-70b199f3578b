package com.reinsurance.dto;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.lightning.common.annotation.Excel;
import com.jd.lightning.common.core.domain.BaseDTO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 再保分出方申请表对象 t_cedeout_programme_apply
 * 
 * <AUTHOR>
 * @date 2024-03-19
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class CedeoutProgrammeApplyDTO extends BaseDTO
{
    private static final long serialVersionUID = 1L;

    /** 主键自增 */
    private Long id;

    /** 批次号 */
    @Excel(name = "批次号")
    private String batchNo;

    /** 版本号 */
    @Excel(name = "版本号")
    private Long version;

    /** 虚拟合同编码 */
    @Excel(name = "虚拟合同编码")
    private String virtualCode;

    /** 方案编码 */
    @Excel(name = "方案编码")
    private String programmeCode;

    /** 方案名称 */
    @Excel(name = "方案名称")
    private String programmeName;

    /** 分保方案类型（0=正常分保,1=临时分保,2=二级再保） */
    @Excel(name = "分保方案类型", readConverterExp = "0=正常分保,1=临时分保,2=二级再保")
    private Integer cedeoutType;

    /** 累计风险编码 */
    @Excel(name = "累计风险编码")
    private String addupRiskCode;

    /** 累计风险名称 */
    @Excel(name = "累计风险名称")
    private String addupRiskName;

    /** 分出模式（0=净保费,1=毛保费） */
    @Excel(name = "分出模式", readConverterExp = "0=净保费,1=毛保费")
    private Integer cedeoutMode;

    /** 分出方式（0=溢额,1=成数,2=混合） */
    @Excel(name = "分出方式", readConverterExp = "0=溢额,1=成数,2=混合")
    private Integer cedeoutWay;

    /** 分保公司数 */
    @Excel(name = "分保公司数")
    private Integer cedeoutCompanyNum;

    /** 溢额层数 */
    @Excel(name = "溢额层数")
    private Integer excessLevel;

    /** 累计风险保额方式（0=不累计,1=责任层累计,2=险种层累计） */
    @Excel(name = "累计风险保额方式", readConverterExp = "0=不累计,1=责任层累计,2=险种层累计")
    private Integer addupAmountType;

    /** 缴费频率（12=年交） */
    @Excel(name = "缴费频率", readConverterExp = "1=2=年交")
    private Integer payIntv;

    /** 第一层级溢额类型（0=自留额,1=层次线,2=临时限额,3=最低分出额,4=最大限额） */
    @Excel(name = "第一层级溢额类型", readConverterExp = "0=自留额,1=层次线,2=临时限额,3=最低分出额,4=最大限额")
    private Integer firstExcessType;

    /** 第一层级溢额值 */
    @Excel(name = "第一层级溢额值")
    private BigDecimal firstExcessValue;

    /** 第二层级溢额类型（0=自留额,1=层次线,2=临时限额,3=最低分出额,4=最大限额） */
    @Excel(name = "第二层级溢额类型", readConverterExp = "0=自留额,1=层次线,2=临时限额,3=最低分出额,4=最大限额")
    private Integer secondExcessType;

    /** 第二层级溢额值 */
    @Excel(name = "第二层级溢额值")
    private BigDecimal secondExcessValue;

    /** 自留比例 */
    @Excel(name = "自留比例")
    private BigDecimal selfScale;
    
    /** 方案回溯标识(0=常规方案,1=回溯方案) */
    @Excel(name = "方案回溯标识", readConverterExp = "0=常规方案,1=回溯方案")
    private Integer backTrackStatus;

    /** 状态（0=有效,1=无效） */
    @Excel(name = "状态", readConverterExp = "0=有效,1=无效")
    private Integer status;

    /** 是否删除（0=未删除,1=已删除） */
    @Excel(name = "是否删除", readConverterExp = "0=未删除,1=已删除")
    private Integer isDel;

    /** 业务发生开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "业务发生开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startBusiOccurDate;

    /** 业务发生结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "业务发生结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endBusiOccurDate;
}
