package com.reinsurance.dto;

import java.math.BigDecimal;

import com.jd.lightning.common.annotation.Excel;
import com.jd.lightning.common.core.domain.BaseDTO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 再保分出方案费率申请对象 t_cedeout_programme_rate_apply
 * 
 * <AUTHOR>
 * @date 2024-03-19
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class CedeoutProgrammeRateApplyDTO extends BaseDTO
{
    private static final long serialVersionUID = 1L;

    /** 主键自增 */
    private Long id;

    /** 批次号 */
    @Excel(name = "批次号")
    private String batchNo;

    /** 版本号 */
    @Excel(name = "版本号")
    private Long version;

    /** 再保公司编码 */
    @Excel(name = "再保公司编码")
    private String companyCode;

    /** 再保公司名称 */
    @Excel(name = "再保公司名称")
    private String companyName;

    /** 虚拟合同编码 */
    @Excel(name = "虚拟合同编码")
    private String virtualCode;

    /** 方案编码 */
    @Excel(name = "方案编码")
    private String programmeCode;

    /** 分出保额下限 */
    @Excel(name = "分出保额下限")
    private BigDecimal minCedeoutAmount;

    /** 分出保额上限 */
    @Excel(name = "分出保额上限")
    private BigDecimal maxCedeoutAmount;

    /** 分出费率编码 */
    @Excel(name = "分出费率编码")
    private String cedeoutRateCode;

    /** 佣金费率编码 */
    @Excel(name = "佣金费率编码")
    private String commissionRateCode;

    /** 折扣费率编码 */
    @Excel(name = "折扣费率编码")
    private String disRateCode;

    /** 状态（0=有效,1=无效） */
    @Excel(name = "状态", readConverterExp = "0=有效,1=无效")
    private Integer status;

    /** 是否删除（0=未删除,1=已删除） */
    @Excel(name = "是否删除", readConverterExp = "0=未删除,1=已删除")
    private Integer isDel;

}
