package com.reinsurance.dto;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.jd.lightning.common.annotation.Excel;
import com.jd.lightning.common.core.domain.BaseDTO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 再保合同轨迹对象 t_cedeout_contract_track
 * 
 * <AUTHOR>
 * @date 2024-03-18
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class CedeoutContractTrackDTO extends BaseDTO
{
    private static final long serialVersionUID = 1L;

    /** 轨迹Id */
    private Long trackId;

    /** 业务表Id */
    private Long id;

    /** 批次号 */
    @Excel(name = "批次号")
    private String batchNo;

    /** 版本号 */
    @Excel(name = "版本号")
    private Long version;

    /** 再保公司编码 */
    @Excel(name = "再保公司编码")
    private String companyCode;

    /** 合同编码 */
    @Excel(name = "合同编码")
    private String contractCode;

    /** 合同号 */
    @Excel(name = "合同号")
    private String contractNo;

    /** 合同名称 */
    @Excel(name = "合同名称")
    private String contractName;

    /** 合同简称 */
    @Excel(name = "合同简称")
    private String contractAbbr;

    /** 签订日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "签订日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date signDate;

    /** 生效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "生效日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date effectiveDate;

    /** 失效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "失效日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date expiredDate;

    /** 合同类型（0=主合同,1=补充协议） */
    @Excel(name = "合同类型", readConverterExp = "0=主合同,1=补充协议")
    private Integer contractType;

    /**是否临分(0=否,1=是)*/
    @Excel(name = "是否临分", readConverterExp = "0=否,1=是")
    private Integer cedeoutType;

    /**再保合同类型(01=事故超赔,02=修正共保方式,03=共保方式,04=风险保费方式,05=赔付率超赔,06=损失终止,07=险位超赔)*/
    @Excel(name = "再保合同类型", dictType = "prp_reinsu_class")
    private String contractClass;

    /**合同属性(1=保险合同,2=混合合同,3=非保险合同)*/
    @Excel(name = "合同属性", dictType = " prp_contract_attr")
    private String contractAttr;

    /** 主合同Id */
    private Long mainContractId;
    
    /** 主合同编码 */
    @Excel(name = "主合同编码")
    private String mainContractCode;
    
    /** 电子版合同地址 */
    @Excel(name = "电子版合同地址")
    private String filePath;

    /** 状态（0=有效,1=无效） */
    @Excel(name = "状态", readConverterExp = "0=有效,1=无效")
    private Integer status;

    /** 是否删除（0=未删除,1=已删除） */
    @Excel(name = "是否删除", readConverterExp = "0=未删除,1=已删除")
    private Integer isDel;

}
