package com.reinsurance.dto;

import java.math.BigDecimal;

import com.jd.lightning.common.annotation.Excel;
import com.jd.lightning.common.core.domain.BaseDTO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 险种责任申请对象 t_risk_liability_apply
 * 
 * <AUTHOR>
 * @date 2024-03-20
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class RiskLiabilityApplyDTO extends BaseDTO
{
    private static final long serialVersionUID = 1L;

    /** 主键自增 */
    private Long id;

    /** 批次号 */
    @Excel(name = "批次号")
    private String batchNo;

    /** 版本号 */
    @Excel(name = "版本号")
    private Long version;

    /** 险种编码 */
    @Excel(name = "险种编码")
    private String riskCode;

    /** 险种名称 */
    @Excel(name = "险种名称")
    private String riskName;

    /** 责任编码 */
    @Excel(name = "责任编码")
    private String liabilityCode;

    /** 责任名称 */
    @Excel(name = "责任名称")
    private String liabilityName;

    /** 险种渠道 */
    @Excel(name = "险种渠道")
    private String saleChnl;

    /** 是否分出（0=否,1=是） */
    @Excel(name = "是否分出", readConverterExp = "0=否,1=是")
    private Integer isCedeOut;

    /** 是否纳入巨灾（0=否,1=是） */
    @Excel(name = "是否纳入巨灾", readConverterExp = "0=否,1=是")
    private Integer intoCalamity;

    /** 准备金类型（0=无,1=寿险准备金,2=未到期责任准备金,3=长期健康险准备金） */
    @Excel(name = "准备金类型", readConverterExp = "0=无,1=寿险准备金,2=未到期责任准备金,3=长期健康险准备金")
    private Integer reserveType;

    /** 期限类型（1=短险,2=长险） */
    @Excel(name = "期限类型", readConverterExp = "1=短险,2=长险")
    private Integer periodType;

    /** 产品类型（0=重疾险,1=健康险,2=寿险,3=意外险） */
    @Excel(name = "产品类型", readConverterExp = "0=重疾险,1=健康险,2=寿险,3=意外险")
    private Integer productType;

    /** 豁免类型（0=被保人豁免,1=投保人豁免） */
    @Excel(name = "豁免类型", readConverterExp = "0=被保人豁免,1=投保人豁免")
    private Integer exemptType;

    /** 主附类型（M=主险,S=附加险） */
    @Excel(name = "主附类型", readConverterExp = "M=主险,S=附加险")
    private String riskType;

    /** 业务类型（0=巨灾业务,1=财务再业务-DD,2=财务再业务-LTPA,3=财务再业务-YRT,4=保证费率再业务,5=传统再业务） */
    @Excel(name = "业务类型", readConverterExp = "0=巨灾业务,1=财务再业务-DD,2=财务再业务-LTPA,3=财务再业务-YRT,4=保证费率再业务,5=传统再业务")
    private Integer businessType;

    /** 再保计算率 */
    @Excel(name = "再保计算率")
    private BigDecimal rsCalcRate;

    /** 税率 */
    @Excel(name = "税率")
    private BigDecimal taxRate;

    /** 再保合同类型（0=寿险溢额合同,1=医疗险合同,2=重疾合同） */
    @Excel(name = "再保合同类型", readConverterExp = "0=寿险溢额合同,1=医疗险合同,2=重疾合同")
    private Integer rsContractType;

    /** 产品长短险标识（0=一年期,1=一年期及以内,2=一年其以上） */
    @Excel(name = "产品长短险标识", readConverterExp = "0=一年期,1=一年期及以内,2=一年其以上")
    private Integer periodFlag;

    /** 免赔类型（0=年,1=次） */
    @Excel(name = "免赔类型", readConverterExp = "0=年,1=次")
    private Integer deductibleType;

    /** 理赔通知限额（0=医疗险,1=非医疗险） */
    @Excel(name = "理赔通知限额", readConverterExp = "0=医疗险,1=非医疗险")
    private Integer claimNotifyLimit;

    /** 理赔参与限额（0=年,1=次） */
    @Excel(name = "理赔参与限额", readConverterExp = "0=年,1=次")
    private Integer claimInvolvedLimit;

    @Excel(name = "理赔次数")
    private Integer claimCount;

    @Excel(name = "是否主责任", dictType = "sys_yes_no")
    private String mainDuty;
    
    /**政保合作业务标志(0=否, 1=是)*/
    @ApiModelProperty("政保合作业务标志，字典：regulator_ins_gov_flag")
    @Excel(name = "政保合作业务标志", dictType = "regulator_ins_gov_flag")
    private String insGovFlag;
    
    /**保险产品大类-E*/
    @ApiModelProperty("保险产品大类-E，字典：regulator_product_type")
    @Excel(name = "保险产品大类-E", dictType = "regulator_product_type")
    private String insProductType;
    
    /**保险产品大类名称-E*/
    private String insProductTypeName;
    
    /**责任分类-E*/
    @ApiModelProperty("责任分类-E，字典：regulator_liability_type")
    @Excel(name = "责任分类-E", dictType = "regulator_liability_type")
    private String insLiabilityType;
    
    /**责任分类名称-E*/
    private String insLiabilityTypeName;

    /**责任分类-B*/
    @ApiModelProperty("责任分类-B，字典：prp_liability_type")
    @Excel(name = "责任分类-B", dictType = "prp_liability_type")
    private String prpLiabilityType;

    /**保险产品大类-B*/
    @ApiModelProperty("保险产品大类-B，字典：regulator_product_type")
    @Excel(name = "保险产品大类-B", dictType = "regulator_product_type")
    private String prpProductType;

    /**保险期限-B*/
    @ApiModelProperty("保险期限类型-B，字典：prp_period_type")
    @Excel(name = "保险期限类型-B", dictType = "prp_period_type")
    private String prpInsuPeriod;

    /** 状态（0=有效,1=无效） */
    @Excel(name = "状态", readConverterExp = "0=有效,1=无效")
    private Integer status;

    /** 是否删除（0=未删除,1=已删除） */
    @Excel(name = "是否删除", readConverterExp = "0=未删除,1=已删除")
    private Integer isDel;

}
