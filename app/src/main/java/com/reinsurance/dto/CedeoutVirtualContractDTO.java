package com.reinsurance.dto;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import com.jd.lightning.common.annotation.Excel;
import com.jd.lightning.common.core.domain.BaseDTO;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 虚拟合同对象 t_cedeout_virtual_contract
 * 
 * <AUTHOR>
 * @date 2024-03-19
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class CedeoutVirtualContractDTO extends BaseDTO
{
    private static final long serialVersionUID = 1L;

    /** 主键自增 */
    @NotNull(groups = {CedeoutVirtualContractDTO.Update.class}, message = "主键ID不能为空")
    private Long id;

    /** 批次号 */
    private String batchNo;

    /** 版本号 */
    private Long version;

    /** 虚拟合同id */
    private Long virtualId;

    /** 虚拟合同编码 */
    @Excel(name = "虚拟合同编码")
    private String virtualCode;

    /** 虚拟合同名称 */
    @Excel(name = "虚拟合同名称")
    @NotNull(groups = {CedeoutContractDTO.Update.class, CedeoutContractDTO.Add.class}, message = "虚拟合同名称不能为空")
    private String virtualName;

    /** 签订日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "签订日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date signDate;

    /** 生效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "生效日期", width = 30, dateFormat = "yyyy-MM-dd")
    @NotNull(groups = {CedeoutContractDTO.Add.class}, message = "生效日期不能为空")
    private Date effectiveDate;

    /** 失效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "失效日期", width = 30, dateFormat = "yyyy-MM-dd")
    @NotNull(groups = {CedeoutContractDTO.Add.class}, message = "失效日期不能为空")
    private Date expiredDate;

    /** 状态（0=有效,1=无效） */
    @Excel(name = "状态", readConverterExp = "0=有效,1=无效")
    @NotNull(groups = {CedeoutContractDTO.Update.class, CedeoutContractDTO.Add.class}, message = "状态不能为空")
    private Integer status;

    /** 是否删除（0=未删除,1=已删除） */
    private Integer isDel;

    /** 分保份额比例 例 30%，70% */
    @Excel(name = "分保份额比例")
    private String shareRatio;

    private List<CedeoutVirtualCompanyDTO> virtualCompanyDTOList;

    public interface Update {
    }
    public interface Add {
    }
}
