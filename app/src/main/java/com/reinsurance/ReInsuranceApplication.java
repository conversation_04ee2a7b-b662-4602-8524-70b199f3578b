package com.reinsurance;


import com.idadt.lightning.communication.service.EnableHttpCommunicationConfig;
import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.ComponentScan;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * 启动程序
 * 
 * <AUTHOR>
 */
@SpringBootApplication(exclude = { DataSourceAutoConfiguration.class })
@EnableHttpCommunicationConfig
@EnableSwagger2
@EnableCaching
@ComponentScan(basePackages = {"com.reinsurance.*","com.jd.*","com.jd.lightning.quartz.*"})
@MapperScan({"com.*.mapper","com.jd.lightning.quartz.mapper"})
@EnableEncryptableProperties
public class ReInsuranceApplication
{
    public static void main(String[] args)
    {
    	System.setProperty("spring.devtools.restart.enabled", "false");
        SpringApplication.run(ReInsuranceApplication.class, args);
        System.out.println("再保系统启动成功");
    }
}
