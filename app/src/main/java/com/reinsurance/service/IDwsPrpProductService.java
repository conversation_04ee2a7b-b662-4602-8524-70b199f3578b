package com.reinsurance.service;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import com.jd.lightning.common.core.domain.Result;
import com.reinsurance.dto.DwsPrpProductDTO;
import com.reinsurance.query.DwsPrpProductQuery;

/**
 * 保单登记再保产品信息Service接口
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
public interface IDwsPrpProductService {
    
    /**
     * 查询保单登记再保产品信息
     *
     * @param Id 保单登记再保产品信息主键
     * @return 保单登记再保产品信息
     */
    public DwsPrpProductDTO selectDwsPrpProductById(Long Id);

    /**
     * 查询保单登记再保产品信息列表
     *
     * @param dwsPrpProductQuery 保单登记再保产品信息
     * @return 保单登记再保产品信息集合
     */
    public List<DwsPrpProductDTO> selectDwsPrpProductList(DwsPrpProductQuery dwsPrpProductQuery);

    /**
     * 新增保单登记再保产品信息
     *
     * @param dwsPrpProductDTO 保单登记再保产品信息
     * @return 结果
     */
    public int insertDwsPrpProduct(DwsPrpProductDTO dwsPrpProductDTO);

    /**
     * 批量新增保单登记再保产品信息
     *
     * @param dwsPrpProductList 保单登记再保产品信息列表
     * @return 结果
     */
    public int insertBatchDwsPrpProduct(List<DwsPrpProductDTO> dwsPrpProductList);

    /**
     * 修改保单登记再保产品信息
     *
     * @param dwsPrpProductDTO 保单登记再保产品信息
     * @return 结果
     */
    public int updateDwsPrpProduct(DwsPrpProductDTO dwsPrpProductDTO);

    /**
     * 批量删除保单登记再保产品信息
     *
     * @param Ids 需要删除的保单登记再保产品信息主键集合
     * @return 结果
     */
    public int deleteDwsPrpProductByIds(Long[] Ids);

    /**
     * 删除保单登记再保产品信息信息
     *
     * @param Id 保单登记再保产品信息主键
     * @return 结果
     */
    public int deleteDwsPrpProductById(Long Id);

    /**
     * 导入保单登记再保产品信息
     *
     * @param companyCode 保险机构代码
     * @param file 导入文件
     * @return 结果
     */
    public Result importDwsPrpProduct(String companyCode, MultipartFile file);

    /**
     * 导出保单登记再保产品信息
     *
     * @param response 响应对象
     * @param dwsPrpProductQuery 查询条件
     */
    public void exportDwsPrpProduct(HttpServletResponse response, DwsPrpProductQuery dwsPrpProductQuery);

    /**
     * 检查保单登记再保产品信息是否存在
     *
     * @param dwsPrpProductQuery 查询条件
     * @return 结果
     */
    public int selectDwsPrpProductExists(DwsPrpProductQuery dwsPrpProductQuery);

    /**
     * 更新推送状态
     *
     * @param Ids 主键数组
     * @return 结果
     */
    public Result updateDwsPrpProductPushStatus(Long[] Ids);

    /**
     * 查询年度报表推送状态
     *
     * @param reportYear 报表年份
     * @return 结果
     */
    public Integer selectAnnualReportShouldPushStatus(int reportYear);
}
