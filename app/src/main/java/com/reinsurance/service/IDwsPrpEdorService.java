package com.reinsurance.service;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import com.jd.lightning.common.core.domain.Result;
import com.reinsurance.dto.DwsPrpEdorDTO;
import com.reinsurance.query.DwsPrpEdorQuery;

/**
 * 再保保全险种明细Service接口
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface IDwsPrpEdorService {
    
    /**
     * 查询再保保全险种明细
     *
     * @param Id 再保保全险种明细主键
     * @return 再保保全险种明细
     */
    public DwsPrpEdorDTO selectDwsPrpEdorById(Long Id);

    /**
     * 查询再保保全险种明细列表
     *
     * @param dwsPrpEdorQuery 再保保全险种明细
     * @return 再保保全险种明细集合
     */
    public List<DwsPrpEdorDTO> selectDwsPrpEdorList(DwsPrpEdorQuery dwsPrpEdorQuery);

    /**
     * 新增再保保全险种明细
     *
     * @param dwsPrpEdorDTO 再保保全险种明细
     * @return 结果
     */
    public int insertDwsPrpEdor(DwsPrpEdorDTO dwsPrpEdorDTO);

    /**
     * 批量新增再保保全险种明细
     *
     * @param dwsPrpEdorList 再保保全险种明细列表
     * @return 结果
     */
    public int insertBatchDwsPrpEdor(List<DwsPrpEdorDTO> dwsPrpEdorList);

    /**
     * 修改再保保全险种明细
     *
     * @param dwsPrpEdorDTO 再保保全险种明细
     * @return 结果
     */
    public int updateDwsPrpEdor(DwsPrpEdorDTO dwsPrpEdorDTO);

    /**
     * 批量删除再保保全险种明细
     *
     * @param Ids 需要删除的再保保全险种明细主键集合
     * @return 结果
     */
    public int deleteDwsPrpEdorByIds(Long[] Ids);

    /**
     * 删除再保保全险种明细信息
     *
     * @param Id 再保保全险种明细主键
     * @return 结果
     */
    public int deleteDwsPrpEdorById(Long Id);

    /**
     * 导入再保保全险种明细
     *
     * @param companyCode 保险机构代码
     * @param companyName 保险机构名称
     * @param manageCom 管理机构
     * @param file 导入文件
     * @return 结果
     */
    public Result importDwsPrpEdor(String companyCode, String companyName, String manageCom, MultipartFile file);

    /**
     * 导出再保保全险种明细
     *
     * @param response 响应对象
     * @param dwsPrpEdorQuery 查询条件
     */
    public void exportDwsPrpEdor(HttpServletResponse response, DwsPrpEdorQuery dwsPrpEdorQuery);

    /**
     * 检查再保保全险种明细是否存在
     *
     * @param dwsPrpEdorQuery 查询条件
     * @return 结果
     */
    public int selectDwsPrpEdorExists(DwsPrpEdorQuery dwsPrpEdorQuery);

    /**
     * 更新推送状态
     *
     * @param Ids 主键数组
     * @return 结果
     */
    public Result updateDwsPrpEdorPushStatus(Long[] Ids);

    /**
     * 查询年度报表推送状态
     *
     * @param reportYear 报表年份
     * @return 结果
     */
    public Integer selectAnnualReportShouldPushStatus(int reportYear);

    /**
     * 生成再保保全险种明细数据
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param reportYear 报表年份
     * @param reportMonth 报表月份
     * @return 结果
     */
    public Result generatePrpEdorData(String startDate, String endDate, Integer reportYear, Integer reportMonth);
}
