package com.reinsurance.service;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import com.jd.lightning.common.core.domain.Result;
import com.reinsurance.dto.DwsPrpAccountDTO;
import com.reinsurance.query.DwsPrpAccountQuery;

/**
 * 保单登记再保账单信息Service接口
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface IDwsPrpAccountService {
    
    /**
     * 查询保单登记再保账单信息
     *
     * @param Id 保单登记再保账单信息主键
     * @return 保单登记再保账单信息
     */
    public DwsPrpAccountDTO selectDwsPrpAccountById(Long Id);

    /**
     * 查询保单登记再保账单信息列表
     *
     * @param dwsPrpAccountQuery 保单登记再保账单信息
     * @return 保单登记再保账单信息集合
     */
    public List<DwsPrpAccountDTO> selectDwsPrpAccountList(DwsPrpAccountQuery dwsPrpAccountQuery);

    /**
     * 新增保单登记再保账单信息
     *
     * @param dwsPrpAccountDTO 保单登记再保账单信息
     * @return 结果
     */
    public int insertDwsPrpAccount(DwsPrpAccountDTO dwsPrpAccountDTO);

    /**
     * 批量新增保单登记再保账单信息
     *
     * @param dwsPrpAccountList 保单登记再保账单信息列表
     * @return 结果
     */
    public int insertBatchDwsPrpAccount(List<DwsPrpAccountDTO> dwsPrpAccountList);

    /**
     * 修改保单登记再保账单信息
     *
     * @param dwsPrpAccountDTO 保单登记再保账单信息
     * @return 结果
     */
    public int updateDwsPrpAccount(DwsPrpAccountDTO dwsPrpAccountDTO);

    /**
     * 批量删除保单登记再保账单信息
     *
     * @param Ids 需要删除的保单登记再保账单信息主键集合
     * @return 结果
     */
    public int deleteDwsPrpAccountByIds(Long[] Ids);

    /**
     * 删除保单登记再保账单信息信息
     *
     * @param Id 保单登记再保账单信息主键
     * @return 结果
     */
    public int deleteDwsPrpAccountById(Long Id);

    /**
     * 导入保单登记再保账单信息
     *
     * @param companyCode 保险机构代码
     * @param companyName 保险机构名称
     * @param manageCom 管理机构
     * @param file 导入文件
     * @return 结果
     */
    public Result importDwsPrpAccount(String companyCode, String companyName, String manageCom, MultipartFile file);

    /**
     * 导出保单登记再保账单信息
     *
     * @param response 响应对象
     * @param dwsPrpAccountQuery 查询条件
     */
    public void exportDwsPrpAccount(HttpServletResponse response, DwsPrpAccountQuery dwsPrpAccountQuery);

    /**
     * 检查保单登记再保账单信息是否存在
     *
     * @param dwsPrpAccountQuery 查询条件
     * @return 结果
     */
    public int selectDwsPrpAccountExists(DwsPrpAccountQuery dwsPrpAccountQuery);

    /**
     * 更新推送状态
     *
     * @param Ids 主键数组
     * @return 结果
     */
    public Result updateDwsPrpAccountPushStatus(Long[] Ids);

    /**
     * 查询年度报表推送状态
     *
     * @param reportYear 报表年份
     * @return 结果
     */
    public Integer selectAnnualReportShouldPushStatus(int reportYear);

    /**
     * 生成再保账单信息表数据
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param reportYear 报表年份
     * @param reportMonth 报表月份
     * @return 结果
     */
    public Result generatePrpAccountData(String startDate, String endDate, Integer reportYear, Integer reportMonth);
}
