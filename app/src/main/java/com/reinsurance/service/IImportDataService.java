package com.reinsurance.service;

import java.util.List;

import com.jd.lightning.common.core.domain.Result;
import com.reinsurance.dto.ImportDataLogDTO;
import com.reinsurance.dto.ImportDataLogDetailDTO;
import com.reinsurance.query.ImportDataConfigExportQuery;
import com.reinsurance.query.ImportDataConfigQuery;
import com.reinsurance.query.ImportDataLogDetailQuery;
import com.reinsurance.query.ImportDataLogQuery;
import com.reinsurance.dto.ImportDataConfigDTO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * 导入导出数据配置Service接口
 * 
 * <AUTHOR>
 * @date 2024-03-21
 */
public interface IImportDataService 
{
    /**
     * 查询导入导出数据配置
     * 
     * @param id 导入导出数据配置主键
     * @return 导入导出数据配置
     */
    public ImportDataConfigDTO selectImportDataConfigById(Long id);

    /**
     * 查询导入导出数据配置列表
     * 
     * @param importDataConfigQuery 导入导出数据配置
     * @return 导入导出数据配置集合
     */
    public List<ImportDataConfigDTO> selectImportDataConfigList(ImportDataConfigQuery importDataConfigQuery);

    /**
     * 导出配置信息
     * @param importDataConfigQuery 参数
     * @param response 响应
     */
    void exportDataConfig(ImportDataConfigExportQuery importDataConfigQuery, HttpServletResponse response);

    /**
     * 导入配置信息
     * @param file 文件
     * @return 处理结果
     */
    Result importDataConfig(MultipartFile file);

    /**
     * 新增导入导出数据配置
     * 
     * @param importDataConfigDTO 导入导出数据配置
     * @return 结果
     */
    public int insertImportDataConfig(ImportDataConfigDTO importDataConfigDTO);

    /**
     * 修改导入导出数据配置
     * 
     * @param importDataConfigDTO 导入导出数据配置
     * @return 结果
     */
    public int updateImportDataConfig(ImportDataConfigDTO importDataConfigDTO);

    /**
     * 批量删除导入导出数据配置
     * 
     * @param ids 需要删除的导入导出数据配置主键集合
     * @return 结果
     */
    public int deleteImportDataConfigByIds(Long[] ids);

    /**
     * 删除导入导出数据配置信息
     * 
     * @param id 导入导出数据配置主键
     * @return 结果
     */
    public int deleteImportDataConfigById(Long id);
    
    /**
     * 查询导入配置数据操作日志
     * 
     * @param id 导入配置数据操作日志主键
     * @return 导入配置数据操作日志
     */
    public ImportDataLogDTO selectImportDataLogById(Long id);

    /**
     * 查询导入配置数据操作日志列表
     * 
     * @param importDataLogQuery 导入配置数据操作日志
     * @return 导入配置数据操作日志集合
     */
    public List<ImportDataLogDTO> selectImportDataLogList(ImportDataLogQuery importDataLogQuery);

    /**
     * 导出日志列表
     *
     * @param importDataLogQuery 导入配置数据操作日志
     * @return 导入配置数据操作日志集合
     */
    public List<ImportDataLogDTO> exportImportDataLogList(ImportDataLogQuery importDataLogQuery);

    /**
     * 新增导入配置数据操作日志
     * 
     * @param importDataLogDTO 导入配置数据操作日志
     * @return 结果
     */
    public int insertImportDataLog(ImportDataLogDTO importDataLogDTO);

    /**
     * 修改导入配置数据操作日志
     * 
     * @param importDataLogDTO 导入配置数据操作日志
     * @return 结果
     */
    public int updateImportDataLog(ImportDataLogDTO importDataLogDTO);

    /**
     * 批量删除导入配置数据操作日志
     * 
     * @param ids 需要删除的导入配置数据操作日志主键集合
     * @return 结果
     */
    public int deleteImportDataLogByIds(Long[] ids);

    /**
     * 删除导入配置数据操作日志信息
     * 
     * @param id 导入配置数据操作日志主键
     * @return 结果
     */
    public int deleteImportDataLogById(Long id);
    
    /**
     * 查询导入配置数据操作日志明细
     * 
     * @param id 导入配置数据操作日志明细主键
     * @return 导入配置数据操作日志明细
     */
    public ImportDataLogDetailDTO selectImportDataLogDetailById(Long id);

    /**
     * 查询导入配置数据操作日志明细列表
     * 
     * @param importDataLogDetailQuery 导入配置数据操作日志明细
     * @return 导入配置数据操作日志明细集合
     */
    public List<ImportDataLogDetailDTO> selectImportDataLogDetailList(ImportDataLogDetailQuery importDataLogDetailQuery);

    /**
     * 新增导入配置数据操作日志明细
     * 
     * @param importDataLogDetailDTO 导入配置数据操作日志明细
     * @return 结果
     */
    public int insertImportDataLogDetail(ImportDataLogDetailDTO importDataLogDetailDTO);

    /**
     * 修改导入配置数据操作日志明细
     * 
     * @param importDataLogDetailDTO 导入配置数据操作日志明细
     * @return 结果
     */
    public int updateImportDataLogDetail(ImportDataLogDetailDTO importDataLogDetailDTO);

    /**
     * 批量删除导入配置数据操作日志明细
     * 
     * @param ids 需要删除的导入配置数据操作日志明细主键集合
     * @return 结果
     */
    public int deleteImportDataLogDetailByIds(Long[] ids);

    /**
     * 删除导入配置数据操作日志明细信息
     * 
     * @param id 导入配置数据操作日志明细主键
     * @return 结果
     */
    public int deleteImportDataLogDetailById(Long id);

    /**
     * 验证成功
     * @param id
     */
    void validateSuccess(Long id);

    /**
     * 验证回退
     * @param id
     */
    void validateBack(Long id);

}
