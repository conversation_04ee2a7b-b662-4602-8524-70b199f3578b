package com.reinsurance.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.reinsurance.dto.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.multipart.MultipartFile;

import com.jd.lightning.common.core.domain.Result;
import com.reinsurance.domain.DwsPolicyEntity;
import com.reinsurance.query.CedeoutBatchLogQuery;
import com.reinsurance.query.DwsReinsuTradeBillDetailQuery;
import com.reinsurance.query.DwsReinsuTradeQuery;
import com.reinsurance.query.ReInsuranceReportDataQuery;

public interface IDwsReinsuTradeService {

	 /**
     * 查询再保分出摊回明细
     * 
     * @param id 再保分出摊回明细主键
     * @return 再保分出摊回明细
     */
	 DwsReinsuTradeDTO selectDwsReinsuTradeById(Long id);

    /**
     * 查询再保分出摊回明细列表
     * 
     * @param dwsReinsuTradeQuery 再保分出摊回明细Query
     * @return 再保分出摊回明细Entity 集合
     */
    List<DwsReinsuTradeDTO> selectDwsReinsuTradeList(DwsReinsuTradeQuery dwsReinsuTradeQuery);

    /**
     * 新增再保分出摊回明细
     * 
     * @param dwsReinsuTradeDTO 再保分出摊回明细
     * @return 结果
     */
    int insertDwsReinsuTrade(DwsReinsuTradeDTO dwsReinsuTradeDTO);

    /**
     * 修改再保分出摊回明细
     * 
     * @param dwsReinsuTradeDTO 再保分出摊回明细
     * @return 结果
     */
    int updateDwsReinsuTrade(DwsReinsuTradeDTO dwsReinsuTradeDTO);
    
    /**
     * 释放上一保单年度的占用风险保额
     *
     * @param dwsReinsuTradeQuery 再保分出摊回明细
     * @return 结果
     */
    int updateReleasePreviousDwsReinsuTrade(DwsReinsuTradeQuery dwsReinsuTradeQuery);
    
    /**
     * 批量删除再保分出摊回明细
     * 
     * @param ids 需要删除的再保分出摊回明细主键集合
     * @return 结果
     */
    int deleteDwsReinsuTradeByIds(Long[] ids);

    /**
     * 删除再保分出摊回明细信息
     * 
     * @param id 再保分出摊回明细主键
     * @return 结果
     */
    int deleteDwsReinsuTradeById(Long id);

    /**
     * 修改复制分出记录的计算分出保费的字段（根据源数据修改）
     * @return
     */
    int updateCopyDwsReinsuTradeBySrcDwsReinsuTrade();
    
    /**
     * 批量新增再保分出摊回明细（通过streamload方式发送http请求）
     * 
     * @param dwsReinsuTradeDTOs 再保分出摊回明细
     * @return 结果
     */
    int insertBatchDwsReinsuTrade(List<DwsReinsuTradeDTO> dwsReinsuTradeDTOs);
    
    /**
     * 批量更新再保分出摊回明细
     * 
     * @param dwsReinsuTradeDTOs 再保分出摊回明细
     * @return 结果
     */
    int updateBatchDwsReinsuTrade(List<DwsReinsuTradeDTO> dwsReinsuTradeDTOs);
    
    /**
     * 查询待出分的记录（按指定规则排序）
     * @parm jobParam
     * @param dataCopy 数据复制标识
     * @param limit 查询条数
     * @return
     */
    List<DwsReinsuTradeDTO> selectWaitCedeoutDwsReinsuTrade(JobParamDTO jobParam, Integer dataCopy, Integer limit);

    /**
     * 查询被保人保单信息
     * @param dwsReinsuTradeQuery	查询条件
     * @return
     */
    List<AnomalousDataDTO> selectInsuredList(DwsReinsuTradeQuery dwsReinsuTradeQuery);

    /**
     * 查询被保人保单信息
     * @param dwsReinsuTradeQuery	查询条件
     * @return
     */
    List<DwsPolicyEntity> insuredUnCedeoutList(DwsReinsuTradeQuery dwsReinsuTradeQuery);

    /**
     * 获取被保险人已使用的风险保额
     * @param dwsReinsuTradeQuery 
     * @return
     */
    BigDecimal selectOccupyRiskAmountByInsuredNo(DwsReinsuTradeQuery dwsReinsuTradeQuery);

    /**
     * 异常数据计算列表
     * @param cedeoutBatchLogQuery
     * @return
     */
    List<AnomalousDataDTO> selectAnomalousDataCalcList(CedeoutBatchLogQuery cedeoutBatchLogQuery);
    
    /**
     * 账单列表
     * @param dwsReinsuTradeBillDetailQuery
     * @return
     */
    List<DwsReinsuTradeBillDetailDTO> selectReinsuTradeBillList(DwsReinsuTradeBillDetailQuery dwsReinsuTradeBillDetailQuery);
    
    /**
     * 调整再保账单
     * 
     * @param dwsReinsuTradeBillDetailDTO 再保账单
     * @return 结果
     */
    Result updateAdjustReinsuTradeBill(DwsReinsuTradeBillDetailDTO dwsReinsuTradeBillDetailDTO);
    
    /**
     * 批量调整账单
     * 
     * @param dwsReinsuTradeBillDetailDTOs 再保账单
     * @return 结果
     */
    Result updateBatchAdjustReinsuTradeBill(List<DwsReinsuTradeBillDetailDTO> dwsReinsuTradeBillDetailDTOs);
    
    /**
     * 获取再保报表数据总条数
     * @param reInsuranceReportDataQuery
     * @return
     */
    Integer selectReportDataCount(ReInsuranceReportDataQuery reInsuranceReportDataQuery);
    
    /**
    /**
     * 获取再保报表数据
     * @param reInsuranceReportDataQuery
     * @return
     */
    List<Map<String, Object>> selectReportDataList(ReInsuranceReportDataQuery reInsuranceReportDataQuery);
    
    /**
     * 获取再保报表汇总数据
     * @param reInsuranceReportDataQuery
     * @return
     */
    List<Map<String, Object>> selectSummaryReportData(ReInsuranceReportDataQuery reInsuranceReportDataQuery);
    
    /**
     * 导入历史数据
     * @param executor
     * @param files
     */
    Result importHistoryData(String executor, MultipartFile[] files);
    
    /**
     * 插入导入的历史数据到数据库表中
     * @param importDwsReinsuTradeList
     * @return
     */
    int insertImportDwsReinsuTrade(List<ImportDwsReinsuTradeDTO> importDwsReinsuTradeList);
    
    /**
     * 查询待摊回（详见Mapper层sql）的分出数据集合
     * @param dwsReinsuTradeQuery
     * @return
     */
    List<DwsReinsuTradeDTO> selectWaitReturnOnCedeoutReinsuTrade(DwsReinsuTradeQuery dwsReinsuTradeQuery);
    
    /**
     * 批量新增再保分出摊回明细（调用Mapper层通过连接池方式）
     * @param dwsReinsuTradeDTOs 再保分出摊回明细
     * @return 结果
     */
    int insertBatchDwsReinsuTradeByPool(List<DwsReinsuTradeDTO> dwsReinsuTradeDTOs);
    
    /**
     * 批量修改分出数据的摊回状态（释放风险保额）适用于未达溢额线的分出记录
     * @param dwsReinsuTradeDTOs
     * @return
     */
    int updateBatchCedeoutTradeReturnStatus(List<DwsReinsuTradeDTO> dwsReinsuTradeDTOs);
    
    /**
     * 根据账单编号查询分出摊回明细数据总条数
     * @param billNo
     * @return
     */
    int selectDwsReinsuTradeCountByBillNo(@Param("billNo") String billNo);
    
    /**
     * 根据账单编号查询分出摊回明细数据
     * @param outColumns
     * @param billNo
     * @param pageSize
     * @param startRows
     * @return
     */
    List<Map<String, Object>> selectDwsReinsuTradeListByBillNo(@Param("outColumns")List<String> outColumns, @Param("billNo") String billNo, @Param("pageSize") int pageSize, @Param("startRows")int startRows);
    
    /**
     * 根据账单号汇总账单对应的分保费、分保佣金、退回分保费、退回分保佣金、摊回理赔款、摊回满期金、摊回生存金
     * @param billNos
     * @return
     */
    Map<String, DwsEastZbzdxxbDTO> selectDwsReinsuTradeMoneyByBillNos(@Param("billNos") List<String> billNos);
    
    /**
     * 分组汇总分出摊回记录转换为East预提账单
     * @param dwsReinsuTradeQuery
     * @return
     */
    List<DwsEastZbzdxxbDTO> selectDwsReinsuTradeGroupSummaryAsZbzdxxb(DwsReinsuTradeQuery dwsReinsuTradeQuery);
    
    /**
     * 虚拟一个分出摊回对象<br/>
     * 先查t_dws_reinsu_policy_liability/t_dws_edor_cont_liability/t_dws_cont_history，没数据再查v_dws_policy，没数据再查v_dws_cont_liability）
     * @param dwsReinsuTradeQuery
     * @return
     */
    DwsReinsuTradeDTO selectOneVirtualReinsuTrade(DwsReinsuTradeQuery dwsReinsuTradeQuery);

    /**
     * 根据账单号汇总账单对应的分保费、分保佣金、退回分保费、退回分保佣金、摊回理赔款、摊回满期金、摊回生存金
     * @param billNos
     * @return
     */
    Map<String, DwsPrpAccountDTO> selectDwsReinsuTradeSummaryMoneyAsPrpAccount(@Param("billNos") List<String> billNos);

    /**
     * 分组汇总分出摊回记录转换为保单登记未结算账单
     * @param dwsReinsuTradeQuery
     * @return
     */
    List<DwsPrpAccountDTO> selectDwsReinsuTradeGroupSummaryAsPrpAccount(DwsReinsuTradeQuery dwsReinsuTradeQuery);
}