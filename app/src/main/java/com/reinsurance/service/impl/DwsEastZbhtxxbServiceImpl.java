package com.reinsurance.service.impl;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import com.alibaba.excel.util.ListUtils;
import com.jd.lightning.common.annotation.DataSource;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.enums.DataSourceType;
import com.jd.lightning.common.utils.DateUtils;
import com.jd.lightning.common.utils.SecurityUtils;
import com.jd.lightning.common.utils.StringUtils;
import com.jd.lightning.common.utils.poi.ExcelUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.reinsurance.mapper.DwsEastZbhtxxbMapper;
import com.reinsurance.dto.DwsEastZbhtxxbDTO;
import com.reinsurance.enums.BasicDataEnums.EastContractType;
import com.reinsurance.enums.BasicDataEnums.EastReport;
import com.reinsurance.enums.BasicDataEnums.EastReportEitherFlag;
import com.reinsurance.enums.BasicDataEnums.RegulatorReport;
import com.reinsurance.enums.BasicDataEnums.RedisKeyModule;
import com.reinsurance.enums.BasicDataEnums.ReportDataSource;
import com.reinsurance.enums.BasicDataEnums.ReportPushStatus;
import com.reinsurance.enums.CedeoutEnums;
import com.reinsurance.query.DwsEastZbhtxxbQuery;
import com.reinsurance.domain.DwsEastZbhtxxbEntity;
import com.reinsurance.service.IDwsEastZbhtxxbService;
import com.reinsurance.service.IDwsRegulatoryReportService;
import com.reinsurance.service.IRedisService;
import com.reinsurance.utils.ReDateUtil;
import com.reinsurance.utils.ReinsuObjectUtil;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.HttpStatus;
import lombok.extern.slf4j.Slf4j;

/**
 * East再保合同信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-05
 */
@Slf4j
@Service
@DataSource(DataSourceType.SLAVE)
public class DwsEastZbhtxxbServiceImpl implements IDwsEastZbhtxxbService {
	
	@Autowired
	private IRedisService redisService;
	
    @Autowired
    private DwsEastZbhtxxbMapper dwsEastZbhtxxbMapper;
    
    @Autowired
    private IDwsRegulatoryReportService dwsRegulatoryReportService;

    /**
     * 查询East再保合同信息
     *
     * @param id East再保合同信息主键
     * @return East再保合同信息
     */
    @Override
    public DwsEastZbhtxxbDTO selectDwsEastZbhtxxbById(Long id) {
        DwsEastZbhtxxbEntity dwsEastZbhtxxbEntity = dwsEastZbhtxxbMapper.selectDwsEastZbhtxxbById(id);
        return ReinsuObjectUtil.convertModel(dwsEastZbhtxxbEntity, DwsEastZbhtxxbDTO.class);
    }

    /**
     * 查询East再保合同信息列表
     *
     * @param dwsEastZbhtxxbQuery East再保合同信息
     * @return East再保合同信息
     */
    @Override
    public List<DwsEastZbhtxxbDTO> selectDwsEastZbhtxxbList(DwsEastZbhtxxbQuery dwsEastZbhtxxbQuery) {
        List<DwsEastZbhtxxbEntity> list = dwsEastZbhtxxbMapper.selectDwsEastZbhtxxbList(dwsEastZbhtxxbQuery);
        return ReinsuObjectUtil.convertList(list, DwsEastZbhtxxbDTO.class);
    }

    @Override
	public Integer selectAnnualReportShouldPushStatus(int reportYear) {
		return dwsEastZbhtxxbMapper.selectAnnualReportShouldPushStatus(reportYear);
	}
    
    /**
     * 新增East再保合同信息
     *
     * @param dwsEastZbhtxxbDTO East再保合同信息
     * @return 结果
     */
    @Override
    public int insertDwsEastZbhtxxb(DwsEastZbhtxxbDTO dwsEastZbhtxxbDTO) {
        DwsEastZbhtxxbEntity dwsEastZbhtxxbEntity = ReinsuObjectUtil.convertModel(dwsEastZbhtxxbDTO, DwsEastZbhtxxbEntity.class);
        dwsEastZbhtxxbEntity.setCreateTime(DateUtils.getNowDate());
        return dwsEastZbhtxxbMapper.insertDwsEastZbhtxxb(dwsEastZbhtxxbEntity);
    }

    /**
     * 修改East再保合同信息
     *
     * @param dwsEastZbhtxxbDTO East再保合同信息
     * @return 结果
     */
    @Override
    public int updateDwsEastZbhtxxb(DwsEastZbhtxxbDTO dwsEastZbhtxxbDTO) {
        DwsEastZbhtxxbEntity dwsEastZbhtxxbEntity = ReinsuObjectUtil.convertModel(dwsEastZbhtxxbDTO, DwsEastZbhtxxbEntity.class);
        return dwsEastZbhtxxbMapper.updateDwsEastZbhtxxb(dwsEastZbhtxxbEntity);
    }

    @Override
	public Result updateDwsEastZbhtxxbPushStatus(Long[] ids) {
    	if(ids == null || ids.length <=0) {
    		return Result.error("请选择要推送的" + EastReport.再保合同信息表.getDesc());
    	}
    	List<DwsEastZbhtxxbEntity> zbhtxxbList = dwsEastZbhtxxbMapper.selectDwsEastZbhtxxbListByIds(ids);
    	if(CollUtil.isEmpty(zbhtxxbList)) {
    		return Result.error("要推送的" + EastReport.再保合同信息表.getDesc() + "不存在。");
    	}
    	long alreadyPushedCount = zbhtxxbList.stream().filter(ht -> ReportPushStatus.已推送.getCode() == ht.getPushStatus()).count();
    	if(alreadyPushedCount > 0) {//包含已推送的数据
    		return Result.error("要推送的" + EastReport.再保合同信息表.getDesc() + "包含已推送的数据，不允许重复推送。");
    	}
		int updateRows = dwsEastZbhtxxbMapper.updateDwsEastZbhtxxbPushStatus(ReportPushStatus.已推送.getCode(), SecurityUtils.getUsername(), ids);
		if(updateRows > 0) {
			List<Integer> reportYears = zbhtxxbList.stream().map(DwsEastZbhtxxbEntity::getReportYear).distinct().collect(Collectors.toList());
			dwsRegulatoryReportService.insertOrUpdateRegulatoryReport(BusinessType.UPDATE, RegulatorReport.East数据报送, EastReport.再保合同信息表.getCode(), reportYears);
		}
		return Result.success("推送成功", updateRows);
	}

	/**
     * 批量删除East再保合同信息
     *
     * @param ids 需要删除的East再保合同信息主键
     * @return 结果
     */
    @Override
    public int deleteDwsEastZbhtxxbByIds(Long[] ids) {
        return dwsEastZbhtxxbMapper.deleteDwsEastZbhtxxbByIds(ids);
    }

    /**
     * 删除East再保合同信息信息
     *
     * @param id East再保合同信息主键
     * @return 结果
     */
    @Override
    public int deleteDwsEastZbhtxxbById(Long id) {
    	DwsEastZbhtxxbEntity entity = dwsEastZbhtxxbMapper.selectDwsEastZbhtxxbById(id);
    	if(entity == null) {
    		return 0;
    	}
    	if(ReportDataSource.系统.getCode() == entity.getDataSource()) {
    		return -1;
    	}
    	if(ReportPushStatus.已推送.getCode() == entity.getPushStatus()) {
    		return -2;
    	}
        int deleteRows = dwsEastZbhtxxbMapper.deleteDwsEastZbhtxxbById(id);
        if(deleteRows > 0) {
        	dwsRegulatoryReportService.insertOrUpdateRegulatoryReport(BusinessType.DELETE, RegulatorReport.East数据报送, EastReport.再保合同信息表.getCode(), Arrays.asList(entity.getReportYear()));
        }
        return deleteRows;
    }

	@Override
	public int insertBatchDwsEastZbhtxxb(List<DwsEastZbhtxxbDTO> dwsEastZbhtxxbList) {
		List<DwsEastZbhtxxbEntity> dwsEastZbhtxxbEntitys = ReinsuObjectUtil.convertList(dwsEastZbhtxxbList, DwsEastZbhtxxbEntity.class);
		return dwsEastZbhtxxbMapper.insertBatchDwsEastZbhtxxb(dwsEastZbhtxxbEntitys);
	}

	@Override
	public Result importDwsEastZbhtxxb(String companyCode, String companyName, String manageCom, MultipartFile file) {
		try {
			ExcelUtil<DwsEastZbhtxxbDTO> excelUtil = new ExcelUtil<>(DwsEastZbhtxxbDTO.class);
	        List<DwsEastZbhtxxbDTO> zbhtxxbList = excelUtil.importExcel(file.getInputStream());
	        if(CollUtil.isEmpty(zbhtxxbList)) {
	        	return Result.error("导入的合同信息表为空。");
	        }
	        String error = this.checkImportData(companyCode, companyName, zbhtxxbList);
	        if(StringUtils.isNotBlank(error)) {
	        	return Result.error(error);
	        }
	        String keySuffix = zbhtxxbList.get(0).getBxjgdm();
	        List<String> serialNos = redisService.getUniqueCodes(RedisKeyModule.EAST, keySuffix, zbhtxxbList.size());
			if(CollUtil.isEmpty(serialNos) || zbhtxxbList.size() != serialNos.size()) {
				return Result.error("生成流水号失败，请联系管理员。");
			}
	        int index = 0;
	        for(DwsEastZbhtxxbDTO zbhtxxb : zbhtxxbList) {
	        	String sjbspch = ReDateUtil.getLastDayOfMonthPath(zbhtxxb.getReportYear(), zbhtxxb.getReportMonth());
	        	zbhtxxb.setSjbspch(sjbspch);
	        	zbhtxxb.setManagecom(manageCom);
	        	zbhtxxb.setLsh(serialNos.get(index));
	        	zbhtxxb.setCreateTime(DateUtils.getNowDate());
	        	zbhtxxb.setUpdateTime(DateUtils.getNowDate());
	        	zbhtxxb.setCreateBy(SecurityUtils.getUsername());
	        	zbhtxxb.setUpdateBy(SecurityUtils.getUsername());
	        	zbhtxxb.setDataSource(ReportDataSource.人工.getCode());
	        	zbhtxxb.setPushStatus(ReportPushStatus.未推送.getCode());
	        	zbhtxxb.setAccountPeriod(zbhtxxb.getReportYear() + String.format("%02d", zbhtxxb.getReportMonth()));
	        	index++;
	        }
	        List<DwsEastZbhtxxbEntity> dwsEastZbcpxxbList = ReinsuObjectUtil.convertList(zbhtxxbList, DwsEastZbhtxxbEntity.class);
	        int insertRows = dwsEastZbhtxxbMapper.insertBatchDwsEastZbhtxxb(dwsEastZbcpxxbList);
	        if(insertRows > 0) {
	        	List<Integer> reportYears = zbhtxxbList.stream().map(DwsEastZbhtxxbDTO::getReportYear).distinct().collect(Collectors.toList());
	        	dwsRegulatoryReportService.insertOrUpdateRegulatoryReport(BusinessType.IMPORT, RegulatorReport.East数据报送, EastReport.再保合同信息表.getCode(), reportYears);
	        }
	        return Result.success("成功导入" + insertRows + "行", HttpStatus.HTTP_OK);
		}catch(Exception e) {
			log.error("再保导入East合同信息出错, 错误原因:", e);
			return Result.error("导入合同信息表出错，请联系管理员。");
		}
	}

	@Override
	public int selectDwsEastZbhtxxbExists(DwsEastZbhtxxbQuery dwsEastZbhtxxbQuery) {
		return dwsEastZbhtxxbMapper.selectDwsEastZbhtxxbExists(dwsEastZbhtxxbQuery);
	}
	
	@Override
	public void exportDwsEastZbhtxxb(HttpServletResponse response, DwsEastZbhtxxbQuery dwsEastZbhtxxbQuery) {
		List<DwsEastZbhtxxbDTO> list = this.selectDwsEastZbhtxxbList(dwsEastZbhtxxbQuery);
        ExcelUtil<DwsEastZbhtxxbDTO> util = new ExcelUtil<DwsEastZbhtxxbDTO>(DwsEastZbhtxxbDTO.class);
        util.exportExcel(response, list, "East合同信息表");
	}
	
	private String checkImportData(String companyCode, String companyName, List<DwsEastZbhtxxbDTO> zbhtxxbList) {
		int index = 0;
		StringBuffer result = new StringBuffer();
		for(DwsEastZbhtxxbDTO zbhtxxb : zbhtxxbList) {
			List<String> emptys = ListUtils.newArrayList();
			List<String> errors = ListUtils.newArrayList();
			if(StringUtils.isBlank(zbhtxxb.getBxjgdm())) {
				emptys.add("保险机构代码");
			}else {
				if(!zbhtxxb.getBxjgdm().equals(companyCode)) {
					errors.add("保险机构代码");
				}
			}
			if(StringUtils.isBlank(zbhtxxb.getBxjgmc())) {
				emptys.add("保险机构名称");
			}else {
				if(!zbhtxxb.getBxjgmc().equals(companyName)) {
					errors.add("保险机构名称");
				}
			}
			if(StringUtils.isBlank(zbhtxxb.getZbxhthm())) {
				emptys.add("再保险合同号码");
			}
			if(StringUtils.isBlank(zbhtxxb.getZbxhtmc())) {
				emptys.add("再保险合同名称");
			}
			if(StringUtils.isBlank(zbhtxxb.getHtfl())) {
				emptys.add("合同分类");
			}else {
				if(!EastContractType.分出合同.getDesc().equals(zbhtxxb.getHtfl())) {
					errors.add("合同分类");
				}
			}
			if(StringUtils.isBlank(zbhtxxb.getHtfylx())) {
				emptys.add("合同附约类型");
			}else {
				if(!CedeoutEnums.合同类型_主合同.getName().equals(zbhtxxb.getHtfylx()) && 
					!CedeoutEnums.合同类型_附约.getName().equals(zbhtxxb.getHtfylx())) {
					errors.add("合同附约类型");
				}
			}
			if(StringUtils.isBlank(zbhtxxb.getHtzt())) {
				emptys.add("合同状态");
			}
			
			if(StringUtils.isBlank(zbhtxxb.getJzzbhtbz())) {
				emptys.add("巨灾再保合同标志");
			}else {
				if(StringUtils.isBlank(EastReportEitherFlag.getDescByCode(zbhtxxb.getJzzbhtbz()))) {
					errors.add("巨灾再保合同标志");
				}
			}
			if(StringUtils.isBlank(zbhtxxb.getLfhtbz())) {
				emptys.add("临分合同标志");
			}else {
				if(StringUtils.isBlank(EastReportEitherFlag.getDescByCode(zbhtxxb.getLfhtbz()))) {
					errors.add("临分合同标志");
				}
			}
			if(StringUtils.isBlank(zbhtxxb.getHtqsrq())) {
				emptys.add("合同签署日期");
			}
			if(StringUtils.isBlank(zbhtxxb.getHtsxqq())) {
				emptys.add("合同生效起期");
			}
			if(StringUtils.isBlank(zbhtxxb.getHtsxzq())) {
				emptys.add("合同生效止期");
			}
			if(StringUtils.isBlank(zbhtxxb.getHtlx())) {
				emptys.add("合同类型");
			}else {
				if(!EastContractType.比例合同.getDesc().equals(zbhtxxb.getHtlx())) {
					errors.add("合同类型");
				}
			}
			if(StringUtils.isBlank(zbhtxxb.getZbxgsdm())) {
				emptys.add("再保险公司代码");
			}
			if(StringUtils.isBlank(zbhtxxb.getZbxgsmc())) {
				emptys.add("再保险公司名称");
			}
			if(StringUtils.isBlank(zbhtxxb.getYbxgsdm())) {
				emptys.add("原保险公司代码");
			}else {
				if(!zbhtxxb.getYbxgsdm().equals(companyCode)) {
					errors.add("原保险公司代码");
				}
			}
			if(StringUtils.isBlank(zbhtxxb.getYbxgsmc())) {
				emptys.add("原保险公司名称");
			}else {
				if(!zbhtxxb.getYbxgsmc().equals(companyName)) {
					errors.add("原保险公司名称");
				}
			}
			if(StringUtils.isBlank(zbhtxxb.getCjrq())) {
				emptys.add("采集日期");
			}
			if(zbhtxxb.getReportYear() == null) {
				emptys.add("所属年份");
			}
			if(zbhtxxb.getReportMonth() == null) {
				emptys.add("所属月份");
			}
			index++;
			if(emptys.size() > 0) {
				result.append("第" + index + "行：" + String.join("、", emptys) + "为空。<br/>");
			}
			if(errors.size() > 0) {
				result.append("第" + index + "行：" + String.join("、", errors) + "有误。<br/>");
			}
		}
		return result.toString();
	}
	
}
