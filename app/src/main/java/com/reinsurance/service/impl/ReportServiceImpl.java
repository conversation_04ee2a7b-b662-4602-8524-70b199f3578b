package com.reinsurance.service.impl;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import com.jd.lightning.common.constant.UserConstants;
import com.jd.lightning.common.core.domain.entity.SysDictData;
import com.jd.lightning.common.utils.DateUtils;
import com.jd.lightning.common.utils.StringUtils;
import com.jd.lightning.system.service.ISysDictDataService;
import com.jd.lightning.system.service.ISysDictTypeService;

import com.reinsurance.utils.ReDateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.reinsurance.mapper.ReportDataMapper;
import com.reinsurance.mapper.ReportTemplateDetailMapper;
import com.reinsurance.mapper.ReportTemplateMapper;
import com.reinsurance.dto.ReportDataDTO;
import com.reinsurance.dto.ReportTemplateDTO;
import com.reinsurance.dto.ReportTemplateDetailDTO;
import com.reinsurance.enums.CedeoutEnums;
import com.reinsurance.enums.BasicDataEnums.BusinessReportStatus;
import com.reinsurance.enums.BasicDataEnums.FileStoragePath;
import com.reinsurance.enums.BasicDataEnums.FileSuffix;
import com.reinsurance.enums.BasicDataEnums.RedisKeyModule;
import com.reinsurance.enums.BasicDataEnums.ReportStatLogic;
import com.reinsurance.enums.BasicDataEnums.ReportStatMethod;
import com.reinsurance.query.ReInsuranceReportDataQuery;
import com.reinsurance.query.ReportDataQuery;
import com.reinsurance.query.ReportTemplateDetailQuery;
import com.reinsurance.query.ReportTemplateQuery;
import com.reinsurance.service.IDwsReinsuTradeService;
import com.reinsurance.service.IFileService;
import com.reinsurance.service.IRedisService;
import com.reinsurance.service.IReportService;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;

import com.reinsurance.constant.RsConstant;
import com.reinsurance.domain.ReportDataEntity;
import com.reinsurance.domain.ReportTemplateDetailEntity;
import com.reinsurance.domain.ReportTemplateEntity;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.google.common.collect.Lists;
import com.reinsurance.utils.ReinsuJsonUtil;
import com.reinsurance.utils.ReinsuObjectUtil;

/**
 * 报表模板Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-04
 */
@Slf4j
@Service
public class ReportServiceImpl implements IReportService {
	
	@Autowired 
	private IRedisService redisService;
	
	@Autowired
    private ReportDataMapper reportDataMapper;
	
    @Autowired
    private ReportTemplateMapper reportTemplateMapper;
    
    @Autowired
    private ReportTemplateDetailMapper reportTemplateDetailMapper;
    
    @Autowired 
	private IDwsReinsuTradeService dwsReinsuTradeService;
    
    @Autowired 
	private ISysDictDataService sysDictDataService;
    
    @Autowired 
	private ISysDictTypeService sysDictTypeService;
    
    @Autowired
    private IFileService fileService;
    
    
    /**
     * 查询报表模板
     *
     * @param id 报表模板主键
     * @return 报表模板
     */
    @Override
    public ReportTemplateDTO selectReportTemplateById(Long id) {
    	ReportTemplateEntity reportTemplateEntity = reportTemplateMapper.selectReportTemplateById(id);
    	return ReinsuObjectUtil.convertModel(reportTemplateEntity, ReportTemplateDTO.class);
    }

    /**
     * 查询报表模板列表
     *
     * @param reportTemplateQuery 报表模板
     * @return 报表模板
     */
    @Override
    public List<ReportTemplateDTO> selectReportTemplateList(ReportTemplateQuery reportTemplateQuery) {
        List<ReportTemplateEntity> list = reportTemplateMapper.selectReportTemplateList(reportTemplateQuery);
        return ReinsuObjectUtil.convertList(list, ReportTemplateDTO.class);
    }

    /**
     * 新增报表模板
     *
     * @param reportTemplateDTO 报表模板
     * @return 结果
     */
    @Override
    public int insertReportTemplate(ReportTemplateDTO reportTemplateDTO) {
    	if(CollUtil.isEmpty(reportTemplateDTO.getCheckedColumnList())) {
			return 0;
		}
    	reportTemplateDTO.setCreateTime(DateUtils.getNowDate());
    	reportTemplateDTO.setReportCode(redisService.getUniqueCode(RedisKeyModule.REPORT));
    	ReportTemplateEntity reportTemplateEntity = ReinsuObjectUtil.convertModel(reportTemplateDTO, ReportTemplateEntity.class);
    	List<ReportTemplateDetailEntity> reportTemplateDetailList = ReinsuObjectUtil.convertList(reportTemplateDTO.getCheckedColumnList(), ReportTemplateDetailEntity.class);
		reportTemplateDetailList.forEach(detail ->{
			detail.setReportCode(reportTemplateDTO.getReportCode());
			detail.setReportName(reportTemplateDTO.getReportName());
			detail.setCreateBy(reportTemplateDTO.getUpdateBy());
			detail.setCreateTime(reportTemplateDTO.getUpdateTime());
			detail.setStatus(CedeoutEnums.状态_有效.getValue());
			detail.setIsDel(CedeoutEnums.未删除.getValue());
		});
		reportTemplateDetailMapper.batchInsertReportTemplateDetail(reportTemplateDetailList);
    	return reportTemplateMapper.insertReportTemplate(reportTemplateEntity);
    }

    /**
     * 修改报表模板
     *
     * @param reportTemplateDTO 报表模板
     * @return 结果
     */
    @Override
    @Transactional
    public int updateReportTemplate(ReportTemplateDTO reportTemplateDTO) {
    	if(CollUtil.isEmpty(reportTemplateDTO.getCheckedColumnList())) {
			return 0;
		}
    	reportTemplateDTO.setUpdateTime(DateUtils.getNowDate());
    	ReportTemplateEntity reportTemplateEntity = ReinsuObjectUtil.convertModel(reportTemplateDTO, ReportTemplateEntity.class);
		reportTemplateDetailMapper.deleteReportTemplateDetailByReportCode(reportTemplateDTO.getReportCode());
		List<ReportTemplateDetailEntity> reportTemplateDetailList = ReinsuObjectUtil.convertList(reportTemplateDTO.getCheckedColumnList(), ReportTemplateDetailEntity.class);
		reportTemplateDetailList.forEach(detail ->{
			detail.setReportCode(reportTemplateDTO.getReportCode());
			detail.setReportName(reportTemplateDTO.getReportName());
			detail.setCreateBy(reportTemplateDTO.getUpdateBy());
			detail.setCreateTime(reportTemplateDTO.getUpdateTime());
			detail.setStatus(CedeoutEnums.状态_有效.getValue());
			detail.setIsDel(CedeoutEnums.未删除.getValue());
		});
		reportTemplateDetailMapper.batchInsertReportTemplateDetail(reportTemplateDetailList);
        return reportTemplateMapper.updateReportTemplate(reportTemplateEntity);
    }

    /**
     * 批量删除报表模板
     *
     * @param ids 需要删除的报表模板主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteReportTemplateByIds(Long[] ids) {
    	List<ReportTemplateEntity> reportTemplateList = reportTemplateMapper.selectReportTemplateByIds(ids);
    	if(CollUtil.isEmpty(reportTemplateList)) {
    		return 0;
    	}
    	String [] reportCodes = reportTemplateList.stream().map(ReportTemplateEntity::getReportCode).toArray(String[]::new);
    	reportTemplateDetailMapper.deleteReportTemplateDetailByReportCodes(reportCodes);
        return reportTemplateMapper.deleteReportTemplateByIds(ids);
    }

    /**
     * 删除报表模板信息
     *
     * @param id 报表模板主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteReportTemplateById(Long id) {
    	ReportTemplateEntity reportTemplate = reportTemplateMapper.selectReportTemplateById(id);
    	if(reportTemplate == null) {
    		return 0;
    	}
    	reportTemplateDetailMapper.deleteReportTemplateDetailByReportCode(reportTemplate.getReportCode());
        return reportTemplateMapper.deleteReportTemplateById(id);
    }

	/**
     * 查询报表模板详情列表
     *
     * @param reportCode 报表模板详情
     * @return 报表模板详情
     */
    @Override
    public List<ReportTemplateDetailDTO> selectReportCheckedColumn(String reportCode) {
    	ReportTemplateDetailQuery reportTemplateDetailQuery = new ReportTemplateDetailQuery();
    	reportTemplateDetailQuery.setReportCode(reportCode);
        List<ReportTemplateDetailEntity> list = reportTemplateDetailMapper.selectReportTemplateDetailList(reportTemplateDetailQuery);
        return ReinsuObjectUtil.convertList(list, ReportTemplateDetailDTO.class);
    }

	@Override
	public ReportDataDTO selectReportDataById(Long id) {
		ReportDataEntity reportDataEntity = reportDataMapper.selectReportDataById(id);
		return ReinsuObjectUtil.convertModel(reportDataEntity, ReportDataDTO.class);
	}

	@Override
	public List<ReportDataDTO> selectReportDataList(ReportDataQuery query) {
		List<ReportDataEntity> reportDataList = reportDataMapper.selectReportDataList(query);
		return ReinsuObjectUtil.convertList(reportDataList, ReportDataDTO.class);
	}

	@Override
	public int insertReportData(ReportDataDTO reportDataDTO) {
		reportDataDTO.setParentReportCode("0");
		reportDataDTO.setCreateTime(DateUtils.getNowDate());
		ReportDataEntity reportDataEntity = ReinsuObjectUtil.convertModel(reportDataDTO, ReportDataEntity.class);
		int result = reportDataMapper.insertReportData(reportDataEntity);
		if(result > 0) {
			reportDataDTO.setId(reportDataEntity.getId());
		}
		return result;
	}

	@Override
	public int updateReportData(ReportDataDTO reportDataDTO) {
		reportDataDTO.setUpdateTime(DateUtils.getNowDate());
		ReportDataEntity reportDataEntity = ReinsuObjectUtil.convertModel(reportDataDTO, ReportDataEntity.class);
		return reportDataMapper.updateReportData(reportDataEntity);
	}

	@Override
	public int deleteReportDataById(Long id) {
		return reportDataMapper.deleteReportDataById(id);
	}

	@Override
	public int deleteReportDataByIds(Long[] ids) {
		return reportDataMapper.deleteReportDataByIds(ids);
	}

	@Override
	public int reInsuranceReportStatistics(ReportDataDTO reportDataDTO) {
		ReportTemplateEntity template = reportTemplateMapper.selectReportTemplateByReportCode(reportDataDTO.getReportCode());
		if(template == null) {
			return 0;
		}
		reportDataDTO.setParentReportCode("0");
		reportDataDTO.setReportType(template.getReportType());
		reportDataDTO.setReportName(template.getReportName());
		reportDataDTO.setCreateTime(DateUtils.getNowDate());
		reportDataDTO.setStatus(BusinessReportStatus.STATUS_2.getCode());
		ReportDataEntity reportDataEntity = ReinsuObjectUtil.convertModel(reportDataDTO, ReportDataEntity.class);
		int result = reportDataMapper.insertReportData(reportDataEntity);
		if(result > 0){
			reportDataDTO.setId(reportDataEntity.getId());
			CompletableFuture.runAsync(() -> executeStatLogic(template, reportDataDTO));
		}
		return result;
	}
	
	@Override
	public List<ReportTemplateDetailDTO> getReInsuranceReportColumn(){
		SysDictData sysDictDataQuery = new SysDictData();
		sysDictDataQuery.setStatus(UserConstants.DICT_NORMAL);
		sysDictDataQuery.setDictType(RsConstant.rsReportColumnDictTypeKey);
		List<SysDictData> columnList = sysDictDataService.selectDictDataList(sysDictDataQuery);
    	List<ReportTemplateDetailDTO> reportColumnList = new ArrayList<ReportTemplateDetailDTO>();
    	for(SysDictData dictData : columnList) {
			ReportTemplateDetailDTO reportColumn = new ReportTemplateDetailDTO();
			reportColumn.setFieldName(dictData.getDictValue());
			reportColumn.setHeaderName(dictData.getDictLabel());
			reportColumn.setShowOrder(dictData.getDictSort());
			reportColumnList.add(reportColumn);
    	}
    	if(CollUtil.isNotEmpty(reportColumnList)) {
    		Collections.sort(reportColumnList, (column1, column2) -> column1.getShowOrder().compareTo(column1.getShowOrder()));
    	}
		return reportColumnList;
	}
	
	
	private void executeStatLogic(ReportTemplateEntity template, ReportDataDTO reportDataDTO) {
    	int reportDataType = template.getReportDataType();
		ExcelWriter excelWriter = null;
		try {
			List<ReportTemplateDetailEntity> columnList = reportTemplateDetailMapper.selectReportColumnsByReportCode(reportDataDTO.getReportCode());
			if(CollUtil.isEmpty(columnList)) {
				reportDataDTO.setRemark("报表模板未设置输出列");
				return;
			}
			//获取所有需要映射字段的字典信息
			Map<String, Map<String, String>> tradeColumnMappingDictHash = new HashMap<String, Map<String, String>>();
			List<SysDictData> tradeColumnMappingDict = sysDictTypeService.selectDictDataByType(RsConstant.tradeColumnMappingDict);
			for(SysDictData mapping : tradeColumnMappingDict) {
				List<SysDictData> dictDataList = sysDictTypeService.selectDictDataByType(mapping.getDictLabel());
				Map<String, String> dictDataListHash = new HashMap<String, String>();
				if(CollUtil.isNotEmpty(dictDataList)) {
					dictDataListHash = dictDataList.stream().collect(Collectors.toMap(dict -> dict.getDictValue(), dict -> dict.getDictLabel()));
				}
				tradeColumnMappingDictHash.put(mapping.getDictValue(), dictDataListHash);
			}
			
			String filePath = "";
			if(template.getStatMethod() == ReportStatMethod.聚合.getCode()) {//聚合
				ReInsuranceReportDataQuery reInsuranceReportDataQuery = new ReInsuranceReportDataQuery();
				reInsuranceReportDataQuery.setAccountStartDate(ReDateUtil.offsetDay(reportDataDTO.getStartDate(), 1).toJdkDate());
				reInsuranceReportDataQuery.setAccountEndDate(ReDateUtil.offsetDay(reportDataDTO.getEndDate(), 1).toJdkDate());
				reInsuranceReportDataQuery.setGroupColumnList(columnList.stream().filter(column -> column.getStatLogic() == ReportStatLogic.分组.getCode()).map(ReportTemplateDetailEntity::getFieldName).collect(Collectors.toList()));
				reInsuranceReportDataQuery.setSummaryColumnList(columnList.stream().filter(column -> column.getStatLogic() == ReportStatLogic.聚合.getCode()).map(ReportTemplateDetailEntity::getFieldName).collect(Collectors.toList()));
				reInsuranceReportDataQuery.setRiskCode(reportDataDTO.getRiskCode());
				reInsuranceReportDataQuery.setReportDataType(reportDataType);
				List<Map<String, Object>> reportDataList = dwsReinsuTradeService.selectSummaryReportData(reInsuranceReportDataQuery);
				if(CollUtil.isEmpty(reportDataList)) {
					return;
				}
				filePath = this.getFilePath(reportDataDTO);
				List<List<String>> headers = this.getHeaders(columnList);
				List<List<Object>> datas = this.getDatas(tradeColumnMappingDictHash, columnList, reportDataList);
				EasyExcel.write(filePath).excelType(ExcelTypeEnum.XLSX).sheet().head(headers).doWrite(datas);
			}else {//明细
				filePath = this.getFilePath(reportDataDTO);
				List<List<String>> headers = this.getHeaders(columnList);
				
				HorizontalCellStyleStrategy handlerFontStyleStrategy = new HorizontalCellStyleStrategy();
				WriteCellStyle headWriteCellStyle = new WriteCellStyle();
				WriteFont writeFont = new WriteFont();
				writeFont.setFontHeightInPoints((short)12);
				headWriteCellStyle.setWriteFont(writeFont);
				handlerFontStyleStrategy.setHeadWriteCellStyle(headWriteCellStyle);
				excelWriter = EasyExcel.write(filePath).registerWriteHandler(handlerFontStyleStrategy).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).head(headers).build();
				
				
				ReInsuranceReportDataQuery reInsuranceReportDataQuery = new ReInsuranceReportDataQuery();
				reInsuranceReportDataQuery.setAccountStartDate(ReDateUtil.offsetDay(reportDataDTO.getStartDate(), 1).toJdkDate());
				reInsuranceReportDataQuery.setAccountEndDate(ReDateUtil.offsetDay(reportDataDTO.getEndDate(), 1).toJdkDate());
				reInsuranceReportDataQuery.setOutputColumnList(columnList.stream().map(ReportTemplateDetailEntity::getFieldName).collect(Collectors.toList()));
				reInsuranceReportDataQuery.setRiskCode(reportDataDTO.getRiskCode());
				reInsuranceReportDataQuery.setReportDataType(reportDataType);
				int totalRows = dwsReinsuTradeService.selectReportDataCount(reInsuranceReportDataQuery);
				if(totalRows > 0) {
					int pageSize = 10000;
					if(totalRows <= pageSize) {//小于等于1万条，直接查明细
						List<Map<String, Object>> reportDataList = dwsReinsuTradeService.selectReportDataList(reInsuranceReportDataQuery);
						if(CollUtil.isNotEmpty(reportDataList)) {
							WriteSheet writeSheet = EasyExcel.writerSheet(template.getReportName()).build();
							List<List<Object>> datas = this.getDatas(tradeColumnMappingDictHash, columnList, reportDataList);
							excelWriter.write(datas, writeSheet);
						}
						log.info("再保报表生成中, reportCode:{}, totalRows:{}", reportDataDTO.getReportCode(), totalRows);
					}else {//避免内存溢出需要分页查询，每次查1万条
						int sheetNo = 1;
						WriteSheet writeSheet = null;
						int totalPage = (totalRows+pageSize-1)/pageSize;
						reInsuranceReportDataQuery.getParams().put("pageSize", pageSize);
						for(int pageNo=1; pageNo<=totalPage; pageNo++) {
							int startRows = (pageNo - 1) * pageSize;
							reInsuranceReportDataQuery.getParams().put("startRows", startRows);
							List<Map<String, Object>> reportDataList = dwsReinsuTradeService.selectReportDataList(reInsuranceReportDataQuery);
							if(CollUtil.isNotEmpty(reportDataList)) {
								List<List<Object>> datas = this.getDatas(tradeColumnMappingDictHash, columnList, reportDataList);
								if(startRows % (pageSize * 50) == 0) {//每50万行生成一个sheet页
									writeSheet = EasyExcel.writerSheet(template.getReportName() + String.valueOf(sheetNo)).build();
									sheetNo++;
								}
								excelWriter.write(datas, writeSheet);
							}
							log.info("再保报表生成中, reportCode:{}, totalRows:{}, startRows:{}", reportDataDTO.getReportCode(), totalRows, startRows);
						}
					}
				}
			}
			reportDataDTO.setReportDataPath(filePath);
			if(StringUtils.isNotBlank(reportDataDTO.getReportDataPath())) {
				reportDataDTO.setStatus(BusinessReportStatus.STATUS_0.getCode());
			}else {
				reportDataDTO.setStatus(BusinessReportStatus.STATUS_3.getCode());
			}
		}catch(Exception e) {
			reportDataDTO.setRemark("程序运行异常");
			reportDataDTO.setStatus(BusinessReportStatus.STATUS_3.getCode());
			log.error("生成再保报表失败, reportDataDTO:{}, 错误原因:", ReinsuJsonUtil.toJsonString(reportDataDTO), e);
		}finally {
			if(excelWriter != null) {
				excelWriter.finish();
			}
			reportDataDTO.setUpdateBy(reportDataDTO.getCreateBy());
			reportDataDTO.setUpdateTime(DateUtils.getNowDate());
			this.updateReportData(reportDataDTO);
		}
	}
	
	private List<List<String>> getHeaders(List<ReportTemplateDetailEntity> columnList){
		List<List<String>> headers = new ArrayList<List<String>>();
		for(ReportTemplateDetailEntity column : columnList) {
			headers.add(Lists.newArrayList(column.getHeaderName()));
		}
		return headers;
	}
	
	private List<List<Object>> getDatas(Map<String, Map<String, String>> tradeColumnMappingDictHash, List<ReportTemplateDetailEntity> columnList, List<Map<String, Object>> reportDataList) {
		List<List<Object>> datas = new ArrayList<List<Object>>();
		for(Map<String, Object> data : reportDataList) {
			List<Object> rowDatas = Lists.newArrayList();
			for(ReportTemplateDetailEntity column : columnList) {
				String fieldName = column.getFieldName();
				Object dataValue = data.get(fieldName);
				if(dataValue != null && dataValue instanceof Date) {
					rowDatas.add(DateUtil.formatDate((Date)dataValue));
				}else {
					if(tradeColumnMappingDictHash.containsKey(fieldName)) {
						String dataLabel = tradeColumnMappingDictHash.get(fieldName).get(String.valueOf(dataValue));
						rowDatas.add(StringUtils.isNotEmpty(dataLabel) ? dataLabel : dataValue);
					}else {
						rowDatas.add(dataValue);
					}
				}
			}
			datas.add(rowDatas);
		}
		return datas;
	}
	
	private String getFilePath(ReportDataDTO reportDataDTO) {
		String filePath = fileService.getStoragePath() + FileStoragePath.REPORT.getPath() + DateUtils.datePath() + "/";
		File dir = new File(filePath);
		if(!dir.exists()) {
			dir.mkdirs();
		}
		return filePath + reportDataDTO.getReportName() + String.valueOf(reportDataDTO.getId()) + FileSuffix.EXCEL.getSuffix();
	}
    
}
