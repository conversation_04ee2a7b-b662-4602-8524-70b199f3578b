package com.reinsurance.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.jd.lightning.common.utils.SecurityUtils;
import com.reinsurance.dto.*;
import com.reinsurance.service.ICedeoutContractService;
import com.reinsurance.domain.*;
import com.reinsurance.enums.CedeoutEnums;
import com.reinsurance.mapper.*;
import com.reinsurance.query.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.reinsurance.utils.ReinsuObjectUtil;
import com.jd.lightning.common.annotation.DataSource;
import com.jd.lightning.common.enums.DataSourceType;
import com.jd.lightning.common.utils.DateUtils;

import org.springframework.util.CollectionUtils;

/**
 * 再保合同Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-03-18
 */
@Service
@DataSource(DataSourceType.MASTER)
public class CedeoutContractServiceImpl implements ICedeoutContractService
{
    @Autowired
    private CedeoutContractMapper cedeoutContractMapper;
    
    @Autowired
    private CedeoutContractApplyMapper cedeoutContractApplyMapper;
    
    @Autowired
    private CedeoutContractTrackMapper cedeoutContractTrackMapper;
    
    @Autowired
    private CedeoutContractLiabilityMapper cedeoutContractLiabilityMapper;
    
    @Autowired
    private CedeoutContractLiabilityApplyMapper cedeoutContractLiabilityApplyMapper;
    
    @Autowired
    private CedeoutContractLiabilityTrackMapper cedeoutContractLiabilityTrackMapper;

    @Autowired
    private CedeoutProgrammeLiabilityMapper cedeoutProgrammeLiabilityMapper;

    /**
     * 查询再保合同
     * 
     * @param id 再保合同主键
     * @return 再保合同
     */
    @Override
    public CedeoutContractDTO selectCedeoutContractById(Long id) {
    	CedeoutContractEntity cedeoutContractEntity = cedeoutContractMapper.selectCedeoutContractById(id);
        CedeoutContractDTO dto = ReinsuObjectUtil.convertModel(cedeoutContractEntity, CedeoutContractDTO.class);
        return dto;
    }

    @Override
    public List<CedeoutContractDTO> selectCedeoutContractCodeList() {
        return cedeoutContractMapper.selectCedeoutContractCodeList();
    }

    /**
     * 查询再保合同列表
     * 
     * @param cedeoutContractQuery 再保合同
     * @return 再保合同
     */
    @Override
    public List<CedeoutContractDTO> selectCedeoutContractList(CedeoutContractQuery cedeoutContractQuery)
    {
    	List<CedeoutContractEntity> cedeoutContractEntitys = cedeoutContractMapper.selectCedeoutContractList(cedeoutContractQuery);
    	return ReinsuObjectUtil.convertList(cedeoutContractEntitys, CedeoutContractDTO.class);
    }

    /**
     * 新增再保合同
     * 
     * @param cedeoutContractDTO 再保合同
     * @return 结果
     */
    @Override
    public int insertCedeoutContract(CedeoutContractDTO cedeoutContractDTO) {
        CedeoutContractEntity entity = cedeoutContractMapper.selectCedeoutContractByCode(cedeoutContractDTO.getContractCode());
        if(entity != null){
            return -1;
        }
    	CedeoutContractEntity cedeoutContractEntity = ReinsuObjectUtil.convertModel(cedeoutContractDTO, CedeoutContractEntity.class);
        if(cedeoutContractDTO.getContractType() == CedeoutEnums.再保合同信息维护_主合同.getValue()){
            cedeoutContractEntity.setMainContractId(null);
            cedeoutContractEntity.setMainContractCode(null);
        }else{
            CedeoutContractEntity contractEntity = cedeoutContractMapper.selectCedeoutContractById(Long.valueOf(cedeoutContractDTO.getMainContractId()));
            cedeoutContractEntity.setMainContractId(contractEntity.getId());
            cedeoutContractEntity.setMainContractCode(contractEntity.getContractCode());
        }
        String batchNo = DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN) + RandomUtil.randomNumbers(5);
        cedeoutContractEntity.setBatchNo(batchNo);
        cedeoutContractEntity.setVersion(Long.valueOf(1));
        cedeoutContractEntity.setIsDel(CedeoutEnums.未删除.getValue());
        cedeoutContractEntity.setStatus(CedeoutEnums.状态_有效.getValue());
        cedeoutContractEntity.setCreateBy(SecurityUtils.getUsername());
        cedeoutContractEntity.setUpdateBy(SecurityUtils.getUsername());
        cedeoutContractEntity.setCreateTime(DateUtils.getNowDate());
        cedeoutContractEntity.setUpdateTime(DateUtils.getNowDate());
        return cedeoutContractMapper.insertCedeoutContract(cedeoutContractEntity);
    }

    /**
     * 修改再保合同
     * 
     * @param cedeoutContractDTO 再保合同
     * @return 结果
     */
    @Override
    public int updateCedeoutContract(CedeoutContractDTO cedeoutContractDTO) {
        //判断方案险种关联 合同
        if(cedeoutContractDTO.getStatus() == CedeoutEnums.状态_无效.getValue()){
            List<CedeoutProgrammeLiabilityEntity> list1 = cedeoutProgrammeLiabilityMapper.selectCedeoutProgrammeLiabilityListByContractCode(cedeoutContractDTO.getContractCode());
            if(!CollectionUtils.isEmpty(list1)){
                return -5;
            }
        }
        CedeoutContractEntity entity = cedeoutContractMapper.selectCedeoutContractById(cedeoutContractDTO.getId());
        if(entity.getContractType() == CedeoutEnums.再保合同信息维护_主合同.getValue()){
            if(cedeoutContractDTO.getContractType() == CedeoutEnums.再保合同信息维护_补充协议.getValue()){
                CedeoutContractQuery query = new CedeoutContractQuery();
                query.setMainContractId(cedeoutContractDTO.getMainContractId());
                List<CedeoutContractEntity> list = cedeoutContractMapper.selectCedeoutContractList(query);
                if(!CollectionUtils.isEmpty(list)){
                    return -2;
                }
                if(entity.getId() == cedeoutContractDTO.getMainContractId()){
                    return -3;
                }
            }
        }

        if(!entity.getContractCode().equals(cedeoutContractDTO.getContractCode())){
            CedeoutContractEntity entity1 = cedeoutContractMapper.selectCedeoutContractByCode(cedeoutContractDTO.getContractCode());
            if(entity1 != null){
                return -1;
            }
            CedeoutContractLiabilityQuery query1 = new CedeoutContractLiabilityQuery();
            query1.setContractCode(entity.getContractCode());
            List<CedeoutContractLiabilityEntity> liabilityEntityList = cedeoutContractLiabilityMapper.selectCedeoutContractLiabilityList(query1);
            if(!CollectionUtils.isEmpty(liabilityEntityList)){
                return -4;
            }
        }
    	CedeoutContractEntity cedeoutContractEntity = ReinsuObjectUtil.convertModel(cedeoutContractDTO, CedeoutContractEntity.class);
        cedeoutContractEntity.setUpdateBy(SecurityUtils.getUsername());
        cedeoutContractEntity.setUpdateTime(DateUtils.getNowDate());
        if(cedeoutContractEntity.getContractType() == CedeoutEnums.再保合同信息维护_主合同.getValue()){
            cedeoutContractDTO.setMainContractId(null);
            cedeoutContractEntity.setMainContractCode(null);
        }
        return cedeoutContractMapper.updateCedeoutContract(cedeoutContractEntity);
    }

    /**
     * 批量删除再保合同
     * 
     * @param ids 需要删除的再保合同主键
     * @return 结果
     */
    @Override
    public int deleteCedeoutContractByIds(Long[] ids) {
        return cedeoutContractMapper.deleteCedeoutContractByIds(ids);
    }

    /**
     * 删除再保合同信息
     * 
     * @param id 再保合同主键
     * @return 结果
     */
    @Override
    public int deleteCedeoutContractById(Long id) {
        CedeoutContractEntity entity = cedeoutContractMapper.selectCedeoutContractById(id);
        CedeoutContractQuery query = new CedeoutContractQuery();
        query.setMainContractCode(entity.getContractCode());
        List<CedeoutContractEntity> list = cedeoutContractMapper.selectCedeoutContractList(query);
        if(!CollectionUtils.isEmpty(list)){
            return -1;
        }

        CedeoutContractLiabilityQuery query1 = new CedeoutContractLiabilityQuery();
        query1.setContractCode(entity.getContractCode());
        List<CedeoutContractLiabilityEntity> liabilityEntityList = cedeoutContractLiabilityMapper.selectCedeoutContractLiabilityList(query1);
        if(!CollectionUtils.isEmpty(liabilityEntityList)){
            return -2;
        }

        //判断方案险种关联 合同
        List<CedeoutProgrammeLiabilityEntity> list1 = cedeoutProgrammeLiabilityMapper.selectCedeoutProgrammeLiabilityListByContractCode(entity.getContractCode());
        if(!CollectionUtils.isEmpty(list1)){
            return -3;
        }


        return cedeoutContractMapper.deleteCedeoutContractById(id);
    }
    
    /**
     * 查询再保合同申请
     * 
     * @param id 再保合同申请主键
     * @return 再保合同申请
     */
    @Override
    public CedeoutContractApplyDTO selectCedeoutContractApplyById(Long id)
    {
    	CedeoutContractApplyEntity cedeoutContractApplyEntity = cedeoutContractApplyMapper.selectCedeoutContractApplyById(id);
        return ReinsuObjectUtil.convertModel(cedeoutContractApplyEntity, CedeoutContractApplyDTO.class);
    }

    /**
     * 查询再保合同申请列表
     * 
     * @param cedeoutContractApplyQuery 再保合同申请
     * @return 再保合同申请
     */
    @Override
    public List<CedeoutContractApplyDTO> selectCedeoutContractApplyList(CedeoutContractApplyQuery cedeoutContractApplyQuery)
    {
    	List<CedeoutContractApplyEntity> cedeoutContractApplyEntitys = cedeoutContractApplyMapper.selectCedeoutContractApplyList(cedeoutContractApplyQuery);
    	return ReinsuObjectUtil.convertList(cedeoutContractApplyEntitys, CedeoutContractApplyDTO.class);
    }

    /**
     * 新增再保合同申请
     * 
     * @param cedeoutContractApplyDTO 再保合同申请
     * @return 结果
     */
    @Override
    public int insertCedeoutContractApply(CedeoutContractApplyDTO cedeoutContractApplyDTO)
    {
    	CedeoutContractApplyEntity cedeoutContractApplyEntity = ReinsuObjectUtil.convertModel(cedeoutContractApplyDTO, CedeoutContractApplyEntity.class);
        if(cedeoutContractApplyEntity.getCreateTime() == null) {
        	cedeoutContractApplyEntity.setCreateTime(DateUtils.getNowDate());
        }
        return cedeoutContractApplyMapper.insertCedeoutContractApply(cedeoutContractApplyEntity);
    }

    /**
     * 修改再保合同申请
     * 
     * @param cedeoutContractApplyDTO 再保合同申请
     * @return 结果
     */
    @Override
    public int updateCedeoutContractApply(CedeoutContractApplyDTO cedeoutContractApplyDTO)
    {
    	CedeoutContractApplyEntity cedeoutContractApplyEntity = ReinsuObjectUtil.convertModel(cedeoutContractApplyDTO, CedeoutContractApplyEntity.class);
        if(cedeoutContractApplyEntity.getUpdateTime() == null) {
        	cedeoutContractApplyEntity.setUpdateTime(DateUtils.getNowDate());
        }
        return cedeoutContractApplyMapper.updateCedeoutContractApply(cedeoutContractApplyEntity);
    }

    /**
     * 批量删除再保合同申请
     * 
     * @param ids 需要删除的再保合同申请主键
     * @return 结果
     */
    @Override
    public int deleteCedeoutContractApplyByIds(Long[] ids)
    {
        return cedeoutContractApplyMapper.deleteCedeoutContractApplyByIds(ids);
    }

    /**
     * 删除再保合同申请信息
     * 
     * @param id 再保合同申请主键
     * @return 结果
     */
    @Override
    public int deleteCedeoutContractApplyById(Long id)
    {
        return cedeoutContractApplyMapper.deleteCedeoutContractApplyById(id);
    }
    
    /**
     * 查询再保合同轨迹
     * 
     * @param trackId 再保合同轨迹主键
     * @return 再保合同轨迹
     */
    @Override
    public CedeoutContractTrackDTO selectCedeoutContractTrackByTrackId(Long trackId)
    {
        CedeoutContractTrackEntity cedeoutContractTrackEntity = cedeoutContractTrackMapper.selectCedeoutContractTrackByTrackId(trackId);
        return ReinsuObjectUtil.convertModel(cedeoutContractTrackEntity, CedeoutContractTrackDTO.class);
    }

    /**
     * 查询再保合同轨迹列表
     * 
     * @param cedeoutContractTrackQuery 再保合同轨迹
     * @return 再保合同轨迹
     */
    @Override
    public List<CedeoutContractTrackDTO> selectCedeoutContractTrackList(CedeoutContractTrackQuery cedeoutContractTrackQuery)
    {
    	List<CedeoutContractTrackEntity> cedeoutContractTrackEntitys = cedeoutContractTrackMapper.selectCedeoutContractTrackList(cedeoutContractTrackQuery);
    	return ReinsuObjectUtil.convertList(cedeoutContractTrackEntitys, CedeoutContractTrackDTO.class);
    }

    /**
     * 新增再保合同轨迹
     * 
     * @param cedeoutContractTrackDTO 再保合同轨迹
     * @return 结果
     */
    @Override
    public int insertCedeoutContractTrack(CedeoutContractTrackDTO cedeoutContractTrackDTO)
    {
    	CedeoutContractTrackEntity tCedeoutContractTrackEntity = ReinsuObjectUtil.convertModel(cedeoutContractTrackDTO, CedeoutContractTrackEntity.class);
        if(tCedeoutContractTrackEntity.getCreateTime() == null) {
        	tCedeoutContractTrackEntity.setCreateTime(DateUtils.getNowDate());
        }
        return cedeoutContractTrackMapper.insertCedeoutContractTrack(tCedeoutContractTrackEntity);
    }

    /**
     * 修改再保合同轨迹
     * 
     * @param cedeoutContractTrackDTO 再保合同轨迹
     * @return 结果
     */
    @Override
    public int updateCedeoutContractTrack(CedeoutContractTrackDTO cedeoutContractTrackDTO)
    {
    	CedeoutContractTrackEntity tCedeoutContractTrackEntity = ReinsuObjectUtil.convertModel(cedeoutContractTrackDTO, CedeoutContractTrackEntity.class);
        if(tCedeoutContractTrackEntity.getUpdateTime() == null) {
        	tCedeoutContractTrackEntity.setUpdateTime(DateUtils.getNowDate());
        }
        return cedeoutContractTrackMapper.updateCedeoutContractTrack(tCedeoutContractTrackEntity);
    }

    /**
     * 批量删除再保合同轨迹
     * 
     * @param trackIds 需要删除的再保合同轨迹主键
     * @return 结果
     */
    @Override
    public int deleteCedeoutContractTrackByTrackIds(Long[] trackIds)
    {
        return cedeoutContractTrackMapper.deleteCedeoutContractTrackByTrackIds(trackIds);
    }

    /**
     * 删除再保合同轨迹信息
     * 
     * @param trackId 再保合同轨迹主键
     * @return 结果
     */
    @Override
    public int deleteCedeoutContractTrackByTrackId(Long trackId)
    {
        return cedeoutContractTrackMapper.deleteCedeoutContractTrackByTrackId(trackId);
    }
    
    /**
     * 查询再保合同责任
     * 
     * @param id 再保合同责任主键
     * @return 再保合同责任
     */
    @Override
    public CedeoutContractLiabilityDTO selectCedeoutContractLiabilityById(Long id)
    {
    	CedeoutContractLiabilityEntity cedeoutContractLiabilityEntity = cedeoutContractLiabilityMapper.selectCedeoutContractLiabilityById(id);
    	return ReinsuObjectUtil.convertModel(cedeoutContractLiabilityEntity, CedeoutContractLiabilityDTO.class);
    }

    /**
     * 查询再保合同责任列表
     * 
     * @param cedeoutContractLiabilityQuery 再保合同责任
     * @return 再保合同责任
     */
    @Override
    public List<CedeoutContractLiabilityDTO> selectCedeoutContractLiabilityList(CedeoutContractLiabilityQuery cedeoutContractLiabilityQuery) {
        List<CedeoutContractLiabilityDTO> dtoList = new ArrayList<>();
    	List<CedeoutContractLiabilityEntity> cedeoutContractLiabilityEntitys = cedeoutContractLiabilityMapper.selectCedeoutContractLiabilityList(cedeoutContractLiabilityQuery);
    	if(!CollectionUtils.isEmpty(cedeoutContractLiabilityEntitys)){
            dtoList = ReinsuObjectUtil.convertList(cedeoutContractLiabilityEntitys, CedeoutContractLiabilityDTO.class);
            for (CedeoutContractLiabilityDTO dto :dtoList) {
                CedeoutContractEntity entity = cedeoutContractMapper.selectCedeoutContractByCode(dto.getContractCode());
                dto.setContractName(entity == null ? null : entity.getContractName());
            }
        }
    	return dtoList;
    }

    /**
     * 新增再保合同责任
     * 
     * @param cedeoutContractLiabilityDTO 再保合同责任
     * @return 结果
     */
    @Override
    public int insertCedeoutContractLiability(CedeoutContractLiabilityDTO cedeoutContractLiabilityDTO) {
    	CedeoutContractLiabilityEntity cedeoutContractLiabilityEntity = ReinsuObjectUtil.convertModel(cedeoutContractLiabilityDTO, CedeoutContractLiabilityEntity.class);
        String batchNo = DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN) + RandomUtil.randomNumbers(5);
        cedeoutContractLiabilityEntity.setBatchNo(batchNo);
        cedeoutContractLiabilityEntity.setVersion(Long.valueOf(1));
        cedeoutContractLiabilityEntity.setIsDel(CedeoutEnums.未删除.getValue());
        cedeoutContractLiabilityEntity.setStatus(CedeoutEnums.状态_有效.getValue());
        cedeoutContractLiabilityEntity.setCreateBy(SecurityUtils.getUsername());
        cedeoutContractLiabilityEntity.setUpdateBy(SecurityUtils.getUsername());
        cedeoutContractLiabilityEntity.setCreateTime(DateUtils.getNowDate());
        cedeoutContractLiabilityEntity.setUpdateTime(DateUtils.getNowDate());
        return cedeoutContractLiabilityMapper.insertCedeoutContractLiability(cedeoutContractLiabilityEntity);
    }

    /**
     * 修改再保合同责任
     * 
     * @param cedeoutContractLiabilityDTO 再保合同责任
     * @return 结果
     */
    @Override
    public int updateCedeoutContractLiability(CedeoutContractLiabilityDTO cedeoutContractLiabilityDTO) {
    	CedeoutContractLiabilityEntity cedeoutContractLiabilityEntity = ReinsuObjectUtil.convertModel(cedeoutContractLiabilityDTO, CedeoutContractLiabilityEntity.class);
        CedeoutContractLiabilityEntity entity = cedeoutContractLiabilityMapper.selectCedeoutContractLiabilityById(cedeoutContractLiabilityDTO.getId());
        if(!entity.getRiskCode().equals(cedeoutContractLiabilityDTO.getRiskCode())
                || !entity.getLiabilityCode().equals(cedeoutContractLiabilityDTO.getLiabilityCode())){
            CedeoutProgrammeLiabilityQuery query = new CedeoutProgrammeLiabilityQuery();
            query.setRiskCode(cedeoutContractLiabilityDTO.getRiskCode());
            query.setLiabilityCode(cedeoutContractLiabilityDTO.getLiabilityCode());
            query.setContractCode(cedeoutContractLiabilityDTO.getContractCode());
            List<CedeoutProgrammeLiabilityEntity> list = cedeoutProgrammeLiabilityMapper.selectCedeoutProgrammeLiabilityList(query);
            if(!CollectionUtils.isEmpty(list)){
                return -2;
            }
        }
    	cedeoutContractLiabilityEntity.setUpdateBy(SecurityUtils.getUsername());
        cedeoutContractLiabilityEntity.setUpdateTime(DateUtils.getNowDate());
        return cedeoutContractLiabilityMapper.updateCedeoutContractLiability(cedeoutContractLiabilityEntity);
    }

    /**
     * 批量删除再保合同责任
     * 
     * @param ids 需要删除的再保合同责任主键
     * @return 结果
     */
    @Override
    public int deleteCedeoutContractLiabilityByIds(Long[] ids)
    {
        return cedeoutContractLiabilityMapper.deleteCedeoutContractLiabilityByIds(ids);
    }

    /**
     * 删除再保合同责任信息
     * 
     * @param id 再保合同责任主键
     * @return 结果
     */
    @Override
    public int deleteCedeoutContractLiabilityById(Long id) {
        CedeoutContractLiabilityEntity entity = cedeoutContractLiabilityMapper.selectCedeoutContractLiabilityById(id);
        CedeoutProgrammeLiabilityQuery query = new CedeoutProgrammeLiabilityQuery();
        query.setRiskCode(entity.getRiskCode());
        query.setLiabilityCode(entity.getLiabilityCode());
        query.setContractCode(entity.getContractCode());
        List<CedeoutProgrammeLiabilityEntity> list = cedeoutProgrammeLiabilityMapper.selectCedeoutProgrammeLiabilityList(query);
        if(!CollectionUtils.isEmpty(list)){
            return -2;
        }
        return cedeoutContractLiabilityMapper.deleteCedeoutContractLiabilityById(id);
    }
    
    /**
     * 查询再保合同责任申请
     * 
     * @param id 再保合同责任申请主键
     * @return 再保合同责任申请
     */
    @Override
    public CedeoutContractLiabilityApplyDTO selectCedeoutContractLiabilityApplyById(Long id)
    {
    	CedeoutContractLiabilityApplyEntity cedeoutContractLiabilityApplyEntity = cedeoutContractLiabilityApplyMapper.selectCedeoutContractLiabilityApplyById(id);
    	return ReinsuObjectUtil.convertModel(cedeoutContractLiabilityApplyEntity, CedeoutContractLiabilityApplyDTO.class);
    }

    /**
     * 查询再保合同责任申请列表
     * 
     * @param cedeoutContractLiabilityApplyQuery 再保合同责任申请
     * @return 再保合同责任申请
     */
    @Override
    public List<CedeoutContractLiabilityApplyDTO> selectCedeoutContractLiabilityApplyList(CedeoutContractLiabilityApplyQuery cedeoutContractLiabilityApplyQuery)
    {
    	List<CedeoutContractLiabilityApplyEntity> cedeoutContractLiabilityApplyEntitys = cedeoutContractLiabilityApplyMapper.selectCedeoutContractLiabilityApplyList(cedeoutContractLiabilityApplyQuery);
    	return ReinsuObjectUtil.convertList(cedeoutContractLiabilityApplyEntitys, CedeoutContractLiabilityApplyDTO.class);
    }

    /**
     * 新增再保合同责任申请
     * 
     * @param cedeoutContractLiabilityApplyDTO 再保合同责任申请
     * @return 结果
     */
    @Override
    public int insertCedeoutContractLiabilityApply(CedeoutContractLiabilityApplyDTO cedeoutContractLiabilityApplyDTO)
    {
    	CedeoutContractLiabilityApplyEntity cedeoutContractLiabilityApplyEntity = ReinsuObjectUtil.convertModel(cedeoutContractLiabilityApplyDTO, CedeoutContractLiabilityApplyEntity.class);
        if(cedeoutContractLiabilityApplyEntity.getCreateTime() == null) {
        	cedeoutContractLiabilityApplyEntity.setCreateTime(DateUtils.getNowDate());
        }
        return cedeoutContractLiabilityApplyMapper.insertCedeoutContractLiabilityApply(cedeoutContractLiabilityApplyEntity);
    }

    /**
     * 修改再保合同责任申请
     * 
     * @param cedeoutContractLiabilityApplyDTO 再保合同责任申请
     * @return 结果
     */
    @Override
    public int updateCedeoutContractLiabilityApply(CedeoutContractLiabilityApplyDTO cedeoutContractLiabilityApplyDTO)
    {
    	CedeoutContractLiabilityApplyEntity cedeoutContractLiabilityApplyEntity = ReinsuObjectUtil.convertModel(cedeoutContractLiabilityApplyDTO, CedeoutContractLiabilityApplyEntity.class);
        if(cedeoutContractLiabilityApplyEntity.getUpdateTime() == null) {
        	cedeoutContractLiabilityApplyEntity.setUpdateTime(DateUtils.getNowDate());
        }
        return cedeoutContractLiabilityApplyMapper.updateCedeoutContractLiabilityApply(cedeoutContractLiabilityApplyEntity);
    }

    /**
     * 批量删除再保合同责任申请
     * 
     * @param ids 需要删除的再保合同责任申请主键
     * @return 结果
     */
    @Override
    public int deleteCedeoutContractLiabilityApplyByIds(Long[] ids)
    {
        return cedeoutContractLiabilityApplyMapper.deleteCedeoutContractLiabilityApplyByIds(ids);
    }

    /**
     * 删除再保合同责任申请信息
     * 
     * @param id 再保合同责任申请主键
     * @return 结果
     */
    @Override
    public int deleteCedeoutContractLiabilityApplyById(Long id)
    {
        return cedeoutContractLiabilityApplyMapper.deleteCedeoutContractLiabilityApplyById(id);
    }
    
    /**
     * 查询再保合同责任轨迹
     * 
     * @param trackId 再保合同责任轨迹主键
     * @return 再保合同责任轨迹
     */
    @Override
    public CedeoutContractLiabilityTrackDTO selectCedeoutContractLiabilityTrackByTrackId(Long trackId)
    {
    	CedeoutContractLiabilityTrackEntity cedeoutContractLiabilityTrackEntity = cedeoutContractLiabilityTrackMapper.selectCedeoutContractLiabilityTrackByTrackId(trackId);
    	return ReinsuObjectUtil.convertModel(cedeoutContractLiabilityTrackEntity, CedeoutContractLiabilityTrackDTO.class);
    }

    /**
     * 查询再保合同责任轨迹列表
     * 
     * @param cedeoutContractLiabilityTrackQuery 再保合同责任轨迹
     * @return 再保合同责任轨迹
     */
    @Override
    public List<CedeoutContractLiabilityTrackDTO> selectCedeoutContractLiabilityTrackList(CedeoutContractLiabilityTrackQuery cedeoutContractLiabilityTrackQuery)
    {
    	List<CedeoutContractLiabilityTrackEntity> cedeoutContractLiabilityTrackEntitys = cedeoutContractLiabilityTrackMapper.selectCedeoutContractLiabilityTrackList(cedeoutContractLiabilityTrackQuery);
    	return ReinsuObjectUtil.convertList(cedeoutContractLiabilityTrackEntitys, CedeoutContractLiabilityTrackDTO.class);
    }

    /**
     * 新增再保合同责任轨迹
     * 
     * @param cedeoutContractLiabilityTrackDTO 再保合同责任轨迹
     * @return 结果
     */
    @Override
    public int insertCedeoutContractLiabilityTrack(CedeoutContractLiabilityTrackDTO cedeoutContractLiabilityTrackDTO)
    {
    	CedeoutContractLiabilityTrackEntity cedeoutContractLiabilityTrackEntity = ReinsuObjectUtil.convertModel(cedeoutContractLiabilityTrackDTO, CedeoutContractLiabilityTrackEntity.class);
        if(cedeoutContractLiabilityTrackEntity.getCreateTime() == null) {
        	cedeoutContractLiabilityTrackEntity.setCreateTime(DateUtils.getNowDate());
        }
        return cedeoutContractLiabilityTrackMapper.insertCedeoutContractLiabilityTrack(cedeoutContractLiabilityTrackEntity);
    }

    /**
     * 修改再保合同责任轨迹
     * 
     * @param cedeoutContractLiabilityTrackDTO 再保合同责任轨迹
     * @return 结果
     */
    @Override
    public int updateCedeoutContractLiabilityTrack(CedeoutContractLiabilityTrackDTO cedeoutContractLiabilityTrackDTO)
    {
    	CedeoutContractLiabilityTrackEntity cedeoutContractLiabilityTrackEntity = ReinsuObjectUtil.convertModel(cedeoutContractLiabilityTrackDTO, CedeoutContractLiabilityTrackEntity.class);
        if(cedeoutContractLiabilityTrackEntity.getUpdateTime() == null) {
        	cedeoutContractLiabilityTrackEntity.setUpdateTime(DateUtils.getNowDate());
        }
        return cedeoutContractLiabilityTrackMapper.updateCedeoutContractLiabilityTrack(cedeoutContractLiabilityTrackEntity);
    }

    /**
     * 批量删除再保合同责任轨迹
     * 
     * @param trackIds 需要删除的再保合同责任轨迹主键
     * @return 结果
     */
    @Override
    public int deleteCedeoutContractLiabilityTrackByTrackIds(Long[] trackIds)
    {
        return cedeoutContractLiabilityTrackMapper.deleteCedeoutContractLiabilityTrackByTrackIds(trackIds);
    }

    /**
     * 删除再保合同责任轨迹信息
     * 
     * @param trackId 再保合同责任轨迹主键
     * @return 结果
     */
    @Override
    public int deleteCedeoutContractLiabilityTrackByTrackId(Long trackId)
    {
        return cedeoutContractLiabilityTrackMapper.deleteCedeoutContractLiabilityTrackByTrackId(trackId);
    }

	@Override
	public List<DwsEastZbhtxxbDTO> selectOriginalContractAsEastZbhtxxb(CedeoutContractQuery cedeoutContractQuery) {
		return cedeoutContractMapper.selectOriginalContractAsEastZbhtxxb(cedeoutContractQuery);
	}
	
	@Override
	public List<DwsEastZbcpxxbDTO> selectContractLiabilityAsEastZbcpxxb(CedeoutContractLiabilityQuery cedeoutContractLiabilityQuery) {
		return cedeoutContractLiabilityMapper.selectContractLiabilityAsEastZbcpxxb(cedeoutContractLiabilityQuery);
	}

    @Override
    public List<DwsPrpInsureContDTO> selectOriginalContractAsPrpInsureCont(CedeoutContractQuery cedeoutContractQuery) {
        return cedeoutContractMapper.selectOriginalContractAsPrpInsureCont(cedeoutContractQuery);
    }

    @Override
    public List<DwsPrpProductDTO> selectContractLiabilityAsPrpProduct(CedeoutContractLiabilityQuery cedeoutContractLiabilityQuery) {
        return cedeoutContractLiabilityMapper.selectContractLiabilityAsPrpProduct(cedeoutContractLiabilityQuery);
    }
}
