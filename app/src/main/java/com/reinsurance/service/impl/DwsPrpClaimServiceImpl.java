package com.reinsurance.service.impl;

import java.util.List;
import java.util.Arrays;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.jd.lightning.common.annotation.DataSource;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.enums.DataSourceType;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.DateUtils;
import com.jd.lightning.common.utils.SecurityUtils;
import com.jd.lightning.common.utils.poi.ExcelUtil;
import com.jd.lightning.common.utils.StringUtils;
import com.jd.lightning.common.constant.HttpStatus;

import com.reinsurance.mapper.DwsPrpClaimMapper;
import com.reinsurance.dto.DwsPrpClaimDTO;
import com.reinsurance.query.DwsPrpClaimQuery;
import com.reinsurance.domain.DwsPrpClaimEntity;
import com.reinsurance.utils.ReinsuObjectUtil;
import com.reinsurance.service.IDwsPrpClaimService;
import com.reinsurance.service.IDwsRegulatoryReportService;
import com.reinsurance.enums.ReportDataSource;
import com.reinsurance.enums.ReportPushStatus;
import com.reinsurance.enums.RegulatorReport;
import com.reinsurance.enums.PrpReport;

import lombok.extern.slf4j.Slf4j;

/**
 * 再保理赔险种明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Slf4j
@Service
@DataSource(DataSourceType.SLAVE)
public class DwsPrpClaimServiceImpl implements IDwsPrpClaimService {
    
    @Autowired
    private DwsPrpClaimMapper dwsPrpClaimMapper;

    @Autowired
    private IDwsRegulatoryReportService dwsRegulatoryReportService;

    /**
     * 查询再保理赔险种明细
     *
     * @param Id 再保理赔险种明细主键
     * @return 再保理赔险种明细
     */
    @Override
    public DwsPrpClaimDTO selectDwsPrpClaimById(Long Id) {
        DwsPrpClaimEntity entity = dwsPrpClaimMapper.selectDwsPrpClaimById(Id);
        return ReinsuObjectUtil.convertModel(entity, DwsPrpClaimDTO.class);
    }

    /**
     * 查询再保理赔险种明细列表
     *
     * @param dwsPrpClaimQuery 再保理赔险种明细
     * @return 再保理赔险种明细
     */
    @Override
    public List<DwsPrpClaimDTO> selectDwsPrpClaimList(DwsPrpClaimQuery dwsPrpClaimQuery) {
        List<DwsPrpClaimEntity> entityList = dwsPrpClaimMapper.selectDwsPrpClaimList(dwsPrpClaimQuery);
        return ReinsuObjectUtil.convertList(entityList, DwsPrpClaimDTO.class);
    }

    /**
     * 新增再保理赔险种明细
     *
     * @param dwsPrpClaimDTO 再保理赔险种明细
     * @return 结果
     */
    @Override
    public int insertDwsPrpClaim(DwsPrpClaimDTO dwsPrpClaimDTO) {
        DwsPrpClaimEntity dwsPrpClaimEntity = ReinsuObjectUtil.convertModel(dwsPrpClaimDTO, DwsPrpClaimEntity.class);
        dwsPrpClaimEntity.setCreateTime(DateUtils.getNowDate());
        return dwsPrpClaimMapper.insertDwsPrpClaim(dwsPrpClaimEntity);
    }

    /**
     * 批量新增再保理赔险种明细
     *
     * @param dwsPrpClaimList 再保理赔险种明细列表
     * @return 结果
     */
    @Override
    public int insertBatchDwsPrpClaim(List<DwsPrpClaimDTO> dwsPrpClaimList) {
        List<DwsPrpClaimEntity> entitys = ReinsuObjectUtil.convertList(dwsPrpClaimList, DwsPrpClaimEntity.class);
        return dwsPrpClaimMapper.insertBatchDwsPrpClaim(entitys);
    }

    /**
     * 修改再保理赔险种明细
     *
     * @param dwsPrpClaimDTO 再保理赔险种明细
     * @return 结果
     */
    @Override
    public int updateDwsPrpClaim(DwsPrpClaimDTO dwsPrpClaimDTO) {
        DwsPrpClaimEntity dwsPrpClaimEntity = ReinsuObjectUtil.convertModel(dwsPrpClaimDTO, DwsPrpClaimEntity.class);
        dwsPrpClaimEntity.setUpdateTime(DateUtils.getNowDate());
        return dwsPrpClaimMapper.updateDwsPrpClaim(dwsPrpClaimEntity);
    }

    /**
     * 批量删除再保理赔险种明细
     *
     * @param Ids 需要删除的再保理赔险种明细主键
     * @return 结果
     */
    @Override
    public int deleteDwsPrpClaimByIds(Long[] Ids) {
        return dwsPrpClaimMapper.deleteDwsPrpClaimByIds(Ids);
    }

    /**
     * 删除再保理赔险种明细信息
     *
     * @param Id 再保理赔险种明细主键
     * @return 结果
     */
    @Override
    public int deleteDwsPrpClaimById(Long Id) {
        DwsPrpClaimEntity entity = dwsPrpClaimMapper.selectDwsPrpClaimById(Id);
        if(entity == null) {
            return 0;
        }
        if(ReportDataSource.系统.getCode() == entity.getDataSource()) {
            return -1;
        }
        if(ReportPushStatus.已推送.getCode() == entity.getPushStatus()) {
            return -2;
        }
        int deleteRows = dwsPrpClaimMapper.deleteDwsPrpClaimById(Id);
        if(deleteRows > 0) {
            dwsRegulatoryReportService.insertOrUpdateRegulatoryReport(BusinessType.DELETE, RegulatorReport.保单登记数据报送, PrpReport.LRClaim.getCode(), Arrays.asList(entity.getPolYear()));
        }
        return deleteRows;
    }

    /**
     * 导入再保理赔险种明细
     *
     * @param companyCode 保险机构代码
     * @param companyName 保险机构名称
     * @param manageCom 管理机构
     * @param file 导入文件
     * @return 结果
     */
    @Override
    public Result importDwsPrpClaim(String companyCode, String companyName, String manageCom, MultipartFile file) {
        try {
            // 1. 解析Excel文件
            ExcelUtil<DwsPrpClaimDTO> util = new ExcelUtil<>(DwsPrpClaimDTO.class);
            List<DwsPrpClaimDTO> claimList = util.importExcel(file.getInputStream());
            
            if (StringUtils.isNull(claimList) || claimList.size() == 0) {
                return Result.error("导入数据不能为空！");
            }
            
            // 2. 数据校验和处理
            for (DwsPrpClaimDTO dto : claimList) {
                dto.setCompanyCode(companyCode);
                dto.setDataSource(ReportDataSource.人工.getCode());
                dto.setPushStatus(ReportPushStatus.未推送.getCode());
                dto.setCreateBy(SecurityUtils.getUsername());
                dto.setCreateTime(DateUtils.getNowDate());
            }
            
            // 3. 批量入库
            List<DwsPrpClaimEntity> dwsPrpClaimList = ReinsuObjectUtil.convertList(claimList, DwsPrpClaimEntity.class);
            int insertRows = dwsPrpClaimMapper.insertBatchDwsPrpClaim(dwsPrpClaimList);
            if(insertRows > 0) {
                // 4. 更新监管报表推送状态
                List<Integer> reportYears = claimList.stream().map(DwsPrpClaimDTO::getPolYear).distinct().collect(Collectors.toList());
                dwsRegulatoryReportService.insertOrUpdateRegulatoryReport(BusinessType.IMPORT, RegulatorReport.保单登记数据报送, PrpReport.LRClaim.getCode(), reportYears);
            }
            return Result.success("成功导入" + insertRows + "行", HttpStatus.HTTP_OK);
        }catch(Exception e) {
            log.error("再保导入理赔险种明细出错, 错误原因:", e);
            return Result.error("导入理赔险种明细表出错，请联系管理员。");
        }
    }

    /**
     * 导出再保理赔险种明细
     *
     * @param response 响应对象
     * @param dwsPrpClaimQuery 查询条件
     */
    @Override
    public void exportDwsPrpClaim(HttpServletResponse response, DwsPrpClaimQuery dwsPrpClaimQuery) {
        List<DwsPrpClaimDTO> list = selectDwsPrpClaimList(dwsPrpClaimQuery);
        ExcelUtil<DwsPrpClaimDTO> util = new ExcelUtil<>(DwsPrpClaimDTO.class);
        util.exportExcel(response, list, "再保理赔险种明细数据");
    }

    /**
     * 检查再保理赔险种明细是否存在
     *
     * @param dwsPrpClaimQuery 查询条件
     * @return 结果
     */
    @Override
    public int selectDwsPrpClaimExists(DwsPrpClaimQuery dwsPrpClaimQuery) {
        return dwsPrpClaimMapper.selectDwsPrpClaimExists(dwsPrpClaimQuery);
    }

    /**
     * 更新推送状态
     *
     * @param Ids 主键数组
     * @return 结果
     */
    @Override
    public Result updateDwsPrpClaimPushStatus(Long[] Ids) {
        String pushBy = SecurityUtils.getUsername();
        int updateRows = dwsPrpClaimMapper.updateDwsPrpClaimPushStatus(ReportPushStatus.已推送.getCode(), pushBy, Ids);
        if(updateRows > 0) {
            return Result.success("推送成功");
        }
        return Result.error("推送失败");
    }

    /**
     * 查询年度报表推送状态
     *
     * @param reportYear 报表年份
     * @return 结果
     */
    @Override
    public Integer selectAnnualReportShouldPushStatus(int reportYear) {
        return dwsPrpClaimMapper.selectAnnualReportShouldPushStatus(reportYear);
    }

    /**
     * 生成再保理赔险种明细数据
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param reportYear 报表年份
     * @param reportMonth 报表月份
     * @return 结果
     */
    @Override
    public Result generatePrpClaimData(String startDate, String endDate, Integer reportYear, Integer reportMonth) {
        // TODO: 实现数据生成逻辑
        return Result.success("数据生成功能待实现");
    }
}
