package com.reinsurance.service.impl;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import com.alibaba.excel.util.ListUtils;
import com.jd.lightning.common.annotation.DataSource;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.enums.DataSourceType;
import com.jd.lightning.common.utils.DateUtils;
import com.jd.lightning.common.utils.SecurityUtils;
import com.jd.lightning.common.utils.StringUtils;
import com.jd.lightning.common.utils.poi.ExcelUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.reinsurance.mapper.DwsEastZbcpxxbMapper;
import com.reinsurance.dto.DwsEastZbcpxxbDTO;
import com.reinsurance.enums.CedeoutEnums;
import com.reinsurance.enums.BasicDataEnums.EastCedeoutWay;
import com.reinsurance.enums.BasicDataEnums.EastReport;
import com.reinsurance.enums.BasicDataEnums.RegulatorReport;
import com.reinsurance.enums.BasicDataEnums.RedisKeyModule;
import com.reinsurance.enums.BasicDataEnums.ReportDataSource;
import com.reinsurance.enums.BasicDataEnums.ReportPushStatus;
import com.reinsurance.query.DwsEastZbcpxxbQuery;
import com.reinsurance.domain.DwsEastZbcpxxbEntity;
import com.reinsurance.service.IDwsEastZbcpxxbService;
import com.reinsurance.service.IDwsRegulatoryReportService;
import com.reinsurance.service.IRedisService;
import com.reinsurance.utils.ReDateUtil;
import com.reinsurance.utils.ReinsuObjectUtil;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.HttpStatus;
import lombok.extern.slf4j.Slf4j;

/**
 * East再保产品信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-05
 */
@Slf4j
@Service
@DataSource(DataSourceType.SLAVE)
public class DwsEastZbcpxxbServiceImpl implements IDwsEastZbcpxxbService {
	
	@Autowired
	private IRedisService redisService;
	
    @Autowired
    private DwsEastZbcpxxbMapper dwsEastZbcpxxbMapper;
    
    @Autowired
    private IDwsRegulatoryReportService dwsRegulatoryReportService;
    
    /**
     * 查询East再保产品信息
     *
     * @param id East再保产品信息主键
     * @return East再保产品信息
     */
    @Override
    public DwsEastZbcpxxbDTO selectDwsEastZbcpxxbById(Long id) {
        DwsEastZbcpxxbEntity dwsEastZbcpxxbEntity = dwsEastZbcpxxbMapper.selectDwsEastZbcpxxbById(id);
        return ReinsuObjectUtil.convertModel(dwsEastZbcpxxbEntity, DwsEastZbcpxxbDTO.class);
    }

    /**
     * 查询East再保产品信息列表
     *
     * @param dwsEastZbcpxxbQuery East再保产品信息
     * @return East再保产品信息
     */
    @Override
    public List<DwsEastZbcpxxbDTO> selectDwsEastZbcpxxbList(DwsEastZbcpxxbQuery dwsEastZbcpxxbQuery) {
        List<DwsEastZbcpxxbEntity> list = dwsEastZbcpxxbMapper.selectDwsEastZbcpxxbList(dwsEastZbcpxxbQuery);
        return ReinsuObjectUtil.convertList(list, DwsEastZbcpxxbDTO.class);
    }

    @Override
	public Integer selectAnnualReportShouldPushStatus(int reportYear) {
		return dwsEastZbcpxxbMapper.selectAnnualReportShouldPushStatus(reportYear);
	}

	/**
     * 新增East再保产品信息
     *
     * @param dwsEastZbcpxxbDTO East再保产品信息
     * @return 结果
     */
    @Override
    public int insertDwsEastZbcpxxb(DwsEastZbcpxxbDTO dwsEastZbcpxxbDTO) {
        DwsEastZbcpxxbEntity dwsEastZbcpxxbEntity = ReinsuObjectUtil.convertModel(dwsEastZbcpxxbDTO, DwsEastZbcpxxbEntity.class);
        dwsEastZbcpxxbEntity.setCreateTime(DateUtils.getNowDate());
        return dwsEastZbcpxxbMapper.insertDwsEastZbcpxxb(dwsEastZbcpxxbEntity);
    }

    @Override
	public int insertBatchDwsEastZbcpxxb(List<DwsEastZbcpxxbDTO> dwsEastZbcpxxbList) {
    	List<DwsEastZbcpxxbEntity> entitys = ReinsuObjectUtil.convertList(dwsEastZbcpxxbList, DwsEastZbcpxxbEntity.class);
		return dwsEastZbcpxxbMapper.insertBatchDwsEastZbcpxxb(entitys);
	}

	/**
     * 修改East再保产品信息
     *
     * @param dwsEastZbcpxxbDTO East再保产品信息
     * @return 结果
     */
    @Override
    public int updateDwsEastZbcpxxb(DwsEastZbcpxxbDTO dwsEastZbcpxxbDTO) {
        DwsEastZbcpxxbEntity dwsEastZbcpxxbEntity = ReinsuObjectUtil.convertModel(dwsEastZbcpxxbDTO, DwsEastZbcpxxbEntity.class);
        return dwsEastZbcpxxbMapper.updateDwsEastZbcpxxb(dwsEastZbcpxxbEntity);
    }

    @Override
	public Result updateDwsEastZbcpxxbPushStatus(Long[] ids) {
    	if(ids == null || ids.length <=0) {
    		return Result.error("请选择要推送的" + EastReport.再保产品信息表.getDesc());
    	}
    	List<DwsEastZbcpxxbEntity> zbcpxxbList = dwsEastZbcpxxbMapper.selectDwsEastZbcpxxbListByIds(ids);
    	if(CollUtil.isEmpty(zbcpxxbList)) {
    		return Result.error("要推送的" + EastReport.再保产品信息表.getDesc() + "不存在。");
    	}
    	long alreadyPushedCount = zbcpxxbList.stream().filter(cp -> ReportPushStatus.已推送.getCode() == cp.getPushStatus()).count();
    	if(alreadyPushedCount > 0) {//包含已推送的数据
    		return Result.error("要推送的" + EastReport.再保产品信息表.getDesc() + "包含已推送的数据，不允许重复推送。");
    	}
		int updateRows = dwsEastZbcpxxbMapper.updateDwsEastZbcpxxbPushStatus(ReportPushStatus.已推送.getCode(), SecurityUtils.getUsername(), ids);
		if(updateRows > 0) {
			List<Integer> reportYears = zbcpxxbList.stream().map(DwsEastZbcpxxbEntity::getReportYear).distinct().collect(Collectors.toList());
			dwsRegulatoryReportService.insertOrUpdateRegulatoryReport(BusinessType.UPDATE, RegulatorReport.East数据报送, EastReport.再保产品信息表.getCode(), reportYears);
		}
		return Result.success("推送成功", updateRows);
	}

	/**
     * 批量删除East再保产品信息
     *
     * @param ids 需要删除的East再保产品信息主键
     * @return 结果
     */
    @Override
    public int deleteDwsEastZbcpxxbByIds(Long[] ids) {
        return dwsEastZbcpxxbMapper.deleteDwsEastZbcpxxbByIds(ids);
    }

    /**
     * 删除East再保产品信息信息
     *
     * @param id East再保产品信息主键
     * @return 结果
     */
    @Override
    public int deleteDwsEastZbcpxxbById(Long id) {
    	DwsEastZbcpxxbEntity entity = dwsEastZbcpxxbMapper.selectDwsEastZbcpxxbById(id);
    	if(entity == null) {
    		return 0;
    	}
    	if(ReportDataSource.系统.getCode() == entity.getDataSource()) {
    		return -1;
    	}
    	if(ReportPushStatus.已推送.getCode() == entity.getPushStatus()) {
    		return -2;
    	}
        int deleteRows = dwsEastZbcpxxbMapper.deleteDwsEastZbcpxxbById(id);
        if(deleteRows > 0) {
        	dwsRegulatoryReportService.insertOrUpdateRegulatoryReport(BusinessType.DELETE, RegulatorReport.East数据报送, EastReport.再保产品信息表.getCode(), Arrays.asList(entity.getReportYear()));
        }
        return deleteRows;
    }

	@Override
	public Result importDwsEastZbcpxxb(String companyCode, String companyName, String manageCom, MultipartFile file) {
		try {
			ExcelUtil<DwsEastZbcpxxbDTO> excelUtil = new ExcelUtil<>(DwsEastZbcpxxbDTO.class);
	        List<DwsEastZbcpxxbDTO> zbcpxxbList = excelUtil.importExcel(file.getInputStream());
	        if(CollUtil.isEmpty(zbcpxxbList)) {
	        	return Result.error("导入的产品信息表为空。");
	        }
	        String error = this.checkImportData(companyCode, companyName, zbcpxxbList);
	        if(StringUtils.isNotBlank(error)) {
	        	return Result.error(error);
	        }
	        String keySuffix = zbcpxxbList.get(0).getBxjgdm();
	        List<String> serialNos = redisService.getUniqueCodes(RedisKeyModule.EAST, keySuffix, zbcpxxbList.size());
			if(CollUtil.isEmpty(serialNos) || zbcpxxbList.size() != serialNos.size()) {
				return Result.error("生成流水号失败，请联系管理员。");
			}
	        int index = 0;
	        for(DwsEastZbcpxxbDTO zbcpxxb : zbcpxxbList) {
	        	String sjbspch = ReDateUtil.getLastDayOfMonthPath(zbcpxxb.getReportYear(), zbcpxxb.getReportMonth());
	        	zbcpxxb.setSjbspch(sjbspch);
	        	zbcpxxb.setManagecom(manageCom);
	        	zbcpxxb.setLsh(serialNos.get(index));
	        	zbcpxxb.setCreateTime(DateUtils.getNowDate());
	        	zbcpxxb.setUpdateTime(DateUtils.getNowDate());
	        	zbcpxxb.setCreateBy(SecurityUtils.getUsername());
	        	zbcpxxb.setUpdateBy(SecurityUtils.getUsername());
	        	zbcpxxb.setDataSource(ReportDataSource.人工.getCode());
	        	zbcpxxb.setPushStatus(ReportPushStatus.未推送.getCode());
	        	if(!String.valueOf(BigDecimal.ONE.negate().intValue()).equals(zbcpxxb.getFbbl())) {
	        		zbcpxxb.setFbbl(String.format("%.2f", Float.valueOf(zbcpxxb.getFbbl())));
	        	}
	        	if(!String.valueOf(BigDecimal.ONE.negate().intValue()).equals(zbcpxxb.getZlbl())) {
	        		zbcpxxb.setZlbl(String.format("%.2f", Float.valueOf(zbcpxxb.getZlbl())));
	        	}
	        	zbcpxxb.setZbrcyfebl(String.format("%.2f", Float.valueOf(zbcpxxb.getZbrcyfebl())));
	        	zbcpxxb.setAccountPeriod(zbcpxxb.getReportYear() + String.format("%02d", zbcpxxb.getReportMonth()));
	        	index++;
	        }
	        List<DwsEastZbcpxxbEntity> dwsEastZbcpxxbList = ReinsuObjectUtil.convertList(zbcpxxbList, DwsEastZbcpxxbEntity.class);
	        int insertRows = dwsEastZbcpxxbMapper.insertBatchDwsEastZbcpxxb(dwsEastZbcpxxbList);
	        if(insertRows > 0) {
	        	List<Integer> reportYears = zbcpxxbList.stream().map(DwsEastZbcpxxbDTO::getReportYear).distinct().collect(Collectors.toList());
	        	dwsRegulatoryReportService.insertOrUpdateRegulatoryReport(BusinessType.IMPORT, RegulatorReport.East数据报送, EastReport.再保产品信息表.getCode(), reportYears);
	        }
	        return Result.success("成功导入" + insertRows + "行", HttpStatus.HTTP_OK);
		}catch(Exception e) {
			log.error("再保导入East产品信息出错, 错误原因:", e);
			return Result.error("导入产品信息表出错，请联系管理员。");
		}
	}
	
	@Override
	public int selectDwsEastZbcpxxbExists(DwsEastZbcpxxbQuery dwsEastZbcpxxbQuery) {
		return dwsEastZbcpxxbMapper.selectDwsEastZbcpxxbExists(dwsEastZbcpxxbQuery);
	}

	@Override
	public void exportDwsEastZbcpxxb(HttpServletResponse response, DwsEastZbcpxxbQuery dwsEastZbcpxxbQuery) {
		List<DwsEastZbcpxxbDTO> list = this.selectDwsEastZbcpxxbList(dwsEastZbcpxxbQuery);
        ExcelUtil<DwsEastZbcpxxbDTO> util = new ExcelUtil<DwsEastZbcpxxbDTO>(DwsEastZbcpxxbDTO.class);
        util.exportExcel(response, list, "East产品信息表");
	}
	
	private String checkImportData(String companyCode, String companyName, List<DwsEastZbcpxxbDTO> zbcpxxbList) {
		int index = 0;
		StringBuffer result = new StringBuffer();
		for(DwsEastZbcpxxbDTO zbcpxxb : zbcpxxbList) {
			List<String> emptys = ListUtils.newArrayList();
			List<String> errors = ListUtils.newArrayList();
			if(StringUtils.isBlank(zbcpxxb.getBxjgdm())) {
				emptys.add("保险机构代码");
			}else {
				if(!zbcpxxb.getBxjgdm().equals(companyCode)) {
					errors.add("保险机构代码");
				}
			}
			if(StringUtils.isBlank(zbcpxxb.getBxjgmc())) {
				emptys.add("保险机构名称");
			}else {
				if(!zbcpxxb.getBxjgmc().equals(companyName)) {
					errors.add("保险机构名称");
				}
			}
			if(StringUtils.isBlank(zbcpxxb.getZbxhthm())) {
				emptys.add("再保险合同号码");
			}
			if(StringUtils.isBlank(zbcpxxb.getZbxhtmc())) {
				emptys.add("再保险合同名称");
			}
			if(StringUtils.isBlank(zbcpxxb.getHtfylx())) {
				emptys.add("合同附约类型");
			}else {
				if(!CedeoutEnums.合同类型_主合同.getName().equals(zbcpxxb.getHtfylx()) && 
					!CedeoutEnums.合同类型_附约.getName().equals(zbcpxxb.getHtfylx())) {
					errors.add("合同附约类型");
				}
			}
			if(StringUtils.isBlank(zbcpxxb.getXzbm())) {
				emptys.add("险种编码");
			}
			if(StringUtils.isBlank(zbcpxxb.getXzjc())) {
				emptys.add("险种简称");
			}
			if(StringUtils.isBlank(zbcpxxb.getXl())) {
				emptys.add("险类");
			}
			if(StringUtils.isBlank(zbcpxxb.getZrdm())) {
				emptys.add("责任代码");
			}
			if(StringUtils.isBlank(zbcpxxb.getZrmc())) {
				emptys.add("责任名称");
			}
			if(StringUtils.isBlank(zbcpxxb.getZbxgsdm())) {
				emptys.add("再保险公司代码");
			}
			if(StringUtils.isBlank(zbcpxxb.getZbxgsmc())) {
				emptys.add("再保险公司名称");
			}
			if(StringUtils.isBlank(zbcpxxb.getFbxh())) {
				emptys.add("分保序号");
			}
			if(StringUtils.isBlank(zbcpxxb.getFbfs())) {
				emptys.add("分保方式");
			}else {
				if(EastCedeoutWay.getCodeByDesc(zbcpxxb.getFbfs()) == null) {
					errors.add("分保方式");
				}
			}
			if(StringUtils.isBlank(zbcpxxb.getZbrcyfebl())) {
				emptys.add("再保人参与份额比例");
			}
			if(StringUtils.isBlank(zbcpxxb.getCjrq())) {
				emptys.add("采集日期");
			}
			if(zbcpxxb.getReportYear() == null) {
				emptys.add("所属年份");
			}
			if(zbcpxxb.getReportMonth() == null) {
				emptys.add("所属月份");
			}
			index++;
			if(emptys.size() > 0) {
				result.append("第" + index + "行：" + String.join("、", emptys) + "为空。<br/>");
			}
			if(errors.size() > 0) {
				result.append("第" + index + "行：" + String.join("、", errors) + "有误。<br/>");
			}
		}
		return result.toString();
	}
    
}
