package com.reinsurance.service.impl;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import cn.hutool.http.HttpStatus;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.poi.ExcelUtil;

import com.reinsurance.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.jd.lightning.common.annotation.DataSource;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.enums.DataSourceType;
import com.jd.lightning.common.utils.DateUtils;
import com.jd.lightning.common.utils.SecurityUtils;
import com.jd.lightning.common.utils.StringUtils;

import com.reinsurance.enums.BasicDataEnums.PrpReport;
import com.reinsurance.enums.BasicDataEnums.RegulatorReport;
import com.reinsurance.enums.BasicDataEnums.RedisKeyModule;
import com.reinsurance.enums.BasicDataEnums.ReportDataSource;
import com.reinsurance.enums.BasicDataEnums.ReportPushStatus;

import cn.hutool.core.collection.CollUtil;

import com.reinsurance.mapper.DwsPrpProductMapper;
import com.reinsurance.dto.DwsPrpProductDTO;
import com.reinsurance.query.DwsPrpProductQuery;
import com.reinsurance.domain.DwsPrpProductEntity;
import com.reinsurance.utils.ReinsuObjectUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 保单登记再保产品信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Slf4j
@Service
@DataSource(DataSourceType.SLAVE)
public class DwsPrpProductServiceImpl implements IDwsPrpProductService {
    
    @Autowired
    private DwsPrpProductMapper dwsPrpProductMapper;

    @Autowired
    private IRedisService redisService;

    @Autowired
    private ISysConfigExtService sysConfigExtService;

    @Autowired
    private ICedeoutContractService cedeoutContractService;

    @Autowired
    private IDwsRegulatoryReportService dwsRegulatoryReportService;
    
    /**
     * 查询保单登记再保产品信息
     *
     * @param Id 保单登记再保产品信息主键
     * @return 保单登记再保产品信息
     */
    @Override
    public DwsPrpProductDTO selectDwsPrpProductById(Long Id) {
        DwsPrpProductEntity dwsPrpProductEntity = dwsPrpProductMapper.selectDwsPrpProductById(Id);
        return ReinsuObjectUtil.convertModel(dwsPrpProductEntity, DwsPrpProductDTO.class);
    }

    /**
     * 查询保单登记再保产品信息列表
     *
     * @param dwsPrpProductQuery 保单登记再保产品信息
     * @return 保单登记再保产品信息
     */
    @Override
    public List<DwsPrpProductDTO> selectDwsPrpProductList(DwsPrpProductQuery dwsPrpProductQuery) {
        List<DwsPrpProductEntity> list = dwsPrpProductMapper.selectDwsPrpProductList(dwsPrpProductQuery);
        return ReinsuObjectUtil.convertList(list, DwsPrpProductDTO.class);
    }

    /**
     * 新增保单登记再保产品信息
     *
     * @param dwsPrpProductDTO 保单登记再保产品信息
     * @return 结果
     */
    @Override
    public int insertDwsPrpProduct(DwsPrpProductDTO dwsPrpProductDTO) {
        DwsPrpProductEntity dwsPrpProductEntity = ReinsuObjectUtil.convertModel(dwsPrpProductDTO, DwsPrpProductEntity.class);
        dwsPrpProductEntity.setCreateTime(DateUtils.getNowDate());
        return dwsPrpProductMapper.insertDwsPrpProduct(dwsPrpProductEntity);
    }

    /**
     * 批量新增保单登记再保产品信息
     *
     * @param dwsPrpProductList 保单登记再保产品信息列表
     * @return 结果
     */
    @Override
    public int insertBatchDwsPrpProduct(List<DwsPrpProductDTO> dwsPrpProductList) {
        List<DwsPrpProductEntity> entitys = ReinsuObjectUtil.convertList(dwsPrpProductList, DwsPrpProductEntity.class);
        return dwsPrpProductMapper.insertBatchDwsPrpProduct(entitys);
    }

    /**
     * 修改保单登记再保产品信息
     *
     * @param dwsPrpProductDTO 保单登记再保产品信息
     * @return 结果
     */
    @Override
    public int updateDwsPrpProduct(DwsPrpProductDTO dwsPrpProductDTO) {
        DwsPrpProductEntity dwsPrpProductEntity = ReinsuObjectUtil.convertModel(dwsPrpProductDTO, DwsPrpProductEntity.class);
        return dwsPrpProductMapper.updateDwsPrpProduct(dwsPrpProductEntity);
    }

    /**
     * 批量删除保单登记再保产品信息
     *
     * @param Ids 需要删除的保单登记再保产品信息主键
     * @return 结果
     */
    @Override
    public int deleteDwsPrpProductByIds(Long[] Ids) {
        return dwsPrpProductMapper.deleteDwsPrpProductByIds(Ids);
    }

    /**
     * 删除保单登记再保产品信息信息
     *
     * @param Id 保单登记再保产品信息主键
     * @return 结果
     */
    @Override
    public int deleteDwsPrpProductById(Long Id) {
        DwsPrpProductEntity entity = dwsPrpProductMapper.selectDwsPrpProductById(Id);
        if(entity == null) {
            return 0;
        }
        if(ReportDataSource.系统.getCode() == entity.getDataSource()) {
            return -1;
        }
        if(ReportPushStatus.已推送.getCode() == entity.getPushStatus()) {
            return -2;
        }
        int deleteRows = dwsPrpProductMapper.deleteDwsPrpProductById(Id);
        if(deleteRows > 0) {
            dwsRegulatoryReportService.insertOrUpdateRegulatoryReport(BusinessType.DELETE, RegulatorReport.保单登记数据报送, PrpReport.LRProduct.getCode(), Arrays.asList(entity.getReportYear()));
        }
        return deleteRows;
    }

    /**
     * 导入保单登记再保产品信息
     *
     * @param companyCode 保险机构代码
     * @param file 导入文件
     * @return 结果
     */
    @Override
    public Result importDwsPrpProduct(String companyCode, MultipartFile file) {
        try {
            // 1. 数据解析
            ExcelUtil<DwsPrpProductDTO> excelUtil = new ExcelUtil<DwsPrpProductDTO>(DwsPrpProductDTO.class);
            List<DwsPrpProductDTO> productList = excelUtil.importExcel(file.getInputStream());
            if(CollUtil.isEmpty(productList)) {
                return Result.error("导入的产品信息表为空。");
            }
            // 2. 信息校验
            String error = this.checkImportData(companyCode, productList);
            if(StringUtils.isNotBlank(error)) {
                return Result.error(error);
            }
            // 3. 生成流水号
            String keySuffix = productList.get(0).getCompanyCode();
            List<String> serialNos = redisService.getUniqueCodes(RedisKeyModule.PRP, keySuffix, productList.size());
            if(CollUtil.isEmpty(serialNos) || productList.size() != serialNos.size()) {
                return Result.error("生成流水号失败，请联系管理员。");
            }
            // 4.设置系统字段
            int index = 0;
            for(DwsPrpProductDTO product : productList) {
                product.setTransactionNo(serialNos.get(index));
                product.setCreateTime(DateUtils.getNowDate());
                product.setUpdateTime(DateUtils.getNowDate());
                product.setCreateBy(SecurityUtils.getUsername());
                product.setUpdateBy(SecurityUtils.getUsername());
                product.setDataSource(ReportDataSource.系统.getCode());
                product.setPushStatus(ReportPushStatus.未推送.getCode());
                product.setAccountPeriod(product.getReportYear() + String.format("%02d", product.getReportMonth()));
                index++;
            }
            // 5. 批量入库
            List<DwsPrpProductEntity> dwsPrpProductList = ReinsuObjectUtil.convertList(productList, DwsPrpProductEntity.class);
            int insertRows = dwsPrpProductMapper.insertBatchDwsPrpProduct(dwsPrpProductList);
            if(insertRows > 0) {// 5. 更新监管报表推送状态
                List<Integer> reportYears = productList.stream().map(DwsPrpProductDTO::getReportYear).distinct().collect(Collectors.toList());
                dwsRegulatoryReportService.insertOrUpdateRegulatoryReport(BusinessType.IMPORT, RegulatorReport.保单登记数据报送, PrpReport.LRProduct.getCode(), reportYears);
            }
            return Result.success("成功导入" + insertRows + "行", HttpStatus.HTTP_OK);
        }catch(Exception e) {
            log.error("再保导入保单登记产品信息出错, 错误原因:", e);
            return Result.error("导入产品信息表出错，请联系管理员。");
        }
    }

    /**
     * 导出保单登记再保产品信息
     *
     * @param response 响应对象
     * @param dwsPrpProductQuery 查询条件
     */
    @Override
    public void exportDwsPrpProduct(HttpServletResponse response, DwsPrpProductQuery dwsPrpProductQuery) {
        List<DwsPrpProductDTO> list = this.selectDwsPrpProductList(dwsPrpProductQuery);
        ExcelUtil<DwsPrpProductDTO> util = new ExcelUtil<DwsPrpProductDTO>(DwsPrpProductDTO.class);
        util.exportExcel(response, list, "保单登记产品信息表");
    }

    /**
     * 检查保单登记再保产品信息是否存在
     *
     * @param dwsPrpProductQuery 查询条件
     * @return 结果
     */
    @Override
    public int selectDwsPrpProductExists(DwsPrpProductQuery dwsPrpProductQuery) {
        return dwsPrpProductMapper.selectDwsPrpProductExists(dwsPrpProductQuery);
    }

    /**
     * 更新推送状态
     *
     * @param Ids 主键数组
     * @return 结果
     */
    @Override
    public Result updateDwsPrpProductPushStatus(Long[] Ids) {
        List<DwsPrpProductEntity> productList = dwsPrpProductMapper.selectDwsPrpProductByIds(Ids);
        if(CollUtil.isEmpty(productList)) {
            return Result.error("要推送的" + PrpReport.LRProduct.getDesc() + "不存在。");
        }
        long alreadyPushedCount = productList.stream().filter(cp -> ReportPushStatus.已推送.getCode() == cp.getPushStatus()).count();
        if(alreadyPushedCount > 0) {//包含已推送的数据
            return Result.error("要推送的" + PrpReport.LRProduct.getDesc() + "包含已推送的数据，不允许重复推送。");
        }
        int updateRows = dwsPrpProductMapper.updateDwsPrpProductPushStatus(ReportPushStatus.已推送.getCode(), SecurityUtils.getUsername(), Ids);
        if(updateRows > 0) {
            List<Integer> reportYears = productList.stream().map(DwsPrpProductEntity::getReportYear).distinct().collect(Collectors.toList());
            dwsRegulatoryReportService.insertOrUpdateRegulatoryReport(BusinessType.UPDATE, RegulatorReport.保单登记数据报送, PrpReport.LRProduct.getCode(), reportYears);
        }
        return Result.success("推送成功", updateRows);
    }

    /**
     * 查询年度报表推送状态
     *
     * @param reportYear 报表年份
     * @return 结果
     */
    @Override
    public Integer selectAnnualReportShouldPushStatus(int reportYear) {
        return dwsPrpProductMapper.selectAnnualReportShouldPushStatus(reportYear);
    }

    private String checkImportData(String companyCode, List<DwsPrpProductDTO> productList) {
        int index = 0;
        StringBuffer result = new StringBuffer();
        for(DwsPrpProductDTO product : productList) {
            List<String> emptys = new ArrayList<>();
            List<String> errors = new ArrayList<>();
            if(StringUtils.isBlank(product.getCompanyCode())) {
                emptys.add("保险机构代码");
            }else {
                if(!product.getCompanyCode().equals(companyCode)) {
                    errors.add("保险机构代码");
                }
            }
            if(StringUtils.isBlank(product.getReInsuranceContNo())) {
                emptys.add("再保险合同号码");
            }
            if(StringUtils.isBlank(product.getReInsuranceContName())) {
                emptys.add("再保险合同名称");
            }
            if(StringUtils.isBlank(product.getContOrAmendmentType())) {
                emptys.add("合同附约类型");
            }
            if(StringUtils.isBlank(product.getProductCode())) {
                emptys.add("产品编码");
            }
            if(StringUtils.isBlank(product.getProductName())) {
                emptys.add("产品名称");
            }
            if(StringUtils.isBlank(product.getGPFlag())) {
                emptys.add("团个性质");
            }
            if(StringUtils.isBlank(product.getProductType())) {
                emptys.add("险类代码");
            }
            if(StringUtils.isBlank(product.getLiabilityCode())) {
                emptys.add("责任代码");
            }
            if(StringUtils.isBlank(product.getLiabilityName())) {
                emptys.add("责任名称");
            }
            if(StringUtils.isBlank(product.getReinsurerCode())) {
                emptys.add("再保险公司代码");
            }
            if(StringUtils.isBlank(product.getReinsurerName())) {
                emptys.add("再保险公司名称");
            }
            if(StringUtils.isBlank(product.getReinsuranceShare())) {
                emptys.add("再保人参与份额比例");
            }
            if(StringUtils.isBlank(product.getReinsurMode())) {
                emptys.add("分保方式");
            }
            if(StringUtils.isBlank(product.getReInsuranceType())) {
                emptys.add("再保类型");
            }
            if(StringUtils.isBlank(product.getTermType())) {
                emptys.add("保险期限类型");
            }
            if(product.getReportYear() == null) {
                emptys.add("所属年份");
            }
            if(product.getReportMonth() == null) {
                emptys.add("所属月份");
            }
            index++;
            if(emptys.size() > 0) {
                result.append("第" + index + "行：" + String.join("、", emptys) + "为空。<br/>");
            }
            if(errors.size() > 0) {
                result.append("第" + index + "行：" + String.join("、", errors) + "有误。<br/>");
            }
        }
        return result.toString();
    }
}
