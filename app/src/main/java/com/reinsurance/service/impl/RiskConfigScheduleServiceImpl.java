package com.reinsurance.service.impl;

import java.util.ArrayList;
import java.util.List;

import com.reinsurance.dto.RiskConfigScheduleDTO;
import com.reinsurance.enums.CedeoutEnums;
import com.reinsurance.domain.*;
import com.reinsurance.mapper.*;
import com.reinsurance.query.*;
import org.springframework.stereotype.Service;
import com.reinsurance.utils.ReinsuObjectUtil;
import com.jd.lightning.common.utils.DateUtils;
import com.reinsurance.service.IRiskConfigScheduleService;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

/**
 * 险种配置进度Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-03-21
 */
@Service
public class RiskConfigScheduleServiceImpl implements IRiskConfigScheduleService {
    @Resource
    private RiskConfigScheduleMapper riskConfigScheduleMapper;

    @Resource
    private RiskLiabilityMapper riskLiabilityMapper;

    @Resource
    private LiabilityMappingMapper liabilityMappingMapper;

    @Resource
    private LiabilityFormulaMapper liabilityFormulaMapper;

    @Resource
    private CedeoutContractLiabilityMapper cedeoutContractLiabilityMapper;

    @Resource
    private CedeoutProgrammeLiabilityMapper cedeoutProgrammeLiabilityMapper;

    /**
     * 查询险种配置进度
     * 
     * @param id 险种配置进度主键
     * @return 险种配置进度
     */
    @Override
    public RiskConfigScheduleDTO selectRiskConfigScheduleById(Long id)
    {
    	RiskConfigScheduleEntity riskConfigScheduleEntity = riskConfigScheduleMapper.selectRiskConfigScheduleById(id);
    	return ReinsuObjectUtil.convertModel(riskConfigScheduleEntity, RiskConfigScheduleDTO.class);
    }

    /**
     * 查询险种配置进度列表
     * 
     * @param query 险种配置进度
     * @return 险种配置进度
     */
    @Override
    public List<RiskConfigScheduleDTO> selectRiskConfigScheduleList(RiskConfigScheduleQuery query) {
        List<RiskConfigScheduleDTO> dtoList = new ArrayList<>();
        // 查询原始合同配置
        CedeoutContractLiabilityQuery liabilityQuery = new CedeoutContractLiabilityQuery();
        liabilityQuery.setRiskCode(query.getRiskCode());
        liabilityQuery.setStatus(CedeoutEnums.状态_有效.getValue());
        List<CedeoutContractLiabilityEntity> list4 = cedeoutContractLiabilityMapper.selectCedeoutContractLiabilityList(liabilityQuery);
        //为空 则分出展示 无需配置
        if(CollectionUtils.isEmpty(list4)){
            RiskConfigScheduleDTO dto = new RiskConfigScheduleDTO();
            dto.setRiskCode(query.getRiskCode());
            dto.setNodeCode(Long.valueOf(CedeoutEnums.险种责任信息配置_步骤一.getValue()));
            dto.setNodeName(CedeoutEnums.险种责任信息配置_步骤一.getName());
            dto.setConfigStepStatus(CedeoutEnums.节点无需配置_灰色.getValue());
            dto.setIsCedeOut(1); //不分出
            dtoList.add(dto);
            return dtoList;
        }
        //查询险种责任信息配置
        RiskLiabilityQuery riskLiabilityQuery = new RiskLiabilityQuery();
        riskLiabilityQuery.setStatus(CedeoutEnums.状态_有效.getValue());
        riskLiabilityQuery.setRiskCode(query.getRiskCode());
        List<RiskLiabilityEntity> list = riskLiabilityMapper.selectRiskLiabilityList(riskLiabilityQuery);
        RiskConfigScheduleDTO dto1 = new RiskConfigScheduleDTO();
        dto1.setRiskCode(query.getRiskCode());
        dto1.setNodeCode(Long.valueOf(CedeoutEnums.险种责任信息配置_步骤一.getValue()));
        dto1.setNodeName(CedeoutEnums.险种责任信息配置_步骤一.getName());
        dto1.setConfigStepStatus(CollectionUtils.isEmpty(list) ? CedeoutEnums.节点未配置_红色.getValue() : CedeoutEnums.节点配置完成_绿色.getValue());
        dto1.setIsCedeOut(0);
        dtoList.add(dto1);

        // 查询再保责任映射配置
        LiabilityMappingQuery liabilityMappingQuery = new LiabilityMappingQuery();
        liabilityMappingQuery.setRiskCode(query.getRiskCode());
        liabilityMappingQuery.setStatus(CedeoutEnums.状态_有效.getValue());
        List<LiabilityMappingEntity> list1 = liabilityMappingMapper.selectLiabilityMappingList(liabilityMappingQuery);
        RiskConfigScheduleDTO dto2 = new RiskConfigScheduleDTO();
        dto2.setRiskCode(query.getRiskCode());
        dto2.setNodeCode(Long.valueOf(CedeoutEnums.再保责任映射配置_步骤二.getValue()));
        dto2.setNodeName(CedeoutEnums.再保责任映射配置_步骤二.getName());
        dto2.setConfigStepStatus(CollectionUtils.isEmpty(list1) ? CedeoutEnums.节点未配置_红色.getValue() : CedeoutEnums.节点配置完成_绿色.getValue());
        dto2.setIsCedeOut(0);
        dtoList.add(dto2);

        // 查询算法关联配置
        LiabilityFormulaQuery liabilityFormulaQuery = new LiabilityFormulaQuery();
        liabilityFormulaQuery.setRiskCode(query.getRiskCode());
        liabilityFormulaQuery.setStatus(CedeoutEnums.状态_有效.getValue());
        List<LiabilityFormulaEntity> list3 = liabilityFormulaMapper.selectLiabilityFormulaList(liabilityFormulaQuery);
        RiskConfigScheduleDTO dto3 = new RiskConfigScheduleDTO();
        dto3.setRiskCode(query.getRiskCode());
        dto3.setNodeCode(Long.valueOf(CedeoutEnums.算法关联配置_步骤三.getValue()));
        dto3.setNodeName(CedeoutEnums.算法关联配置_步骤三.getName());
        dto3.setConfigStepStatus(CollectionUtils.isEmpty(list3) ? CedeoutEnums.节点未配置_红色.getValue() : CedeoutEnums.节点配置完成_绿色.getValue());
        dto3.setIsCedeOut(0);
        dtoList.add(dto3);

        // 查询原始合同配置
        RiskConfigScheduleDTO dto4 = new RiskConfigScheduleDTO();
        dto4.setRiskCode(query.getRiskCode());
        dto4.setNodeCode(Long.valueOf(CedeoutEnums.原始合同配置_步骤四.getValue()));
        dto4.setNodeName(CedeoutEnums.原始合同配置_步骤四.getName());
        dto4.setConfigStepStatus(CollectionUtils.isEmpty(list4) ? CedeoutEnums.节点未配置_红色.getValue() : CedeoutEnums.节点配置完成_绿色.getValue());
        dto4.setIsCedeOut(0);
        dtoList.add(dto4);

        // 查询再保方案配置
        CedeoutProgrammeLiabilityQuery programmeLiabilityQuery = new CedeoutProgrammeLiabilityQuery();
        programmeLiabilityQuery.setRiskCode(query.getRiskCode());
        programmeLiabilityQuery.setStatus(CedeoutEnums.状态_有效.getValue());
        List<CedeoutProgrammeLiabilityEntity> list5 = cedeoutProgrammeLiabilityMapper.selectCedeoutProgrammeLiabilityList(programmeLiabilityQuery);
        RiskConfigScheduleDTO dto5 = new RiskConfigScheduleDTO();
        dto5.setRiskCode(query.getRiskCode());
        dto5.setNodeCode(Long.valueOf(CedeoutEnums.再保方案配置_步骤五.getValue()));
        dto5.setNodeName(CedeoutEnums.再保方案配置_步骤五.getName());
        dto5.setConfigStepStatus(CollectionUtils.isEmpty(list5) ? CedeoutEnums.节点未配置_红色.getValue() : CedeoutEnums.节点配置完成_绿色.getValue());
        dto5.setIsCedeOut(0);
        dtoList.add(dto5);
    	return dtoList;
    }

    /**
     * 新增险种配置进度
     * 
     * @param riskConfigScheduleDTO 险种配置进度
     * @return 结果
     */
    @Override
    public int insertRiskConfigSchedule(RiskConfigScheduleDTO riskConfigScheduleDTO)
    {
    	RiskConfigScheduleEntity riskConfigScheduleEntity = ReinsuObjectUtil.convertModel(riskConfigScheduleDTO, RiskConfigScheduleEntity.class);
    	if(riskConfigScheduleEntity.getCreateTime() == null) {
    		riskConfigScheduleEntity.setCreateTime(DateUtils.getNowDate());
    	}
        return riskConfigScheduleMapper.insertRiskConfigSchedule(riskConfigScheduleEntity);
    }

    /**
     * 修改险种配置进度
     * 
     * @param riskConfigScheduleDTO 险种配置进度
     * @return 结果
     */
    @Override
    public int updateRiskConfigSchedule(RiskConfigScheduleDTO riskConfigScheduleDTO)
    {
    	RiskConfigScheduleEntity riskConfigScheduleEntity = ReinsuObjectUtil.convertModel(riskConfigScheduleDTO, RiskConfigScheduleEntity.class);
    	if(riskConfigScheduleEntity.getUpdateTime() == null) {
    		riskConfigScheduleEntity.setUpdateTime(DateUtils.getNowDate());
    	}
        return riskConfigScheduleMapper.updateRiskConfigSchedule(riskConfigScheduleEntity);
    }

    /**
     * 批量删除险种配置进度
     * 
     * @param ids 需要删除的险种配置进度主键
     * @return 结果
     */
    @Override
    public int deleteRiskConfigScheduleByIds(Long[] ids)
    {
        return riskConfigScheduleMapper.deleteRiskConfigScheduleByIds(ids);
    }

    /**
     * 删除险种配置进度信息
     * 
     * @param id 险种配置进度主键
     * @return 结果
     */
    @Override
    public int deleteRiskConfigScheduleById(Long id)
    {
        return riskConfigScheduleMapper.deleteRiskConfigScheduleById(id);
    }
}
