package com.reinsurance.service.impl;

import java.util.List;
import java.util.Arrays;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.jd.lightning.common.annotation.DataSource;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.enums.DataSourceType;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.DateUtils;
import com.jd.lightning.common.utils.SecurityUtils;
import com.jd.lightning.common.utils.poi.ExcelUtil;
import com.jd.lightning.common.utils.StringUtils;
import com.jd.lightning.common.constant.HttpStatus;

import com.reinsurance.mapper.DwsPrpInsureContMapper;
import com.reinsurance.dto.DwsPrpInsureContDTO;
import com.reinsurance.query.DwsPrpInsureContQuery;
import com.reinsurance.domain.DwsPrpInsureContEntity;
import com.reinsurance.utils.ReinsuObjectUtil;
import com.reinsurance.service.IDwsPrpInsureContService;
import com.reinsurance.service.IDwsRegulatoryReportService;
import com.reinsurance.enums.ReportDataSource;
import com.reinsurance.enums.ReportPushStatus;
import com.reinsurance.enums.RegulatorReport;
import com.reinsurance.enums.PrpReport;

import lombok.extern.slf4j.Slf4j;

/**
 * 保单登记再保合同信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Slf4j
@Service
@DataSource(DataSourceType.SLAVE)
public class DwsPrpInsureContServiceImpl implements IDwsPrpInsureContService {
    
    @Autowired
    private DwsPrpInsureContMapper dwsPrpInsureContMapper;

    @Autowired
    private IDwsRegulatoryReportService dwsRegulatoryReportService;

    /**
     * 查询保单登记再保合同信息
     *
     * @param Id 保单登记再保合同信息主键
     * @return 保单登记再保合同信息
     */
    @Override
    public DwsPrpInsureContDTO selectDwsPrpInsureContById(Long Id) {
        DwsPrpInsureContEntity entity = dwsPrpInsureContMapper.selectDwsPrpInsureContById(Id);
        return ReinsuObjectUtil.convertModel(entity, DwsPrpInsureContDTO.class);
    }

    /**
     * 查询保单登记再保合同信息列表
     *
     * @param dwsPrpInsureContQuery 保单登记再保合同信息
     * @return 保单登记再保合同信息
     */
    @Override
    public List<DwsPrpInsureContDTO> selectDwsPrpInsureContList(DwsPrpInsureContQuery dwsPrpInsureContQuery) {
        List<DwsPrpInsureContEntity> entityList = dwsPrpInsureContMapper.selectDwsPrpInsureContList(dwsPrpInsureContQuery);
        return ReinsuObjectUtil.convertList(entityList, DwsPrpInsureContDTO.class);
    }

    /**
     * 新增保单登记再保合同信息
     *
     * @param dwsPrpInsureContDTO 保单登记再保合同信息
     * @return 结果
     */
    @Override
    public int insertDwsPrpInsureCont(DwsPrpInsureContDTO dwsPrpInsureContDTO) {
        DwsPrpInsureContEntity dwsPrpInsureContEntity = ReinsuObjectUtil.convertModel(dwsPrpInsureContDTO, DwsPrpInsureContEntity.class);
        dwsPrpInsureContEntity.setCreateTime(DateUtils.getNowDate());
        return dwsPrpInsureContMapper.insertDwsPrpInsureCont(dwsPrpInsureContEntity);
    }

    /**
     * 批量新增保单登记再保合同信息
     *
     * @param dwsPrpInsureContList 保单登记再保合同信息列表
     * @return 结果
     */
    @Override
    public int insertBatchDwsPrpInsureCont(List<DwsPrpInsureContDTO> dwsPrpInsureContList) {
        List<DwsPrpInsureContEntity> entitys = ReinsuObjectUtil.convertList(dwsPrpInsureContList, DwsPrpInsureContEntity.class);
        return dwsPrpInsureContMapper.insertBatchDwsPrpInsureCont(entitys);
    }

    /**
     * 修改保单登记再保合同信息
     *
     * @param dwsPrpInsureContDTO 保单登记再保合同信息
     * @return 结果
     */
    @Override
    public int updateDwsPrpInsureCont(DwsPrpInsureContDTO dwsPrpInsureContDTO) {
        DwsPrpInsureContEntity dwsPrpInsureContEntity = ReinsuObjectUtil.convertModel(dwsPrpInsureContDTO, DwsPrpInsureContEntity.class);
        dwsPrpInsureContEntity.setUpdateTime(DateUtils.getNowDate());
        return dwsPrpInsureContMapper.updateDwsPrpInsureCont(dwsPrpInsureContEntity);
    }

    /**
     * 批量删除保单登记再保合同信息
     *
     * @param Ids 需要删除的保单登记再保合同信息主键
     * @return 结果
     */
    @Override
    public int deleteDwsPrpInsureContByIds(Long[] Ids) {
        return dwsPrpInsureContMapper.deleteDwsPrpInsureContByIds(Ids);
    }

    /**
     * 删除保单登记再保合同信息信息
     *
     * @param Id 保单登记再保合同信息主键
     * @return 结果
     */
    @Override
    public int deleteDwsPrpInsureContById(Long Id) {
        DwsPrpInsureContEntity entity = dwsPrpInsureContMapper.selectDwsPrpInsureContById(Id);
        if(entity == null) {
            return 0;
        }
        if(ReportDataSource.系统.getCode() == entity.getDataSource()) {
            return -1;
        }
        if(ReportPushStatus.已推送.getCode() == entity.getPushStatus()) {
            return -2;
        }
        int deleteRows = dwsPrpInsureContMapper.deleteDwsPrpInsureContById(Id);
        if(deleteRows > 0) {
            dwsRegulatoryReportService.insertOrUpdateRegulatoryReport(BusinessType.DELETE, RegulatorReport.保单登记数据报送, PrpReport.LRInsureCont.getCode(), Arrays.asList(entity.getReportYear()));
        }
        return deleteRows;
    }

    /**
     * 导入保单登记再保合同信息
     *
     * @param companyCode 保险机构代码
     * @param companyName 保险机构名称
     * @param manageCom 管理机构
     * @param file 导入文件
     * @return 结果
     */
    @Override
    public Result importDwsPrpInsureCont(String companyCode, String companyName, String manageCom, MultipartFile file) {
        try {
            // 1. 解析Excel文件
            ExcelUtil<DwsPrpInsureContDTO> util = new ExcelUtil<>(DwsPrpInsureContDTO.class);
            List<DwsPrpInsureContDTO> insureContList = util.importExcel(file.getInputStream());
            
            if (StringUtils.isNull(insureContList) || insureContList.size() == 0) {
                return Result.error("导入数据不能为空！");
            }
            
            // 2. 数据校验和处理
            for (DwsPrpInsureContDTO dto : insureContList) {
                dto.setCompanyCode(companyCode);
                dto.setDataSource(ReportDataSource.人工.getCode());
                dto.setPushStatus(ReportPushStatus.未推送.getCode());
                dto.setCreateBy(SecurityUtils.getUsername());
                dto.setCreateTime(DateUtils.getNowDate());
            }
            
            // 3. 批量入库
            List<DwsPrpInsureContEntity> dwsPrpInsureContList = ReinsuObjectUtil.convertList(insureContList, DwsPrpInsureContEntity.class);
            int insertRows = dwsPrpInsureContMapper.insertBatchDwsPrpInsureCont(dwsPrpInsureContList);
            if(insertRows > 0) {
                // 4. 更新监管报表推送状态
                List<Integer> reportYears = insureContList.stream().map(DwsPrpInsureContDTO::getReportYear).distinct().collect(Collectors.toList());
                dwsRegulatoryReportService.insertOrUpdateRegulatoryReport(BusinessType.IMPORT, RegulatorReport.保单登记数据报送, PrpReport.LRInsureCont.getCode(), reportYears);
            }
            return Result.success("成功导入" + insertRows + "行", HttpStatus.HTTP_OK);
        }catch(Exception e) {
            log.error("再保导入保单登记合同信息出错, 错误原因:", e);
            return Result.error("导入合同信息表出错，请联系管理员。");
        }
    }

    /**
     * 导出保单登记再保合同信息
     *
     * @param response 响应对象
     * @param dwsPrpInsureContQuery 查询条件
     */
    @Override
    public void exportDwsPrpInsureCont(HttpServletResponse response, DwsPrpInsureContQuery dwsPrpInsureContQuery) {
        List<DwsPrpInsureContDTO> list = selectDwsPrpInsureContList(dwsPrpInsureContQuery);
        ExcelUtil<DwsPrpInsureContDTO> util = new ExcelUtil<>(DwsPrpInsureContDTO.class);
        util.exportExcel(response, list, "保单登记再保合同信息数据");
    }

    /**
     * 检查保单登记再保合同信息是否存在
     *
     * @param dwsPrpInsureContQuery 查询条件
     * @return 结果
     */
    @Override
    public int selectDwsPrpInsureContExists(DwsPrpInsureContQuery dwsPrpInsureContQuery) {
        return dwsPrpInsureContMapper.selectDwsPrpInsureContExists(dwsPrpInsureContQuery);
    }

    /**
     * 更新推送状态
     *
     * @param Ids 主键数组
     * @return 结果
     */
    @Override
    public Result updateDwsPrpInsureContPushStatus(Long[] Ids) {
        String pushBy = SecurityUtils.getUsername();
        int updateRows = dwsPrpInsureContMapper.updateDwsPrpInsureContPushStatus(ReportPushStatus.已推送.getCode(), pushBy, Ids);
        if(updateRows > 0) {
            return Result.success("推送成功");
        }
        return Result.error("推送失败");
    }

    /**
     * 查询年度报表推送状态
     *
     * @param reportYear 报表年份
     * @return 结果
     */
    @Override
    public Integer selectAnnualReportShouldPushStatus(int reportYear) {
        return dwsPrpInsureContMapper.selectAnnualReportShouldPushStatus(reportYear);
    }

    /**
     * 生成再保合同信息表数据
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param reportYear 报表年份
     * @param reportMonth 报表月份
     * @return 结果
     */
    @Override
    public Result generatePrpInsureContData(String startDate, String endDate, Integer reportYear, Integer reportMonth) {
        // TODO: 实现数据生成逻辑
        return Result.success("数据生成功能待实现");
    }
}
