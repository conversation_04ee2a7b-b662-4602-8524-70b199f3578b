package com.reinsurance.service.impl;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import com.alibaba.excel.util.ListUtils;
import com.jd.lightning.common.annotation.DataSource;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.enums.DataSourceType;
import com.jd.lightning.common.utils.DateUtils;
import com.jd.lightning.common.utils.SecurityUtils;
import com.jd.lightning.common.utils.StringUtils;
import com.jd.lightning.common.utils.poi.ExcelUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.reinsurance.mapper.DwsEastBlzbbdmxbMapper;
import com.reinsurance.mapper.DwsEastZbzdxxbMapper;
import com.reinsurance.dto.DwsEastZbzdxxbDTO;
import com.reinsurance.enums.CedeoutEnums;
import com.reinsurance.enums.BasicDataEnums.ConfirmStatus;
import com.reinsurance.enums.BasicDataEnums.EastReport;
import com.reinsurance.enums.BasicDataEnums.EastReportEitherFlag;
import com.reinsurance.enums.BasicDataEnums.EastZdfl;
import com.reinsurance.enums.BasicDataEnums.RegulatorReport;
import com.reinsurance.enums.BasicDataEnums.RedisKeyModule;
import com.reinsurance.enums.BasicDataEnums.ReportDataSource;
import com.reinsurance.enums.BasicDataEnums.ReportPushStatus;
import com.reinsurance.query.DwsEastZbzdxxbQuery;
import com.reinsurance.domain.DwsEastZbzdxxbEntity;
import com.reinsurance.service.IDwsEastZbzdxxbService;
import com.reinsurance.service.IDwsRegulatoryReportService;
import com.reinsurance.service.IRedisService;
import com.reinsurance.utils.ReDateUtil;
import com.reinsurance.utils.ReinsuObjectUtil;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.HttpStatus;
import lombok.extern.slf4j.Slf4j;

/**
 * East再保账单信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-05
 */
@Slf4j
@Service
@DataSource(DataSourceType.SLAVE)
public class DwsEastZbzdxxbServiceImpl implements IDwsEastZbzdxxbService {
	
	@Autowired
	private IRedisService redisService;
	
    @Autowired
    private DwsEastZbzdxxbMapper dwsEastZbzdxxbMapper;
    
    @Autowired
    private DwsEastBlzbbdmxbMapper dwsEastBlzbbdmxbMapper;
    
    @Autowired
    private IDwsRegulatoryReportService dwsRegulatoryReportService;

    /**
     * 查询East再保账单信息
     *
     * @param id East再保账单信息主键
     * @return East再保账单信息
     */
    @Override
    public DwsEastZbzdxxbDTO selectDwsEastZbzdxxbById(Long id) {
        DwsEastZbzdxxbEntity dwsEastZbzdxxbEntity = dwsEastZbzdxxbMapper.selectDwsEastZbzdxxbById(id);
        return ReinsuObjectUtil.convertModel(dwsEastZbzdxxbEntity, DwsEastZbzdxxbDTO.class);
    }

    /**
     * 查询East再保账单信息列表
     *
     * @param dwsEastZbzdxxbQuery East再保账单信息
     * @return East再保账单信息
     */
    @Override
    public List<DwsEastZbzdxxbDTO> selectDwsEastZbzdxxbList(DwsEastZbzdxxbQuery dwsEastZbzdxxbQuery) {
        List<DwsEastZbzdxxbEntity> list = dwsEastZbzdxxbMapper.selectDwsEastZbzdxxbList(dwsEastZbzdxxbQuery);
        return ReinsuObjectUtil.convertList(list, DwsEastZbzdxxbDTO.class);
    }

    @Override
	public Integer selectAnnualReportShouldPushStatus(int reportYear) {
		return dwsEastZbzdxxbMapper.selectAnnualReportShouldPushStatus(reportYear);
	}
    
    /**
     * 新增East再保账单信息
     *
     * @param dwsEastZbzdxxbDTO East再保账单信息
     * @return 结果
     */
    @Override
    public int insertDwsEastZbzdxxb(DwsEastZbzdxxbDTO dwsEastZbzdxxbDTO) {
        DwsEastZbzdxxbEntity dwsEastZbzdxxbEntity = ReinsuObjectUtil.convertModel(dwsEastZbzdxxbDTO, DwsEastZbzdxxbEntity.class);
        dwsEastZbzdxxbEntity.setCreateTime(DateUtils.getNowDate());
        return dwsEastZbzdxxbMapper.insertDwsEastZbzdxxb(dwsEastZbzdxxbEntity);
    }

    @Override
	public int insertBatchDwsEastZbzdxxb(List<DwsEastZbzdxxbDTO> dwsEastZbzdxxbList) {
    	List<DwsEastZbzdxxbEntity> entitys = ReinsuObjectUtil.convertList(dwsEastZbzdxxbList, DwsEastZbzdxxbEntity.class);
		return dwsEastZbzdxxbMapper.insertBatchDwsEastZbzdxxb(entitys);
	}

	/**
     * 修改East再保账单信息
     *
     * @param dwsEastZbzdxxbDTO East再保账单信息
     * @return 结果
     */
    @Override
    public int updateDwsEastZbzdxxb(DwsEastZbzdxxbDTO dwsEastZbzdxxbDTO) {
        DwsEastZbzdxxbEntity dwsEastZbzdxxbEntity = ReinsuObjectUtil.convertModel(dwsEastZbzdxxbDTO, DwsEastZbzdxxbEntity.class);
        return dwsEastZbzdxxbMapper.updateDwsEastZbzdxxb(dwsEastZbzdxxbEntity);
    }
    
    /**
     * 修改East再保账单信息
     *
     * @param dwsEastZbzdxxbDTO East再保账单信息
     * @return 结果
     */
    @Override
    public int updateZbzdxxbImportStatusByLsh(DwsEastZbzdxxbDTO dwsEastZbzdxxbDTO) {
        DwsEastZbzdxxbEntity dwsEastZbzdxxbEntity = ReinsuObjectUtil.convertModel(dwsEastZbzdxxbDTO, DwsEastZbzdxxbEntity.class);
        return dwsEastZbzdxxbMapper.updateZbzdxxbImportStatusByLsh(dwsEastZbzdxxbEntity);
    }
    
    @Override
	public Result updateDwsEastZbzdxxbPushStatus(Long[] ids) {
    	if(ids == null || ids.length <=0) {
    		return Result.error("请选择要推送的" + EastReport.再保账单信息表.getDesc());
    	}
    	List<DwsEastZbzdxxbEntity> zbzdxxbList = dwsEastZbzdxxbMapper.selectDwsEastZbzdxxbListByIds(ids);
    	if(CollUtil.isEmpty(zbzdxxbList)) {
    		return Result.error("要推送的" + EastReport.再保账单信息表.getDesc() + "不存在。");
    	}
		long incompleteCount = zbzdxxbList.stream().filter(zd -> CedeoutEnums.导入状态_未导入.getValue() == zd.getImportStatus()).count();
		if(incompleteCount > 0) {
			return Result.error("要推送的" + EastReport.再保账单信息表.getDesc() + "包含未导入“比例再保保单明细”的账单，不允许推送。");
    	}
    	long alreadyPushedCount = zbzdxxbList.stream().filter(zd -> ReportPushStatus.已推送.getCode() == zd.getPushStatus()).count();
    	if(alreadyPushedCount > 0) {//包含已推送的数据
    		return Result.error("要推送的" + EastReport.再保账单信息表.getDesc() + "包含已推送的数据，不允许重复推送。");
    	}
    	List<String> billLshs = zbzdxxbList.stream().map(DwsEastZbzdxxbEntity::getLsh).collect(Collectors.toList());
    	dwsEastBlzbbdmxbMapper.updateDwsEastZbzdxxbPushStatus(ReportPushStatus.已推送.getCode(), SecurityUtils.getUsername(), billLshs);
		int updateRows = dwsEastZbzdxxbMapper.updateDwsEastZbzdxxbPushStatus(ReportPushStatus.已推送.getCode(), SecurityUtils.getUsername(), ids);
		if(updateRows > 0) {
			List<Integer> reportYears = zbzdxxbList.stream().map(DwsEastZbzdxxbEntity::getReportYear).distinct().collect(Collectors.toList());
			dwsRegulatoryReportService.insertOrUpdateRegulatoryReport(BusinessType.UPDATE, RegulatorReport.East数据报送, EastReport.再保账单信息表.getCode(), reportYears);
		}
		return Result.success("推送成功", updateRows);
	}

	/**
     * 批量删除East再保账单信息
     *
     * @param ids 需要删除的East再保账单信息主键
     * @return 结果
     */
    @Override
    public int deleteDwsEastZbzdxxbByIds(Long[] ids) {
        return dwsEastZbzdxxbMapper.deleteDwsEastZbzdxxbByIds(ids);
    }

    /**
     * 删除East再保账单信息信息
     *
     * @param id East再保账单信息主键
     * @return 结果
     */
    @Override
    public int deleteDwsEastZbzdxxbById(Long id) {
    	DwsEastZbzdxxbEntity entity = dwsEastZbzdxxbMapper.selectDwsEastZbzdxxbById(id);
    	if(entity == null) {
    		return 0;
    	}
    	if(ReportDataSource.系统.getCode() == entity.getDataSource()) {
    		return -1;
    	}
    	if(ReportPushStatus.已推送.getCode() == entity.getPushStatus()) {
    		return -2;
    	}
        int deleteRows = dwsEastZbzdxxbMapper.deleteDwsEastZbzdxxbById(id);
        if(deleteRows > 0) {
        	dwsEastBlzbbdmxbMapper.deleteDwsEastBlzbbdmxbByBillLsh(entity.getLsh());
        	dwsRegulatoryReportService.insertOrUpdateRegulatoryReport(BusinessType.DELETE, RegulatorReport.East数据报送, EastReport.再保账单信息表.getCode(), Arrays.asList(entity.getReportYear()));
        }
        return deleteRows;
    }

	@Override
	public Result importDwsEastZbzdxxb(String companyCode, String companyName, String manageCom, MultipartFile file) {
		try {
			ExcelUtil<DwsEastZbzdxxbDTO> excelUtil = new ExcelUtil<>(DwsEastZbzdxxbDTO.class);
	        List<DwsEastZbzdxxbDTO> zbzdxxbList = excelUtil.importExcel(file.getInputStream());
	        if(CollUtil.isEmpty(zbzdxxbList)) {
	        	return Result.error("导入的账单信息表为空。");
	        }
	        String error = this.checkImportData(companyCode, companyName, zbzdxxbList);
	        if(StringUtils.isNotBlank(error)) {
	        	return Result.error(error);
	        }
	        String keySuffix = zbzdxxbList.get(0).getBxjgdm();
	        List<String> serialNos = redisService.getUniqueCodes(RedisKeyModule.EAST, keySuffix, zbzdxxbList.size());
			if(CollUtil.isEmpty(serialNos) || zbzdxxbList.size() != serialNos.size()) {
				return Result.error("生成流水号失败，请联系管理员。");
			}
	        int index = 0;
	        for(DwsEastZbzdxxbDTO zbzdxxb : zbzdxxbList) {
	        	String sjbspch = ReDateUtil.getLastDayOfMonthPath(zbzdxxb.getReportYear(), zbzdxxb.getReportMonth());
	        	zbzdxxb.setSjbspch(sjbspch);
	        	zbzdxxb.setManagecom(manageCom);
	        	zbzdxxb.setLsh(serialNos.get(index));
	        	zbzdxxb.setCreateTime(DateUtils.getNowDate());
	        	zbzdxxb.setUpdateTime(DateUtils.getNowDate());
	        	zbzdxxb.setCreateBy(SecurityUtils.getUsername());
	        	zbzdxxb.setUpdateBy(SecurityUtils.getUsername());
	        	zbzdxxb.setDataSource(ReportDataSource.人工.getCode());
	        	zbzdxxb.setPushStatus(ReportPushStatus.未推送.getCode());
	        	zbzdxxb.setImportStatus(CedeoutEnums.导入状态_未导入.getValue());
	        	zbzdxxb.setAccountPeriod(zbzdxxb.getReportYear() + String.format("%02d", zbzdxxb.getReportMonth()));
	        	index++;
	        }
	        List<DwsEastZbzdxxbEntity> dwsEastZbzdxxbList = ReinsuObjectUtil.convertList(zbzdxxbList, DwsEastZbzdxxbEntity.class);
	        int insertRows = dwsEastZbzdxxbMapper.insertBatchDwsEastZbzdxxb(dwsEastZbzdxxbList);
	        if(insertRows > 0) {
	        	List<Integer> reportYears = zbzdxxbList.stream().map(DwsEastZbzdxxbDTO::getReportYear).distinct().collect(Collectors.toList());
	        	dwsRegulatoryReportService.insertOrUpdateRegulatoryReport(BusinessType.IMPORT, RegulatorReport.East数据报送, EastReport.再保账单信息表.getCode(), reportYears);
	        }
	        return Result.success("成功导入" + insertRows + "行", HttpStatus.HTTP_OK);
		}catch(Exception e) {
			log.error("再保导入East账单信息出错, 错误原因:", e);
			return Result.error("导入账单信息表出错，请联系管理员。");
		}
	}
    
	@Override
	public int selectDwsEastZbzdxxbExists(DwsEastZbzdxxbQuery dwsEastZbzdxxbQuery) {
		return dwsEastZbzdxxbMapper.selectDwsEastZbzdxxbExists(dwsEastZbzdxxbQuery);
	}
	
	@Override
	public void exportDwsEastZbzdxxb(HttpServletResponse response, DwsEastZbzdxxbQuery dwsEastZbzdxxbQuery) {
		List<DwsEastZbzdxxbDTO> list = this.selectDwsEastZbzdxxbList(dwsEastZbzdxxbQuery);
        ExcelUtil<DwsEastZbzdxxbDTO> util = new ExcelUtil<DwsEastZbzdxxbDTO>(DwsEastZbzdxxbDTO.class);
        util.exportExcel(response, list, "East账单信息表");
	}
    
	
	private String checkImportData(String companyCode, String companyName, List<DwsEastZbzdxxbDTO> zbzdxxbList) {
		int index = 0;
		StringBuffer result = new StringBuffer();
		for(DwsEastZbzdxxbDTO zbzdxxb : zbzdxxbList) {
			List<String> emptys = ListUtils.newArrayList();
			List<String> errors = ListUtils.newArrayList();
			if(StringUtils.isBlank(zbzdxxb.getBxjgdm())) {
				emptys.add("保险机构代码");
			}else {
				if(!zbzdxxb.getBxjgdm().equals(companyCode)) {
					errors.add("保险机构代码");
				}
			}
			if(StringUtils.isBlank(zbzdxxb.getBxjgmc())) {
				emptys.add("保险机构名称");
			}else {
				if(!zbzdxxb.getBxjgmc().equals(companyName)) {
					errors.add("保险机构名称");
				}
			}
			if(StringUtils.isBlank(zbzdxxb.getZdfl())) {
				emptys.add("账单分类");
			}else {
				if(EastZdfl.getCodeByDesc(zbzdxxb.getZdfl()) == null) {
					errors.add("账单分类");
				}
			}
			if(StringUtils.isBlank(zbzdxxb.getZdbh())) {
				emptys.add("账单编号");
			}
			if(StringUtils.isBlank(zbzdxxb.getYtzdbz())) {
				emptys.add("预提账单标志");
			}else {
				if(StringUtils.isBlank(EastReportEitherFlag.getDescByCode(zbzdxxb.getYtzdbz()))) {
					errors.add("预提账单标志");
				}
			}
			if(StringUtils.isBlank(zbzdxxb.getZdqq())) {
				emptys.add("账单起期");
			}
			if(StringUtils.isBlank(zbzdxxb.getZdzq())) {
				emptys.add("账单止期");
			}
			if(StringUtils.isBlank(zbzdxxb.getZbxgsdm())) {
				emptys.add("再保险公司代码");
			}
			if(StringUtils.isBlank(zbzdxxb.getYbxgsdm())) {
				emptys.add("原保险公司代码");
			}else {
				if(!zbzdxxb.getYbxgsdm().equals(companyCode)) {
					errors.add("原保险公司代码");
				}
			}
			if(StringUtils.isBlank(zbzdxxb.getYbxgsmc())) {
				emptys.add("原保险公司名称");
			}else {
				if(!zbzdxxb.getYbxgsmc().equals(companyName)) {
					errors.add("原保险公司名称");
				}
			}
			if(StringUtils.isBlank(zbzdxxb.getXnhtbz())) {
				emptys.add("虚拟合同标志");
			}else {
				if(StringUtils.isBlank(EastReportEitherFlag.getDescByCode(zbzdxxb.getXnhtbz()))) {
					errors.add("虚拟合同标志");
				}
			}
			if(StringUtils.isBlank(zbzdxxb.getZbxhthm())) {
				emptys.add("再保险合同号码");
			}
			if(StringUtils.isBlank(zbzdxxb.getZbxhtmc())) {
				emptys.add("再保险合同名称");
			}
			if(StringUtils.isBlank(zbzdxxb.getJszt())) {
				emptys.add("结算状态");
			}else {
				if(ConfirmStatus.getCodeBySettle(zbzdxxb.getJszt()) == null){
					errors.add("结算状态");
				}
				if(ConfirmStatus.已确认.getSettle().equals(zbzdxxb.getJszt())) {
					if(StringUtils.isBlank(zbzdxxb.getJsrq())) {
						emptys.add("结算日期");
					}
					if(StringUtils.isBlank(zbzdxxb.getHbdm())) {
						emptys.add("货币代码");
					}
					if(zbzdxxb.getJshl() == null) {
						emptys.add("结算汇率");
					}
					if(StringUtils.isBlank(zbzdxxb.getZdjylsbh())) {
						emptys.add("账单交易流水编号");
					}
				}
			}
			if(StringUtils.isBlank(zbzdxxb.getCjrq())) {
				emptys.add("采集日期");
			}
			if(zbzdxxb.getReportYear() == null) {
				emptys.add("所属年份");
			}
			if(zbzdxxb.getReportMonth() == null) {
				emptys.add("所属月份");
			}
			index++;
			if(emptys.size() > 0) {
				result.append("第" + index + "行：" + String.join("、", emptys) + "为空。<br/>");
			}
			if(errors.size() > 0) {
				result.append("第" + index + "行：" + String.join("、", errors) + "有误。<br/>");
			}
		}
		return result.toString();
	}
}
