package com.reinsurance.service.impl;

import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import com.jd.lightning.common.annotation.DataSource;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.domain.entity.SysDictData;
import com.jd.lightning.common.enums.DataSourceType;
import com.jd.lightning.common.utils.DateUtils;
import com.jd.lightning.common.utils.SecurityUtils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.reinsurance.mapper.DwsReinsuSettleBillMapper;
import com.reinsurance.mapper.DwsReinsuTradeMapper;
import com.reinsurance.dto.DwsEastZbzdxxbDTO;
import com.reinsurance.dto.DwsReinsuSettleBillDTO;
import com.reinsurance.enums.BasicDataEnums.ConfirmStatus;
import com.reinsurance.enums.BasicDataEnums.FileSuffix;
import com.reinsurance.enums.BasicDataEnums.RedisKeyModule;
import com.reinsurance.enums.CedeoutEnums;
import com.reinsurance.query.DwsReinsuSettleBillQuery;
import com.reinsurance.constant.RsConstant;
import com.reinsurance.domain.DwsReinsuSettleBillEntity;
import com.reinsurance.service.IDwsReinsuSettleBillService;
import com.reinsurance.service.IDwsReinsuTradeService;
import com.reinsurance.service.IRedisService;
import com.reinsurance.service.ISysDictExtService;
import com.reinsurance.utils.ReinsuJsonUtil;
import com.reinsurance.utils.ReinsuObjectUtil;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.google.common.collect.Lists;

/**
 * 再保结算账单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-05
 */
@Slf4j
@Service
@DataSource(DataSourceType.SLAVE)
public class DwsReinsuSettleBillServiceImpl implements IDwsReinsuSettleBillService {
	
	@Autowired
	private IRedisService redisService;
	
	@Autowired 
	private ISysDictExtService sysDictExtService;
	
	@Autowired
    private DwsReinsuTradeMapper dwsReinsuTradeMapper;
	
	@Autowired
    private IDwsReinsuTradeService dwsReinsuTradeService;
	
    @Autowired
    private DwsReinsuSettleBillMapper dwsReinsuSettleBillMapper;

    /**
     * 查询再保结算账单
     *
     * @param id 再保结算账单主键
     * @return 再保结算账单
     */
    @Override
    public DwsReinsuSettleBillDTO selectDwsReinsuSettleBillById(Long id) {
        DwsReinsuSettleBillEntity dwsReinsuSettleBillEntity = dwsReinsuSettleBillMapper.selectDwsReinsuSettleBillById(id);
        return ReinsuObjectUtil.convertModel(dwsReinsuSettleBillEntity, DwsReinsuSettleBillDTO.class);
    }
    
    /**
     * 查询再保结算账单
     *
     * @param billNo 账单号
     * @return 再保结算账单
     */
    @Override
    public DwsReinsuSettleBillDTO selectDwsReinsuSettleBillByBillNo(String billNo) {
        DwsReinsuSettleBillEntity dwsReinsuSettleBillEntity = dwsReinsuSettleBillMapper.selectDwsReinsuSettleBillByBillNo(billNo);
        return ReinsuObjectUtil.convertModel(dwsReinsuSettleBillEntity, DwsReinsuSettleBillDTO.class);
    }

    /**
     * 查询再保结算账单列表
     *
     * @param dwsReinsuSettleBillQuery 再保结算账单
     * @return 再保结算账单
     */
    @Override
    public List<DwsReinsuSettleBillDTO> selectDwsReinsuSettleBillList(DwsReinsuSettleBillQuery dwsReinsuSettleBillQuery) {
        List<DwsReinsuSettleBillEntity> list = dwsReinsuSettleBillMapper.selectDwsReinsuSettleBillList(dwsReinsuSettleBillQuery);
        return ReinsuObjectUtil.convertList(list, DwsReinsuSettleBillDTO.class);
    }

    @Override
	public void exportDwsReinsuTradeListByBillIds(HttpServletResponse response, Long[] ids) {
    	OutputStream os = null;
    	final int pageSize = 10000;
    	ExcelWriter excelWriter = null;
    	try {
    		List<DwsReinsuSettleBillEntity> dwsReinsuSettleBillList = dwsReinsuSettleBillMapper.selectDwsReinsuSettleBillByIds(ids);
        	if(CollUtil.isEmpty(dwsReinsuSettleBillList)) {
        		return;
        	}
    		List<SysDictData> headerList = sysDictExtService.selectDictDataList(RsConstant.billExportColumnDictTypeKey);
    		if(CollUtil.isEmpty(headerList)) {
    			return;
    		}
    		List<SysDictData> appendHeaderList = sysDictExtService.selectDictDataList(RsConstant.billExportColumnAppendKey);
    		
    		String fileName = URLEncoder.encode("再保账单明细数据" + System.currentTimeMillis() + FileSuffix.EXCEL.getSuffix(), "utf-8");
    		
    		List<List<String>> headers = this.getHeaders(headerList, appendHeaderList);
    		Map<String, Map<String, String>> columnMappingDictMap = sysDictExtService.getColumnMappingDict();
    		List<String> tradeOutColumns = headerList.stream().map(SysDictData::getDictValue).collect(Collectors.toList());
    		
    		os = response.getOutputStream();
    		response.setCharacterEncoding("utf-8");
    		response.setHeader("Content-disposition", "attachment;filename=" + fileName);
    		response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    		excelWriter = EasyExcel.write(os).registerWriteHandler(this.getCellStyle()).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).head(headers).build();
        	for(DwsReinsuSettleBillEntity bill : dwsReinsuSettleBillList) {
        		int totalRows = dwsReinsuTradeService.selectDwsReinsuTradeCountByBillNo(bill.getBillNo());
        		if(totalRows <= 0) {
        			continue;
        		}
    			WriteSheet writeSheet = EasyExcel.writerSheet(bill.getCompanyCode()+bill.getBillNo()).build();
    			
    			int totalPage = (totalRows + pageSize - 1) / pageSize;
        		for(int pageNo=1; pageNo <= totalPage; pageNo++) {
					int startRows = (pageNo - 1) * pageSize;
					List<Map<String, Object>> dwsReinsuTradeList = dwsReinsuTradeService.selectDwsReinsuTradeListByBillNo(tradeOutColumns, bill.getBillNo(), pageSize, startRows);
            		if(CollUtil.isNotEmpty(dwsReinsuTradeList)) {
            			List<List<Object>> datas = this.getDatas(columnMappingDictMap, headerList, appendHeaderList, bill, dwsReinsuTradeList);
            			excelWriter.write(datas, writeSheet);
            		}
        		}
        		log.info("再保导出账单明细数据处理中, 处理完成id:{}", bill.getId());
        	}
    	}catch(Exception e) {
    		log.error("再保导出账单明细数据出错, ids:{}, 错误原因:", ids, e);
    	}finally {
    		if(excelWriter != null) {
    			excelWriter.finish();
    		}
    		if(os != null) {
    			try {
					os.flush();
					os.close();
				} catch (IOException e) {}
    		}
    	}
	}

	@Override
	public Result insertBatchReinsuSettleBill(DwsReinsuSettleBillQuery dwsReinsuSettleBillQuery) {
    	if(StringUtils.isEmpty(dwsReinsuSettleBillQuery.getCompanyCode()) || StringUtils.isEmpty(dwsReinsuSettleBillQuery.getCompanyName())) {
    		return Result.error("请选择再保公司");
    	}
    	if(dwsReinsuSettleBillQuery.getAccountStartDate() == null || dwsReinsuSettleBillQuery.getAccountEndDate() == null) {
    		return Result.error("请选择账单日期范围");
    	}
    	int compare = DateUtil.compare(dwsReinsuSettleBillQuery.getAccountStartDate(), dwsReinsuSettleBillQuery.getAccountEndDate());
    	if(compare >= 1) {
    		return Result.error("选择的账单起始日期晚于截止日期");
    	}
    	
    	dwsReinsuSettleBillQuery.setActualStartDate(DateUtils.addDays(dwsReinsuSettleBillQuery.getAccountStartDate(), BigDecimal.ONE.intValue()));
    	dwsReinsuSettleBillQuery.setActualEndDate(DateUtils.addDays(dwsReinsuSettleBillQuery.getAccountEndDate(), BigDecimal.ONE.intValue()));
    	List<Map<String, Object>> contractList = dwsReinsuTradeMapper.selectIncludedContractByCommpanyCode(dwsReinsuSettleBillQuery);
    	if(CollUtil.isEmpty(contractList)) {
    		log.info("再保生成账单(未匹配到分出摊回明细), dwsReinsuSettleBill:{}", ReinsuJsonUtil.toJsonString(dwsReinsuSettleBillQuery));
    		return Result.error("未查询到待确认的分出摊回明细数据");
    	}
    	for(Map<String, Object> contract : contractList) {
    		dwsReinsuSettleBillQuery.setContractCode((String)contract.get("contractCode"));
    		int count = dwsReinsuSettleBillMapper.selectDwsReinsuSettleExists(dwsReinsuSettleBillQuery);
    		if(count > 0) {
    			log.info("再保生成账单(合同存在交叉账单), dwsReinsuSettleBill:{}", ReinsuJsonUtil.toJsonString(dwsReinsuSettleBillQuery));
    			return Result.error("“"+ (String)contract.get("contractName") + "”在" + dwsReinsuSettleBillQuery.getAccountStartDate() + "至" + dwsReinsuSettleBillQuery.getAccountEndDate() + "范围内存在交集的账单");
    		}
    	}
    	
    	List<DwsReinsuSettleBillEntity> dwsReinsuSettleBillList = new ArrayList<DwsReinsuSettleBillEntity>();
    	for(Map<String, Object> contract : contractList) {
    		DwsReinsuSettleBillEntity bill = new DwsReinsuSettleBillEntity();
    		bill.setCurrency(RsConstant.rmb);
    		bill.setExchangeRate(BigDecimal.ONE);
    		bill.setCreateTime(DateUtils.getNowDate());
    		bill.setUpdateTime(DateUtils.getNowDate());
    		bill.setCreateBy(SecurityUtils.getUsername());
    		bill.setUpdateBy(SecurityUtils.getUsername());
    		bill.setConfirmStatus(ConfirmStatus.未确认.getCode());
    		bill.setContractCode((String)contract.get("contractCode"));
    		bill.setContractName((String)contract.get("contractName"));
    		bill.setCompanyCode(dwsReinsuSettleBillQuery.getCompanyCode());
    		bill.setCompanyName(dwsReinsuSettleBillQuery.getCompanyName());
    		bill.setAccountStartDate(dwsReinsuSettleBillQuery.getAccountStartDate());
    		bill.setAccountEndDate(dwsReinsuSettleBillQuery.getAccountEndDate());
    		bill.setActualStartDate(dwsReinsuSettleBillQuery.getActualStartDate());
    		bill.setActualEndDate(dwsReinsuSettleBillQuery.getActualEndDate());
    		bill.setRemark("总行数:" + (Long)contract.get("totalRows"));
    		bill.setBillNo(redisService.getUniqueCode(RedisKeyModule.BILL));
    		try {
				int updateRows = dwsReinsuTradeMapper.updateDwsReinsuTradeBillNo(bill);
				bill.setIsDel(CedeoutEnums.未删除.getValue());
    			bill.setRemark(bill.getRemark() + ";更新行数:" + updateRows);
			}catch(Exception e) {
				bill.setIsDel(CedeoutEnums.已删除.getValue());
				bill.setRemark(bill.getRemark() + ";更新行数:0(系统异常)");
				log.error("再保生成账单(更新对应的分出摊回记录账单号出错), bill:{}", ReinsuJsonUtil.toJsonString(bill) + ", 错误原因:", e);
			}
    		dwsReinsuSettleBillList.add(bill);
    	}
    	int insertRows = dwsReinsuSettleBillMapper.insertBatchDwsReinsuSettleBill(dwsReinsuSettleBillList);
		return Result.success(insertRows);
	}

    /**
     * 修改再保结算账单
     *
     * @param dwsReinsuSettleBillDTO 再保结算账单
     * @return 结果
     */
    @Override
    public int updateDwsReinsuSettleBill(DwsReinsuSettleBillDTO dwsReinsuSettleBillDTO) {
    	DwsReinsuSettleBillEntity dwsReinsuSettleBillEntity = dwsReinsuSettleBillMapper.selectDwsReinsuSettleBillById(dwsReinsuSettleBillDTO.getId());
    	if(dwsReinsuSettleBillEntity == null) {
    		return -1;
    	}
    	int oldConfirmStatus = dwsReinsuSettleBillEntity.getConfirmStatus();
    	dwsReinsuSettleBillEntity.setUpdateTime(DateUtils.getNowDate());
        dwsReinsuSettleBillEntity.setUpdateBy(SecurityUtils.getUsername());
    	dwsReinsuSettleBillEntity.setSettleDate(dwsReinsuSettleBillDTO.getSettleDate());
    	dwsReinsuSettleBillEntity.setSettleTradeNo(dwsReinsuSettleBillDTO.getSettleTradeNo());
    	dwsReinsuSettleBillEntity.setFundTradeNo(dwsReinsuSettleBillDTO.getFundTradeNo());
        dwsReinsuSettleBillEntity.setAccVoucherNo(dwsReinsuSettleBillDTO.getAccVoucherNo());
        dwsReinsuSettleBillEntity.setAccVoucherDate(dwsReinsuSettleBillDTO.getAccVoucherDate());
    	dwsReinsuSettleBillEntity.setConfirmStatus(dwsReinsuSettleBillDTO.getConfirmStatus());
        if(ConfirmStatus.未确认.getCode() == dwsReinsuSettleBillEntity.getConfirmStatus()) {
        	dwsReinsuSettleBillEntity.setConfirmer(null);
        	dwsReinsuSettleBillEntity.setConfirmDate(null);
        }else {
        	dwsReinsuSettleBillEntity.setConfirmer(SecurityUtils.getUsername());
        	dwsReinsuSettleBillEntity.setConfirmDate(DateUtils.getNowDate());
        }
        if(oldConfirmStatus != dwsReinsuSettleBillEntity.getConfirmStatus()) {//更新分出摊回明细的确认状态
        	dwsReinsuTradeMapper.updateDwsReinsuTradeConfirmStatus(dwsReinsuSettleBillEntity);
        }
        return dwsReinsuSettleBillMapper.updateDwsReinsuSettleBill(dwsReinsuSettleBillEntity);
    }

    /**
     * 批量删除再保结算账单
     *
     * @param ids 需要删除的再保结算账单主键
     * @return 结果
     */
    @Override
    public int deleteDwsReinsuSettleBillByIds(Long[] ids) {
        return dwsReinsuSettleBillMapper.deleteDwsReinsuSettleBillByIds(ids);
    }

    /**
     * 删除再保结算账单信息
     *
     * @param id 再保结算账单主键
     * @return 结果
     */
    @Override
    public int deleteDwsReinsuSettleBillById(Long id) {
        return dwsReinsuSettleBillMapper.deleteDwsReinsuSettleBillById(id);
    }
    
    @Override
	public List<DwsEastZbzdxxbDTO> selectSettleBillAsZbzdxxb(DwsReinsuSettleBillQuery dwsReinsuSettleBillQuery) {
		return dwsReinsuSettleBillMapper.selectSettleBillAsZbzdxxb(dwsReinsuSettleBillQuery);
	}

	private HorizontalCellStyleStrategy getCellStyle(){
    	HorizontalCellStyleStrategy handlerFontStyleStrategy = new HorizontalCellStyleStrategy();
		WriteCellStyle headWriteCellStyle = new WriteCellStyle();
		WriteFont writeFont = new WriteFont();
		writeFont.setFontHeightInPoints((short)12);
		headWriteCellStyle.setWriteFont(writeFont);
		handlerFontStyleStrategy.setHeadWriteCellStyle(headWriteCellStyle);
		return handlerFontStyleStrategy;
    }
    
    private List<List<String>> getHeaders(List<SysDictData> headerList, List<SysDictData> appendHeaderList){
		List<List<String>> headers = new ArrayList<List<String>>();
		for(SysDictData column : headerList) {
			headers.add(Lists.newArrayList(column.getDictLabel()));
		}
		for(SysDictData field : appendHeaderList) {
			headers.add(Lists.newArrayList(field.getDictLabel()));
		}
		return headers;
	}
    
    private List<List<Object>> getDatas(Map<String, Map<String, String>> columnMappingDictMap, List<SysDictData> headerList, List<SysDictData> appendHeaderList, DwsReinsuSettleBillEntity bill, List<Map<String, Object>> dwsReinsuTradeList) {
		List<List<Object>> datas = new ArrayList<List<Object>>();
		
		List<Object> appendDataList = new ArrayList<Object>();
		if(CollUtil.isNotEmpty(appendHeaderList)) {
			for(SysDictData field : appendHeaderList) {
				Object dataValue = BeanUtil.getFieldValue(bill, field.getDictValue());
				if(dataValue != null && dataValue instanceof Date) {
					appendDataList.add(DateUtil.formatDate((Date)dataValue));
				}else {
					if("confirmStatus".equals(field.getDictValue())){
						appendDataList.add(ConfirmStatus.getDescByCode((Integer)dataValue));
					}else {
						appendDataList.add(dataValue);
					}
				}
			}
		}
		
		for(Map<String, Object> data : dwsReinsuTradeList) {
			List<Object> rowDatas = Lists.newArrayList();
			for(SysDictData column : headerList) {
				String fieldName = column.getDictValue();
				Object dataValue = data.get(fieldName);
				if(dataValue != null && dataValue instanceof Date) {
					rowDatas.add(DateUtil.formatDate((Date)dataValue));
				}else {
					if(columnMappingDictMap.containsKey(fieldName)) {
						String dataLabel = columnMappingDictMap.get(fieldName).get(String.valueOf(dataValue));
						rowDatas.add(StringUtils.isNotEmpty(dataLabel) ? dataLabel : dataValue);
					}else {
						rowDatas.add(dataValue);
					}
				}
			}
			rowDatas.addAll(appendDataList);
			datas.add(rowDatas);
		}
		return datas;
	}
    
}
