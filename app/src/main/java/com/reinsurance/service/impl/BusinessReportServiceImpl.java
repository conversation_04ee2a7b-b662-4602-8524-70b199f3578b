package com.reinsurance.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.reinsurance.utils.ReinsuObjectUtil;
import com.jd.lightning.common.core.domain.entity.SysDictData;
import com.jd.lightning.common.enums.DataSourceType;
import com.jd.lightning.common.utils.DateUtils;
import com.jd.lightning.common.utils.SecurityUtils;
import com.jd.lightning.common.utils.file.FileUtils;
import com.jd.lightning.framework.datasource.DynamicDataSourceContextHolder;
import com.reinsurance.query.BusinessReportQuery;
import com.reinsurance.service.IBusinessReportService;
import com.reinsurance.domain.ReportDataEntity;
import com.reinsurance.domain.RiskLiabilityEntity;
import com.reinsurance.dto.FinancialStatementDTO;
import com.reinsurance.dto.FinancialStatementExportDTO;
import com.reinsurance.dto.ReportDataDTO;
import com.reinsurance.enums.BasicDataEnums;
import com.reinsurance.enums.CedeoutEnums;
import com.reinsurance.mapper.BasicDataMaintainMapper;
import com.reinsurance.mapper.ReportDataMapper;
import com.reinsurance.mapper.RiskLiabilityMapper;
import com.reinsurance.query.ReportDataQuery;
import com.reinsurance.service.IFileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BusinessReportServiceImpl implements IBusinessReportService {

    @Autowired
    private ReportDataMapper reportDataMapper;

    @Autowired
    private BasicDataMaintainMapper basicDataMaintainMapper;

    @Autowired
    private RiskLiabilityMapper riskLiabilityMapper;

    @Autowired
    private IFileService fileService;

    /**
     * 报表统计导出
     * @param query
     */
    @Override
    public void reportStatistics(BusinessReportQuery query) {
        DateTime startDate = DateUtil.parse(query.getStartDate(), DatePattern.NORM_DATE_PATTERN);
        DateTime endDate = DateUtil.parse(query.getEndDate(), DatePattern.NORM_DATE_PATTERN);
        //数据库无记录 - 生成报表记录
        ReportDataEntity reportDataEntity = new ReportDataEntity();
        String reportCode = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN) + RandomUtil.randomNumbers(5);
        reportDataEntity.setReportCode(reportCode);
        reportDataEntity.setReportName("财务接口报表导出-" + reportCode + ".xlsx");
        reportDataEntity.setReportType(query.getReportType());
        reportDataEntity.setParentReportCode("0");
        reportDataEntity.setStartDate(startDate);
        reportDataEntity.setEndDate(endDate);
        String username = SecurityUtils.getUsername();
        Date nowDate = DateUtils.getNowDate();
        reportDataEntity.setStatus(BasicDataEnums.BusinessReportStatus.STATUS_2.getCode());
        reportDataEntity.setCreateBy(username);
        reportDataEntity.setCreateTime(nowDate);
        reportDataEntity.setUpdateBy(username);
        reportDataEntity.setUpdateTime(nowDate);
        reportDataMapper.insertReportData(reportDataEntity);
        CompletableFuture.runAsync(() -> export(query, reportDataEntity));
    }

    /**
     * 报表统计导入
     * @param reportDataDTO
     * @param file
     */
    @Override
    public void importReportStatistics(ReportDataDTO reportDataDTO, MultipartFile file) {



    }

    /**
     * 查询业务报表记录
     * @param query
     * @return
     */
    @Override
    public List<ReportDataDTO> reportList(BusinessReportQuery query) {
        ReportDataQuery reportDataQuery = new ReportDataQuery();
        reportDataQuery.setParentReportCode("0");
        reportDataQuery.setReportName(query.getReportName());
        if (StrUtil.isNotBlank(query.getStartDate())) {
            DateTime startDate = DateUtil.parse(query.getStartDate(), DatePattern.NORM_DATE_PATTERN);
            reportDataQuery.setStartDate(startDate);
        }
        if (StrUtil.isNotBlank(query.getEndDate())) {
            DateTime endDate = DateUtil.parse(query.getEndDate(), DatePattern.NORM_DATE_PATTERN);
            reportDataQuery.setEndDate(endDate);
        }
        if (StrUtil.isNotBlank(query.getReportType())) {
            //指定报表类型查询
            reportDataQuery.setReportType(query.getReportType());
        } else {
            //不指定，查询所有业务报表
            reportDataQuery.setReportTypes(BasicDataEnums.ReportType.businessReport());
        }
        List<ReportDataEntity> reportDataEntities = reportDataMapper.selectReportDataList(reportDataQuery);
        return ReinsuObjectUtil.convertList(reportDataEntities, ReportDataDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reportDelete(Long id) {
        ReportDataEntity reportDataEntity = reportDataMapper.selectReportDataById(id);
        if (StrUtil.isNotBlank(reportDataEntity.getReportDataPath())) {
            //删除文件
            FileUtils.deleteFile(reportDataEntity.getReportDataPath());
        }
        //删除数据
        reportDataMapper.deleteReportDataById(id);
    }

    private void export(BusinessReportQuery query, ReportDataEntity reportDataEntity) {
        //财务接口报表
        if (BasicDataEnums.ReportType.TYPE_1.getCode().equals(query.getReportType())) {
            //查询机构
            DynamicDataSourceContextHolder.setDataSourceType(DataSourceType.SLAVE.name());
            List<SysDictData> sysDictData = basicDataMaintainMapper.selectOrgShortNameDictLabel();
            //机构数据，key：编码，value：短名称
            Map<String, String> orgMap = sysDictData.stream()
                    .filter(data -> data.getDictValue().length() <= 4)
                    .collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel, (v1, v2) -> v1));
            //查询dws表数据
            List<FinancialStatementDTO> financialStatementDTOS = basicDataMaintainMapper.selectFinancialStatement(query.getStartDate(), query.getEndDate());
            DynamicDataSourceContextHolder.clearDataSourceType();
            //险种准备金类型
            Map<String, Integer> riskReserveTypeMap = null;
            if (CollectionUtil.isNotEmpty(financialStatementDTOS)) {
                List<RiskLiabilityEntity> riskReserveTypeList = riskLiabilityMapper.selectRiskLiabilityReserveType();
                riskReserveTypeMap = riskReserveTypeList.stream()
                        .collect(Collectors.toMap(RiskLiabilityEntity::getRiskCode, RiskLiabilityEntity::getReserveType, (v1, v2) -> v1));
            }
            List<FinancialStatementExportDTO> resultList = CollUtil.list(false);
            for (FinancialStatementDTO financialStatementDTO : financialStatementDTOS) {
                FinancialStatementExportDTO exportDTO = new FinancialStatementExportDTO();
                exportDTO.setOrgName(orgMap.get(financialStatementDTO.getOrgCode()));
                exportDTO.setRiskCode(financialStatementDTO.getRiskCode());
                exportDTO.setRiskName(financialStatementDTO.getRiskName());
                exportDTO.setRepremiumPayable(Optional.ofNullable(financialStatementDTO.getRepremiumPayable()).orElse(BigDecimal.ZERO));
                exportDTO.setCedeoutCommission(Optional.ofNullable(financialStatementDTO.getCedeoutCommission()).orElse(BigDecimal.ZERO));
                exportDTO.setReturnExpiredGold(Optional.ofNullable(financialStatementDTO.getReturnExpiredGold()).orElse(BigDecimal.ZERO));
                String riskCode = financialStatementDTO.getRiskCode();
                BigDecimal returnClaimAmount = Optional.ofNullable(financialStatementDTO.getReturnClaimAmount()).orElse(BigDecimal.ZERO);
                if (CedeoutEnums.准备金类型_寿险.getValue().equals(riskReserveTypeMap.get(riskCode))) {
                    //摊回死亡给付 = 寿险责任准备金
                    exportDTO.setReturnClaim2(returnClaimAmount);
                } else if (CedeoutEnums.准备金类型_未到期责任.getValue().equals(riskReserveTypeMap.get(riskCode))) {
                    //摊回赔款支出 = 未到期责任准备金
                    exportDTO.setReturnClaim1(returnClaimAmount);
                } else if (CedeoutEnums.准备金类型_长期健康险.getValue().equals(riskReserveTypeMap.get(riskCode))) {
                    //摊回医疗给付 = 长期健康险责任准备金
                    exportDTO.setReturnClaim3(returnClaimAmount);
                } else {
                    //未找到准备金类型，不set数据
                }
                resultList.add(exportDTO);
            }
            //文件存储路径
            String path = fileService.getStoragePath() + DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN) + "/" + reportDataEntity.getReportName();
            try {
                FileUtil.touch(path);
                EasyExcel.write(path, FinancialStatementExportDTO.class).sheet("sheet1").doWrite(resultList);
                reportDataEntity.setReportDataPath(path);
                reportDataEntity.setStatus(BasicDataEnums.BusinessReportStatus.STATUS_0.getCode());
            } catch (Exception e) {
                log.error("导出业务报表失败：", e);
                reportDataEntity.setStatus(BasicDataEnums.BusinessReportStatus.STATUS_3.getCode());
                //删除文件
                FileUtil.del(path);
            }
            //修改文件记录
            reportDataEntity.setUpdateTime(DateUtils.getNowDate());
            reportDataMapper.updateReportData(reportDataEntity);
        }

    }

}
