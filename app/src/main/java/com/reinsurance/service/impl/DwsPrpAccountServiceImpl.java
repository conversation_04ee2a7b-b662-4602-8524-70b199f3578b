package com.reinsurance.service.impl;

import java.util.List;
import java.util.Arrays;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.jd.lightning.common.annotation.DataSource;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.enums.DataSourceType;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.DateUtils;
import com.jd.lightning.common.utils.SecurityUtils;
import com.jd.lightning.common.utils.poi.ExcelUtil;
import com.jd.lightning.common.utils.StringUtils;
import com.jd.lightning.common.constant.HttpStatus;

import com.reinsurance.mapper.DwsPrpAccountMapper;
import com.reinsurance.dto.DwsPrpAccountDTO;
import com.reinsurance.query.DwsPrpAccountQuery;
import com.reinsurance.domain.DwsPrpAccountEntity;
import com.reinsurance.utils.ReinsuObjectUtil;
import com.reinsurance.service.IDwsPrpAccountService;
import com.reinsurance.service.IDwsRegulatoryReportService;
import com.reinsurance.enums.ReportDataSource;
import com.reinsurance.enums.ReportPushStatus;
import com.reinsurance.enums.RegulatorReport;
import com.reinsurance.enums.PrpReport;

import lombok.extern.slf4j.Slf4j;

/**
 * 保单登记再保账单信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Slf4j
@Service
@DataSource(DataSourceType.SLAVE)
public class DwsPrpAccountServiceImpl implements IDwsPrpAccountService {
    
    @Autowired
    private DwsPrpAccountMapper dwsPrpAccountMapper;

    @Autowired
    private IDwsRegulatoryReportService dwsRegulatoryReportService;

    /**
     * 查询保单登记再保账单信息
     *
     * @param Id 保单登记再保账单信息主键
     * @return 保单登记再保账单信息
     */
    @Override
    public DwsPrpAccountDTO selectDwsPrpAccountById(Long Id) {
        DwsPrpAccountEntity entity = dwsPrpAccountMapper.selectDwsPrpAccountById(Id);
        return ReinsuObjectUtil.convertModel(entity, DwsPrpAccountDTO.class);
    }

    /**
     * 查询保单登记再保账单信息列表
     *
     * @param dwsPrpAccountQuery 保单登记再保账单信息
     * @return 保单登记再保账单信息
     */
    @Override
    public List<DwsPrpAccountDTO> selectDwsPrpAccountList(DwsPrpAccountQuery dwsPrpAccountQuery) {
        List<DwsPrpAccountEntity> entityList = dwsPrpAccountMapper.selectDwsPrpAccountList(dwsPrpAccountQuery);
        return ReinsuObjectUtil.convertList(entityList, DwsPrpAccountDTO.class);
    }

    /**
     * 新增保单登记再保账单信息
     *
     * @param dwsPrpAccountDTO 保单登记再保账单信息
     * @return 结果
     */
    @Override
    public int insertDwsPrpAccount(DwsPrpAccountDTO dwsPrpAccountDTO) {
        DwsPrpAccountEntity dwsPrpAccountEntity = ReinsuObjectUtil.convertModel(dwsPrpAccountDTO, DwsPrpAccountEntity.class);
        dwsPrpAccountEntity.setCreateTime(DateUtils.getNowDate());
        return dwsPrpAccountMapper.insertDwsPrpAccount(dwsPrpAccountEntity);
    }

    /**
     * 批量新增保单登记再保账单信息
     *
     * @param dwsPrpAccountList 保单登记再保账单信息列表
     * @return 结果
     */
    @Override
    public int insertBatchDwsPrpAccount(List<DwsPrpAccountDTO> dwsPrpAccountList) {
        List<DwsPrpAccountEntity> entitys = ReinsuObjectUtil.convertList(dwsPrpAccountList, DwsPrpAccountEntity.class);
        return dwsPrpAccountMapper.insertBatchDwsPrpAccount(entitys);
    }

    /**
     * 修改保单登记再保账单信息
     *
     * @param dwsPrpAccountDTO 保单登记再保账单信息
     * @return 结果
     */
    @Override
    public int updateDwsPrpAccount(DwsPrpAccountDTO dwsPrpAccountDTO) {
        DwsPrpAccountEntity dwsPrpAccountEntity = ReinsuObjectUtil.convertModel(dwsPrpAccountDTO, DwsPrpAccountEntity.class);
        dwsPrpAccountEntity.setUpdateTime(DateUtils.getNowDate());
        return dwsPrpAccountMapper.updateDwsPrpAccount(dwsPrpAccountEntity);
    }

    /**
     * 批量删除保单登记再保账单信息
     *
     * @param Ids 需要删除的保单登记再保账单信息主键
     * @return 结果
     */
    @Override
    public int deleteDwsPrpAccountByIds(Long[] Ids) {
        return dwsPrpAccountMapper.deleteDwsPrpAccountByIds(Ids);
    }

    /**
     * 删除保单登记再保账单信息信息
     *
     * @param Id 保单登记再保账单信息主键
     * @return 结果
     */
    @Override
    public int deleteDwsPrpAccountById(Long Id) {
        DwsPrpAccountEntity entity = dwsPrpAccountMapper.selectDwsPrpAccountById(Id);
        if(entity == null) {
            return 0;
        }
        if(ReportDataSource.系统.getCode() == entity.getDataSource()) {
            return -1;
        }
        if(ReportPushStatus.已推送.getCode() == entity.getPushStatus()) {
            return -2;
        }
        int deleteRows = dwsPrpAccountMapper.deleteDwsPrpAccountById(Id);
        if(deleteRows > 0) {
            dwsRegulatoryReportService.insertOrUpdateRegulatoryReport(BusinessType.DELETE, RegulatorReport.保单登记数据报送, PrpReport.LRAccount.getCode(), Arrays.asList(entity.getReportYear()));
        }
        return deleteRows;
    }

    /**
     * 导入保单登记再保账单信息
     *
     * @param companyCode 保险机构代码
     * @param companyName 保险机构名称
     * @param manageCom 管理机构
     * @param file 导入文件
     * @return 结果
     */
    @Override
    public Result importDwsPrpAccount(String companyCode, String companyName, String manageCom, MultipartFile file) {
        try {
            // 1. 解析Excel文件
            ExcelUtil<DwsPrpAccountDTO> util = new ExcelUtil<>(DwsPrpAccountDTO.class);
            List<DwsPrpAccountDTO> accountList = util.importExcel(file.getInputStream());
            
            if (StringUtils.isNull(accountList) || accountList.size() == 0) {
                return Result.error("导入数据不能为空！");
            }
            
            // 2. 数据校验和处理
            for (DwsPrpAccountDTO dto : accountList) {
                dto.setCompanyCode(companyCode);
                dto.setDataSource(ReportDataSource.人工.getCode());
                dto.setPushStatus(ReportPushStatus.未推送.getCode());
                dto.setCreateBy(SecurityUtils.getUsername());
                dto.setCreateTime(DateUtils.getNowDate());
            }
            
            // 3. 批量入库
            List<DwsPrpAccountEntity> dwsPrpAccountList = ReinsuObjectUtil.convertList(accountList, DwsPrpAccountEntity.class);
            int insertRows = dwsPrpAccountMapper.insertBatchDwsPrpAccount(dwsPrpAccountList);
            if(insertRows > 0) {
                // 4. 更新监管报表推送状态
                List<Integer> reportYears = accountList.stream().map(DwsPrpAccountDTO::getReportYear).distinct().collect(Collectors.toList());
                dwsRegulatoryReportService.insertOrUpdateRegulatoryReport(BusinessType.IMPORT, RegulatorReport.保单登记数据报送, PrpReport.LRAccount.getCode(), reportYears);
            }
            return Result.success("成功导入" + insertRows + "行", HttpStatus.HTTP_OK);
        }catch(Exception e) {
            log.error("再保导入保单登记账单信息出错, 错误原因:", e);
            return Result.error("导入账单信息表出错，请联系管理员。");
        }
    }

    /**
     * 导出保单登记再保账单信息
     *
     * @param response 响应对象
     * @param dwsPrpAccountQuery 查询条件
     */
    @Override
    public void exportDwsPrpAccount(HttpServletResponse response, DwsPrpAccountQuery dwsPrpAccountQuery) {
        List<DwsPrpAccountDTO> list = selectDwsPrpAccountList(dwsPrpAccountQuery);
        ExcelUtil<DwsPrpAccountDTO> util = new ExcelUtil<>(DwsPrpAccountDTO.class);
        util.exportExcel(response, list, "保单登记再保账单信息数据");
    }

    /**
     * 检查保单登记再保账单信息是否存在
     *
     * @param dwsPrpAccountQuery 查询条件
     * @return 结果
     */
    @Override
    public int selectDwsPrpAccountExists(DwsPrpAccountQuery dwsPrpAccountQuery) {
        return dwsPrpAccountMapper.selectDwsPrpAccountExists(dwsPrpAccountQuery);
    }

    /**
     * 更新推送状态
     *
     * @param Ids 主键数组
     * @return 结果
     */
    @Override
    public Result updateDwsPrpAccountPushStatus(Long[] Ids) {
        String pushBy = SecurityUtils.getUsername();
        int updateRows = dwsPrpAccountMapper.updateDwsPrpAccountPushStatus(ReportPushStatus.已推送.getCode(), pushBy, Ids);
        if(updateRows > 0) {
            return Result.success("推送成功");
        }
        return Result.error("推送失败");
    }

    /**
     * 查询年度报表推送状态
     *
     * @param reportYear 报表年份
     * @return 结果
     */
    @Override
    public Integer selectAnnualReportShouldPushStatus(int reportYear) {
        return dwsPrpAccountMapper.selectAnnualReportShouldPushStatus(reportYear);
    }

    /**
     * 生成再保账单信息表数据
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param reportYear 报表年份
     * @param reportMonth 报表月份
     * @return 结果
     */
    @Override
    public Result generatePrpAccountData(String startDate, String endDate, Integer reportYear, Integer reportMonth) {
        // TODO: 实现数据生成逻辑
        return Result.success("数据生成功能待实现");
    }
}
