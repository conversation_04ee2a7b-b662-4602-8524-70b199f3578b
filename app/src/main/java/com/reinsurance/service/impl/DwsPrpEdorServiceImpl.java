package com.reinsurance.service.impl;

import java.util.List;
import java.util.Arrays;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.jd.lightning.common.annotation.DataSource;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.enums.DataSourceType;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.DateUtils;
import com.jd.lightning.common.utils.SecurityUtils;
import com.jd.lightning.common.utils.poi.ExcelUtil;
import com.jd.lightning.common.utils.StringUtils;
import com.jd.lightning.common.constant.HttpStatus;

import com.reinsurance.mapper.DwsPrpEdorMapper;
import com.reinsurance.dto.DwsPrpEdorDTO;
import com.reinsurance.query.DwsPrpEdorQuery;
import com.reinsurance.domain.DwsPrpEdorEntity;
import com.reinsurance.utils.ReinsuObjectUtil;
import com.reinsurance.service.IDwsPrpEdorService;
import com.reinsurance.service.IDwsRegulatoryReportService;
import com.reinsurance.enums.ReportDataSource;
import com.reinsurance.enums.ReportPushStatus;
import com.reinsurance.enums.RegulatorReport;
import com.reinsurance.enums.PrpReport;

import lombok.extern.slf4j.Slf4j;

/**
 * 再保保全险种明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Slf4j
@Service
@DataSource(DataSourceType.SLAVE)
public class DwsPrpEdorServiceImpl implements IDwsPrpEdorService {
    
    @Autowired
    private DwsPrpEdorMapper dwsPrpEdorMapper;

    @Autowired
    private IDwsRegulatoryReportService dwsRegulatoryReportService;

    /**
     * 查询再保保全险种明细
     *
     * @param Id 再保保全险种明细主键
     * @return 再保保全险种明细
     */
    @Override
    public DwsPrpEdorDTO selectDwsPrpEdorById(Long Id) {
        DwsPrpEdorEntity entity = dwsPrpEdorMapper.selectDwsPrpEdorById(Id);
        return ReinsuObjectUtil.convertModel(entity, DwsPrpEdorDTO.class);
    }

    /**
     * 查询再保保全险种明细列表
     *
     * @param dwsPrpEdorQuery 再保保全险种明细
     * @return 再保保全险种明细
     */
    @Override
    public List<DwsPrpEdorDTO> selectDwsPrpEdorList(DwsPrpEdorQuery dwsPrpEdorQuery) {
        List<DwsPrpEdorEntity> entityList = dwsPrpEdorMapper.selectDwsPrpEdorList(dwsPrpEdorQuery);
        return ReinsuObjectUtil.convertList(entityList, DwsPrpEdorDTO.class);
    }

    /**
     * 新增再保保全险种明细
     *
     * @param dwsPrpEdorDTO 再保保全险种明细
     * @return 结果
     */
    @Override
    public int insertDwsPrpEdor(DwsPrpEdorDTO dwsPrpEdorDTO) {
        DwsPrpEdorEntity dwsPrpEdorEntity = ReinsuObjectUtil.convertModel(dwsPrpEdorDTO, DwsPrpEdorEntity.class);
        dwsPrpEdorEntity.setCreateTime(DateUtils.getNowDate());
        return dwsPrpEdorMapper.insertDwsPrpEdor(dwsPrpEdorEntity);
    }

    /**
     * 批量新增再保保全险种明细
     *
     * @param dwsPrpEdorList 再保保全险种明细列表
     * @return 结果
     */
    @Override
    public int insertBatchDwsPrpEdor(List<DwsPrpEdorDTO> dwsPrpEdorList) {
        List<DwsPrpEdorEntity> entitys = ReinsuObjectUtil.convertList(dwsPrpEdorList, DwsPrpEdorEntity.class);
        return dwsPrpEdorMapper.insertBatchDwsPrpEdor(entitys);
    }

    /**
     * 修改再保保全险种明细
     *
     * @param dwsPrpEdorDTO 再保保全险种明细
     * @return 结果
     */
    @Override
    public int updateDwsPrpEdor(DwsPrpEdorDTO dwsPrpEdorDTO) {
        DwsPrpEdorEntity dwsPrpEdorEntity = ReinsuObjectUtil.convertModel(dwsPrpEdorDTO, DwsPrpEdorEntity.class);
        dwsPrpEdorEntity.setUpdateTime(DateUtils.getNowDate());
        return dwsPrpEdorMapper.updateDwsPrpEdor(dwsPrpEdorEntity);
    }

    /**
     * 批量删除再保保全险种明细
     *
     * @param Ids 需要删除的再保保全险种明细主键
     * @return 结果
     */
    @Override
    public int deleteDwsPrpEdorByIds(Long[] Ids) {
        return dwsPrpEdorMapper.deleteDwsPrpEdorByIds(Ids);
    }

    /**
     * 删除再保保全险种明细信息
     *
     * @param Id 再保保全险种明细主键
     * @return 结果
     */
    @Override
    public int deleteDwsPrpEdorById(Long Id) {
        DwsPrpEdorEntity entity = dwsPrpEdorMapper.selectDwsPrpEdorById(Id);
        if(entity == null) {
            return 0;
        }
        if(ReportDataSource.系统.getCode() == entity.getDataSource()) {
            return -1;
        }
        if(ReportPushStatus.已推送.getCode() == entity.getPushStatus()) {
            return -2;
        }
        int deleteRows = dwsPrpEdorMapper.deleteDwsPrpEdorById(Id);
        if(deleteRows > 0) {
            dwsRegulatoryReportService.insertOrUpdateRegulatoryReport(BusinessType.DELETE, RegulatorReport.保单登记数据报送, PrpReport.LREndor.getCode(), Arrays.asList(entity.getPolYear()));
        }
        return deleteRows;
    }

    /**
     * 导入再保保全险种明细
     *
     * @param companyCode 保险机构代码
     * @param companyName 保险机构名称
     * @param manageCom 管理机构
     * @param file 导入文件
     * @return 结果
     */
    @Override
    public Result importDwsPrpEdor(String companyCode, String companyName, String manageCom, MultipartFile file) {
        try {
            // 1. 解析Excel文件
            ExcelUtil<DwsPrpEdorDTO> util = new ExcelUtil<>(DwsPrpEdorDTO.class);
            List<DwsPrpEdorDTO> edorList = util.importExcel(file.getInputStream());
            
            if (StringUtils.isNull(edorList) || edorList.size() == 0) {
                return Result.error("导入数据不能为空！");
            }
            
            // 2. 数据校验和处理
            for (DwsPrpEdorDTO dto : edorList) {
                dto.setCompanyCode(companyCode);
                dto.setDataSource(ReportDataSource.人工.getCode());
                dto.setPushStatus(ReportPushStatus.未推送.getCode());
                dto.setCreateBy(SecurityUtils.getUsername());
                dto.setCreateTime(DateUtils.getNowDate());
            }
            
            // 3. 批量入库
            List<DwsPrpEdorEntity> dwsPrpEdorList = ReinsuObjectUtil.convertList(edorList, DwsPrpEdorEntity.class);
            int insertRows = dwsPrpEdorMapper.insertBatchDwsPrpEdor(dwsPrpEdorList);
            if(insertRows > 0) {
                // 4. 更新监管报表推送状态
                List<Integer> reportYears = edorList.stream().map(DwsPrpEdorDTO::getPolYear).distinct().collect(Collectors.toList());
                dwsRegulatoryReportService.insertOrUpdateRegulatoryReport(BusinessType.IMPORT, RegulatorReport.保单登记数据报送, PrpReport.LREndor.getCode(), reportYears);
            }
            return Result.success("成功导入" + insertRows + "行", HttpStatus.HTTP_OK);
        }catch(Exception e) {
            log.error("再保导入保全险种明细出错, 错误原因:", e);
            return Result.error("导入保全险种明细表出错，请联系管理员。");
        }
    }

    /**
     * 导出再保保全险种明细
     *
     * @param response 响应对象
     * @param dwsPrpEdorQuery 查询条件
     */
    @Override
    public void exportDwsPrpEdor(HttpServletResponse response, DwsPrpEdorQuery dwsPrpEdorQuery) {
        List<DwsPrpEdorDTO> list = selectDwsPrpEdorList(dwsPrpEdorQuery);
        ExcelUtil<DwsPrpEdorDTO> util = new ExcelUtil<>(DwsPrpEdorDTO.class);
        util.exportExcel(response, list, "再保保全险种明细数据");
    }

    /**
     * 检查再保保全险种明细是否存在
     *
     * @param dwsPrpEdorQuery 查询条件
     * @return 结果
     */
    @Override
    public int selectDwsPrpEdorExists(DwsPrpEdorQuery dwsPrpEdorQuery) {
        return dwsPrpEdorMapper.selectDwsPrpEdorExists(dwsPrpEdorQuery);
    }

    /**
     * 更新推送状态
     *
     * @param Ids 主键数组
     * @return 结果
     */
    @Override
    public Result updateDwsPrpEdorPushStatus(Long[] Ids) {
        String pushBy = SecurityUtils.getUsername();
        int updateRows = dwsPrpEdorMapper.updateDwsPrpEdorPushStatus(ReportPushStatus.已推送.getCode(), pushBy, Ids);
        if(updateRows > 0) {
            return Result.success("推送成功");
        }
        return Result.error("推送失败");
    }

    /**
     * 查询年度报表推送状态
     *
     * @param reportYear 报表年份
     * @return 结果
     */
    @Override
    public Integer selectAnnualReportShouldPushStatus(int reportYear) {
        return dwsPrpEdorMapper.selectAnnualReportShouldPushStatus(reportYear);
    }

    /**
     * 生成再保保全险种明细数据
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param reportYear 报表年份
     * @param reportMonth 报表月份
     * @return 结果
     */
    @Override
    public Result generatePrpEdorData(String startDate, String endDate, Integer reportYear, Integer reportMonth) {
        // TODO: 实现数据生成逻辑
        return Result.success("数据生成功能待实现");
    }
}
