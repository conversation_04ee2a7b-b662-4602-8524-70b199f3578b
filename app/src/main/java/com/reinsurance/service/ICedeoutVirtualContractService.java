package com.reinsurance.service;

import java.util.List;

import com.reinsurance.dto.CedeoutVirtualCompanyTrackDTO;
import com.reinsurance.dto.CedeoutVirtualCompanyApplyDTO;
import com.reinsurance.dto.CedeoutVirtualCompanyDTO;
import com.reinsurance.dto.CedeoutVirtualContractApplyDTO;
import com.reinsurance.dto.CedeoutVirtualContractDTO;
import com.reinsurance.dto.CedeoutVirtualContractTrackDTO;
import com.reinsurance.query.CedeoutVirtualCompanyApplyQuery;
import com.reinsurance.query.CedeoutVirtualCompanyQuery;
import com.reinsurance.query.CedeoutVirtualCompanyTrackQuery;
import com.reinsurance.query.CedeoutVirtualContractApplyQuery;
import com.reinsurance.query.CedeoutVirtualContractQuery;
import com.reinsurance.query.CedeoutVirtualContractTrackQuery;

/**
 * 虚拟合同Service接口
 * 
 * <AUTHOR>
 * @date 2024-03-19
 */
public interface ICedeoutVirtualContractService 
{
    /**
     * 查询虚拟合同
     * 
     * @param id 虚拟合同主键
     * @return 虚拟合同
     */
    public CedeoutVirtualContractDTO selectCedeoutVirtualContractById(Long id);

    /**
     * 查询虚拟合同列表
     * 
     * @param cedeoutVirtualContractQuery 虚拟合同
     * @return 虚拟合同集合
     */
    public List<CedeoutVirtualContractDTO> selectCedeoutVirtualContractList(CedeoutVirtualContractQuery cedeoutVirtualContractQuery);

    /**
     * 新增虚拟合同
     * 
     * @param cedeoutVirtualContractDTO 虚拟合同
     * @return 结果
     */
    public int insertCedeoutVirtualContract(CedeoutVirtualContractDTO cedeoutVirtualContractDTO);

    /**
     * 修改虚拟合同
     * 
     * @param cedeoutVirtualContractDTO 虚拟合同
     * @return 结果
     */
    public int updateCedeoutVirtualContract(CedeoutVirtualContractDTO cedeoutVirtualContractDTO);

    /**
     * 批量删除虚拟合同
     * 
     * @param ids 需要删除的虚拟合同主键集合
     * @return 结果
     */
    public int deleteCedeoutVirtualContractByIds(Long[] ids);

    /**
     * 删除虚拟合同信息
     * 
     * @param id 虚拟合同主键
     * @return 结果
     */
    public int deleteCedeoutVirtualContractById(Long id);
    
    
    /**
     * 查询虚拟合同申请
     * 
     * @param id 虚拟合同申请主键
     * @return 虚拟合同申请
     */
    public CedeoutVirtualContractApplyDTO selectCedeoutVirtualContractApplyById(Long id);

    /**
     * 查询虚拟合同申请列表
     * 
     * @param cedeoutVirtualContractApplyQuery 虚拟合同申请
     * @return 虚拟合同申请集合
     */
    public List<CedeoutVirtualContractApplyDTO> selectCedeoutVirtualContractApplyList(CedeoutVirtualContractApplyQuery cedeoutVirtualContractApplyQuery);

    /**
     * 新增虚拟合同申请
     * 
     * @param cedeoutVirtualContractApplyDTO 虚拟合同申请
     * @return 结果
     */
    public int insertCedeoutVirtualContractApply(CedeoutVirtualContractApplyDTO cedeoutVirtualContractApplyDTO);

    /**
     * 修改虚拟合同申请
     * 
     * @param cedeoutVirtualContractApplyDTO 虚拟合同申请
     * @return 结果
     */
    public int updateCedeoutVirtualContractApply(CedeoutVirtualContractApplyDTO cedeoutVirtualContractApplyDTO);

    /**
     * 批量删除虚拟合同申请
     * 
     * @param ids 需要删除的虚拟合同申请主键集合
     * @return 结果
     */
    public int deleteCedeoutVirtualContractApplyByIds(Long[] ids);

    /**
     * 删除虚拟合同申请信息
     * 
     * @param id 虚拟合同申请主键
     * @return 结果
     */
    public int deleteCedeoutVirtualContractApplyById(Long id);
    
    /**
     * 查询虚拟合同轨迹
     * 
     * @param trackId 虚拟合同轨迹主键
     * @return 虚拟合同轨迹
     */
    public CedeoutVirtualContractTrackDTO selectCedeoutVirtualContractTrackByTrackId(Long trackId);

    /**
     * 查询虚拟合同轨迹列表
     * 
     * @param cedeoutVirtualContractTrackQuery 虚拟合同轨迹
     * @return 虚拟合同轨迹集合
     */
    public List<CedeoutVirtualContractTrackDTO> selectCedeoutVirtualContractTrackList(CedeoutVirtualContractTrackQuery cedeoutVirtualContractTrackQuery);

    /**
     * 新增虚拟合同轨迹
     * 
     * @param cedeoutVirtualContractTrack 虚拟合同轨迹
     * @return 结果
     */
    public int insertCedeoutVirtualContractTrack(CedeoutVirtualContractTrackDTO cedeoutVirtualContractTrackDTO);

    /**
     * 修改虚拟合同轨迹
     * 
     * @param cedeoutVirtualContractTrack 虚拟合同轨迹
     * @return 结果
     */
    public int updateCedeoutVirtualContractTrack(CedeoutVirtualContractTrackDTO cedeoutVirtualContractTrackDTO);

    /**
     * 批量删除虚拟合同轨迹
     * 
     * @param trackIds 需要删除的虚拟合同轨迹主键集合
     * @return 结果
     */
    public int deleteCedeoutVirtualContractTrackByTrackIds(Long[] trackIds);

    /**
     * 删除虚拟合同轨迹信息
     * 
     * @param trackId 虚拟合同轨迹主键
     * @return 结果
     */
    public int deleteCedeoutVirtualContractTrackByTrackId(Long trackId);
    
    
    /**
     * 查询虚拟合同再保公司关系
     * 
     * @param id 虚拟合同再保公司关系主键
     * @return 虚拟合同再保公司关系
     */
    public CedeoutVirtualCompanyDTO selectCedeoutVirtualCompanyById(Long id);

    /**
     * 查询虚拟合同再保公司关系列表
     * 
     * @param cedeoutVirtualCompanyQuery 虚拟合同再保公司关系
     * @return 虚拟合同再保公司关系集合
     */
    public List<CedeoutVirtualCompanyDTO> selectCedeoutVirtualCompanyList(CedeoutVirtualCompanyQuery cedeoutVirtualCompanyQuery);

    /**
     * 新增虚拟合同再保公司关系
     * 
     * @param cedeoutVirtualCompanyDTO 虚拟合同再保公司关系
     * @return 结果
     */
    public int insertCedeoutVirtualCompany(CedeoutVirtualCompanyDTO cedeoutVirtualCompanyDTO);

    /**
     * 修改虚拟合同再保公司关系
     * 
     * @param cedeoutVirtualCompanyDTO 虚拟合同再保公司关系
     * @return 结果
     */
    public int updateCedeoutVirtualCompany(CedeoutVirtualCompanyDTO cedeoutVirtualCompanyDTO);

    /**
     * 批量删除虚拟合同再保公司关系
     * 
     * @param ids 需要删除的虚拟合同再保公司关系主键集合
     * @return 结果
     */
    public int deleteCedeoutVirtualCompanyByIds(Long[] ids);

    /**
     * 删除虚拟合同再保公司关系信息
     * 
     * @param id 虚拟合同再保公司关系主键
     * @return 结果
     */
    public int deleteCedeoutVirtualCompanyById(Long id);
    
    /**
     * 查询虚拟合同再保公司关系申请
     * 
     * @param id 虚拟合同再保公司关系申请主键
     * @return 虚拟合同再保公司关系申请
     */
    public CedeoutVirtualCompanyApplyDTO selectCedeoutVirtualCompanyApplyById(Long id);

    /**
     * 查询虚拟合同再保公司关系申请列表
     * 
     * @param cedeoutVirtualCompanyApply 虚拟合同再保公司关系申请
     * @return 虚拟合同再保公司关系申请集合
     */
    public List<CedeoutVirtualCompanyApplyDTO> selectCedeoutVirtualCompanyApplyList(CedeoutVirtualCompanyApplyQuery cedeoutVirtualCompanyApplyQuery);

    /**
     * 新增虚拟合同再保公司关系申请
     * 
     * @param cedeoutVirtualCompanyApplyDTO 虚拟合同再保公司关系申请
     * @return 结果
     */
    public int insertCedeoutVirtualCompanyApply(CedeoutVirtualCompanyApplyDTO cedeoutVirtualCompanyApplyDTO);

    /**
     * 修改虚拟合同再保公司关系申请
     * 
     * @param cedeoutVirtualCompanyApplyDTO 虚拟合同再保公司关系申请
     * @return 结果
     */
    public int updateCedeoutVirtualCompanyApply(CedeoutVirtualCompanyApplyDTO cedeoutVirtualCompanyApplyDTO);

    /**
     * 批量删除虚拟合同再保公司关系申请
     * 
     * @param ids 需要删除的虚拟合同再保公司关系申请主键集合
     * @return 结果
     */
    public int deleteCedeoutVirtualCompanyApplyByIds(Long[] ids);

    /**
     * 删除虚拟合同再保公司关系申请信息
     * 
     * @param id 虚拟合同再保公司关系申请主键
     * @return 结果
     */
    public int deleteCedeoutVirtualCompanyApplyById(Long id);
    
    /**
     * 查询虚拟合同再保公司关系轨迹
     * 
     * @param trackId 虚拟合同再保公司关系轨迹主键
     * @return 虚拟合同再保公司关系轨迹
     */
    public CedeoutVirtualCompanyTrackDTO selectCedeoutVirtualCompanyTrackByTrackId(Long trackId);

    /**
     * 查询虚拟合同再保公司关系轨迹列表
     * 
     * @param cedeoutVirtualCompanyTrackQuery 虚拟合同再保公司关系轨迹
     * @return 虚拟合同再保公司关系轨迹集合
     */
    public List<CedeoutVirtualCompanyTrackDTO> selectCedeoutVirtualCompanyTrackList(CedeoutVirtualCompanyTrackQuery cedeoutVirtualCompanyTrackQuery);

    /**
     * 新增虚拟合同再保公司关系轨迹
     * 
     * @param cedeoutVirtualCompanyTrackDTO 虚拟合同再保公司关系轨迹
     * @return 结果
     */
    public int insertCedeoutVirtualCompanyTrack(CedeoutVirtualCompanyTrackDTO cedeoutVirtualCompanyTrackDTO);

    /**
     * 修改虚拟合同再保公司关系轨迹
     * 
     * @param cedeoutVirtualCompanyTrackDTO 虚拟合同再保公司关系轨迹
     * @return 结果
     */
    public int updateCedeoutVirtualCompanyTrack(CedeoutVirtualCompanyTrackDTO cedeoutVirtualCompanyTrackDTO);

    /**
     * 批量删除虚拟合同再保公司关系轨迹
     * 
     * @param trackIds 需要删除的虚拟合同再保公司关系轨迹主键集合
     * @return 结果
     */
    public int deleteCedeoutVirtualCompanyTrackByTrackIds(Long[] trackIds);

    /**
     * 删除虚拟合同再保公司关系轨迹信息
     * 
     * @param trackId 虚拟合同再保公司关系轨迹主键
     * @return 结果
     */
    public int deleteCedeoutVirtualCompanyTrackByTrackId(Long trackId);
}
