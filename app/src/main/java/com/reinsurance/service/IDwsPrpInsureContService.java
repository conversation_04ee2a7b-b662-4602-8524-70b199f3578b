package com.reinsurance.service;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import com.jd.lightning.common.core.domain.Result;
import com.reinsurance.dto.DwsPrpInsureContDTO;
import com.reinsurance.query.DwsPrpInsureContQuery;

/**
 * 保单登记再保合同信息Service接口
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface IDwsPrpInsureContService {
    
    /**
     * 查询保单登记再保合同信息
     *
     * @param Id 保单登记再保合同信息主键
     * @return 保单登记再保合同信息
     */
    public DwsPrpInsureContDTO selectDwsPrpInsureContById(Long Id);

    /**
     * 查询保单登记再保合同信息列表
     *
     * @param dwsPrpInsureContQuery 保单登记再保合同信息
     * @return 保单登记再保合同信息集合
     */
    public List<DwsPrpInsureContDTO> selectDwsPrpInsureContList(DwsPrpInsureContQuery dwsPrpInsureContQuery);

    /**
     * 新增保单登记再保合同信息
     *
     * @param dwsPrpInsureContDTO 保单登记再保合同信息
     * @return 结果
     */
    public int insertDwsPrpInsureCont(DwsPrpInsureContDTO dwsPrpInsureContDTO);

    /**
     * 批量新增保单登记再保合同信息
     *
     * @param dwsPrpInsureContList 保单登记再保合同信息列表
     * @return 结果
     */
    public int insertBatchDwsPrpInsureCont(List<DwsPrpInsureContDTO> dwsPrpInsureContList);

    /**
     * 修改保单登记再保合同信息
     *
     * @param dwsPrpInsureContDTO 保单登记再保合同信息
     * @return 结果
     */
    public int updateDwsPrpInsureCont(DwsPrpInsureContDTO dwsPrpInsureContDTO);

    /**
     * 批量删除保单登记再保合同信息
     *
     * @param Ids 需要删除的保单登记再保合同信息主键集合
     * @return 结果
     */
    public int deleteDwsPrpInsureContByIds(Long[] Ids);

    /**
     * 删除保单登记再保合同信息信息
     *
     * @param Id 保单登记再保合同信息主键
     * @return 结果
     */
    public int deleteDwsPrpInsureContById(Long Id);

    /**
     * 导入保单登记再保合同信息
     *
     * @param companyCode 保险机构代码
     * @param companyName 保险机构名称
     * @param manageCom 管理机构
     * @param file 导入文件
     * @return 结果
     */
    public Result importDwsPrpInsureCont(String companyCode, String companyName, String manageCom, MultipartFile file);

    /**
     * 导出保单登记再保合同信息
     *
     * @param response 响应对象
     * @param dwsPrpInsureContQuery 查询条件
     */
    public void exportDwsPrpInsureCont(HttpServletResponse response, DwsPrpInsureContQuery dwsPrpInsureContQuery);

    /**
     * 检查保单登记再保合同信息是否存在
     *
     * @param dwsPrpInsureContQuery 查询条件
     * @return 结果
     */
    public int selectDwsPrpInsureContExists(DwsPrpInsureContQuery dwsPrpInsureContQuery);

    /**
     * 更新推送状态
     *
     * @param Ids 主键数组
     * @return 结果
     */
    public Result updateDwsPrpInsureContPushStatus(Long[] Ids);

    /**
     * 查询年度报表推送状态
     *
     * @param reportYear 报表年份
     * @return 结果
     */
    public Integer selectAnnualReportShouldPushStatus(int reportYear);

    /**
     * 生成再保合同信息表数据
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param reportYear 报表年份
     * @param reportMonth 报表月份
     * @return 结果
     */
    public Result generatePrpInsureContData(String startDate, String endDate, Integer reportYear, Integer reportMonth);
}
