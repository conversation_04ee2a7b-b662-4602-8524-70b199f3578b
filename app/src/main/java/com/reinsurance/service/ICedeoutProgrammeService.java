package com.reinsurance.service;

import java.util.List;
import java.util.Map;

import com.reinsurance.dto.CedeoutLiabilityContractRelDTO;
import com.reinsurance.dto.CedeoutProgrammeApplyDTO;
import com.reinsurance.dto.CedeoutProgrammeDTO;
import com.reinsurance.dto.CedeoutProgrammeLiabilityApplyDTO;
import com.reinsurance.dto.CedeoutProgrammeLiabilityDTO;
import com.reinsurance.dto.CedeoutProgrammeLiabilityTrackDTO;
import com.reinsurance.dto.CedeoutProgrammeRateApplyDTO;
import com.reinsurance.dto.CedeoutProgrammeRateDTO;
import com.reinsurance.dto.CedeoutProgrammeRateTrackDTO;
import com.reinsurance.dto.CedeoutProgrammeTrackDTO;
import com.reinsurance.query.CedeoutProgrammeApplyQuery;
import com.reinsurance.query.CedeoutProgrammeLiabilityApplyQuery;
import com.reinsurance.query.CedeoutProgrammeLiabilityQuery;
import com.reinsurance.query.CedeoutProgrammeLiabilityTrackQuery;
import com.reinsurance.query.CedeoutProgrammeQuery;
import com.reinsurance.query.CedeoutProgrammeRateApplyQuery;
import com.reinsurance.query.CedeoutProgrammeRateQuery;
import com.reinsurance.query.CedeoutProgrammeRateTrackQuery;
import com.reinsurance.query.CedeoutProgrammeTrackQuery;

/**
 * 再保分出方案Service接口
 * 
 * <AUTHOR>
 * @date 2024-03-19
 */
public interface ICedeoutProgrammeService 
{
    /**
     * 查询再保分出方案
     * 
     * @param id 再保分出方案主键
     * @return 再保分出方案
     */
    CedeoutProgrammeDTO selectCedeoutProgrammeById(Long id);

    /**
     * 查询再保分出方案列表
     * 
     * @param cedeoutProgrammeQuery 再保分出方案
     * @return 再保分出方案集合
     */
    List<CedeoutProgrammeDTO> selectCedeoutProgrammeList(CedeoutProgrammeQuery cedeoutProgrammeQuery);

    /**
     * 新增再保分出方案
     * 
     * @param cedeoutProgrammeDTO 再保分出方案
     * @return 结果
     */
    int insertCedeoutProgramme(CedeoutProgrammeDTO cedeoutProgrammeDTO);

    /**
     * 修改再保分出方案
     * 
     * @param cedeoutProgrammeDTO 再保分出方案
     * @return 结果
     */
    int updateCedeoutProgramme(CedeoutProgrammeDTO cedeoutProgrammeDTO);

    /**
     * 批量删除再保分出方案
     * 
     * @param ids 需要删除的再保分出方案主键集合
     * @return 结果
     */
    public int deleteCedeoutProgrammeByIds(Long[] ids);

    /**
     * 删除再保分出方案信息
     * 
     * @param id 再保分出方案主键
     * @return 结果
     */
    public int updateCedeoutProgrammeById(Long id);
    
    
    /**
     * 查询再保分出方申请表
     * 
     * @param id 再保分出方申请表主键
     * @return 再保分出方申请表
     */
    public CedeoutProgrammeApplyDTO selectCedeoutProgrammeApplyById(Long id);

    /**
     * 查询再保分出方申请表列表
     * 
     * @param cedeoutProgrammeApplyQuery 再保分出方申请表
     * @return 再保分出方申请表集合
     */
    public List<CedeoutProgrammeApplyDTO> selectCedeoutProgrammeApplyList(CedeoutProgrammeApplyQuery cedeoutProgrammeApplyQuery);

    /**
     * 新增再保分出方申请表
     * 
     * @param cedeoutProgrammeApplyDTO 再保分出方申请表
     * @return 结果
     */
    public int insertCedeoutProgrammeApply(CedeoutProgrammeApplyDTO cedeoutProgrammeApplyDTO);

    /**
     * 修改再保分出方申请表
     * 
     * @param cedeoutProgrammeApplyDTO 再保分出方申请表
     * @return 结果
     */
    public int updateCedeoutProgrammeApply(CedeoutProgrammeApplyDTO cedeoutProgrammeApplyDTO);

    /**
     * 批量删除再保分出方申请表
     * 
     * @param ids 需要删除的再保分出方申请表主键集合
     * @return 结果
     */
    public int deleteCedeoutProgrammeApplyByIds(Long[] ids);

    /**
     * 删除再保分出方申请表信息
     * 
     * @param id 再保分出方申请表主键
     * @return 结果
     */
    public int deleteCedeoutProgrammeApplyById(Long id);
    
    /**
     * 查询再保分出方案轨迹
     * 
     * @param trackId 再保分出方案轨迹主键
     * @return 再保分出方案轨迹
     */
    public CedeoutProgrammeTrackDTO selectCedeoutProgrammeTrackByTrackId(Long trackId);

    /**
     * 查询再保分出方案轨迹列表
     * 
     * @param cedeoutProgrammeTrackQuery 再保分出方案轨迹
     * @return 再保分出方案轨迹集合
     */
    public List<CedeoutProgrammeTrackDTO> selectCedeoutProgrammeTrackList(CedeoutProgrammeTrackQuery cedeoutProgrammeTrackQuery);

    /**
     * 新增再保分出方案轨迹
     * 
     * @param cedeoutProgrammeTrackDTO 再保分出方案轨迹
     * @return 结果
     */
    public int insertCedeoutProgrammeTrack(CedeoutProgrammeTrackDTO cedeoutProgrammeTrackDTO);

    /**
     * 修改再保分出方案轨迹
     * 
     * @param cedeoutProgrammeTrackDTO 再保分出方案轨迹
     * @return 结果
     */
    public int updateCedeoutProgrammeTrack(CedeoutProgrammeTrackDTO cedeoutProgrammeTrackDTO);

    /**
     * 批量删除再保分出方案轨迹
     * 
     * @param trackIds 需要删除的再保分出方案轨迹主键集合
     * @return 结果
     */
    public int deleteCedeoutProgrammeTrackByTrackIds(Long[] trackIds);

    /**
     * 删除再保分出方案轨迹信息
     * 
     * @param trackId 再保分出方案轨迹主键
     * @return 结果
     */
    public int deleteCedeoutProgrammeTrackByTrackId(Long trackId);
    
    /**
     * 查询再保分出方案责任申请
     * 
     * @param id 再保分出方案责任申请主键
     * @return 再保分出方案责任申请
     */
    public CedeoutProgrammeLiabilityApplyDTO selectCedeoutProgrammeLiabilityApplyById(Long id);

    /**
     * 查询再保分出方案责任申请列表
     * 
     * @param cedeoutProgrammeLiabilityApplyQuery 再保分出方案责任申请
     * @return 再保分出方案责任申请集合
     */
    public List<CedeoutProgrammeLiabilityApplyDTO> selectCedeoutProgrammeLiabilityApplyList(CedeoutProgrammeLiabilityApplyQuery cedeoutProgrammeLiabilityApplyQuery);

    /**
     * 新增再保分出方案责任申请
     * 
     * @param cedeoutProgrammeLiabilityApplyDTO 再保分出方案责任申请
     * @return 结果
     */
    public int insertCedeoutProgrammeLiabilityApply(CedeoutProgrammeLiabilityApplyDTO cedeoutProgrammeLiabilityApplyDTO);

    /**
     * 修改再保分出方案责任申请
     * 
     * @param cedeoutProgrammeLiabilityApplyDTO 再保分出方案责任申请
     * @return 结果
     */
    public int updateCedeoutProgrammeLiabilityApply(CedeoutProgrammeLiabilityApplyDTO cedeoutProgrammeLiabilityApplyDTO);

    /**
     * 批量删除再保分出方案责任申请
     * 
     * @param ids 需要删除的再保分出方案责任申请主键集合
     * @return 结果
     */
    public int deleteCedeoutProgrammeLiabilityApplyByIds(Long[] ids);

    /**
     * 删除再保分出方案责任申请信息
     * 
     * @param id 再保分出方案责任申请主键
     * @return 结果
     */
    public int deleteCedeoutProgrammeLiabilityApplyById(Long id);
    
    
    /**
     * 查询再保分出方案责任
     * 
     * @param id 再保分出方案责任主键
     * @return 再保分出方案责任
     */
    public CedeoutProgrammeLiabilityDTO selectCedeoutProgrammeLiabilityById(Long id);

    /**
     * 查询再保分出方案责任列表
     * 
     * @param cedeoutProgrammeLiabilityQuery 再保分出方案责任
     * @return 再保分出方案责任集合
     */
    public List<CedeoutProgrammeLiabilityDTO> selectCedeoutProgrammeLiabilityList(CedeoutProgrammeLiabilityQuery cedeoutProgrammeLiabilityQuery);

    /**
     * 新增再保分出方案责任
     * 
     * @param cedeoutProgrammeLiabilityDTO 再保分出方案责任
     * @return 结果
     */
    public int insertCedeoutProgrammeLiability(CedeoutProgrammeLiabilityDTO cedeoutProgrammeLiabilityDTO);

    /**
     * 修改再保分出方案责任
     * 
     * @param cedeoutProgrammeLiabilityDTO 再保分出方案责任
     * @return 结果
     */
    public int updateCedeoutProgrammeLiability(CedeoutProgrammeLiabilityDTO cedeoutProgrammeLiabilityDTO);

    /**
     * 批量删除再保分出方案责任
     * 
     * @param ids 需要删除的再保分出方案责任主键集合
     * @return 结果
     */
    public int deleteCedeoutProgrammeLiabilityByIds(Long[] ids);

    /**
     * 删除再保分出方案责任信息
     * 
     * @param id 再保分出方案责任主键
     * @return 结果
     */
    public int deleteCedeoutProgrammeLiabilityById(Long id);
    
    /**
     * 获取所再保方案配置的险种编码
     * @param cedeoutProgrammeLiabilityQuery	
     * @return
     */
    List<String> selectProgrammeRiskCodeList();
    
    /**
     * 查询再保分出方案责任轨迹
     * 
     * @param trackId 再保分出方案责任轨迹主键
     * @return 再保分出方案责任轨迹
     */
    public CedeoutProgrammeLiabilityTrackDTO selectCedeoutProgrammeLiabilityTrackByTrackId(Long trackId);

    /**
     * 查询再保分出方案责任轨迹列表
     * 
     * @param cedeoutProgrammeLiabilityTrackQuery 再保分出方案责任轨迹
     * @return 再保分出方案责任轨迹集合
     */
    public List<CedeoutProgrammeLiabilityTrackDTO> selectCedeoutProgrammeLiabilityTrackList(CedeoutProgrammeLiabilityTrackQuery cedeoutProgrammeLiabilityTrackQuery);

    /**
     * 新增再保分出方案责任轨迹
     * 
     * @param cedeoutProgrammeLiabilityTrackDTO 再保分出方案责任轨迹
     * @return 结果
     */
    public int insertCedeoutProgrammeLiabilityTrack(CedeoutProgrammeLiabilityTrackDTO cedeoutProgrammeLiabilityTrackDTO);

    /**
     * 修改再保分出方案责任轨迹
     * 
     * @param cedeoutProgrammeLiabilityTrackDTO 再保分出方案责任轨迹
     * @return 结果
     */
    public int updateCedeoutProgrammeLiabilityTrack(CedeoutProgrammeLiabilityTrackDTO cedeoutProgrammeLiabilityTrackDTO);

    /**
     * 批量删除再保分出方案责任轨迹
     * 
     * @param trackIds 需要删除的再保分出方案责任轨迹主键集合
     * @return 结果
     */
    public int deleteCedeoutProgrammeLiabilityTrackByTrackIds(Long[] trackIds);

    /**
     * 删除再保分出方案责任轨迹信息
     * 
     * @param trackId 再保分出方案责任轨迹主键
     * @return 结果
     */
    public int deleteCedeoutProgrammeLiabilityTrackByTrackId(Long trackId);
    
    /**
     * 查询再保分出方案费率申请
     * 
     * @param id 再保分出方案费率申请主键
     * @return 再保分出方案费率申请
     */
    public CedeoutProgrammeRateApplyDTO selectCedeoutProgrammeRateApplyById(Long id);

    /**
     * 查询再保分出方案费率申请列表
     * 
     * @param cedeoutProgrammeRateApplyQuery 再保分出方案费率申请
     * @return 再保分出方案费率申请集合
     */
    public List<CedeoutProgrammeRateApplyDTO> selectCedeoutProgrammeRateApplyList(CedeoutProgrammeRateApplyQuery cedeoutProgrammeRateApplyQuery);

    /**
     * 新增再保分出方案费率申请
     * 
     * @param cedeoutProgrammeRateApplyDTO 再保分出方案费率申请
     * @return 结果
     */
    public int insertCedeoutProgrammeRateApply(CedeoutProgrammeRateApplyDTO cedeoutProgrammeRateApplyDTO);

    /**
     * 修改再保分出方案费率申请
     * 
     * @param cedeoutProgrammeRateApplyDTO 再保分出方案费率申请
     * @return 结果
     */
    public int updateCedeoutProgrammeRateApply(CedeoutProgrammeRateApplyDTO cedeoutProgrammeRateApplyDTO);

    /**
     * 批量删除再保分出方案费率申请
     * 
     * @param ids 需要删除的再保分出方案费率申请主键集合
     * @return 结果
     */
    public int deleteCedeoutProgrammeRateApplyByIds(Long[] ids);

    /**
     * 删除再保分出方案费率申请信息
     * 
     * @param id 再保分出方案费率申请主键
     * @return 结果
     */
    public int deleteCedeoutProgrammeRateApplyById(Long id);
    
    /**
     * 查询再保分出方案费率
     * 
     * @param id 再保分出方案费率主键
     * @return 再保分出方案费率
     */
    public CedeoutProgrammeRateDTO selectCedeoutProgrammeRateById(Long id);

    /**
     * 查询再保分出方案费率列表
     * 
     * @param cedeoutProgrammeRateQuery 再保分出方案费率
     * @return 再保分出方案费率集合
     */
    public List<CedeoutProgrammeRateDTO> selectCedeoutProgrammeRateList(CedeoutProgrammeRateQuery cedeoutProgrammeRateQuery);

    /**
     * 新增再保分出方案费率
     * 
     * @param cedeoutProgrammeRateDTO 再保分出方案费率
     * @return 结果
     */
    public int insertCedeoutProgrammeRate(CedeoutProgrammeRateDTO cedeoutProgrammeRateDTO);

    /**
     * 修改再保分出方案费率
     * 
     * @param cedeoutProgrammeRateDTO 再保分出方案费率
     * @return 结果
     */
    public int updateCedeoutProgrammeRate(CedeoutProgrammeRateDTO cedeoutProgrammeRateDTO);

    /**
     * 批量删除再保分出方案费率
     * 
     * @param ids 需要删除的再保分出方案费率主键集合
     * @return 结果
     */
    public int deleteCedeoutProgrammeRateByIds(Long[] ids);

    /**
     * 删除再保分出方案费率信息
     * 
     * @param id 再保分出方案费率主键
     * @return 结果
     */
    public int deleteCedeoutProgrammeRateById(Long id);
    
    /**
     * 查询再保分出方案费率轨迹
     * 
     * @param trackId 再保分出方案费率轨迹主键
     * @return 再保分出方案费率轨迹
     */
    public CedeoutProgrammeRateTrackDTO selectCedeoutProgrammeRateTrackByTrackId(Long trackId);

    /**
     * 查询再保分出方案费率轨迹列表
     * 
     * @param cedeoutProgrammeRateTrackQuery 再保分出方案费率轨迹
     * @return 再保分出方案费率轨迹集合
     */
    public List<CedeoutProgrammeRateTrackDTO> selectCedeoutProgrammeRateTrackList(CedeoutProgrammeRateTrackQuery cedeoutProgrammeRateTrackQuery);

    /**
     * 新增再保分出方案费率轨迹
     * 
     * @param cedeoutProgrammeRateTrackDTO 再保分出方案费率轨迹
     * @return 结果
     */
    public int insertCedeoutProgrammeRateTrack(CedeoutProgrammeRateTrackDTO cedeoutProgrammeRateTrackDTO);

    /**
     * 修改再保分出方案费率轨迹
     * 
     * @param cedeoutProgrammeRateTrackDTO 再保分出方案费率轨迹
     * @return 结果
     */
    public int updateCedeoutProgrammeRateTrack(CedeoutProgrammeRateTrackDTO cedeoutProgrammeRateTrackDTO);

    /**
     * 批量删除再保分出方案费率轨迹
     * 
     * @param trackIds 需要删除的再保分出方案费率轨迹主键集合
     * @return 结果
     */
    public int deleteCedeoutProgrammeRateTrackByTrackIds(Long[] trackIds);

    /**
     * 删除再保分出方案费率轨迹信息
     * 
     * @param trackId 再保分出方案费率轨迹主键
     * @return 结果
     */
    public int deleteCedeoutProgrammeRateTrackByTrackId(Long trackId);
    
    /**
     * 获取所有方案关联的原始合约
     * 
     * @return 
     * key：companyCode + programmeCode + riskCode + liabilityCode<br/>
     * value：CedeoutLiabilityContractRelDTO
     */
    public Map<String, CedeoutLiabilityContractRelDTO> getLiabilityContractHash();
    
    /**
     * 获取分出逻辑所需所有配置类信息
     * @param payIntv	再保方案缴费方式
     * @param backTrackStatus	方案回溯类型
     * @return
     */
    public Map<String, Object> getCedeoutConfigByParams(Integer payIntv, Integer backTrackStatus);
    
    /**
     * 批量修改回溯方案为常规方案
     * @param ids
     * @return
     */
    public int updateProgrammeBackTrackStatusByIds(List<Long> ids);
}
