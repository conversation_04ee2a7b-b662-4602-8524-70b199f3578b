package com.reinsurance.service;

import java.util.List;

import com.reinsurance.dto.*;
import com.reinsurance.query.CedeoutContractApplyQuery;
import com.reinsurance.query.CedeoutContractLiabilityApplyQuery;
import com.reinsurance.query.CedeoutContractLiabilityQuery;
import com.reinsurance.query.CedeoutContractLiabilityTrackQuery;
import com.reinsurance.query.CedeoutContractQuery;
import com.reinsurance.query.CedeoutContractTrackQuery;

/**
 * 再保合同Service接口
 * 
 * <AUTHOR>
 * @date 2024-03-18
 */
public interface ICedeoutContractService 
{
    /**
     * 查询再保合同
     * 
     * @param id 再保合同主键
     * @return 再保合同
     */
    CedeoutContractDTO selectCedeoutContractById(Long id);

    List<CedeoutContractDTO> selectCedeoutContractCodeList();

    /**
     * 查询再保合同列表
     * 
     * @param cedeoutContractQuery 再保合同
     * @return 再保合同集合
     */
    public List<CedeoutContractDTO> selectCedeoutContractList(CedeoutContractQuery cedeoutContractQuery);

    /**
     * 新增再保合同
     * 
     * @param cedeoutContractDTO 再保合同
     * @return 结果
     */
    public int insertCedeoutContract(CedeoutContractDTO cedeoutContractDTO);

    /**
     * 修改再保合同
     * 
     * @param cedeoutContractDTO 再保合同
     * @return 结果
     */
    public int updateCedeoutContract(CedeoutContractDTO cedeoutContractDTO);

    /**
     * 批量删除再保合同
     * 
     * @param ids 需要删除的再保合同主键集合
     * @return 结果
     */
    public int deleteCedeoutContractByIds(Long[] ids);

    /**
     * 删除再保合同信息
     * 
     * @param id 再保合同主键
     * @return 结果
     */
    public int deleteCedeoutContractById(Long id);
    
    /**
     * 查询再保合同申请
     * 
     * @param id 再保合同申请主键
     * @return 再保合同申请
     */
    public CedeoutContractApplyDTO selectCedeoutContractApplyById(Long id);

    /**
     * 查询再保合同申请列表
     * 
     * @param cedeoutContractApplyQuery 再保合同申请
     * @return 再保合同申请集合
     */
    public List<CedeoutContractApplyDTO> selectCedeoutContractApplyList(CedeoutContractApplyQuery cedeoutContractApplyQuery);

    /**
     * 新增再保合同申请
     * 
     * @param cedeoutContractApplyDTO 再保合同申请
     * @return 结果
     */
    public int insertCedeoutContractApply(CedeoutContractApplyDTO cedeoutContractApplyDTO);

    /**
     * 修改再保合同申请
     * 
     * @param cedeoutContractApplyDTO 再保合同申请
     * @return 结果
     */
    public int updateCedeoutContractApply(CedeoutContractApplyDTO cedeoutContractApplyDTO);

    /**
     * 批量删除再保合同申请
     * 
     * @param ids 需要删除的再保合同申请主键集合
     * @return 结果
     */
    public int deleteCedeoutContractApplyByIds(Long[] ids);

    /**
     * 删除再保合同申请信息
     * 
     * @param id 再保合同申请主键
     * @return 结果
     */
    public int deleteCedeoutContractApplyById(Long id);
    
    
    /**
     * 查询再保合同轨迹
     * 
     * @param trackId 再保合同轨迹主键
     * @return 再保合同轨迹
     */
    public CedeoutContractTrackDTO selectCedeoutContractTrackByTrackId(Long trackId);

    /**
     * 查询再保合同轨迹列表
     * 
     * @param cedeoutContractTrackQuery 再保合同轨迹
     * @return 再保合同轨迹集合
     */
    public List<CedeoutContractTrackDTO> selectCedeoutContractTrackList(CedeoutContractTrackQuery cedeoutContractTrackQuery);

    /**
     * 新增再保合同轨迹
     * 
     * @param cedeoutContractTrackDTO 再保合同轨迹
     * @return 结果
     */
    public int insertCedeoutContractTrack(CedeoutContractTrackDTO cedeoutContractTrackDTO);

    /**
     * 修改再保合同轨迹
     * 
     * @param cedeoutContractTrackDTO 再保合同轨迹
     * @return 结果
     */
    public int updateCedeoutContractTrack(CedeoutContractTrackDTO cedeoutContractTrackDTO);

    /**
     * 批量删除再保合同轨迹
     * 
     * @param trackIds 需要删除的再保合同轨迹主键集合
     * @return 结果
     */
    public int deleteCedeoutContractTrackByTrackIds(Long[] trackIds);

    /**
     * 删除再保合同轨迹信息
     * 
     * @param trackId 再保合同轨迹主键
     * @return 结果
     */
    public int deleteCedeoutContractTrackByTrackId(Long trackId);
    
    
    /**
     * 查询再保合同责任
     * 
     * @param id 再保合同责任主键
     * @return 再保合同责任
     */
    public CedeoutContractLiabilityDTO selectCedeoutContractLiabilityById(Long id);

    /**
     * 查询再保合同责任列表
     * 
     * @param cedeoutContractLiabilityQuery 再保合同责任
     * @return 再保合同责任集合
     */
    public List<CedeoutContractLiabilityDTO> selectCedeoutContractLiabilityList(CedeoutContractLiabilityQuery cedeoutContractLiabilityQuery);

    /**
     * 新增再保合同责任
     * 
     * @param cedeoutContractLiabilityDTO 再保合同责任
     * @return 结果
     */
    public int insertCedeoutContractLiability(CedeoutContractLiabilityDTO cedeoutContractLiabilityDTO);

    /**
     * 修改再保合同责任
     * 
     * @param cedeoutContractLiabilityDTO 再保合同责任
     * @return 结果
     */
    public int updateCedeoutContractLiability(CedeoutContractLiabilityDTO cedeoutContractLiabilityDTO);

    /**
     * 批量删除再保合同责任
     * 
     * @param ids 需要删除的再保合同责任主键集合
     * @return 结果
     */
    public int deleteCedeoutContractLiabilityByIds(Long[] ids);

    /**
     * 删除再保合同责任信息
     * 
     * @param id 再保合同责任主键
     * @return 结果
     */
    public int deleteCedeoutContractLiabilityById(Long id);
    
    /**
     * 查询再保合同责任申请
     * 
     * @param id 再保合同责任申请主键
     * @return 再保合同责任申请
     */
    public CedeoutContractLiabilityApplyDTO selectCedeoutContractLiabilityApplyById(Long id);

    /**
     * 查询再保合同责任申请列表
     * 
     * @param cedeoutContractLiabilityApplyQuery 再保合同责任申请
     * @return 再保合同责任申请集合
     */
    public List<CedeoutContractLiabilityApplyDTO> selectCedeoutContractLiabilityApplyList(CedeoutContractLiabilityApplyQuery cedeoutContractLiabilityApplyQuery);

    /**
     * 新增再保合同责任申请
     * 
     * @param cedeoutContractLiabilityApplyDTO 再保合同责任申请
     * @return 结果
     */
    public int insertCedeoutContractLiabilityApply(CedeoutContractLiabilityApplyDTO cedeoutContractLiabilityApplyDTO);

    /**
     * 修改再保合同责任申请
     * 
     * @param cedeoutContractLiabilityApplyDTO 再保合同责任申请
     * @return 结果
     */
    public int updateCedeoutContractLiabilityApply(CedeoutContractLiabilityApplyDTO cedeoutContractLiabilityApplyDTO);

    /**
     * 批量删除再保合同责任申请
     * 
     * @param ids 需要删除的再保合同责任申请主键集合
     * @return 结果
     */
    public int deleteCedeoutContractLiabilityApplyByIds(Long[] ids);

    /**
     * 删除再保合同责任申请信息
     * 
     * @param id 再保合同责任申请主键
     * @return 结果
     */
    public int deleteCedeoutContractLiabilityApplyById(Long id);
    
    
    /**
     * 查询再保合同责任轨迹
     * 
     * @param trackId 再保合同责任轨迹主键
     * @return 再保合同责任轨迹
     */
    public CedeoutContractLiabilityTrackDTO selectCedeoutContractLiabilityTrackByTrackId(Long trackId);

    /**
     * 查询再保合同责任轨迹列表
     * 
     * @param cedeoutContractLiabilityTrackQuery 再保合同责任轨迹
     * @return 再保合同责任轨迹集合
     */
    public List<CedeoutContractLiabilityTrackDTO> selectCedeoutContractLiabilityTrackList(CedeoutContractLiabilityTrackQuery cedeoutContractLiabilityTrackQuery);

    /**
     * 新增再保合同责任轨迹
     * 
     * @param cedeoutContractLiabilityTrackDTO 再保合同责任轨迹
     * @return 结果
     */
    public int insertCedeoutContractLiabilityTrack(CedeoutContractLiabilityTrackDTO cedeoutContractLiabilityTrackDTO);

    /**
     * 修改再保合同责任轨迹
     * 
     * @param cedeoutContractLiabilityTrackDTO 再保合同责任轨迹
     * @return 结果
     */
    public int updateCedeoutContractLiabilityTrack(CedeoutContractLiabilityTrackDTO cedeoutContractLiabilityTrackDTO);

    /**
     * 批量删除再保合同责任轨迹
     * 
     * @param trackIds 需要删除的再保合同责任轨迹主键集合
     * @return 结果
     */
    public int deleteCedeoutContractLiabilityTrackByTrackIds(Long[] trackIds);

    /**
     * 删除再保合同责任轨迹信息
     * 
     * @param trackId 再保合同责任轨迹主键
     * @return 结果
     */
    public int deleteCedeoutContractLiabilityTrackByTrackId(Long trackId);
    
    /**
     * 查询原始再保合同转换为East再保合同DTO对象
     * @param cedeoutContractQuery
     * @return
     */
    public List<DwsEastZbhtxxbDTO> selectOriginalContractAsEastZbhtxxb(CedeoutContractQuery cedeoutContractQuery);
    
    /**
     * 查询原始合同的再保责任转换为为East再保产品DTO对象
     * @param cedeoutContractLiabilityQuery
     * @return
     */
    public List<DwsEastZbcpxxbDTO> selectContractLiabilityAsEastZbcpxxb(CedeoutContractLiabilityQuery cedeoutContractLiabilityQuery);

    /**
     * 从合同责任表查询再保产品信息
     *
     * @param cedeoutContractLiabilityQuery 再保合同产品查询对象
     * @return 结果
     */
    public List<DwsPrpProductDTO> selectContractLiabilityAsPrpProduct(CedeoutContractLiabilityQuery cedeoutContractLiabilityQuery);
}
