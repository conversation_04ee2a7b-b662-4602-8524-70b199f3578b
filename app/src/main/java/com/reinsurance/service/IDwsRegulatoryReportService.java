package com.reinsurance.service;

import java.util.List;

import com.reinsurance.query.DwsRegulatoryReportQuery;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.enums.BusinessType;
import com.reinsurance.dto.DwsRegulatoryReportDTO;
import com.reinsurance.enums.BasicDataEnums.RegulatorReport;

/**
 * 监管报表信息Service接口
 *
 * <AUTHOR>
 * @date 2025-03-05
 */
public interface IDwsRegulatoryReportService {

    /**
     * 查询监管报表信息列表
     *
     * @param dwsRegulatoryReportQuery 监管报表信息Query
     * @return 监管报表信息DTO 集合
     */
    List<DwsRegulatoryReportDTO> selectDwsRegulatoryReportList(DwsRegulatoryReportQuery dwsRegulatoryReportQuery);
    
    /**
     * 检查报表是不是存在
     * @param dwsRegulatoryReportDTO
     * @return
     */
    Result checkReportExists(DwsRegulatoryReportDTO dwsRegulatoryReportDTO);
    
    /**
     * 生成报表数据
     * @param dwsRegulatoryReportDTO
     * @return
     */
    Result insertRegulatoryReportData(DwsRegulatoryReportDTO dwsRegulatoryReportDTO);
    
    /**
     * 更新某类监管east报表年度信息
     * @param businessType
     * @param regulatorReport
     * @param reportCode
     * @param reportYears
     * @return
     */
    int insertOrUpdateRegulatoryReport(BusinessType businessType, RegulatorReport regulatorReport, int reportCode, List<Integer> reportYears);
    
}
