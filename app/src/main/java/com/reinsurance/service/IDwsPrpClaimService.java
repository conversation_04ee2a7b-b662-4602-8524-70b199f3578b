package com.reinsurance.service;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import com.jd.lightning.common.core.domain.Result;
import com.reinsurance.dto.DwsPrpClaimDTO;
import com.reinsurance.query.DwsPrpClaimQuery;

/**
 * 再保理赔险种明细Service接口
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface IDwsPrpClaimService {
    
    /**
     * 查询再保理赔险种明细
     *
     * @param Id 再保理赔险种明细主键
     * @return 再保理赔险种明细
     */
    public DwsPrpClaimDTO selectDwsPrpClaimById(Long Id);

    /**
     * 查询再保理赔险种明细列表
     *
     * @param dwsPrpClaimQuery 再保理赔险种明细
     * @return 再保理赔险种明细集合
     */
    public List<DwsPrpClaimDTO> selectDwsPrpClaimList(DwsPrpClaimQuery dwsPrpClaimQuery);

    /**
     * 新增再保理赔险种明细
     *
     * @param dwsPrpClaimDTO 再保理赔险种明细
     * @return 结果
     */
    public int insertDwsPrpClaim(DwsPrpClaimDTO dwsPrpClaimDTO);

    /**
     * 批量新增再保理赔险种明细
     *
     * @param dwsPrpClaimList 再保理赔险种明细列表
     * @return 结果
     */
    public int insertBatchDwsPrpClaim(List<DwsPrpClaimDTO> dwsPrpClaimList);

    /**
     * 修改再保理赔险种明细
     *
     * @param dwsPrpClaimDTO 再保理赔险种明细
     * @return 结果
     */
    public int updateDwsPrpClaim(DwsPrpClaimDTO dwsPrpClaimDTO);

    /**
     * 批量删除再保理赔险种明细
     *
     * @param Ids 需要删除的再保理赔险种明细主键集合
     * @return 结果
     */
    public int deleteDwsPrpClaimByIds(Long[] Ids);

    /**
     * 删除再保理赔险种明细信息
     *
     * @param Id 再保理赔险种明细主键
     * @return 结果
     */
    public int deleteDwsPrpClaimById(Long Id);

    /**
     * 导入再保理赔险种明细
     *
     * @param companyCode 保险机构代码
     * @param companyName 保险机构名称
     * @param manageCom 管理机构
     * @param file 导入文件
     * @return 结果
     */
    public Result importDwsPrpClaim(String companyCode, String companyName, String manageCom, MultipartFile file);

    /**
     * 导出再保理赔险种明细
     *
     * @param response 响应对象
     * @param dwsPrpClaimQuery 查询条件
     */
    public void exportDwsPrpClaim(HttpServletResponse response, DwsPrpClaimQuery dwsPrpClaimQuery);

    /**
     * 检查再保理赔险种明细是否存在
     *
     * @param dwsPrpClaimQuery 查询条件
     * @return 结果
     */
    public int selectDwsPrpClaimExists(DwsPrpClaimQuery dwsPrpClaimQuery);

    /**
     * 更新推送状态
     *
     * @param Ids 主键数组
     * @return 结果
     */
    public Result updateDwsPrpClaimPushStatus(Long[] Ids);

    /**
     * 查询年度报表推送状态
     *
     * @param reportYear 报表年份
     * @return 结果
     */
    public Integer selectAnnualReportShouldPushStatus(int reportYear);

    /**
     * 生成再保理赔险种明细数据
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param reportYear 报表年份
     * @param reportMonth 报表月份
     * @return 结果
     */
    public Result generatePrpClaimData(String startDate, String endDate, Integer reportYear, Integer reportMonth);
}
