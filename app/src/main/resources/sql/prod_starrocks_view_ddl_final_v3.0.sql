DROP MATERIALIZED VIEW IF EXISTS v_dws_cont_liability;
CREATE MATERIALIZED VIEW v_dws_cont_liability 
DISTRIBUTED BY HASH(`cont_no`) 
REFRESH DEFERRED ASYNC START('2025-04-01 02:30:00') EVERY (interval 1 day) 
as select concat_ws('#', pol_no, duty_code, get_duty_code ,insured_no, cont_year) as unique_key, if(cont_year>1, previous_cont_anniversary, busi_occur_date) as busi_occur_date, 
str_to_date(concat_ws(' ', if(cont_year>1, date_format(previous_cont_anniversary, '%Y-%m-%d'), date_format(busi_occur_date, '%Y-%m-%d')), cont_make_time), '%Y-%m-%d %H:%i:%s') as busi_occur_time, 
cont_type, grp_cont_no, grp_pol_no, cont_no, pol_no, main_pol_no, sale_chnl, sell_type, sale_chnl_name, sell_type_name, manage_com_code, manage_com_name, agent_com_code, agent_com_name, sale_com_code,
sale_com_name, bank_branch_name, sign_date, sign_time, risk_vali_date, risk_end_date, cont_make_date, cont_make_time, cont_year, cont_anniversary, previous_cont_anniversary, cont_app_flag, 
risk_code, risk_name, risk_type3, plan_code, pol_risk_type, risk_app_flag, sub_risk_flag, risk_period, insu_year, insu_year_flag, pay_intv, payend_year, payend_year_flag, pay_to_date, pay_end_date, 
duty_code, duty_name, get_duty_code, get_duty_name, get_duty_state, amount, sum_pay_money, sum_add_money, sum_get_money, base_premium, add_premium, add_scale, risk_free_flag, appnt_no, appnt_name, 
appnt_id_type, appnt_id_no, appnt_sex, appnt_birthday, appnt_occ_type, appnt_occ_code, insured_peoples, main_insured_no, insured_sequence_no, insured_app_age, insured_no, insured_name, insured_id_type, 
insured_id_no, insured_sex, insured_birthday, insured_occ_type, insured_occ_code, insured_pass_flag, cedeout_type, core_conclusion, reinsu_add_scale, if(insuacc_value1<0, 0, insuacc_value1) as insuacc_value, 
if(cash_value1<0, 0, cash_value1) as cash_value, cedeout_count from (
	select c.grpcontno as grp_cont_no, p.grppolno as grp_pol_no, c.conttype as cont_type, g.contno as cont_no, g.polno as pol_no, p.mainpolno as main_pol_no, c.salechnl as sale_chnl, c.selltype as sell_type,
	(select ldcc1.codename from data_warehouse.t_ods_core_ldcode_prod ldcc1 where ldcc1.codetype ='salechnl' and ldcc1.code = c.salechnl)as sale_chnl_name,
	(select ldcc2.codename from data_warehouse.t_ods_core_ldcode_prod ldcc2 where ldcc2.codetype ='selltype' and ldcc2.code = c.selltype)as sell_type_name,
	c.managecom as manage_com_code, dcom.name as manage_com_name, c.agentcom as agent_com_code, 
	(select ifnull(name, '') from data_warehouse.t_ods_core_lacom_prod where agentcom=c.agentcom) as agent_com_name, 
	ifnull(cdr.lastagentcom, c.servicecom) as sale_com_code, ifnull(cdr.lastagentcomname, acom.name) as sale_com_name, acom.name as bank_branch_name, 
	c.signdate as sign_date, c.signtime as sign_time, p.cvalidate as risk_vali_date, p.enddate as risk_end_date, c.makedate as cont_make_date, c.maketime as cont_make_time, 
	(timestampdiff(year, p.cvalidate, CURDATE()) + 1) as cont_year, date_add(p.cvalidate, interval (timestampdiff(year, p.cvalidate, CURDATE()) + 1) year) as cont_anniversary,
	date_add(p.cvalidate, interval (timestampdiff(year, p.cvalidate, CURDATE())) year) as previous_cont_anniversary,c.appflag as cont_app_flag, p.riskcode as risk_code, 
	r.riskname as risk_name, r.risktype3 as risk_type3, (case when p.riskcode='411701' or p.riskcode='511202' or p.riskcode='511515' or p.riskcode='511517' then p.airno else '' end) as plan_code,
	(case when p.riskcode='411302' and p.airno is not null and p.airno != '' then 
					(select max(tocrp.risklevel) from data_warehouse.t_ods_core_risklevelpc3_prod tocrp 
					inner join data_warehouse.t_ods_core_lcpol_prod toclp 
					on substr(toclp.airno, 1, 1)  between tocrp.minoccupation and tocrp.maxoccupation 
		    		and substr(toclp.airno, 10, 1) between tocrp.minsmokeflag  and tocrp.maxsmokeflag 
		    		and substr(toclp.airno, 12, 2) between tocrp.minage        and tocrp.maxage 
		    		and substr(toclp.airno, 15)    between tocrp.minbmi        and tocrp.maxbmi 
		    		and substr(toclp.airno,2, 1) = ';' where toclp.polno=p.polno)
		else  null end) as pol_risk_type,
	p.appflag as risk_app_flag, r.subriskflag as sub_risk_flag, r.riskperiod as risk_period, p.insuyear as insu_year, p.insuyearflag as insu_year_flag, p.payintv as pay_intv, 
	(case when p.payintv = '0' then '1' else p.payendyear end) as payend_year, (case when p.payintv = '0' then 'Y' else p.payendyearflag end) as payend_year_flag, 
	p.payenddate as pay_end_date, p.paytodate as pay_to_date, greatest(c.signdate, p.cvalidate) as busi_occur_date, d.dutycode as duty_code, md.dutyname as duty_name, 
	g.getdutycode as get_duty_code, mg.getdutyname as get_duty_name, g.state as get_duty_state, p.amnt as amount, 
	(select sum(sumactupaymoney) from data_warehouse.t_ods_core_ljapayperson_prod where paytype='ZC' and polno=g.polno) as sum_pay_money,
	(select sum(getmoney) from data_warehouse.t_ods_core_lpedoritem_prod where edortype='IP' and edorstate='0' and contno=g.contno) as sum_add_money,
	(select sum(getmoney) from data_warehouse.t_ods_core_lpedoritem_prod where edortype in ('OP','WP') and edorstate='0' and contno=g.contno) as sum_get_money,
	(select ifnull(sum(prem), 0) from data_warehouse.t_ods_core_lcprem_prod where contno=g.contno and polno=g.polno and payplancode not like '000000%') as base_premium,
	(select ifnull(sum(prem), 0) from data_warehouse.t_ods_core_lcprem_prod where contno=g.contno and polno=g.polno and payplancode like '000000%') as add_premium, 
	(select sum(suppriskscore) from data_warehouse.t_ods_core_lcprem_prod where contno=g.contno and polno=g.polno and payplancode like '000000%') as add_scale,
	(select freeflag from data_warehouse.t_ods_core_lcprem_prod where polno=g.polno and freeflag='1' and payplancode not like '000000%') as risk_free_flag,
	a.appntno as appnt_no, a.appntname as appnt_name, a.idtype as appnt_id_type, a.idno as appnt_id_no, a.appntsex as appnt_sex, a.appntbirthday as appnt_birthday, 
	a.occupationtype as appnt_occ_type, a.occupationcode as appnt_occ_code, p.insuredno as main_insured_no, i.sequenceno as insured_sequence_no, 
	p.insuredpeoples as insured_peoples, (case when p.insuredno = i.insuredno then p.insuredappage else timestampdiff(year, i.birthday, p.cvalidate) end) as insured_app_age, 
	i.insuredno as insured_no, i.name as insured_name, i.idtype as insured_id_type, i.idno as insured_id_no, 
	i.sex as insured_sex, i.birthday as insured_birthday, i.occupationtype as insured_occ_type, i.occupationcode as insured_occ_code, cuw.passflag as insured_pass_flag,
	if(tocturt.id is null, 0, 1) as cedeout_type, tocturt.uw_conclusion as core_conclusion, tocturt.add_prem_point as reinsu_add_scale, '1' as cedeout_count,
	ifnull(case
		 when r.risktype3 = '4' then 
			(select round(sum(insuaccbala), 4) from data_warehouse.t_ods_core_lcinsureacc_prod where contno=c.contno)
		when r.risktype3 = '3' then 
			ifnull( 
				(select round(sum(acc.unitcount * price.unitpricesell), 4) from (
				select contno, insuaccno, sum(unitcount) as unitcount from data_warehouse.t_ods_core_lcinsureaccclass_prod where unitcount!=0 group by contno, insuaccno) acc
				inner join data_warehouse.t_ods_core_loaccunitprice_prod price on price.insuaccno=acc.insuaccno where acc.contno=c.contno 
				and price.startdate=(select max(startdate) from data_warehouse.t_ods_core_loaccunitprice_prod where insuaccno=acc.insuaccno))
				, 
				(select round(sum(insuaccbala), 4) from data_warehouse.t_ods_core_lcinsureacc_prod where contno=c.contno)
			)
		else 
			0
	end, 0) as insuacc_value1,
	(case 
		when r.risktype3 = '1' or r.risktype3 = '2' then 
			(select ifnull(sum(lfz.cashvalue), 0) from data_warehouse.t_ods_core_lfzbcashvalue_prod lfz where lfz.contno=g.contno and lfz.polaryear=timestampdiff(year, p.cvalidate, CURDATE())+1 and lfz.polno=g.polno)
		else 0
	end) as cash_value1
	from data_warehouse.t_ods_core_lcget_prod g 
	inner join data_warehouse.t_ods_core_lccont_prod c on c.contno=g.contno 
	left join data_warehouse.t_ods_core_lcappnt_prod a on a.contno=g.contno 
	inner join data_warehouse.t_ods_core_lcpol_prod p on g.polno = p.polno 
	inner join data_warehouse.t_ods_core_lcinsured_prod i on i.contno=g.contno and i.insuredno=p.insuredno 
	inner join data_warehouse.t_ods_core_lcduty_prod d on d.polno=p.polno and d.dutycode=g.dutycode 
	left join data_warehouse.t_ods_core_lccuwmaster_prod cuw on cuw.contno=g.contno and cuw.insuredno=i.insuredno 
	left join data_warehouse.t_ods_core_lmdutyget_prod mg on mg.getdutycode=g.getdutycode 
	left join data_warehouse.t_ods_core_lmriskapp_prod r on r.riskcode=p.riskcode 
	left join data_warehouse.t_ods_core_lmduty_prod md on md.dutycode=g.dutycode 
	left join data_warehouse.t_ods_core_ldcom_prod dcom on dcom.comcode=c.managecom 
	left join data_warehouse.t_ods_core_lacom_prod acom on acom.agentcom=c.servicecom 
	left join data_warehouse.t_ods_core_lccontdoublerecord_prod cdr on cdr.prtno=c.prtno 
	left join data_warehouse.t_ods_core_t_uw_riconclusion_prod tocturt on tocturt.proposal_no=p.contno and tocturt.riskcode=p.riskcode 
	where not exists (select 1 from data_warehouse.t_ods_core_t_cont_trans_lifelong_prod where contno=g.contno and trans_policy_flag='1' and trans_state='2')
	and g.getendstate='0' and c.appflag='1' and p.riskcode not in ('511114', '511117', '511118') and p.appflag='1' and mg.type='1' and g.dutycode!='P4700101'
union all
	select c.grpcontno as grp_cont_no, p.grppolno as grp_pol_no, c.conttype as cont_type, g.contno as cont_no, g.polno as pol_no, p.mainpolno as main_pol_no, c.salechnl as sale_chnl, c.selltype as sell_type,
	(select ldcc1.codename from data_warehouse.t_ods_core_ldcode_prod ldcc1 where ldcc1.codetype ='salechnl' and ldcc1.code = c.salechnl)as sale_chnl_name,
	(select ldcc2.codename from data_warehouse.t_ods_core_ldcode_prod ldcc2 where ldcc2.codetype ='selltype' and ldcc2.code = c.selltype)as sell_type_name,
	c.managecom as manage_com_code, dcom.name as manage_com_name, c.agentcom as agent_com_code, 
	(select ifnull(name, '') from data_warehouse.t_ods_core_lacom_prod where agentcom=c.agentcom) as agent_com_name, 
	ifnull(cdr.lastagentcom, c.servicecom) as sale_com_code, ifnull(cdr.lastagentcomname, acom.name) as sale_com_name, acom.name as bank_branch_name, 
	c.signdate as sign_date, c.signtime as sign_time, p.cvalidate as risk_vali_date, p.enddate as risk_end_date, c.makedate as cont_make_date, c.maketime as cont_make_time, 
	(timestampdiff(year, p.cvalidate, CURDATE()) + 1) as cont_year, date_add(p.cvalidate, interval (timestampdiff(year, p.cvalidate, CURDATE()) + 1) year) as cont_anniversary,
	date_add(p.cvalidate, interval (timestampdiff(year, p.cvalidate, CURDATE())) year) as previous_cont_anniversary,c.appflag as cont_app_flag, p.riskcode as risk_code, 
	r.riskname as risk_name, r.risktype3 as risk_type3,  '' as plan_code, '' as pol_risk_type,
	p.appflag as risk_app_flag, r.subriskflag as sub_risk_flag, r.riskperiod as risk_period, p.insuyear as insu_year, p.insuyearflag as insu_year_flag, p.payintv as pay_intv, 
	(case when p.payintv = '0' then '1' else p.payendyear end) as payend_year, (case when p.payintv = '0' then 'Y' else p.payendyearflag end) as payend_year_flag, 
	p.payenddate as pay_end_date, p.paytodate as pay_to_date, greatest(c.signdate, p.cvalidate) as busi_occur_date, d.dutycode as duty_code, md.dutyname as duty_name, 
	g.getdutycode as get_duty_code, mg.getdutyname as get_duty_name, g.state as get_duty_state, p.amnt as amount, 
	(select sum(sumactupaymoney) from data_warehouse.t_ods_core_ljapayperson_prod where paytype='ZC' and polno=g.polno) as sum_pay_money,
	(select sum(getmoney) from data_warehouse.t_ods_core_lpedoritem_prod where edortype='IP' and edorstate='0' and contno=g.contno) as sum_add_money,
	(select sum(getmoney) from data_warehouse.t_ods_core_lpedoritem_prod where edortype in ('OP','WP') and edorstate='0' and contno=g.contno) as sum_get_money,
	(select ifnull(sum(prem), 0) from data_warehouse.t_ods_core_lcprem_prod where contno=g.contno and polno=g.polno and payplancode not like '000000%') as base_premium,
	(select ifnull(sum(prem), 0) from data_warehouse.t_ods_core_lcprem_prod where contno=g.contno and polno=g.polno and payplancode like '000000%') as add_premium, 
	(select sum(suppriskscore) from data_warehouse.t_ods_core_lcprem_prod where contno=g.contno and polno=g.polno and payplancode like '000000%') as add_scale,
	(select freeflag from data_warehouse.t_ods_core_lcprem_prod where polno=g.polno and freeflag='1' and payplancode not like '000000%') as risk_free_flag,
	a.appntno as appnt_no, a.appntname as appnt_name, a.idtype as appnt_id_type, a.idno as appnt_id_no, a.appntsex as appnt_sex, a.appntbirthday as appnt_birthday, 
	a.occupationtype as appnt_occ_type, a.occupationcode as appnt_occ_code, p.insuredno as main_insured_no, i.sequenceno as insured_sequence_no, 
	(select count(1) from data_warehouse.t_ods_core_lcinsured_prod where contno=g.contno) as insured_peoples, 
	(case when p.insuredno = i.insuredno then p.insuredappage else timestampdiff(year, i.birthday, p.cvalidate) end) as insured_app_age, 
	i.insuredno as insured_no, i.name as insured_name, i.idtype as insured_id_type, i.idno as insured_id_no, 
	i.sex as insured_sex, i.birthday as insured_birthday, i.occupationtype as insured_occ_type, i.occupationcode as insured_occ_code, cuw.passflag as insured_pass_flag,
	if(tocturt.id is null, 0, 1) as cedeout_type, tocturt.uw_conclusion as core_conclusion, tocturt.add_prem_point as reinsu_add_scale, '1' as cedeout_count, '0' as insuacc_value1, 
	(select ifnull(sum(lfz.cashvalue), 0) from data_warehouse.t_ods_core_lfzbcashvalue_prod lfz where lfz.contno=g.contno and lfz.polaryear=timestampdiff(year, p.cvalidate, CURDATE())+1 and lfz.polno=g.polno) as cash_value1
	from data_warehouse.t_ods_core_lcget_prod g 
	inner join data_warehouse.t_ods_core_lccont_prod c on c.contno=g.contno 
	inner join data_warehouse.t_ods_core_lcpol_prod p on p.contno=g.contno 
	inner join data_warehouse.t_ods_core_lcinsured_prod i on i.contno=g.contno 
	inner join data_warehouse.t_ods_core_lcduty_prod d on d.polno=p.polno and d.dutycode=g.dutycode 
	left join data_warehouse.t_ods_core_lcappnt_prod a on a.contno=g.contno 
	left join data_warehouse.t_ods_core_lccuwmaster_prod cuw on cuw.contno=g.contno and cuw.insuredno=i.insuredno 
	left join data_warehouse.t_ods_core_lmdutyget_prod mg on mg.getdutycode=g.getdutycode 
	left join data_warehouse.t_ods_core_lmriskapp_prod r on r.riskcode=p.riskcode 
	left join data_warehouse.t_ods_core_lmduty_prod md on md.dutycode=g.dutycode 
	left join data_warehouse.t_ods_core_ldcom_prod dcom on dcom.comcode=c.managecom 
	left join data_warehouse.t_ods_core_lacom_prod acom on acom.agentcom=c.servicecom 
	left join data_warehouse.t_ods_core_lccontdoublerecord_prod cdr on cdr.prtno=c.prtno 
	left join data_warehouse.t_ods_core_t_uw_riconclusion_prod tocturt on tocturt.proposal_no=p.contno and tocturt.riskcode=p.riskcode 
	where g.getendstate='0' and c.appflag='1' and c.conttype = '1' and p.appflag='1' and mg.type='1' and r.riskcode in ('511114', '511117', '511118')
) v1 where v1.previous_cont_anniversary >= date_sub(curdate(), interval (select convert(config_value, int) from mysql.huida_reinsurance_backend.sys_config where config_key='sys.view.store.data.lately.days') day)
and not exists(select 1 from data_warehouse.t_ods_core_lccontstate_prod s where (s.statetype='Terminate' or (s.statetype='Available' and s.state=1 and s.statereason not in ('11','13','14')))
and s.enddate is null and s.contno=v1.cont_no and(s.polno=v1.pol_no or s.polno='000000') and(s.insuredno=v1.insured_no or s.insuredno='000000'));



DROP MATERIALIZED VIEW IF EXISTS v_dws_policy;
CREATE MATERIALIZED VIEW v_dws_policy
DISTRIBUTED BY HASH(`cont_no`) 
REFRESH DEFERRED ASYNC START('2025-04-01 02:45:00') EVERY (interval 1 day) 
as select vdcl.*, tlm.rs_liability_code as liability_code, tlm.rs_liability_name as liability_name 
from v_dws_cont_liability vdcl inner join mysql.huida_reinsurance_backend.t_liability_mapping tlm 
on vdcl.get_duty_code=tlm.lis_liability_code and vdcl.risk_code=tlm.risk_code where tlm.status=0 and tlm.is_del=0 
and (vdcl.insured_peoples=1 or (vdcl.insured_peoples=2 and vdcl.main_insured_no != vdcl.insured_no));



DROP MATERIALIZED VIEW IF EXISTS v_dws_edor;
CREATE MATERIALIZED VIEW v_dws_edor 
DISTRIBUTED BY HASH(`edor_accept_no`) 
REFRESH DEFERRED ASYNC START('2025-04-01 03:00:00') EVERY (interval 1 day) 
as select concat_ws('#', i.edoracceptno, i.edortype, i.edorno, i.contno, i.polno, i.insuredno) as unique_key, i.contno as cont_no, i.polno as pol_no, i.insuredno as insured_no, 
i.edoracceptno as edor_accept_no, i.edorno as edor_no, i.edortype as edor_type, i.edorstate as edor_state, a.apptype as edor_app_type, i.makedate as edor_make_date, i.maketime as edor_make_time, 
i.getmoney as edor_get_money, i.getinterest as edor_get_interest, i.edorappdate as edor_app_date, greatest(ifnull(a.confdate, i.edorvalidate), i.edorvalidate) as busi_occur_date, 
str_to_date(concat_ws(' ', date_format(greatest(ifnull(a.confdate, i.edorvalidate), i.edorvalidate), '%Y-%m-%d'), i.maketime), '%Y-%m-%d %H:%i:%s') as busi_occur_time,
i.edorvalidate as edor_validate, a.confdate as edor_conf_date, a.conftime  as edor_conf_time, a.edorappname as edor_app_name, i.standbyflag1 as edor_standby_flag1, 
i.standbyflag2 as edor_standby_flag2, i.standbyflag3 as edor_standby_flag3, i.standbyflag4 as edor_standby_flag4, i.standbyflag5 as edor_standby_flag5
from data_warehouse.t_ods_core_lpedoritem_prod i inner join data_warehouse.t_ods_core_lpedorapp_prod a 
on i.edoracceptno=a.edoracceptno where i.edorstate='0' and a.edorstate='0' and a.othernotype='3' 
and ((i.edortype='AI' and i.getmoney>=100000) or i.edortype in ('WT','CT','XT','RE','NS','RB','FG')) 
and not exists (
	select 1 from data_warehouse.t_ods_core_t_cont_trans_lifelong_prod where contno=i.contno and trans_policy_flag='1' and trans_state='2'
)
and i.makedate >= date_sub(curdate(), interval (select convert(config_value, int) from mysql.huida_reinsurance_backend.sys_config where config_key='sys.view.store.data.lately.days') day);



DROP MATERIALIZED VIEW IF EXISTS v_dws_policy_state;
CREATE MATERIALIZED VIEW v_dws_policy_state 
DISTRIBUTED BY HASH(`cont_no`) 
REFRESH DEFERRED ASYNC START('2025-04-01 03:15:00') EVERY (interval 1 day) 
as select distinct concat_ws('#', s.contno, s.polno, s.insuredno, date_format(s.startdate, '%Y%m%d'), replace(s.maketime, ':', '')) as unique_key, 
s.contno as cont_no, s.polno as pol_no, s.insuredno as insured_no, s.statetype as state_type, s.state as state_state, s.statereason as state_reason, 
s.startdate as busi_occur_date, str_to_date(concat_ws(' ', date_format(s.startdate, '%Y-%m-%d'), s.maketime), '%Y-%m-%d %H:%i:%s') as busi_occur_time,
s.startdate as state_start_date, s.enddate as state_end_date, s.makedate as state_make_date, s.maketime as state_make_time
from data_warehouse.t_ods_core_lccontstate_prod s inner join data_warehouse.t_ods_core_lccont_prod c on s.contno=c.contno 
where (s.statetype='Terminate' or (s.statetype='Available' and s.state=1 and s.statereason not in ('11','13','14'))) and s.enddate is null  
and not exists (
	select 1 from data_warehouse.t_ods_core_ljagetclaim_prod lgc where lgc.contno=s.contno
)
and not exists (
	select 1 from data_warehouse.t_ods_core_lpedoritem_prod i where i.edorstate='0' and i.contno=s.contno 
	and (i.edortype='WT' or i.edortype='CT' or i.edortype='XT') 
) 
and not exists (
	select 1 from data_warehouse.t_ods_core_t_cont_trans_lifelong_prod where contno=s.contno and trans_policy_flag='1' and trans_state='2'
)
and s.startdate >= date_sub(curdate(), interval (select convert(config_value, int) from mysql.huida_reinsurance_backend.sys_config where config_key='sys.view.store.data.lately.days') day);



DROP MATERIALIZED VIEW IF EXISTS v_dws_claim;
CREATE MATERIALIZED VIEW v_dws_claim 
DISTRIBUTED BY HASH(`clm_no`) 
REFRESH DEFERRED ASYNC START('2025-04-01 03:30:00') EVERY (interval 1 day) 
as select concat_ws('#', lc.clmno, lgc.polno, lsr.customerno, lgc.getdutycode) as unique_key, 
lgc.contno as cont_no, lgc.polno as pol_no, lsr.customerno as insured_no, lmr.riskcode as risk_code, lmr.riskname as risk_name, 
lmr.subriskflag as sub_risk_flag, lmr.riskperiod as risk_period, lgc.dutycode as duty_code, lmd.dutyname as duty_name, lgc.getdutycode as get_duty_code, 
lmg.getdutyname as get_duty_name, trl.liability_code as liability_code, trl.liability_name as liability_name, trl.claim_count as clm_count_limit, 
lc.clmno as clm_no, lc.rgtno as clm_rgt_no, lc.caseno as clm_case_no, lc.clmstate as clm_state, lc.standpay as clm_standpay, 
lc.beforepay as clm_beforepay, lc.balancepay as clm_balancepay, lgc.pay as clm_realpay, lc.givetype as clm_give_type, lc.givetypedesc as clm_give_type_desc, 
lr.caseenddate as busi_occur_date, str_to_date(concat_ws(' ', date_format(lr.caseenddate, '%Y-%m-%d'), lc.maketime), '%Y-%m-%d %H:%i:%s') as busi_occur_time,
llc.accdate as clm_acc_date, lr.rptdate as clm_rpt_date, llc.rgtdate as clm_rgt_date, lr.caseenddate as clm_case_end_date, 
lgc.enteraccdate as clm_enter_acc_date, llc.accidentdate as clm_accident_date, lgc.feefinatype as clm_fee_fina_type, lre.accidentreason as clm_accident_reason, 
(select sum(feesum) from data_warehouse.t_ods_core_llinqfee_prod where clmno=lc.clmno) as clm_fee_sum, 
llc.accresult1 as clm_accresult_1, (select ldd.icdname from data_warehouse.t_ods_core_lddisease_prod ldd where ldd.icdcode=llc.accresult1) as clm_accresult_1_name,
llc.accresult2 as clm_accresult_2, (select ldd.icdname from data_warehouse.t_ods_core_lddisease_prod ldd where ldd.icdcode=llc.accresult2) as clm_accresult_2_name,
llu.passflag as insured_pass_flag, lci.defotype as clm_defo_type , lci.defograde as clm_defo_grade, lpf.defoname as clm_defo_name, lpf.defogradename as clm_defo_grade_name, 
lgc.feeoperationtype as clm_fee_type, lbr.baltypedesc as clm_bal_type_desc, lgc.subfeeoperationtype as clm_sub_fee_type, lbr.subbaltypedesc as clm_sub_bal_type_desc, 
lsr.hospitalcode as clm_hospital_code, lsr.hospitalname as clm_hospital_name, lsr.inhospitaldate as clm_in_hospital_date, lsr.outhospitaldate as clm_out_hospital_date, 
lc.makedate as clm_make_date, lc.maketime as clm_make_time 
from data_warehouse.t_ods_core_llclaim_prod lc 
inner join data_warehouse.t_ods_core_llreport_prod lr on lr.rptno=lc.clmno 
inner join data_warehouse.t_ods_core_llcase_prod llc on llc.rgtno=lc.clmno 
inner join data_warehouse.t_ods_core_ljagetclaim_prod lgc on lgc.otherno=lc.clmno 
inner join data_warehouse.t_ods_core_llregister_prod lre on lre.rgtno=lc.rgtno 
inner join data_warehouse.t_ods_core_llsubreport_prod lsr on lsr.subrptno=lc.clmno 
inner join data_warehouse.t_ods_core_lmriskapp_prod lmr on lmr.riskcode=lgc.riskcode 
inner join data_warehouse.t_ods_core_lmduty_prod lmd on lmd.dutycode=lgc.dutycode 
inner join data_warehouse.t_ods_core_lmdutyget_prod lmg on lmg.getdutycode=lgc.getdutycode 
left join data_warehouse.t_ods_core_llcaseinfo_prod lci on lci.clmno=lc.clmno 
left join data_warehouse.t_ods_core_llparadeformity_prod lpf on lpf.defotype=lci.defotype and lpf.defograde=lci.defograde and lpf.defocode=lci.defocode 
left join data_warehouse.t_ods_core_llbalancerela_prod lbr on lbr.baltype=lgc.feeoperationtype  and lbr.subbaltype=lgc.subfeeoperationtype 
left join data_warehouse.t_ods_core_lluwmaster_prod llu on llu.caseno=lc.clmno and llu.insuredno=lsr.customerno and llu.polno=lgc.polno 
left join mysql.huida_reinsurance_backend.t_liability_mapping terlm on lgc.getdutycode=terlm.lis_liability_code and terlm.risk_code=lgc.riskcode and terlm.status=0 and terlm.is_del=0 
left join mysql.huida_reinsurance_backend.t_risk_liability trl on trl.liability_code=terlm.rs_liability_code and trl.risk_code=lgc.riskcode and trl.status=0 and trl.is_del=0 
where lc.clmstate='50' and lc.givetype in('0','1') and lgc.getdutycode!='0' 
and not exists (
	select 1 from data_warehouse.t_ods_core_t_cont_trans_lifelong_prod where contno=lgc.contno and trans_policy_flag='1' and trans_state='2'
);