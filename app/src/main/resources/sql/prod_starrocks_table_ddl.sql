CREATE TABLE `t_cedeout_batch_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT "主键自增",
  `batch_no` varchar(50) NOT NULL COMMENT "批次号",
  `calc_type` tinyint(4) NULL COMMENT "执行方式（0=自动,1=手动）",
  `input_params` varchar(500) NULL COMMENT "执行参数;手动执行时存储页面条件",
  `pass_count` int(11) NULL COMMENT "成功数",
  `fail_count` int(11) NULL COMMENT "失败数",
  `executor` varchar(50) NULL COMMENT "执行人",
  `progress` tinyint(4) NULL COMMENT "执行状态（0=未开始,1=执行中,2=执行完成）",
  `execute_date` date NULL COMMENT "执行日期",
  `status` tinyint(4) NULL COMMENT "状态（0=有效,1=无效）",
  `remark` varchar(100) NULL COMMENT "备注",
  `is_del` tinyint(4) NULL DEFAULT "0" COMMENT "是否删除（0=未删除,1=已删除）",
  `create_by` varchar(50) NULL COMMENT "创建人",
  `create_time` datetime NULL COMMENT "创建时间",
  `update_by` varchar(50) NULL COMMENT "修改人",
  `update_time` datetime NULL COMMENT "修改时间"
) ENGINE=OLAP 
PRIMARY KEY(`id`)
DISTRIBUTED BY HASH(`id`) BUCKETS 16
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "true",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `t_dws_reinsu_policy_liability` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT "自增",
  `batch_no` varchar(64) NOT NULL COMMENT "批次号",
  `busi_type` tinyint(4) NULL COMMENT "业务类型(0=新单,1=续期)",
  `tdch_ids` varchar(128) NULL COMMENT "历史表id集合",
  `unique_key` varchar(128) NULL COMMENT "唯一键",
  `programme_code` varchar(64) NOT NULL COMMENT "再保方案编码",
  `cont_type` tinyint(4) NOT NULL COMMENT "保单类型(1=个险,2=团险)",
  `grp_cont_no` varchar(64) NULL COMMENT "集体合同号码",
  `cont_no` varchar(64) NOT NULL COMMENT "个单合同号码",
  `pol_no` varchar(64) NOT NULL COMMENT "险种号",
  `main_pol_no` varchar(64) NULL COMMENT "主险保单险种号",
  `sale_chnl` varchar(16) NULL COMMENT "销售渠道编码",
  `sale_chnl_name` varchar(64) NULL COMMENT "销售渠道名称",
  `sell_type` varchar(16) NULL COMMENT "销售方式",
  `sell_type_name` varchar(64) NULL COMMENT "销售方式名称",
  `sale_com_code` varchar(64) NULL COMMENT "销售机构编码",
  `sale_com_name` varchar(256) NULL COMMENT "销售机构名称",
  `agent_com_code` varchar(64) NULL COMMENT "代理机构编码",
  `agent_com_name` varchar(256) NULL COMMENT "代理机构名称",
  `manage_com_code` varchar(64) NULL COMMENT "管理机构编码",
  `manage_com_name` varchar(256) NULL COMMENT "管理机构名称",
  `bank_branch_name` varchar(256) NULL COMMENT "银保支行名称",
  `busi_occur_date` date NULL COMMENT "业务发生日",
  `busi_occur_time` datetime NULL COMMENT "业务发生时间",
  `sign_date` date NULL COMMENT "签单日期",
  `sign_time` varchar(16) NULL COMMENT "签单时间",
  `risk_vali_date` date NULL COMMENT "险种生效日期",
  `risk_end_date` date NULL COMMENT "险种终止日期",
  `cont_make_date` date NULL COMMENT "保单创建日期",
  `cont_make_time` varchar(16) NULL COMMENT "保单创建时间",
  `cont_year` int(11) NULL COMMENT "保单年度",
  `cont_month` tinyint(4) NULL DEFAULT "0" COMMENT "分出月份",
  `cont_anniversary` date NULL COMMENT "保单周年日",
  `previous_cont_anniversary` date NULL COMMENT "上一保单周年日",
  `cont_app_flag` tinyint(4) NULL COMMENT "保单状态",
  `risk_code` varchar(64) NULL COMMENT "险种编码",
  `risk_name` varchar(128) NULL COMMENT "险种名称",
  `risk_type3` varchar(16) NULL COMMENT "险种类别(1=传统险,2=分红险,3=投连险,4=万能险)",
  `plan_code` varchar(32) NULL COMMENT "保障计划",
  `pol_risk_type` varchar(8) NULL COMMENT "风险类型",
  `risk_app_flag` tinyint(4) NULL COMMENT "险种状态",
  `sub_risk_flag` varchar(8) NULL COMMENT "主附险标识",
  `risk_period` varchar(8) NULL COMMENT "一年期险种标志(L=长险,M=一年期险,S=极短期险)",
  `insu_year` int(11) NULL COMMENT "保险期间",
  `insu_year_flag` varchar(8) NULL COMMENT "保险期间单位",
  `pay_intv` tinyint(4) NULL COMMENT "缴费方式",
  `payend_year` int(11) NULL COMMENT "缴费期间",
  `payend_year_flag` varchar(8) NULL COMMENT "缴费期间单位",
  `pay_to_date` date NULL COMMENT "保费交至日期",
  `pay_end_date` date NULL COMMENT "终交日期",
  `get_duty_codes` varchar(128) NULL COMMENT "给付责任编码(多个之间用逗号分隔)",
  `get_duty_names` varchar(512) NULL COMMENT "给付责任名称(多个之间用逗号分隔)",
  `liability_code` varchar(64) NULL COMMENT "再保责任编码",
  `liability_name` varchar(128) NULL COMMENT "再保责任名称",
  `amount` decimal(16, 2) NULL COMMENT "保额",
  `base_premium` decimal(16, 2) NULL COMMENT "基础保费",
  `add_premium` decimal(16, 2) NULL COMMENT "加费",
  `add_scale` decimal(16, 2) NULL COMMENT "EM加点",
  `sum_pay_money` decimal(16, 2) NULL COMMENT "累交保费",
  `sum_add_money` decimal(16, 2) NULL COMMENT "累计追加保费",
  `sum_get_money` decimal(16, 2) NULL COMMENT "累计部分领取金额",
  `risk_free_flag` tinyint(4) NULL COMMENT "险种是否已豁免",
  `appnt_no` varchar(64) NULL COMMENT "投保人号",
  `appnt_name` varchar(128) NULL COMMENT "投保人姓名",
  `appnt_id_type` varchar(16) NULL COMMENT "投保人证件类型",
  `appnt_id_no` varchar(64) NULL COMMENT "投保人证件号码",
  `appnt_sex` tinyint(4) NULL COMMENT "投保人性别",
  `appnt_birthday` date NULL COMMENT "投保人出生日期",
  `appnt_occ_type` varchar(16) NULL COMMENT "投保人职业等级",
  `appnt_occ_code` varchar(24) NULL COMMENT "投保人职业编码",
  `insured_peoples` tinyint(4) NULL COMMENT "被保险人个数",
  `insured_app_age` tinyint(4) NULL COMMENT "被保险人投保年龄",
  `insured_no` varchar(64) NULL COMMENT "被保险人客户号",
  `insured_name` varchar(128) NULL COMMENT "被保险人姓名",
  `insured_id_type` varchar(16) NULL COMMENT "被保险人证件类型",
  `insured_id_no` varchar(64) NULL COMMENT "被保险人证件号码",
  `insured_sex` tinyint(4) NULL COMMENT "被保险人性别",
  `insured_birthday` date NULL COMMENT "被保险人出生日期",
  `insured_occ_type` varchar(16) NULL COMMENT "被保险人职业等级",
  `insured_occ_code` varchar(24) NULL COMMENT "被保险人职业编码",
  `insured_pass_flag` tinyint(4) NULL COMMENT "被保险人健康等级(4=次标体,9=标体)",
  `cash_value` decimal(16, 2) NULL COMMENT "现金价值",
  `insuacc_value` decimal(16, 2) NULL COMMENT "账户价值",
  `cedeout_type` tinyint(4) NULL COMMENT "是否临分(0=正常分保,1=临时分保)",
  `core_conclusion` varchar(512) NULL COMMENT "核心临分结论",
  `reinsu_add_scale` decimal(16, 2) NULL COMMENT "再保加费评点",
  `cedeout_count` tinyint(4) NULL COMMENT "分出次数",
  `handle_status` tinyint(4) NOT NULL DEFAULT "0" COMMENT "处理状态(0=未处理,1=成功,2=失败)",
  `handle_date` datetime NULL COMMENT "处理时间",
  `handle_error_num` tinyint(4) NULL DEFAULT "0" COMMENT "错误次数",
  `handle_error_msg` varchar(256) NULL COMMENT "错误原因",
  `status` tinyint(4) NOT NULL DEFAULT "0" COMMENT "状态(0=有效,1=无效)",
  `back_track_data` tinyint(4) NULL DEFAULT "0" COMMENT "状态(0=正常,1=回溯数据)",
  `remark` varchar(128) NULL COMMENT "备注",
  `is_del` tinyint(4) NOT NULL DEFAULT "0" COMMENT "是否删除(0=未删除,1=已删除)",
  `create_by` varchar(64) NULL DEFAULT "system" COMMENT "创建人",
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "创建时间",
  `update_by` varchar(64) NULL DEFAULT "system" COMMENT "修改人",
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "修改时间"
) ENGINE=OLAP 
PRIMARY KEY(`id`)
DISTRIBUTED BY HASH(`id`) BUCKETS 16 
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "true",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `t_dws_reinsu_trade` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT "主键自增",
  `batch_no` varchar(64) NOT NULL COMMENT "批次号",
  `busi_type` tinyint(4) NOT NULL COMMENT "业务类型(0=新单,1=续期,2=保全,3=理赔,4=满期,5=失效)",
  `data_type` tinyint(4) NOT NULL COMMENT "数据类型(0=分出,1=摊回)",
  `cont_type` tinyint(4) NOT NULL COMMENT "保单类型(1=个险,2=团险)",
  `data_copy` tinyint(4) NOT NULL COMMENT "数据复制标识(0=原始,1=复制)",
  `data_group_no` varchar(64) NOT NULL COMMENT "数据分组号",
  `busi_occur_date` date NOT NULL COMMENT "业务发生日期",
  `get_date` date NOT NULL COMMENT "提数日期",
  `get_time` varchar(24) NOT NULL COMMENT "提数时间",
  `account_period` int(11) NOT NULL COMMENT "所属账期",
  `account_date` date NOT NULL COMMENT "账单日期",
  `grp_cont_no` varchar(64) NULL COMMENT "集体合同号码",
  `cont_no` varchar(64) NOT NULL COMMENT "个单合同号码",
  `pol_no` varchar(64) NOT NULL COMMENT "险种号",
  `main_pol_no` varchar(64) NULL COMMENT "主险保单险种号",
  `sale_chnl` varchar(16) NULL COMMENT "销售渠道编码",
  `sale_chnl_name` varchar(64) NULL COMMENT "销售渠道名称",
  `sell_type` varchar(16) NULL COMMENT "销售方式",
  `sell_type_name` varchar(64) NULL COMMENT "销售方式名称",
  `sale_com_code` varchar(64) NULL COMMENT "销售机构编码",
  `sale_com_name` varchar(256) NULL COMMENT "销售机构名称",
  `agent_com_code` varchar(64) NULL COMMENT "代理机构编码",
  `agent_com_name` varchar(256) NULL COMMENT "代理机构名称",
  `manage_com_code` varchar(64) NULL COMMENT "管理机构编码",
  `manage_com_name` varchar(256) NULL COMMENT "管理机构名称",
  `cont_make_date` date NULL COMMENT "保单创建日期",
  `sign_date` date NULL COMMENT "签单日期",
  `sign_time` varchar(24) NULL COMMENT "签单时间",
  `cont_year` int(11) NULL COMMENT "保单年度",
  `cont_month` tinyint(4) NULL DEFAULT "0" COMMENT "分出月份",
  `cont_anniversary` date NULL COMMENT "保单周年日",
  `previous_cont_anniversary` date NULL COMMENT "上一保单周年日",
  `cont_app_flag` tinyint(4) NULL COMMENT "保单状态(1=有效,4=失效)",
  `invalid_state_type` varchar(64) NULL COMMENT "终止类型",
  `invalid_state_reason` varchar(16) NULL COMMENT "终止原因(01=满期终止,02=退保终止,03=解约终止,04=理赔终止,05=协退终止,06=犹退终止,07=失效终止,08=其他终止)",
  `invalid_start_date` date NULL COMMENT "终止开始日期",
  `risk_code` varchar(64) NULL COMMENT "险种编码",
  `risk_name` varchar(128) NULL COMMENT "险种名称",
  `risk_app_flag` tinyint(4) NULL COMMENT "险种状态(1=有效,4=失效)",
  `risk_vali_date` date NULL COMMENT "生效日期",
  `risk_end_date` date NULL COMMENT "保险终止日",
  `sub_risk_flag` varchar(16) NULL COMMENT "主附险标识(M=主险,S=附加险)",
  `risk_period` varchar(16) NULL COMMENT "一年期险种标志(L=长险,M=一年期险,S=极短期险)",
  `risk_type3` varchar(16) NULL COMMENT "险种类别(1=传统险,2=分红险,3=投连险,4=万能险)",
  `plan_code` varchar(32) NULL COMMENT "保障计划",
  `pol_risk_type` varchar(8) NULL COMMENT "风险类型",
  `get_duty_codes` varchar(128) NULL COMMENT "给付责任编码(多个之间用逗号分隔)",
  `get_duty_names` varchar(512) NULL COMMENT "给付责任名称(多个之间用逗号分隔)",
  `liability_code` varchar(64) NULL COMMENT "再保责任编码",
  `liability_name` varchar(128) NULL COMMENT "再保责任名称",
  `insu_year` int(11) NULL COMMENT "保险期间",
  `insu_year_flag` varchar(16) NULL COMMENT "保险期间单位(Y=年, M=月, D=日, A=岁)",
  `pay_intv` tinyint(4) NULL COMMENT "缴费频率(0=趸交, 1=月交, 2=不定期交, 3=季交, 6=半年交, 12=年交)",
  `payend_year` tinyint(4) NULL COMMENT "缴费期间",
  `payend_year_flag` varchar(16) NULL COMMENT "缴费期间单位(Y=年, M=月, D=日, A=岁)",
  `risk_free_flag` tinyint(4) NULL DEFAULT "0" COMMENT "险种是否已豁免(0=未豁免,1=已豁免)",
  `pay_periods` int(11) NULL DEFAULT "0" COMMENT "总缴费期数",
  `in_pay_periods` int(11) NULL DEFAULT "0" COMMENT "已缴费期数",
  `un_pay_periods` int(11) NULL DEFAULT "0" COMMENT "未缴费期数",
  `un_pay_premium` decimal(20, 2) NULL DEFAULT "0" COMMENT "应缴未缴保费",
  `amount` decimal(16, 2) NULL COMMENT "保额",
  `available_amount` decimal(16, 2) NULL COMMENT "有效保额",
  `total_premium` decimal(16, 2) NULL COMMENT "总保费",
  `base_premium` decimal(16, 2) NULL COMMENT "基础保费",
  `add_premium` decimal(16, 2) NULL COMMENT "加费",
  `add_scale` decimal(16, 2) NULL COMMENT "加费评点",
  `pay_to_date` date NULL COMMENT "保费交至日期",
  `pay_end_date` date NULL COMMENT "交费终止日",
  `sum_pay_money` decimal(16, 2) NULL DEFAULT "0" COMMENT "累交保费",
  `sum_add_money` decimal(16, 2) NULL DEFAULT "0" COMMENT "累计加费",
  `sum_get_money` decimal(16, 2) NULL DEFAULT "0" COMMENT "累计领取金额",
  `cash_value` decimal(20, 4) NULL DEFAULT "0" COMMENT "现金价值",
  `insuacc_value` decimal(20, 4) NULL DEFAULT "0" COMMENT "账户价值",
  `appnt_no` varchar(64) NULL COMMENT "投保人客户号",
  `appnt_name` varchar(128) NULL COMMENT "投保人姓名",
  `appnt_id_type` varchar(64) NULL COMMENT "投保人证件类型",
  `appnt_id_no` varchar(64) NULL COMMENT "投保人证件号码",
  `appnt_sex` tinyint(4) NULL COMMENT "投保人性别(0=男,1=女)",
  `appnt_birthday` date NULL COMMENT "投保人出生日期",
  `appnt_occ_type` varchar(16) NULL COMMENT "投保人职业等级",
  `appnt_occ_code` varchar(24) NULL COMMENT "投保人职业代码",
  `insured_peoples` int(11) NULL COMMENT "被保险人数量",
  `insured_no` varchar(64) NULL COMMENT "被保险人客户号",
  `insured_name` varchar(128) NULL COMMENT "被保险人姓名",
  `insured_id_type` varchar(64) NULL COMMENT "被保险人证件类型",
  `insured_id_no` varchar(64) NULL COMMENT "被保险人证件号码",
  `insured_sex` tinyint(4) NULL COMMENT "被保险人性别(0=男,1=女)",
  `insured_birthday` date NULL COMMENT "被保险人出生日期",
  `insured_occ_type` varchar(16) NULL COMMENT "被保险人职业等级",
  `insured_occ_code` varchar(24) NULL COMMENT "被保险人职业代码",
  `insured_socisec` tinyint(4) NULL COMMENT "被保人是否有医保(0否,1=是)",
  `insured_pass_flag` tinyint(4) NULL COMMENT "被保险人健康状况(4=次标体, 9=标体)",
  `insured_app_age` tinyint(4) NULL COMMENT "被保险人投保年龄",
  `edor_accept_no` varchar(64) NULL COMMENT "保全受理号",
  `edor_type` varchar(16) NULL COMMENT "保全项目编码",
  `edor_state` tinyint(4) NULL COMMENT "保全状态(0=有效)",
  `edor_get_money` decimal(16, 2) NULL COMMENT "保全补退金额",
  `edor_get_interest` decimal(16, 2) NULL COMMENT "保全补退利息",
  `edor_app_date` datetime NULL COMMENT "保全申请日期",
  `edor_validate` datetime NULL COMMENT "保全生效日期",
  `edor_conf_date` date NULL COMMENT "保全确认日期",
  `edor_conf_time` varchar(24) NULL COMMENT "保全确认时间",
  `edor_make_date` date NULL COMMENT "保全创建日期",
  `clm_no` varchar(64) NULL COMMENT "赔案号",
  `clm_state` varchar(16) NULL COMMENT "案件状态(10=报案,20=立案,30=审核,35=预付,40=审批,50=结案,60=完成,70=关闭)",
  `clm_give_type` varchar(16) NULL COMMENT "理赔结论代码(0=正常给付,1=部分给付,2=拒付,3=公司撤案,4=客户撤案)",
  `clm_standpay` decimal(16, 2) NULL COMMENT "理算金额",
  `clm_realpay` decimal(16, 2) NULL COMMENT "赔付金额",
  `clm_accident_date` date NULL COMMENT "事故发生日期",
  `clm_acc_date` date NULL COMMENT "出险日期",
  `clm_rpt_date` date NULL COMMENT "报案日",
  `clm_rgt_date` date NULL COMMENT "立案日",
  `clm_case_end_date` date NULL COMMENT "结案日",
  `clm_enter_acc_date` date NULL COMMENT "赔款给付日期",
  `clm_fee_sum` decimal(16, 2) NULL COMMENT "调查费",
  `clm_fee_type` varchar(64) NULL COMMENT "结算业务类型",
  `clm_bal_type_desc` varchar(128) NULL COMMENT "结算业务类型名称",
  `clm_sub_fee_type` varchar(64) NULL COMMENT "结算业务子类型",
  `clm_sub_bal_type_desc` varchar(128) NULL COMMENT "结算业务子类型名称",
  `clm_defo_grade` varchar(64) NULL COMMENT "伤残等级编码",
  `clm_defo_grade_name` varchar(128) NULL COMMENT "伤残等级",
  `clm_defo_type` varchar(64) NULL COMMENT "伤残程度编码",
  `clm_defo_name` varchar(128) NULL COMMENT "伤残程度",
  `clm_hospital_name` varchar(512) NULL COMMENT "医院名称",
  `clm_in_hospital_date` date NULL COMMENT "住院日期",
  `clm_out_hospital_date` date NULL COMMENT "出院日期",
  `clm_accident_reason` varchar(128) NULL COMMENT "出险原因代码(1=意外,2=疾病,9=其它)",
  `clm_accresult_1` varchar(32) NULL COMMENT "出险结果1编码",
  `clm_accresult_2` varchar(32) NULL COMMENT "出险结果2编码",
  `clm_accresult_1_name` varchar(64) NULL COMMENT "出险结果1名称",
  `clm_accresult_2_name` varchar(64) NULL COMMENT "出险结果2名称",
  `clm_make_date` date NULL COMMENT "理赔创建日期",
  `rs_pay_intv` tinyint(4) NULL DEFAULT "12" COMMENT "再保缴费频率(12=年交)",
  `calc_status` tinyint(4) NULL DEFAULT "0" COMMENT "计算状态(0=未计算,1=计算成功,1=计算失败)",
  `calc_type` tinyint(4) NULL COMMENT "计算方式(0=自动,1=手动)",
  `calc_fail_code` tinyint(4) NULL COMMENT "失败原因(0=缺少险种映射,1=缺少准备金,2=缺少费率,3=缺少合同,4=缺少方案,5=缺少公式)",
  `cedeout_type` tinyint(4) NULL COMMENT "是否临分(0=否,1=是)",
  `re_notice_url` varchar(128) NULL COMMENT "临分通知书",
  `company_code` varchar(64) NULL COMMENT "再保公司编码",
  `company_name` varchar(256) NULL COMMENT "再保公司名称",
  `contract_code` varchar(64) NULL COMMENT "再保合约编码",
  `contract_name` varchar(128) NULL COMMENT "再保合约名称",
  `main_contract_code` varchar(64) NULL COMMENT "再保主合约编码",
  `main_contract_name` varchar(128) NULL COMMENT "再保主合约名称",
  `programme_code` varchar(64) NULL COMMENT "方案编码",
  `programme_name` varchar(128) NULL COMMENT "方案名称",
  `programme_self_amount` decimal(16, 2) NULL COMMENT "方案自留额",
  `programme_self_scale` decimal(16, 2) NULL COMMENT "方案自留比例",
  `cedeout_count` tinyint(4) NULL COMMENT "分出次数",
  `cedeout_mode` tinyint(4) NULL COMMENT "分出模式(0=净保费,1=毛保费)",
  `cedeout_way` tinyint(4) NULL COMMENT "分出方式(0=溢额,1=成数,2=混合)",
  `addup_amount_type` tinyint(4) NULL COMMENT "累计风险保额方式(0=不累计,1=责任层累计)",
  `addup_risk_code` varchar(64) NULL COMMENT "风险保额编码",
  `addup_risk_name` varchar(64) NULL COMMENT "风险保额名称",
  `init_risk_amount` decimal(20, 4) NULL COMMENT "初始化风险保额",
  `occupy_risk_amount` decimal(20, 4) NULL COMMENT "占用风险保额",
  `release_risk_amount` decimal(20, 4) NULL COMMENT "释放风险保额",
  `cedeout_amount` decimal(20, 4) NULL COMMENT "分出保额",
  `self_amount` decimal(20, 4) NULL COMMENT "自留额",
  `accept_copies` decimal(16, 2) NULL COMMENT "接受份额",
  `cedeout_scale` decimal(16, 2) NULL COMMENT "分保比例",
  `self_scale` decimal(16, 2) NULL COMMENT "自留比例",
  `cedeout_premium` decimal(16, 2) NULL COMMENT "基础再保费",
  `cedeout_add_premium` decimal(16, 2) NULL COMMENT "加费再保费",
  `cedeout_total_premium` decimal(16, 2) NULL COMMENT "总再保费",
  `cedeout_commission` decimal(16, 2) NULL COMMENT "再保佣金",
  `tax_rate` decimal(16, 2) NULL COMMENT "增值税率",
  `added_tax` decimal(16, 2) NULL COMMENT "增值税",
  `reserves_type` tinyint(4) NULL COMMENT "准备金类型",
  `reserves_id` bigint(20) NULL COMMENT "准备金因子Id",
  `reserves` decimal(20, 4) NULL COMMENT "准备金",
  `rate_code` varchar(64) NULL COMMENT "再保费率编码",
  `rate_data_id` bigint(20) NULL COMMENT "再保费率Id",
  `rate_data_value` decimal(20, 4) NULL COMMENT "再保费率值",
  `cedeout_rate_data_value` decimal(20, 4) NULL COMMENT "再保费率值*拆扣费率值",
  `com_rate_code` varchar(64) NULL COMMENT "佣金费率编码",
  `com_rate_data_id` bigint(20) NULL COMMENT "佣金费率Id",
  `com_rate_data_value` decimal(20, 4) NULL COMMENT "佣金费率值",
  `dis_rate_code` varchar(64) NULL COMMENT "折扣费率编码",
  `dis_rate_data_id` bigint(20) NULL COMMENT "折扣费率Id",
  `dis_rate_data_value` decimal(20, 4) NULL COMMENT "拆扣费率值",
  `return_status` tinyint(4) NULL DEFAULT "0" COMMENT "摊回状态(0=未摊回,1=已摊回,2=已终止)",
  `return_date` date NULL COMMENT "摊回日期",
  `src_out_trade_id` bigint(20) NULL COMMENT "源分出记录Id(摊回记录该字段有值)",
  `return_reason` varchar(512) NULL COMMENT "摊回原因描述(分出记录该字段有值)",
  `return_premium` decimal(16, 2) NULL COMMENT "退回分保费",
  `return_cb_premium` decimal(16, 2) NULL COMMENT "退回次标再保费",
  `return_total_premium` decimal(16, 2) NULL COMMENT "退回总保费",
  `return_claim_amount` decimal(16, 2) NULL COMMENT "摊回理赔金",
  `return_expired_gold` decimal(16, 2) NULL COMMENT "摊回满期金",
  `return_commission` decimal(16, 2) NULL COMMENT "退还佣金",
  `adjust_status` tinyint(4) NULL DEFAULT "0" COMMENT "是否调整过(0=否,1=是)",
  `adjust_date` datetime NULL COMMENT "调整时间",
  `adjuster` varchar(64) NULL COMMENT "调整人",
  `adjust_reason` varchar(256) NULL COMMENT "调整原因",
  `adjust_batch_no` varchar(64) NULL COMMENT "调整批次号",
  `bill_confirm_status` tinyint(4) NULL DEFAULT "1" COMMENT "账单确认状态(0=未确认, 1=已确认)",
  `bill_confirm_date` datetime NULL COMMENT "账单确认日期",
  `bill_confirmer` varchar(64) NULL COMMENT "账单确认人",
  `status` tinyint(4) NULL DEFAULT "0" COMMENT "状态(0=有效,1=无效)",
  `back_track_data` tinyint(4) NULL DEFAULT "0" COMMENT "状态(0=正常,1=回溯数据)",
  `remark` varchar(128) NULL COMMENT "备注",
  `is_del` tinyint(4) NULL DEFAULT "0" COMMENT "是否删除(0=未删除,1=已删除)",
  `create_by` varchar(64) NULL DEFAULT "system" COMMENT "创建人",
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "创建时间",
  `update_by` varchar(64) NULL DEFAULT "system" COMMENT "修改人",
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "修改时间"
) ENGINE=OLAP 
PRIMARY KEY(`id`)
DISTRIBUTED BY HASH(`id`) BUCKETS 16 
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "true",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `t_dws_cont_history` (
  `busi_type` tinyint(4) NOT NULL COMMENT "业务类型(0=新单,1=续期,2=保全,3=理赔,4=满期,5=失效)",
  `unique_key` varchar(128) NOT NULL COMMENT "唯一键",
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT "自增Id",
  `batch_no` varchar(64) NOT NULL COMMENT "批次号",
  `batch_date` date NOT NULL COMMENT "批次执行日期",
  `busi_occur_time` datetime NOT NULL COMMENT "业务发生时间",
  `busi_occur_date` date NOT NULL COMMENT "业务发生日期",
  `cont_type` tinyint(4) NULL COMMENT "保单类型(1=个险,2=团险)",
  `grp_cont_no` varchar(64) NULL COMMENT "团单号",
  `cont_no` varchar(64) NOT NULL COMMENT "个单合同号码",
  `pol_no` varchar(64) NULL COMMENT "险种号",
  `main_pol_no` varchar(64) NULL COMMENT "主险保单险种号",
  `sale_chnl` varchar(16) NULL COMMENT "销售渠道编码",
  `sale_chnl_name` varchar(64) NULL COMMENT "销售渠道名称",
  `sell_type` varchar(16) NULL COMMENT "销售方式",
  `sell_type_name` varchar(64) NULL COMMENT "销售方式名称",
  `sale_com_code` varchar(64) NULL COMMENT "销售机构编码",
  `sale_com_name` varchar(256) NULL COMMENT "销售机构名称",
  `agent_com_code` varchar(64) NULL COMMENT "代理机构编码",
  `agent_com_name` varchar(256) NULL COMMENT "代理机构名称",
  `manage_com_code` varchar(64) NULL COMMENT "管理机构编码",
  `manage_com_name` varchar(256) NULL COMMENT "管理机构名称",
  `bank_branch_name` varchar(600) NULL COMMENT "管理机构名称",
  `sign_date` date NULL COMMENT "签单日期",
  `sign_time` varchar(24) NULL COMMENT "签单时间",
  `risk_vali_date` date NULL COMMENT "生效日期",
  `risk_end_date` date NULL COMMENT "保险终止日",
  `cont_make_date` date NULL COMMENT "保单创建日期",
  `cont_make_time` varchar(24) NULL COMMENT "保单创建时间",
  `cont_year` int(11) NULL COMMENT "保单年度",
  `cont_anniversary` date NULL COMMENT "保单周年日",
  `previous_cont_anniversary` date NULL COMMENT "上一保单周年日",
  `cont_app_flag` tinyint(4) NULL COMMENT "保单状态(1=有效,4=失效)",
  `risk_code` varchar(64) NULL COMMENT "险种编码",
  `risk_name` varchar(360) NULL COMMENT "险种名称",
  `risk_type3` varchar(16) NULL COMMENT "险种类别(1=传统险,2=分红险,3=投连险,4=万能险)",
  `plan_code` varchar(32) NULL COMMENT "保障计划",
  `pol_risk_type` varchar(8) NULL COMMENT "风险类型",
  `risk_app_flag` tinyint(4) NULL COMMENT "险种状态(1=有效,4=失效)",
  `sub_risk_flag` varchar(16) NULL COMMENT "主附险标识(M=主险,S=附加险)",
  `risk_period` varchar(16) NULL COMMENT "一年期险种标志(L=长险,M=一年期险,S=极短期险)",
  `insu_year` int(11) NULL COMMENT "保险期间",
  `insu_year_flag` varchar(16) NULL COMMENT "保险期间单位(Y=年, M=月, D=日, A=岁)",
  `pay_intv` tinyint(4) NULL COMMENT "缴费频率(0=趸交, 1=月交, 2=不定期交, 3=季交, 6=半年交, 12=年交)",
  `payend_year` tinyint(4) NULL COMMENT "缴费期间",
  `payend_year_flag` varchar(16) NULL COMMENT "缴费期间单位(Y=年, M=月, D=日, A=岁)",
  `pay_end_date` date NULL COMMENT "交费终止日",
  `pay_to_date` date NULL COMMENT "保费交至日期",
  `duty_code` varchar(128) NULL COMMENT "责任编码",
  `duty_name` varchar(512) NULL COMMENT "责任名称",
  `get_duty_code` varchar(128) NULL COMMENT "给付责任编码",
  `get_duty_name` varchar(512) NULL COMMENT "给付责任名称",
  `get_duty_state` varchar(30) NULL COMMENT "给付责任状态",
  `amount` decimal(16, 2) NULL COMMENT "保额",
  `sum_pay_money` decimal(16, 2) NULL DEFAULT "0" COMMENT "累交保费",
  `sum_add_money` decimal(16, 2) NULL DEFAULT "0" COMMENT "累计加费",
  `sum_get_money` decimal(16, 2) NULL DEFAULT "0" COMMENT "累计领取金额",
  `base_premium` decimal(16, 2) NULL COMMENT "基础保费",
  `add_premium` decimal(16, 2) NULL COMMENT "加费",
  `add_scale` decimal(16, 2) NULL COMMENT "加费评点",
  `risk_free_flag` tinyint(4) NULL DEFAULT "0" COMMENT "险种是否已豁免(0=未豁免,1=已豁免)",
  `appnt_no` varchar(64) NULL COMMENT "投保人客户号",
  `appnt_name` varchar(128) NULL COMMENT "投保人姓名",
  `appnt_id_type` varchar(64) NULL COMMENT "投保人证件类型",
  `appnt_id_no` varchar(64) NULL COMMENT "投保人证件号码",
  `appnt_sex` tinyint(4) NULL COMMENT "投保人性别(0=男,1=女)",
  `appnt_birthday` date NULL COMMENT "投保人出生日期",
  `appnt_occ_type` varchar(16) NULL COMMENT "投保人职业等级",
  `appnt_occ_code` varchar(24) NULL COMMENT "投保人职业代码",
  `insured_peoples` int(11) NULL COMMENT "被保险人数量",
  `main_insured_no` varchar(64) NULL COMMENT "险种主被保险人号",
  `insured_sequence_no` tinyint(4) NULL COMMENT "被保险人序号",
  `insured_app_age` tinyint(4) NULL COMMENT "被保险人投保年龄",
  `insured_no` varchar(64) NULL COMMENT "被保险人客户号",
  `insured_name` varchar(128) NULL COMMENT "被保险人姓名",
  `insured_id_type` varchar(64) NULL COMMENT "被保险人证件类型",
  `insured_id_no` varchar(64) NULL COMMENT "被保险人证件号码",
  `insured_sex` tinyint(4) NULL COMMENT "被保险人性别(0=男,1=女)",
  `insured_birthday` date NULL COMMENT "被保险人出生日期",
  `insured_occ_type` varchar(16) NULL COMMENT "被保险人职业等级",
  `insured_occ_code` varchar(24) NULL COMMENT "被保险人职业代码",
  `insured_pass_flag` tinyint(4) NULL COMMENT "被保险人健康状况(4=次标体, 9=标体)",
  `cedeout_type` tinyint(4) NULL COMMENT "是否临分(0=否,1=是)",
  `core_conclusion` varchar(24) NULL COMMENT "核心临分结论",
  `reinsu_add_scale` varchar(50) NULL COMMENT "再保加费评点",
  `cedeout_count` tinyint(4) NULL COMMENT "分出次数（冗余字段，无实际作用）",
  `insuacc_value` decimal(20, 4) NULL DEFAULT "0" COMMENT "账户价值",
  `cash_value` decimal(20, 4) NULL DEFAULT "0" COMMENT "现金价值",
  `edor_accept_no` varchar(64) NULL COMMENT "保全受理号",
  `edor_no` varchar(64) NULL COMMENT "保全号",
  `edor_type` varchar(16) NULL COMMENT "保全项目编码",
  `edor_state` tinyint(4) NULL COMMENT "保全状态(0=有效)",
  `edor_app_type` varchar(16) NULL COMMENT "保全号",
  `edor_make_date` date NULL COMMENT "保全创建日期",
  `edor_make_time` varchar(24) NULL COMMENT "保全创建时间",
  `edor_get_money` decimal(16, 2) NULL COMMENT "保全补退金额",
  `edor_get_interest` decimal(16, 2) NULL COMMENT "保全补退利息",
  `edor_app_date` datetime NULL COMMENT "保全申请日期",
  `edor_validate` datetime NULL COMMENT "保全生效日期",
  `edor_conf_date` date NULL COMMENT "保全确认日期",
  `edor_conf_time` varchar(24) NULL COMMENT "保全确认时间",
  `edor_app_name` varchar(360) NULL COMMENT "保全申请人",
  `edor_standby_flag1` varchar(600) NULL COMMENT "保全备用字段1(edorType=RB时存储为回退的保全受理号)",
  `edor_standby_flag2` varchar(600) NULL COMMENT "保全备用字段2",
  `edor_standby_flag3` varchar(3000) NULL COMMENT "保全备用字段3(edorType=RB时存储为回退的保全项目编码)",
  `edor_standby_flag4` varchar(600) NULL COMMENT "保全备用字段4",
  `edor_standby_flag5` varchar(600) NULL COMMENT "保全备用字段5",
  `clm_no` varchar(64) NULL COMMENT "赔案号",
  `clm_rgt_no` varchar(64) NULL COMMENT "立案号",
  `clm_case_no` varchar(64) NULL COMMENT "结案号",
  `clm_state` varchar(18) NULL COMMENT "案件状态(10=报案,20=立案,30=审核,35=预付,40=审批,50=结案,60=完成,70=关闭)",
  `clm_standpay` decimal(16, 2) NULL COMMENT "理算金额",
  `clm_beforepay` decimal(16, 2) NULL COMMENT "",
  `clm_balancepay` decimal(16, 2) NULL COMMENT "",
  `clm_realpay` decimal(16, 2) NULL COMMENT "赔付金额",
  `clm_give_type` varchar(30) NULL COMMENT "理赔结论代码(0=正常给付,1=部分给付,2=拒付,3=公司撤案,4=客户撤案)",
  `clm_give_type_desc` varchar(12000) NULL COMMENT "理赔结论代码详细",
  `clm_accident_date` date NULL COMMENT "事故发生日期",
  `clm_acc_date` date NULL COMMENT "出险日期",
  `clm_rpt_date` date NULL COMMENT "报案日",
  `clm_rgt_date` date NULL COMMENT "立案日",
  `clm_case_end_date` date NULL COMMENT "结案日",
  `clm_enter_acc_date` date NULL COMMENT "赔款给付日期",
  `clm_fee_sum` decimal(38, 2) NULL COMMENT "调查费",
  `clm_fee_fina_type` varchar(64) NULL COMMENT "",
  `clm_accident_reason` varchar(128) NULL COMMENT "出险原因代码(1=意外,2=疾病,9=其它)",
  `clm_accresult_1` varchar(64) NULL COMMENT "出险结果1编码",
  `clm_accresult_2` varchar(600) NULL COMMENT "出险结果2编码",
  `clm_accresult_1_name` varchar(600) NULL COMMENT "出险结果1名称",
  `clm_accresult_2_name` varchar(600) NULL COMMENT "出险结果2名称",
  `clm_defo_type` varchar(64) NULL COMMENT "伤残程度编码",
  `clm_defo_grade` varchar(64) NULL COMMENT "伤残等级编码",
  `clm_defo_name` varchar(1800) NULL COMMENT "伤残程度",
  `clm_defo_grade_name` varchar(240) NULL COMMENT "伤残等级",
  `clm_fee_type` varchar(64) NULL COMMENT "结算业务类型",
  `clm_bal_type_desc` varchar(180) NULL COMMENT "结算业务类型名称",
  `clm_sub_fee_type` varchar(64) NULL COMMENT "结算业务子类型",
  `clm_sub_bal_type_desc` varchar(180) NULL COMMENT "结算业务子类型名称",
  `clm_hospital_code` varchar(512) NULL COMMENT "医院编码",
  `clm_hospital_name` varchar(1800) NULL COMMENT "医院名称",
  `clm_in_hospital_date` date NULL COMMENT "住院日期",
  `clm_out_hospital_date` date NULL COMMENT "出院日期",
  `clm_make_date` date NULL COMMENT "理赔创建日期",
  `clm_make_time` varchar(64) NULL COMMENT "理赔创建时间",
  `state_type` varchar(600) NULL COMMENT "状态类别（Terminate=终止, Available=中止）",
  `state_state` varchar(600) NULL COMMENT "状态记录是否有效(0=有效,1=失效)",
  `state_reason` varchar(600) NULL COMMENT "终止原因（01=满期终止,02=退保终止,03=解约终止,04=理赔终止,05=协退终止,06=犹退终止,07=失效终止,08=其他终止）",
  `state_start_date` date NULL COMMENT "状态开始时间",
  `state_end_date` date NULL COMMENT "状态结束时间",
  `state_make_date` date NULL COMMENT "状态创建日期",
  `state_make_time` varchar(600) NULL COMMENT "状态创建时间",
  `sub_track_status` tinyint(4) NULL DEFAULT "1" COMMENT "关联子数据快照状态(0=未处理,1=成功,2=失败)",
  `handle_status` tinyint(4) NOT NULL DEFAULT "0" COMMENT "处理状态(0=未处理,1=成功,2=失败)",
  `handle_date` datetime NULL COMMENT "处理时间",
  `handle_error_num` tinyint(4) NULL DEFAULT "0" COMMENT "错误次数",
  `handle_error_msg` varchar(256) NULL COMMENT "错误原因",
  `remark` varchar(128) NULL COMMENT "备注",
  `is_del` tinyint(4) NULL DEFAULT "0" COMMENT "是否删除(0=未删除,1=已删除)",
  `create_by` varchar(64) NULL DEFAULT "system" COMMENT "创建人",
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "创建时间",
  `update_by` varchar(64) NULL DEFAULT "system" COMMENT "修改人",
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "修改时间"
) ENGINE=OLAP 
PRIMARY KEY(`busi_type`, `unique_key`)
DISTRIBUTED BY HASH(`busi_type`, `unique_key`) BUCKETS 16 
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "true",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `t_dws_edor_cont_liability` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT "自增Id",
  `unique_key` varchar(128) NOT NULL COMMENT "保单快照主表的唯一键",
  `cont_type` tinyint(4) NULL COMMENT "保单类型(1=个险,2=团险)",
  `grp_cont_no` varchar(64) NULL COMMENT "团单号",
  `cont_no` varchar(64) NOT NULL COMMENT "个单合同号码",
  `pol_no` varchar(64) NULL COMMENT "险种号",
  `main_pol_no` varchar(64) NULL COMMENT "主险保单险种号",
  `sale_chnl` varchar(16) NULL COMMENT "销售渠道编码",
  `sale_chnl_name` varchar(64) NULL COMMENT "销售渠道名称",
  `sell_type` varchar(16) NULL COMMENT "销售方式",
  `sell_type_name` varchar(64) NULL COMMENT "销售方式名称",
  `sale_com_code` varchar(64) NULL COMMENT "销售机构编码",
  `sale_com_name` varchar(256) NULL COMMENT "销售机构名称",
  `agent_com_code` varchar(64) NULL COMMENT "代理机构编码",
  `agent_com_name` varchar(256) NULL COMMENT "代理机构名称",
  `manage_com_code` varchar(64) NULL COMMENT "管理机构编码",
  `manage_com_name` varchar(256) NULL COMMENT "管理机构名称",
  `bank_branch_name` varchar(600) NULL COMMENT "管理机构名称",
  `sign_date` date NULL COMMENT "签单日期",
  `sign_time` varchar(24) NULL COMMENT "签单时间",
  `risk_vali_date` date NULL COMMENT "生效日期",
  `risk_end_date` date NULL COMMENT "保险终止日",
  `cont_make_date` date NULL COMMENT "保单创建日期",
  `cont_make_time` varchar(24) NULL COMMENT "保单创建时间",
  `cont_year` int(11) NULL COMMENT "保单年度",
  `cont_anniversary` date NULL COMMENT "保单周年日",
  `previous_cont_anniversary` date NULL COMMENT "上一保单周年日",
  `cont_app_flag` tinyint(4) NULL COMMENT "保单状态(1=有效,4=失效)",
  `risk_code` varchar(64) NULL COMMENT "险种编码",
  `risk_name` varchar(360) NULL COMMENT "险种名称",
  `risk_type3` varchar(16) NULL COMMENT "险种类别(1=传统险,2=分红险,3=投连险,4=万能险)",
  `plan_code` varchar(32) NULL COMMENT "保障计划",
  `pol_risk_type` varchar(8) NULL COMMENT "风险类型",
  `risk_app_flag` tinyint(4) NULL COMMENT "险种状态(1=有效,4=失效)",
  `sub_risk_flag` varchar(16) NULL COMMENT "主附险标识(M=主险,S=附加险)",
  `risk_period` varchar(16) NULL COMMENT "一年期险种标志(L=长险,M=一年期险,S=极短期险)",
  `insu_year` int(11) NULL COMMENT "保险期间",
  `insu_year_flag` varchar(16) NULL COMMENT "保险期间单位(Y=年, M=月, D=日, A=岁)",
  `pay_intv` tinyint(4) NULL COMMENT "缴费频率(0=趸交, 1=月交, 2=不定期交, 3=季交, 6=半年交, 12=年交)",
  `payend_year` tinyint(4) NULL COMMENT "缴费期间",
  `payend_year_flag` varchar(16) NULL COMMENT "缴费期间单位(Y=年, M=月, D=日, A=岁)",
  `pay_end_date` date NULL COMMENT "交费终止日",
  `pay_to_date` date NULL COMMENT "保费交至日期",
  `duty_code` varchar(128) NULL COMMENT "责任编码",
  `duty_name` varchar(512) NULL COMMENT "责任名称",
  `get_duty_code` varchar(128) NULL COMMENT "给付责任编码",
  `get_duty_name` varchar(512) NULL COMMENT "给付责任名称",
  `get_duty_state` varchar(30) NULL COMMENT "给付责任状态",
  `amount` decimal(16, 2) NULL COMMENT "保额",
  `sum_pay_money` decimal(16, 2) NULL DEFAULT "0" COMMENT "累交保费",
  `sum_add_money` decimal(16, 2) NULL DEFAULT "0" COMMENT "累计加费",
  `sum_get_money` decimal(16, 2) NULL DEFAULT "0" COMMENT "累计领取金额",
  `base_premium` decimal(16, 2) NULL COMMENT "基础保费",
  `add_premium` decimal(16, 2) NULL COMMENT "加费",
  `add_scale` decimal(16, 2) NULL COMMENT "加费评点",
  `risk_free_flag` tinyint(4) NULL DEFAULT "0" COMMENT "险种是否已豁免(0=未豁免,1=已豁免)",
  `appnt_no` varchar(64) NULL COMMENT "投保人客户号",
  `appnt_name` varchar(128) NULL COMMENT "投保人姓名",
  `appnt_id_type` varchar(64) NULL COMMENT "投保人证件类型",
  `appnt_id_no` varchar(64) NULL COMMENT "投保人证件号码",
  `appnt_sex` tinyint(4) NULL COMMENT "投保人性别(0=男,1=女)",
  `appnt_birthday` date NULL COMMENT "投保人出生日期",
  `appnt_occ_type` varchar(16) NULL COMMENT "投保人职业等级",
  `appnt_occ_code` varchar(24) NULL COMMENT "投保人职业代码",
  `insured_peoples` int(11) NULL COMMENT "被保险人数量",
  `main_insured_no` varchar(64) NULL COMMENT "险种主被保险人号",
  `insured_sequence_no` tinyint(4) NULL COMMENT "被保险人序号",
  `insured_app_age` tinyint(4) NULL COMMENT "被保险人投保年龄",
  `insured_no` varchar(64) NULL COMMENT "被保险人客户号",
  `insured_name` varchar(128) NULL COMMENT "被保险人姓名",
  `insured_id_type` varchar(64) NULL COMMENT "被保险人证件类型",
  `insured_id_no` varchar(64) NULL COMMENT "被保险人证件号码",
  `insured_sex` tinyint(4) NULL COMMENT "被保险人性别(0=男,1=女)",
  `insured_birthday` date NULL COMMENT "被保险人出生日期",
  `insured_occ_type` varchar(16) NULL COMMENT "被保险人职业等级",
  `insured_occ_code` varchar(24) NULL COMMENT "被保险人职业代码",
  `insured_pass_flag` tinyint(4) NULL COMMENT "被保险人健康状况(4=次标体, 9=标体)",
  `cedeout_type` tinyint(4) NULL COMMENT "是否临分(0=否,1=是)",
  `core_conclusion` varchar(24) NULL COMMENT "核心临分结论",
  `reinsu_add_scale` varchar(50) NULL COMMENT "再保加费评点",
  `cedeout_count` tinyint(4) NULL COMMENT "分出次数（冗余字段，无实际作用）",
  `insuacc_value` decimal(20, 4) NULL DEFAULT "0" COMMENT "账户价值",
  `cash_value` decimal(20, 4) NULL DEFAULT "0" COMMENT "现金价值",
  `remark` varchar(128) NULL COMMENT "备注",
  `is_del` tinyint(4) NULL DEFAULT "0" COMMENT "是否删除(0=未删除,1=已删除)",
  `create_by` varchar(64) NULL DEFAULT "system" COMMENT "创建人",
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "创建时间",
  `update_by` varchar(64) NULL DEFAULT "system" COMMENT "修改人",
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "修改时间"
) ENGINE=OLAP 
PRIMARY KEY(`id`)
DISTRIBUTED BY HASH(`id`) BUCKETS 16 
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "true",
"replicated_storage" = "true",
"compression" = "LZ4"
);