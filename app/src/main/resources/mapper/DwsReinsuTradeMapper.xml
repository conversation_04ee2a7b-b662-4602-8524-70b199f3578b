<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reinsurance.mapper.DwsReinsuTradeMapper">
    
    <resultMap type="DwsReinsuTradeEntity" id="DwsReinsuTradeResult">
        <result property="id"    column="id"    />
        <result property="batchNo"    column="batch_no"    />
        <result property="busiType"    column="busi_type"    />
        <result property="dataType"    column="data_type"    />
        <result property="contType"    column="cont_type"    />
        <result property="dataCopy"    column="data_copy"    />
        <result property="dataGroupNo"    column="data_group_no"    />
        <result property="busiOccurDate"    column="busi_occur_date"    />
        <result property="getDate"    column="get_date"    />
        <result property="getTime"    column="get_time"    />
        <result property="accountPeriod"    column="account_period"    />
        <result property="accountDate"    column="account_date"    />
        <result property="grpContNo"    column="grp_cont_no"    />
        <result property="grpPolNo"    column="grp_pol_no"    />
        <result property="contNo"    column="cont_no"    />
        <result property="polNo"    column="pol_no"    />
        <result property="mainPolNo"    column="main_pol_no"    />
        <result property="saleChnl"    column="sale_chnl"    />
        <result property="saleChnlName"    column="sale_chnl_name"    />
        <result property="sellType"    column="sell_type"    />
        <result property="sellTypeName"    column="sell_type_name"    />
        <result property="saleComCode"    column="sale_com_code"    />
        <result property="saleComName"    column="sale_com_name"    />
        <result property="agentComCode"    column="agent_com_code"    />
        <result property="agentComName"    column="agent_com_name"    />
        <result property="manageComCode"    column="manage_com_code"    />
        <result property="manageComName"    column="manage_com_name"    />
        <result property="contMakeDate"    column="cont_make_date"    />
        <result property="signDate"    column="sign_date"    />
        <result property="signTime"    column="sign_time"    />
        <result property="contYear"    column="cont_year"    />
        <result property="contMonth"    column="cont_month"    />
        <result property="contAnniversary"    column="cont_anniversary"    />
        <result property="previousContAnniversary"    column="previous_cont_anniversary"    />
        <result property="contAppFlag"    column="cont_app_flag"    />
        <result property="invalidStateType"    column="invalid_state_type"    />
        <result property="invalidStateReason"    column="invalid_state_reason"    />
        <result property="invalidStartDate"    column="invalid_start_date"    />
        <result property="riskCode"    column="risk_code"    />
        <result property="riskName"    column="risk_name"    />
        <result property="riskAppFlag"    column="risk_app_flag"    />
        <result property="riskValiDate"    column="risk_vali_date"    />
        <result property="riskEndDate"    column="risk_end_date"    />
        <result property="subRiskFlag"    column="sub_risk_flag"    />
        <result property="riskPeriod"    column="risk_period"    />
        <result property="riskType3"    column="risk_type3"    />
        <result property="planCode"    column="plan_code"    />
        <result property="polRiskType"    column="pol_risk_type"    />
        <result property="getDutyCodes"    column="get_duty_codes"    />
        <result property="getDutyNames"    column="get_duty_names"    />
        <result property="liabilityCode"    column="liability_code"    />
        <result property="liabilityName"    column="liability_name"    />
        <result property="insuYear"    column="insu_year"    />
        <result property="insuYearFlag"    column="insu_year_flag"    />
        <result property="payIntv"    column="pay_intv"    />
        <result property="payendYear"    column="payend_year"    />
        <result property="payendYearFlag"    column="payend_year_flag"    />
        <result property="riskFreeFlag"    column="risk_free_flag"    />
        <result property="payPeriods"    column="pay_periods"    />
        <result property="inPayPeriods"    column="in_pay_periods"    />
        <result property="unPayPeriods"    column="un_pay_periods"    />
        <result property="unPayPremium"    column="un_pay_premium"    />
        <result property="amount"    column="amount"    />
        <result property="availableAmount"    column="available_amount"    />
        <result property="totalPremium"    column="total_premium"    />
        <result property="basePremium"    column="base_premium"    />
        <result property="addPremium"    column="add_premium"    />
        <result property="addScale"    column="add_scale"    />
        <result property="payToDate"    column="pay_to_date"    />
        <result property="payEndDate"    column="pay_end_date"    />
        <result property="sumPayMoney"    column="sum_pay_money"    />
        <result property="sumAddMoney"    column="sum_add_money"    />
        <result property="sumGetMoney"    column="sum_get_money"    />
        <result property="cashValue"    column="cash_value"    />
        <result property="insuaccValue"    column="insuacc_value"    />
        <result property="appntNo"    column="appnt_no"    />
        <result property="appntName"    column="appnt_name"    />
        <result property="appntIdType"    column="appnt_id_type"    />
        <result property="appntIdNo"    column="appnt_id_no"    />
        <result property="appntSex"    column="appnt_sex"    />
        <result property="appntBirthday"    column="appnt_birthday"    />
        <result property="appntOccType"    column="appnt_occ_type"    />
        <result property="appntOccCode"    column="appnt_occ_code"    />
        <result property="insuredPeoples"    column="insured_peoples"    />
        <result property="insuredNo"    column="insured_no"    />
        <result property="insuredName"    column="insured_name"    />
        <result property="insuredIdType"    column="insured_id_type"    />
        <result property="insuredIdNo"    column="insured_id_no"    />
        <result property="insuredSex"    column="insured_sex"    />
        <result property="insuredBirthday"    column="insured_birthday"    />
        <result property="insuredOccType"    column="insured_occ_type"    />
        <result property="insuredOccCode"    column="insured_occ_code"    />
        <result property="insuredSocisec"    column="insured_socisec"    />
        <result property="insuredPassFlag"    column="insured_pass_flag"    />
        <result property="insuredAppAge"    column="insured_app_age"    />
        <result property="edorAcceptNo"    column="edor_accept_no"    />
        <result property="edorType"    column="edor_type"    />
        <result property="edorState"    column="edor_state"    />
        <result property="edorGetMoney"    column="edor_get_money"    />
        <result property="edorGetInterest"    column="edor_get_interest"    />
        <result property="edorAppDate"    column="edor_app_date"    />
        <result property="edorValidate"    column="edor_validate"    />
        <result property="edorConfDate"    column="edor_conf_date"    />
        <result property="edorConfTime"    column="edor_conf_time"    />
        <result property="edorMakeDate"    column="edor_make_date"    />
        <result property="clmNo"    column="clm_no"    />
        <result property="clmState"    column="clm_state"    />
        <result property="clmGiveType"    column="clm_give_type"    />
        <result property="clmStandpay"    column="clm_standpay"    />
        <result property="clmRealpay"    column="clm_realpay"    />
        <result property="clmAccidentDate"    column="clm_accident_date"    />
        <result property="clmAccDate"    column="clm_acc_date"    />
        <result property="clmRptDate"    column="clm_rpt_date"    />
        <result property="clmRgtDate"    column="clm_rgt_date"    />
        <result property="clmCaseEndDate"    column="clm_case_end_date"    />
        <result property="clmEnterAccDate"    column="clm_enter_acc_date"    />
        <result property="clmFeeSum"    column="clm_fee_sum"    />
        <result property="clmFeeType"    column="clm_fee_type"    />
        <result property="clmBalTypeDesc"    column="clm_bal_type_desc"    />
        <result property="clmSubFeeType"    column="clm_sub_fee_type"    />
        <result property="clmSubBalTypeDesc"    column="clm_sub_bal_type_desc"    />
        <result property="clmDefoGrade"    column="clm_defo_grade"    />
        <result property="clmDefoGradeName"    column="clm_defo_grade_name"    />
        <result property="clmDefoType"    column="clm_defo_type"    />
        <result property="clmDefoName"    column="clm_defo_name"    />
        <result property="clmHospitalName"    column="clm_hospital_name"    />
        <result property="clmInHospitalDate"    column="clm_in_hospital_date"    />
        <result property="clmOutHospitalDate"    column="clm_out_hospital_date"    />
        <result property="clmAccidentReason"    column="clm_accident_reason"    />
        <result property="clmAccresult1"    column="clm_accresult_1"    />
        <result property="clmAccresult2"    column="clm_accresult_2"    />
        <result property="clmAccresult1Name"    column="clm_accresult_1_name"    />
        <result property="clmAccresult2Name"    column="clm_accresult_2_name"    />
        <result property="clmMakeDate"    column="clm_make_date"    />
        <result property="rsPayIntv"    column="rs_pay_intv"    />
        <result property="calcStatus"    column="calc_status"    />
        <result property="calcType"    column="calc_type"    />
        <result property="calcFailCode"    column="calc_fail_code"    />
        <result property="cedeoutType"    column="cedeout_type"    />
        <result property="facultativeWay"    column="facultative_way"    />
        <result property="reNoticeUrl"    column="re_notice_url"    />
        <result property="cedeoutCount"    column="cedeout_count"    />
        <result property="companyCode"    column="company_code"    />
        <result property="companyName"    column="company_name"    />
        <result property="contractCode"    column="contract_code"    />
        <result property="contractName"    column="contract_name"    />
        <result property="mainContractCode"    column="main_contract_code"    />
        <result property="mainContractName"    column="main_contract_name"    />
        <result property="programmeCode"    column="programme_code"    />
        <result property="programmeName"    column="programme_name"    />
        <result property="programmeSelfAmount"    column="programme_self_amount"    />
        <result property="programmeSelfScale"    column="programme_self_scale"    />
        <result property="cedeoutMode"    column="cedeout_mode"    />
        <result property="cedeoutWay"    column="cedeout_way"    />
        <result property="addupAmountType"    column="addup_amount_type"    />
        <result property="addupRiskCode"    column="addup_risk_code"    />
        <result property="addupRiskName"    column="addup_risk_name"    />
        <result property="initRiskAmount"    column="init_risk_amount"    />
        <result property="occupyRiskAmount"    column="occupy_risk_amount"    />
        <result property="releaseRiskAmount"    column="release_risk_amount"    />
        <result property="cedeoutAmount"    column="cedeout_amount"    />
        <result property="selfAmount"    column="self_amount"    />
        <result property="acceptCopies"    column="accept_copies"    />
        <result property="cedeoutScale"    column="cedeout_scale"    />
        <result property="selfScale"    column="self_scale"    />
        <result property="cedeoutPremium"    column="cedeout_premium"    />
        <result property="cedeoutAddPremium"    column="cedeout_add_premium"    />
        <result property="cedeoutTotalPremium"    column="cedeout_total_premium"    />
        <result property="cedeoutCommission"    column="cedeout_commission"    />
        <result property="taxRate"    column="tax_rate"    />
        <result property="addedTax"    column="added_tax"    />
        <result property="rateCode"    column="rate_code"    />
        <result property="rateDataId"    column="rate_data_id"    />
        <result property="rateDataValue"    column="rate_data_value"    />
        <result property="cedeoutRateDataValue"    column="cedeout_rate_data_value"    />
        <result property="comRateCode"    column="com_rate_code"    />
        <result property="comRateDataId"    column="com_rate_data_id"    />
        <result property="comRateDataValue"    column="com_rate_data_value"    />
        <result property="disRateCode"    column="dis_rate_code"    />
        <result property="disRateDataId"    column="dis_rate_data_id"    />
        <result property="disRateDataValue"    column="dis_rate_data_value"    />
        <result property="reservesType"    column="reserves_type"    />
        <result property="reservesId"    column="reserves_id"    />
        <result property="reserves"    column="reserves"    />
        <result property="returnStatus"    column="return_status"    />
        <result property="returnDate"    column="return_date"    />
        <result property="returnReason"    column="return_reason"    />
        <result property="srcOutTradeId"    column="src_out_trade_id"    />
        <result property="returnPremium"    column="return_premium"    />
        <result property="returnCbPremium"    column="return_cb_premium"    />
        <result property="returnTotalPremium"    column="return_total_premium"    />
        <result property="returnClaimAmount"    column="return_claim_amount"    />
        <result property="returnExpiredGold"    column="return_expired_gold"    />
        <result property="returnCommission"    column="return_commission"    />
        <result property="adjustStatus"    column="adjust_status"    />
        <result property="adjustDate"    column="adjust_date"    />
        <result property="adjuster"    column="adjuster"    />
        <result property="adjustReason"    column="adjust_reason"    />
        <result property="adjustBatchNo"    column="adjust_batch_no"    />
        <result property="billConfirmStatus"    column="bill_confirm_status"    />
        <result property="billConfirmDate"    column="bill_confirm_date"    />
        <result property="billConfirmer"    column="bill_confirmer"    />
        <result property="billNo"    column="bill_no"    />
        <result property="status"    column="status"    />
        <result property="backTrackData"    column="back_track_data"    />
        <result property="remark"    column="remark"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDwsReinsuTradeVo">
        select id, batch_no, busi_type, data_type, cont_type, data_copy, data_group_no, busi_occur_date, get_date, get_time, account_period, account_date, grp_cont_no, grp_pol_no, cont_no, pol_no, main_pol_no, sale_chnl, sale_chnl_name, sell_type, sell_type_name, sale_com_code, sale_com_name, agent_com_code, agent_com_name, manage_com_code, manage_com_name, cont_make_date, sign_date, sign_time, cont_year, cont_month, cont_anniversary, previous_cont_anniversary, cont_app_flag, invalid_state_type, invalid_state_reason, invalid_start_date, risk_code, risk_name, risk_app_flag, risk_vali_date, risk_end_date, sub_risk_flag, risk_period, risk_type3, plan_code, pol_risk_type, get_duty_codes, get_duty_names, liability_code, liability_name, insu_year, insu_year_flag, pay_intv, payend_year, payend_year_flag, risk_free_flag, pay_periods, in_pay_periods, un_pay_periods, un_pay_premium, amount, available_amount, total_premium, base_premium, add_premium, add_scale, pay_to_date, pay_end_date, sum_pay_money, sum_add_money, sum_get_money, cash_value, insuacc_value, appnt_no, appnt_name, appnt_id_type, appnt_id_no, appnt_sex, appnt_birthday, appnt_occ_type, appnt_occ_code, insured_peoples, insured_no, insured_name, insured_id_type, insured_id_no, insured_sex, insured_birthday, insured_occ_type, insured_occ_code, insured_socisec, insured_pass_flag, insured_app_age, edor_accept_no, edor_type, edor_state, edor_get_money, edor_get_interest, edor_app_date, edor_validate, edor_conf_date, edor_conf_time, edor_make_date, clm_no, clm_state, clm_give_type, clm_standpay, clm_realpay, clm_accident_date, clm_acc_date, clm_rpt_date, clm_rgt_date, clm_case_end_date, clm_enter_acc_date, clm_fee_sum, clm_fee_type, clm_bal_type_desc, clm_sub_fee_type, clm_sub_bal_type_desc, clm_defo_grade, clm_defo_grade_name, clm_defo_type, clm_defo_name, clm_hospital_name, clm_in_hospital_date, clm_out_hospital_date, clm_accident_reason, clm_accresult_1, clm_accresult_2, clm_accresult_1_name, clm_accresult_2_name, clm_make_date, rs_pay_intv, calc_status, calc_type, calc_fail_code, cedeout_type, facultative_way, re_notice_url, cedeout_count, company_code, company_name, contract_code, contract_name, main_contract_code, main_contract_name, programme_code, programme_name, programme_self_amount, programme_self_scale, cedeout_mode, cedeout_way, addup_amount_type, addup_risk_code, addup_risk_name, init_risk_amount, occupy_risk_amount, release_risk_amount, cedeout_amount, self_amount, accept_copies, cedeout_scale, self_scale, cedeout_premium, cedeout_add_premium, cedeout_total_premium, cedeout_commission, tax_rate, added_tax, rate_code, rate_data_id, rate_data_value, cedeout_rate_data_value, com_rate_code, com_rate_data_id, com_rate_data_value, dis_rate_code, dis_rate_data_id, dis_rate_data_value, reserves_type, reserves_id, reserves, return_status, return_date, return_reason, src_out_trade_id, return_premium, return_cb_premium, return_total_premium, return_claim_amount, return_expired_gold, return_commission, adjust_status, adjust_date, adjuster, adjust_reason, adjust_batch_no, bill_confirm_status, bill_confirm_date, bill_confirmer, bill_no, status, back_track_data, remark, is_del, create_by, create_time, update_by, update_time from t_dws_reinsu_trade
    </sql>

    <select id="selectInsuredList" parameterType="DwsReinsuTradeQuery" resultType="com.reinsurance.dto.AnomalousDataDTO">
        <include refid="selectDwsReinsuTradeVo"/>
        <where>
            <if test="batchNo != null  and batchNo != ''"> and batch_no = #{batchNo}</if>
            <if test="busiType != null "> and busi_type = #{busiType}</if>
            <if test="dataType != null "> and data_type = #{dataType}</if>
            <if test="contType != null "> and cont_type = #{contType}</if>
            <if test="dataCopy != null "> and data_copy = #{dataCopy}</if>
            <if test="busiOccurDate != null "> and busi_occur_date = #{busiOccurDate}</if>
            <if test="getDate != null "> and get_date = #{getDate}</if>
            <if test="getTime != null  and getTime != ''"> and get_time = #{getTime}</if>
            <if test="accountPeriod != null  and accountPeriod != ''"> and account_period = #{accountPeriod}</if>
            <if test="accountDate != null "> and account_date = #{accountDate}</if>
            <if test="grpContNo != null  and grpContNo != ''"> and grp_cont_no = #{grpContNo}</if>
            <if test="grpPolNo != null  and grpPolNo != ''"> and grp_pol_no = #{grpPolNo}</if>
            <if test="contNo != null  and contNo != ''"> and cont_no = #{contNo}</if>
            <if test="polNo != null  and polNo != ''"> and pol_no = #{polNo}</if>
            <if test="mainPolNo != null  and mainPolNo != ''"> and main_pol_no = #{mainPolNo}</if>
            <if test="saleChnl != null  and saleChnl != ''"> and sale_chnl = #{saleChnl}</if>
            <if test="saleChnlName != null  and saleChnlName != ''"> and sale_chnl_name = #{saleChnlName}</if>
            <if test="sellType != null  and sellType != ''"> and sell_type = #{sellType}</if>
            <if test="sellTypeName != null  and sellTypeName != ''"> and sell_type_name = #{sellTypeName}</if>
            <if test="saleComCode != null  and saleComCode != ''"> and sale_com_code = #{saleComCode}</if>
            <if test="saleComName != null  and saleComName != ''"> and sale_com_name like concat('%', #{saleComName}, '%')</if>
            <if test="agentComCode != null  and agentComCode != ''"> and agent_com_code = #{agentComCode}</if>
            <if test="agentComName != null  and agentComName != ''"> and agent_com_name like concat('%', #{agentComName}, '%')</if>
            <if test="manageComCode != null  and manageComCode != ''"> and manage_com_code = #{manageComCode}</if>
            <if test="manageComName != null  and manageComName != ''"> and manage_com_name like concat('%', #{manageComName}, '%')</if>
            <if test="contMakeDate != null "> and cont_make_date = #{contMakeDate}</if>
            <if test="signDate != null "> and sign_date = #{signDate}</if>
            <if test="signTime != null  and signTime != ''"> and sign_time = #{signTime}</if>
            <if test="contYear != null "> and cont_year = #{contYear}</if>
            <if test="contAnniversary != null "> and cont_anniversary = #{contAnniversary}</if>
            <if test="previousContAnniversary != null "> and previous_cont_anniversary = #{previousContAnniversary}</if>
            <if test="contAppFlag != null "> and cont_app_flag = #{contAppFlag}</if>
            <if test="invalidStateType != null  and invalidStateType != ''"> and invalid_state_type = #{invalidStateType}</if>
            <if test="invalidStateReason != null  and invalidStateReason != ''"> and invalid_state_reason = #{invalidStateReason}</if>
            <if test="invalidStartDate != null "> and invalid_start_date = #{invalidStartDate}</if>
            <if test="riskCode != null  and riskCode != ''"> and risk_code = #{riskCode}</if>
            <if test="riskName != null  and riskName != ''"> and risk_name like concat('%', #{riskName}, '%')</if>
            <if test="riskAppFlag != null "> and risk_app_flag = #{riskAppFlag}</if>
            <if test="riskValiDate != null "> and risk_vali_date = #{riskValiDate}</if>
            <if test="riskEndDate != null "> and risk_end_date = #{riskEndDate}</if>
            <if test="subRiskFlag != null  and subRiskFlag != ''"> and sub_risk_flag = #{subRiskFlag}</if>
            <if test="riskPeriod != null  and riskPeriod != ''"> and risk_period = #{riskPeriod}</if>
            <if test="riskType3 != null  and riskType3 != ''"> and risk_type3 = #{riskType3}</if>
            <if test="planCode != null  and planCode != ''"> and plan_code = #{planCode}</if>
            <if test="polRiskType != null and polRiskType != ''"> and pol_risk_type = #{polRiskType}</if>
            <if test="getDutyCodes != null  and getDutyCodes != ''"> and get_duty_codes = #{getDutyCodes}</if>
            <if test="getDutyNames != null  and getDutyNames != ''"> and get_duty_names = #{getDutyNames}</if>
            <if test="liabilityCode != null  and liabilityCode != ''"> and liability_code = #{liabilityCode}</if>
            <if test="liabilityName != null  and liabilityName != ''"> and liability_name like concat('%', #{liabilityName}, '%')</if>
            <if test="insuYear != null "> and insu_year = #{insuYear}</if>
            <if test="insuYearFlag != null  and insuYearFlag != ''"> and insu_year_flag = #{insuYearFlag}</if>
            <if test="payIntv != null "> and pay_intv = #{payIntv}</if>
            <if test="payendYear != null "> and payend_year = #{payendYear}</if>
            <if test="payendYearFlag != null  and payendYearFlag != ''"> and payend_year_flag = #{payendYearFlag}</if>
            <if test="riskFreeFlag != null "> and risk_free_flag = #{riskFreeFlag}</if>
            <if test="payPeriods != null "> and pay_periods = #{payPeriods}</if>
            <if test="inPayPeriods != null "> and in_pay_periods = #{inPayPeriods}</if>
            <if test="unPayPeriods != null "> and un_pay_periods = #{unPayPeriods}</if>
            <if test="unPayPremium != null "> and un_pay_premium = #{unPayPremium}</if>
            <if test="amount != null "> and amount = #{amount}</if>
            <if test="availableAmount != null "> and available_amount = #{availableAmount}</if>
            <if test="totalPremium != null "> and total_premium = #{totalPremium}</if>
            <if test="basePremium != null "> and base_premium = #{basePremium}</if>
            <if test="addPremium != null "> and add_premium = #{addPremium}</if>
            <if test="addScale != null "> and add_scale = #{addScale}</if>
            <if test="payToDate != null "> and pay_to_date = #{payToDate}</if>
            <if test="payEndDate != null "> and pay_end_date = #{payEndDate}</if>
            <if test="sumPayMoney != null "> and sum_pay_money = #{sumPayMoney}</if>
            <if test="sumAddMoney != null "> and sum_add_money = #{sumAddMoney}</if>
            <if test="sumGetMoney != null "> and sum_get_money = #{sumGetMoney}</if>
            <if test="cashValue != null "> and cash_value = #{cashValue}</if>
            <if test="insuaccValue != null "> and insuacc_value = #{insuaccValue}</if>
            <if test="appntNo != null  and appntNo != ''"> and appnt_no = #{appntNo}</if>
            <if test="appntName != null  and appntName != ''"> and appnt_name like concat('%', #{appntName}, '%')</if>
            <if test="appntIdType != null  and appntIdType != ''"> and appnt_id_type = #{appntIdType}</if>
            <if test="appntIdNo != null  and appntIdNo != ''"> and appnt_id_no = #{appntIdNo}</if>
            <if test="appntSex != null "> and appnt_sex = #{appntSex}</if>
            <if test="appntBirthday != null "> and appnt_birthday = #{appntBirthday}</if>
            <if test="appntOccType != null  and appntOccType != ''"> and appnt_occ_type = #{appntOccType}</if>
            <if test="appntOccCode != null  and appntOccCode != ''"> and appnt_occ_code = #{appntOccCode}</if>
            <if test="insuredPeoples != null "> and insured_peoples = #{insuredPeoples}</if>
            <if test="insuredNo != null  and insuredNo != ''"> and insured_no = #{insuredNo}</if>
            <if test="insuredName != null  and insuredName != ''"> and insured_name like concat('%', #{insuredName}, '%')</if>
            <if test="insuredIdType != null  and insuredIdType != ''"> and insured_id_type = #{insuredIdType}</if>
            <if test="insuredIdNo != null  and insuredIdNo != ''"> and insured_id_no = #{insuredIdNo}</if>
            <if test="insuredSex != null "> and insured_sex = #{insuredSex}</if>
            <if test="insuredBirthday != null "> and insured_birthday = #{insuredBirthday}</if>
            <if test="insuredOccType != null  and insuredOccType != ''"> and insured_occ_type = #{insuredOccType}</if>
            <if test="insuredOccCode != null  and insuredOccCode != ''"> and insured_occ_code = #{insuredOccCode}</if>
            <if test="insuredSocisec != null "> and insured_socisec = #{insuredSocisec}</if>
            <if test="insuredPassFlag != null "> and insured_pass_flag = #{insuredPassFlag}</if>
            <if test="insuredAppAge != null "> and insured_app_age = #{insuredAppAge}</if>
            <if test="edorAcceptNo != null  and edorAcceptNo != ''"> and edor_accept_no = #{edorAcceptNo}</if>
            <if test="edorType != null  and edorType != ''"> and edor_type = #{edorType}</if>
            <if test="edorState != null "> and edor_state = #{edorState}</if>
            <if test="edorGetMoney != null "> and edor_get_money = #{edorGetMoney}</if>
            <if test="edorGetInterest != null "> and edor_get_interest = #{edorGetInterest}</if>
            <if test="edorAppDate != null "> and edor_app_date = #{edorAppDate}</if>
            <if test="edorValidate != null "> and edor_validate = #{edorValidate}</if>
            <if test="edorConfDate != null "> and edor_conf_date = #{edorConfDate}</if>
            <if test="edorConfTime != null  and edorConfTime != ''"> and edor_conf_time = #{edorConfTime}</if>
            <if test="edorMakeDate != null "> and edor_make_date = #{edorMakeDate}</if>
            <if test="clmNo != null  and clmNo != ''"> and clm_no = #{clmNo}</if>
            <if test="clmState != null  and clmState != ''"> and clm_state = #{clmState}</if>
            <if test="clmGiveType != null  and clmGiveType != ''"> and clm_give_type = #{clmGiveType}</if>
            <if test="clmStandpay != null "> and clm_standpay = #{clmStandpay}</if>
            <if test="clmRealpay != null "> and clm_realpay = #{clmRealpay}</if>
            <if test="clmAccidentDate != null "> and clm_accident_date = #{clmAccidentDate}</if>
            <if test="clmAccDate != null "> and clm_acc_date = #{clmAccDate}</if>
            <if test="clmRptDate != null "> and clm_rpt_date = #{clmRptDate}</if>
            <if test="clmRgtDate != null "> and clm_rgt_date = #{clmRgtDate}</if>
            <if test="clmCaseEndDate != null "> and clm_case_end_date = #{clmCaseEndDate}</if>
            <if test="clmEnterAccDate != null "> and clm_enter_acc_date = #{clmEnterAccDate}</if>
            <if test="clmFeeSum != null "> and clm_fee_sum = #{clmFeeSum}</if>
            <if test="clmFeeType != null  and clmFeeType != ''"> and clm_fee_type = #{clmFeeType}</if>
            <if test="clmBalTypeDesc != null  and clmBalTypeDesc != ''"> and clm_bal_type_desc = #{clmBalTypeDesc}</if>
            <if test="clmSubFeeType != null  and clmSubFeeType != ''"> and clm_sub_fee_type = #{clmSubFeeType}</if>
            <if test="clmSubBalTypeDesc != null  and clmSubBalTypeDesc != ''"> and clm_sub_bal_type_desc = #{clmSubBalTypeDesc}</if>
            <if test="clmDefoGrade != null  and clmDefoGrade != ''"> and clm_defo_grade = #{clmDefoGrade}</if>
            <if test="clmDefoGradeName != null  and clmDefoGradeName != ''"> and clm_defo_grade_name like concat('%', #{clmDefoGradeName}, '%')</if>
            <if test="clmDefoType != null  and clmDefoType != ''"> and clm_defo_type = #{clmDefoType}</if>
            <if test="clmDefoName != null  and clmDefoName != ''"> and clm_defo_name like concat('%', #{clmDefoName}, '%')</if>
            <if test="clmHospitalName != null  and clmHospitalName != ''"> and clm_hospital_name like concat('%', #{clmHospitalName}, '%')</if>
            <if test="clmInHospitalDate != null "> and clm_in_hospital_date = #{clmInHospitalDate}</if>
            <if test="clmOutHospitalDate != null "> and clm_out_hospital_date = #{clmOutHospitalDate}</if>
            <if test="clmAccidentReason != null  and clmAccidentReason != ''"> and clm_accident_reason = #{clmAccidentReason}</if>
            <if test="clmAccresult1 != null  and clmAccresult1 != ''"> and clm_accresult_1 = #{clmAccresult1}</if>
            <if test="clmAccresult2 != null  and clmAccresult2 != ''"> and clm_accresult_2 = #{clmAccresult2}</if>
            <if test="clmAccresult1Name != null  and clmAccresult1Name != ''"> and clm_accresult_1_name = #{clmAccresult1Name}</if>
            <if test="clmAccresult2Name != null  and clmAccresult2Name != ''"> and clm_accresult_2_name = #{clmAccresult2Name}</if>
            <if test="clmMakeDate != null "> and clm_make_date = #{clmMakeDate}</if>
            <if test="rsPayIntv != null "> and rs_pay_intv = #{rsPayIntv}</if>
            <if test="calcStatus != null "> and calc_status = #{calcStatus}</if>
            <if test="calcType != null "> and calc_type = #{calcType}</if>
            <if test="calcFailCode != null "> and calc_fail_code = #{calcFailCode}</if>
            <if test="cedeoutType != null "> and cedeout_type = #{cedeoutType}</if>
            <if test="reNoticeUrl != null  and reNoticeUrl != ''"> and re_notice_url = #{reNoticeUrl}</if>
            <if test="cedeoutCount != null "> and cedeout_count = #{cedeoutCount}</if>
            <if test="companyCode != null  and companyCode != ''"> and company_code = #{companyCode}</if>
            <if test="companyName != null  and companyName != ''"> and company_name like concat('%', #{companyName}, '%')</if>
            <if test="contractCode != null  and contractCode != ''"> and contract_code = #{contractCode}</if>
            <if test="contractName != null  and contractName != ''"> and contract_name = #{contractName}</if>
            <if test="mainContractCode != null  and mainContractCode != ''"> and main_contract_code = #{mainContractCode}</if>
            <if test="mainContractName != null  and mainContractName != ''"> and main_contract_name = #{mainContractName}</if>
            <if test="programmeCode != null  and programmeCode != ''"> and programme_code = #{programmeCode}</if>
            <if test="programmeName != null  and programmeName != ''"> and programme_name like concat('%', #{programmeName}, '%')</if>
            <if test="programmeSelfAmount != null "> and programme_self_amount = #{programmeSelfAmount}</if>
            <if test="programmeSelfScale != null "> and programme_self_scale = #{programmeSelfScale}</if>
            <if test="cedeoutMode != null "> and cedeout_mode = #{cedeoutMode}</if>
            <if test="cedeoutWay != null "> and cedeout_way = #{cedeoutWay}</if>
            <if test="addupAmountType != null "> and addup_amount_type = #{addupAmountType}</if>
            <if test="addupRiskCode != null  and addupRiskCode != ''"> and addup_risk_code = #{addupRiskCode}</if>
            <if test="addupRiskName != null  and addupRiskName != ''"> and addup_risk_name = #{addupRiskName}</if>
            <if test="initRiskAmount != null "> and init_risk_amount = #{initRiskAmount}</if>
            <if test="occupyRiskAmount != null "> and occupy_risk_amount = #{occupyRiskAmount}</if>
            <if test="releaseRiskAmount != null "> and release_risk_amount = #{releaseRiskAmount}</if>
            <if test="cedeoutAmount != null "> and cedeout_amount = #{cedeoutAmount}</if>
            <if test="selfAmount != null "> and self_amount = #{selfAmount}</if>
            <if test="acceptCopies != null "> and accept_copies = #{acceptCopies}</if>
            <if test="cedeoutScale != null "> and cedeout_scale = #{cedeoutScale}</if>
            <if test="selfScale != null "> and self_scale = #{selfScale}</if>
            <if test="cedeoutPremium != null "> and cedeout_premium = #{cedeoutPremium}</if>
            <if test="cedeoutAddPremium != null "> and cedeout_add_premium = #{cedeoutAddPremium}</if>
            <if test="cedeoutCommission != null "> and cedeout_commission = #{cedeoutCommission}</if>
            <if test="taxRate != null "> and tax_rate = #{taxRate}</if>
            <if test="addedTax != null "> and added_tax = #{addedTax}</if>
            <if test="rateCode != null  and rateCode != ''"> and rate_code = #{rateCode}</if>
            <if test="rateDataId != null "> and rate_data_id = #{rateDataId}</if>
            <if test="rateDataValue != null "> and rate_data_value = #{rateDataValue}</if>
            <if test="cedeoutRateDataValue != null "> and cedeout_rate_data_value = #{cedeoutRateDataValue}</if>
            <if test="comRateCode != null  and comRateCode != ''"> and com_rate_code = #{comRateCode}</if>
            <if test="comRateDataId != null "> and com_rate_data_id = #{comRateDataId}</if>
            <if test="comRateDataValue != null "> and com_rate_data_value = #{comRateDataValue}</if>
            <if test="disRateCode != null  and disRateCode != ''"> and dis_rate_code = #{disRateCode}</if>
            <if test="disRateDataId != null "> and dis_rate_data_id = #{disRateDataId}</if>
            <if test="disRateDataValue != null "> and dis_rate_data_value = #{disRateDataValue}</if>
            <if test="reservesType != null"> and reserves_type = #{reservesType}</if>
            <if test="reservesId != null "> and reserves_id = #{reservesId}</if>
            <if test="reserves != null "> and reserves = #{reserves}</if>
            <if test="returnStatus != null "> and return_status = #{returnStatus}</if>
            <if test="returnDate != null "> and return_date = #{returnDate}</if>
            <if test="returnReason != null "> and return_reason = #{returnReason}</if>
            <if test="srcOutTradeId != null "> and src_out_trade_id = #{srcOutTradeId}</if>
            <if test="returnPremium != null "> and return_premium = #{returnPremium}</if>
            <if test="returnCbPremium != null "> and return_cb_premium = #{returnCbPremium}</if>
            <if test="returnTotalPremium != null "> and return_total_premium = #{returnTotalPremium}</if>
            <if test="returnClaimAmount != null "> and return_claim_amount = #{returnClaimAmount}</if>
            <if test="returnExpiredGold != null "> and return_expired_gold = #{returnExpiredGold}</if>
            <if test="returnCommission != null "> and return_commission = #{returnCommission}</if>
            <if test="adjustStatus != null "> and adjust_status = #{adjustStatus}</if>
            <if test="adjustDate != null "> and adjust_date = #{adjustDate}</if>
            <if test="adjuster != null  and adjuster != ''"> and adjuster = #{adjuster}</if>
            <if test="adjustReason != null  and adjustReason != ''"> and adjust_reason = #{adjustReason}</if>
            <if test="adjustBatchNo != null  and adjustBatchNo != ''"> and adjust_batch_no = #{adjustBatchNo}</if>
            <if test="billNo != null and billNo != ''"> and bill_no = #{billNo}</if>
            <if test="billConfirmStatus != null "> and bill_confirm_status = #{billConfirmStatus}</if>
            <if test="billConfirmDate != null "> and bill_confirm_date = #{billConfirmDate}</if>
            <if test="billConfirmer != null  and billConfirmer != ''"> and bill_confirmer = #{billConfirmStatus}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="backTrackData != null "> and back_track_data = #{backTrackData}</if>
        </where>
        and is_del = 0 order by id desc
    </select>

    <select id="selectDwsReinsuTradeList" parameterType="DwsReinsuTradeQuery" resultMap="DwsReinsuTradeResult">
        <include refid="selectDwsReinsuTradeVo"/>
        <where>  
            <if test="batchNo != null  and batchNo != ''"> and batch_no = #{batchNo}</if>
            <if test="busiType != null "> and busi_type = #{busiType}</if>
            <if test="dataType != null "> and data_type = #{dataType}</if>
            <if test="contType != null "> and cont_type = #{contType}</if>
            <if test="dataCopy != null "> and data_copy = #{dataCopy}</if>
            <if test="dataGroupNo != null "> and data_group_no = #{dataGroupNo}</if>
            <if test="busiOccurDate != null "> and busi_occur_date = #{busiOccurDate}</if>
            <if test="getDate != null "> and get_date = #{getDate}</if>
            <if test="getTime != null  and getTime != ''"> and get_time = #{getTime}</if>
            <if test="accountPeriod != null  and accountPeriod != ''"> and account_period = #{accountPeriod}</if>
            <if test="accountDate != null "> and account_date = #{accountDate}</if>
            <if test="grpContNo != null  and grpContNo != ''"> and grp_cont_no = #{grpContNo}</if>
            <if test="grpPolNo != null  and grpPolNo != ''"> and grp_pol_no = #{grpPolNo}</if>
            <if test="contNo != null  and contNo != ''"> and cont_no = #{contNo}</if>
            <if test="polNo != null  and polNo != ''"> and pol_no = #{polNo}</if>
            <if test="mainPolNo != null  and mainPolNo != ''"> and main_pol_no = #{mainPolNo}</if>
            <if test="saleChnl != null  and saleChnl != ''"> and sale_chnl = #{saleChnl}</if>
            <if test="saleChnlName != null  and saleChnlName != ''"> and sale_chnl_name = #{saleChnlName}</if>
            <if test="sellType != null  and sellType != ''"> and sell_type = #{sellType}</if>
            <if test="sellTypeName != null  and sellTypeName != ''"> and sell_type_name = #{sellTypeName}</if>
            <if test="saleComCode != null  and saleComCode != ''"> and sale_com_code = #{saleComCode}</if>
            <if test="saleComName != null  and saleComName != ''"> and sale_com_name like concat('%', #{saleComName}, '%')</if>
            <if test="agentComCode != null  and agentComCode != ''"> and agent_com_code = #{agentComCode}</if>
            <if test="agentComName != null  and agentComName != ''"> and agent_com_name like concat('%', #{agentComName}, '%')</if>
            <if test="manageComCode != null  and manageComCode != ''"> and manage_com_code = #{manageComCode}</if>
            <if test="manageComName != null  and manageComName != ''"> and manage_com_name like concat('%', #{manageComName}, '%')</if>
            <if test="contMakeDate != null "> and cont_make_date = #{contMakeDate}</if>
            <if test="signDate != null "> and sign_date = #{signDate}</if>
            <if test="signTime != null  and signTime != ''"> and sign_time = #{signTime}</if>
            <if test="contYear != null "> and cont_year = #{contYear}</if>
            <if test="contAnniversary != null "> and cont_anniversary = #{contAnniversary}</if>
            <if test="previousContAnniversary != null "> and previous_cont_anniversary = #{previousContAnniversary}</if>
            <if test="contAppFlag != null "> and cont_app_flag = #{contAppFlag}</if>
            <if test="invalidStateType != null  and invalidStateType != ''"> and invalid_state_type = #{invalidStateType}</if>
            <if test="invalidStateReason != null  and invalidStateReason != ''"> and invalid_state_reason = #{invalidStateReason}</if>
            <if test="invalidStartDate != null "> and invalid_start_date = #{invalidStartDate}</if>
            <if test="riskCode != null  and riskCode != ''"> and risk_code = #{riskCode}</if>
            <if test="riskName != null  and riskName != ''"> and risk_name like concat('%', #{riskName}, '%')</if>
            <if test="riskAppFlag != null "> and risk_app_flag = #{riskAppFlag}</if>
            <if test="riskValiDate != null "> and risk_vali_date = #{riskValiDate}</if>
            <if test="riskEndDate != null "> and risk_end_date = #{riskEndDate}</if>
            <if test="subRiskFlag != null  and subRiskFlag != ''"> and sub_risk_flag = #{subRiskFlag}</if>
            <if test="riskPeriod != null  and riskPeriod != ''"> and risk_period = #{riskPeriod}</if>
            <if test="riskType3 != null  and riskType3 != ''"> and risk_type3 = #{riskType3}</if>
            <if test="planCode != null  and planCode != ''"> and plan_code = #{planCode}</if>
            <if test="polRiskType != null and polRiskType != ''"> and pol_risk_type = #{polRiskType}</if>
            <if test="getDutyCodes != null  and getDutyCodes != ''"> and get_duty_codes = #{getDutyCodes}</if>
            <if test="getDutyNames != null  and getDutyNames != ''"> and get_duty_names = #{getDutyNames}</if>
            <if test="liabilityCode != null  and liabilityCode != ''"> and liability_code = #{liabilityCode}</if>
            <if test="liabilityName != null  and liabilityName != ''"> and liability_name like concat('%', #{liabilityName}, '%')</if>
            <if test="insuYear != null "> and insu_year = #{insuYear}</if>
            <if test="insuYearFlag != null  and insuYearFlag != ''"> and insu_year_flag = #{insuYearFlag}</if>
            <if test="payIntv != null "> and pay_intv = #{payIntv}</if>
            <if test="payendYear != null "> and payend_year = #{payendYear}</if>
            <if test="payendYearFlag != null  and payendYearFlag != ''"> and payend_year_flag = #{payendYearFlag}</if>
            <if test="riskFreeFlag != null "> and risk_free_flag = #{riskFreeFlag}</if>
            <if test="payPeriods != null "> and pay_periods = #{payPeriods}</if>
            <if test="inPayPeriods != null "> and in_pay_periods = #{inPayPeriods}</if>
            <if test="unPayPeriods != null "> and un_pay_periods = #{unPayPeriods}</if>
            <if test="unPayPremium != null "> and un_pay_premium = #{unPayPremium}</if>
            <if test="amount != null "> and amount = #{amount}</if>
            <if test="availableAmount != null "> and available_amount = #{availableAmount}</if>
            <if test="totalPremium != null "> and total_premium = #{totalPremium}</if>
            <if test="basePremium != null "> and base_premium = #{basePremium}</if>
            <if test="addPremium != null "> and add_premium = #{addPremium}</if>
            <if test="addScale != null "> and add_scale = #{addScale}</if>
            <if test="payToDate != null "> and pay_to_date = #{payToDate}</if>
            <if test="payEndDate != null "> and pay_end_date = #{payEndDate}</if>
            <if test="sumPayMoney != null "> and sum_pay_money = #{sumPayMoney}</if>
            <if test="sumAddMoney != null "> and sum_add_money = #{sumAddMoney}</if>
            <if test="sumGetMoney != null "> and sum_get_money = #{sumGetMoney}</if>
            <if test="cashValue != null "> and cash_value = #{cashValue}</if>
            <if test="insuaccValue != null "> and insuacc_value = #{insuaccValue}</if>
            <if test="appntNo != null  and appntNo != ''"> and appnt_no = #{appntNo}</if>
            <if test="appntName != null  and appntName != ''"> and appnt_name like concat('%', #{appntName}, '%')</if>
            <if test="appntIdType != null  and appntIdType != ''"> and appnt_id_type = #{appntIdType}</if>
            <if test="appntIdNo != null  and appntIdNo != ''"> and appnt_id_no = #{appntIdNo}</if>
            <if test="appntSex != null "> and appnt_sex = #{appntSex}</if>
            <if test="appntBirthday != null "> and appnt_birthday = #{appntBirthday}</if>
            <if test="appntOccType != null  and appntOccType != ''"> and appnt_occ_type = #{appntOccType}</if>
            <if test="appntOccCode != null  and appntOccCode != ''"> and appnt_occ_code = #{appntOccCode}</if>
            <if test="insuredPeoples != null "> and insured_peoples = #{insuredPeoples}</if>
            <if test="insuredNo != null  and insuredNo != ''"> and insured_no = #{insuredNo}</if>
            <if test="insuredName != null  and insuredName != ''"> and insured_name like concat('%', #{insuredName}, '%')</if>
            <if test="insuredIdType != null  and insuredIdType != ''"> and insured_id_type = #{insuredIdType}</if>
            <if test="insuredIdNo != null  and insuredIdNo != ''"> and insured_id_no = #{insuredIdNo}</if>
            <if test="insuredSex != null "> and insured_sex = #{insuredSex}</if>
            <if test="insuredBirthday != null "> and insured_birthday = #{insuredBirthday}</if>
            <if test="insuredOccType != null  and insuredOccType != ''"> and insured_occ_type = #{insuredOccType}</if>
            <if test="insuredOccCode != null  and insuredOccCode != ''"> and insured_occ_code = #{insuredOccCode}</if>
            <if test="insuredSocisec != null "> and insured_socisec = #{insuredSocisec}</if>
            <if test="insuredPassFlag != null "> and insured_pass_flag = #{insuredPassFlag}</if>
            <if test="insuredAppAge != null "> and insured_app_age = #{insuredAppAge}</if>
            <if test="edorAcceptNo != null  and edorAcceptNo != ''"> and edor_accept_no = #{edorAcceptNo}</if>
            <if test="edorType != null  and edorType != ''"> and edor_type = #{edorType}</if>
            <if test="edorState != null "> and edor_state = #{edorState}</if>
            <if test="edorGetMoney != null "> and edor_get_money = #{edorGetMoney}</if>
            <if test="edorGetInterest != null "> and edor_get_interest = #{edorGetInterest}</if>
            <if test="edorAppDate != null "> and edor_app_date = #{edorAppDate}</if>
            <if test="edorValidate != null "> and edor_validate = #{edorValidate}</if>
            <if test="edorConfDate != null "> and edor_conf_date = #{edorConfDate}</if>
            <if test="edorConfTime != null  and edorConfTime != ''"> and edor_conf_time = #{edorConfTime}</if>
            <if test="edorMakeDate != null "> and edor_make_date = #{edorMakeDate}</if>
            <if test="clmNo != null  and clmNo != ''"> and clm_no = #{clmNo}</if>
            <if test="clmState != null  and clmState != ''"> and clm_state = #{clmState}</if>
            <if test="clmGiveType != null  and clmGiveType != ''"> and clm_give_type = #{clmGiveType}</if>
            <if test="clmStandpay != null "> and clm_standpay = #{clmStandpay}</if>
            <if test="clmRealpay != null "> and clm_realpay = #{clmRealpay}</if>
            <if test="clmAccidentDate != null "> and clm_accident_date = #{clmAccidentDate}</if>
            <if test="clmAccDate != null "> and clm_acc_date = #{clmAccDate}</if>
            <if test="clmRptDate != null "> and clm_rpt_date = #{clmRptDate}</if>
            <if test="clmRgtDate != null "> and clm_rgt_date = #{clmRgtDate}</if>
            <if test="clmCaseEndDate != null "> and clm_case_end_date = #{clmCaseEndDate}</if>
            <if test="clmEnterAccDate != null "> and clm_enter_acc_date = #{clmEnterAccDate}</if>
            <if test="clmFeeSum != null "> and clm_fee_sum = #{clmFeeSum}</if>
            <if test="clmFeeType != null  and clmFeeType != ''"> and clm_fee_type = #{clmFeeType}</if>
            <if test="clmBalTypeDesc != null  and clmBalTypeDesc != ''"> and clm_bal_type_desc = #{clmBalTypeDesc}</if>
            <if test="clmSubFeeType != null  and clmSubFeeType != ''"> and clm_sub_fee_type = #{clmSubFeeType}</if>
            <if test="clmSubBalTypeDesc != null  and clmSubBalTypeDesc != ''"> and clm_sub_bal_type_desc = #{clmSubBalTypeDesc}</if>
            <if test="clmDefoGrade != null  and clmDefoGrade != ''"> and clm_defo_grade = #{clmDefoGrade}</if>
            <if test="clmDefoGradeName != null  and clmDefoGradeName != ''"> and clm_defo_grade_name like concat('%', #{clmDefoGradeName}, '%')</if>
            <if test="clmDefoType != null  and clmDefoType != ''"> and clm_defo_type = #{clmDefoType}</if>
            <if test="clmDefoName != null  and clmDefoName != ''"> and clm_defo_name like concat('%', #{clmDefoName}, '%')</if>
            <if test="clmHospitalName != null  and clmHospitalName != ''"> and clm_hospital_name like concat('%', #{clmHospitalName}, '%')</if>
            <if test="clmInHospitalDate != null "> and clm_in_hospital_date = #{clmInHospitalDate}</if>
            <if test="clmOutHospitalDate != null "> and clm_out_hospital_date = #{clmOutHospitalDate}</if>
            <if test="clmAccidentReason != null  and clmAccidentReason != ''"> and clm_accident_reason = #{clmAccidentReason}</if>
            <if test="clmAccresult1 != null  and clmAccresult1 != ''"> and clm_accresult_1 = #{clmAccresult1}</if>
            <if test="clmAccresult2 != null  and clmAccresult2 != ''"> and clm_accresult_2 = #{clmAccresult2}</if>
            <if test="clmAccresult1Name != null  and clmAccresult1Name != ''"> and clm_accresult_1_name = #{clmAccresult1Name}</if>
            <if test="clmAccresult2Name != null  and clmAccresult2Name != ''"> and clm_accresult_2_name = #{clmAccresult2Name}</if>
            <if test="clmMakeDate != null "> and clm_make_date = #{clmMakeDate}</if>
            <if test="rsPayIntv != null "> and rs_pay_intv = #{rsPayIntv}</if>
            <if test="calcStatus != null "> and calc_status = #{calcStatus}</if>
            <if test="calcType != null "> and calc_type = #{calcType}</if>
            <if test="calcFailCode != null "> and calc_fail_code = #{calcFailCode}</if>
            <if test="cedeoutType != null "> and cedeout_type = #{cedeoutType}</if>
            <if test="reNoticeUrl != null  and reNoticeUrl != ''"> and re_notice_url = #{reNoticeUrl}</if>
            <if test="cedeoutCount != null "> and cedeout_count = #{cedeoutCount}</if>
            <if test="companyCode != null  and companyCode != ''"> and company_code = #{companyCode}</if>
            <if test="companyName != null  and companyName != ''"> and company_name like concat('%', #{companyName}, '%')</if>
            <if test="contractCode != null  and contractCode != ''"> and contract_code = #{contractCode}</if>
            <if test="contractName != null  and contractName != ''"> and contract_name = #{contractName}</if>
            <if test="mainContractCode != null  and mainContractCode != ''"> and main_contract_code = #{mainContractCode}</if>
            <if test="mainContractName != null  and mainContractName != ''"> and main_contract_name = #{mainContractName}</if>
            <if test="programmeCode != null  and programmeCode != ''"> and programme_code = #{programmeCode}</if>
            <if test="programmeName != null  and programmeName != ''"> and programme_name like concat('%', #{programmeName}, '%')</if>
            <if test="programmeSelfAmount != null "> and programme_self_amount = #{programmeSelfAmount}</if>
            <if test="programmeSelfScale != null "> and programme_self_scale = #{programmeSelfScale}</if>
            <if test="cedeoutMode != null "> and cedeout_mode = #{cedeoutMode}</if>
            <if test="cedeoutWay != null "> and cedeout_way = #{cedeoutWay}</if>
            <if test="addupAmountType != null "> and addup_amount_type = #{addupAmountType}</if>
            <if test="addupRiskCode != null  and addupRiskCode != ''"> and addup_risk_code = #{addupRiskCode}</if>
            <if test="addupRiskName != null  and addupRiskName != ''"> and addup_risk_name = #{addupRiskName}</if>
            <if test="initRiskAmount != null "> and init_risk_amount = #{initRiskAmount}</if>
            <if test="occupyRiskAmount != null "> and occupy_risk_amount = #{occupyRiskAmount}</if>
            <if test="releaseRiskAmount != null "> and release_risk_amount = #{releaseRiskAmount}</if>
            <if test="cedeoutAmount != null "> and cedeout_amount = #{cedeoutAmount}</if>
            <if test="selfAmount != null "> and self_amount = #{selfAmount}</if>
            <if test="acceptCopies != null "> and accept_copies = #{acceptCopies}</if>
            <if test="cedeoutScale != null "> and cedeout_scale = #{cedeoutScale}</if>
            <if test="selfScale != null "> and self_scale = #{selfScale}</if>
            <if test="cedeoutPremium != null "> and cedeout_premium = #{cedeoutPremium}</if>
            <if test="cedeoutAddPremium != null "> and cedeout_add_premium = #{cedeoutAddPremium}</if>
            <if test="cedeoutCommission != null "> and cedeout_commission = #{cedeoutCommission}</if>
            <if test="taxRate != null "> and tax_rate = #{taxRate}</if>
            <if test="addedTax != null "> and added_tax = #{addedTax}</if>
            <if test="rateCode != null  and rateCode != ''"> and rate_code = #{rateCode}</if>
            <if test="rateDataId != null "> and rate_data_id = #{rateDataId}</if>
            <if test="rateDataValue != null "> and rate_data_value = #{rateDataValue}</if>
            <if test="cedeoutRateDataValue != null "> and cedeout_rate_data_value = #{cedeoutRateDataValue}</if>
            <if test="comRateCode != null  and comRateCode != ''"> and com_rate_code = #{comRateCode}</if>
            <if test="comRateDataId != null "> and com_rate_data_id = #{comRateDataId}</if>
            <if test="comRateDataValue != null "> and com_rate_data_value = #{comRateDataValue}</if>
            <if test="disRateCode != null  and disRateCode != ''"> and dis_rate_code = #{disRateCode}</if>
            <if test="disRateDataId != null "> and dis_rate_data_id = #{disRateDataId}</if>
            <if test="disRateDataValue != null "> and dis_rate_data_value = #{disRateDataValue}</if>
            <if test="reservesType != null"> and reserves_type = #{reservesType}</if>
            <if test="reservesId != null "> and reserves_id = #{reservesId}</if>
            <if test="reserves != null "> and reserves = #{reserves}</if>
            <if test="returnStatus != null "> and return_status = #{returnStatus}</if>
            <if test="returnDate != null "> and return_date = #{returnDate}</if>
            <if test="returnReason != null "> and return_reason = #{returnReason}</if>
            <if test="srcOutTradeId != null "> and src_out_trade_id = #{srcOutTradeId}</if>
            <if test="returnPremium != null "> and return_premium = #{returnPremium}</if>
            <if test="returnCbPremium != null "> and return_cb_premium = #{returnCbPremium}</if>
            <if test="returnTotalPremium != null "> and return_total_premium = #{returnTotalPremium}</if>
            <if test="returnClaimAmount != null "> and return_claim_amount = #{returnClaimAmount}</if>
            <if test="returnExpiredGold != null "> and return_expired_gold = #{returnExpiredGold}</if>
            <if test="returnCommission != null "> and return_commission = #{returnCommission}</if>
            <if test="adjustStatus != null "> and adjust_status = #{adjustStatus}</if>
            <if test="adjustDate != null "> and adjust_date = #{adjustDate}</if>
            <if test="adjuster != null  and adjuster != ''"> and adjuster = #{adjuster}</if>
            <if test="adjustReason != null  and adjustReason != ''"> and adjust_reason = #{adjustReason}</if>
            <if test="adjustBatchNo != null  and adjustBatchNo != ''"> and adjust_batch_no = #{adjustBatchNo}</if>
            <if test="billNo != null and billNo != ''"> and bill_no = #{billNo}</if>
            <if test="billConfirmStatus != null "> and bill_confirm_status = #{billConfirmStatus}</if>
            <if test="billConfirmDate != null "> and bill_confirm_date = #{billConfirmDate}</if>
            <if test="billConfirmer != null  and billConfirmer != ''"> and bill_confirmer = #{billConfirmStatus}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="backTrackData != null "> and back_track_data = #{backTrackData}</if>
            <if test="params.contAnniversaryStartDate != null and params.contAnniversaryEndDate != null ">
                and cont_anniversary &gt;= #{params.contAnniversaryStartDate} and cont_anniversary &lt;= #{params.contAnniversaryEndDate}
            </if>
        </where>
        and is_del = 0 order by id desc
    </select>
    
    <select id="selectDwsReinsuTradeById" parameterType="Long" resultMap="DwsReinsuTradeResult">
        <include refid="selectDwsReinsuTradeVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertDwsReinsuTrade" parameterType="DwsReinsuTradeEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_dws_reinsu_trade
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="batchNo != null and batchNo != ''">batch_no,</if>
            <if test="busiType != null">busi_type,</if>
            <if test="dataType != null">data_type,</if>
            <if test="contType != null">cont_type,</if>
            <if test="dataCopy != null">data_copy,</if>
            <if test="dataGroupNo != null">data_group_no,</if>
            <if test="busiOccurDate != null ">busi_occur_date,</if>
            <if test="getDate != null">get_date,</if>
            <if test="getTime != null and getTime != ''">get_time,</if>
            <if test="accountPeriod != null and accountPeriod != ''">account_period,</if>
            <if test="accountDate != null">account_date,</if>
            <if test="grpContNo != null">grp_cont_no,</if>
            <if test="grpPolNo != null">grp_pol_no,</if>
            <if test="contNo != null and contNo != ''">cont_no,</if>
            <if test="polNo != null and polNo != ''">pol_no,</if>
            <if test="mainPolNo != null and mainPolNo != ''">main_pol_no,</if>
            <if test="saleChnl != null">sale_chnl,</if>
            <if test="saleChnlName != null">sale_chnl_name,</if>
            <if test="sellType != null">sell_type,</if>
            <if test="sellTypeName != null">sell_type_name,</if>
            <if test="saleComCode != null">sale_com_code,</if>
            <if test="saleComName != null">sale_com_name,</if>
            <if test="agentComCode != null">agent_com_code,</if>
            <if test="agentComName != null">agent_com_name,</if>
            <if test="manageComCode != null">manage_com_code,</if>
            <if test="manageComName != null">manage_com_name,</if>
            <if test="contMakeDate != null">cont_make_date,</if>
            <if test="signDate != null">sign_date,</if>
            <if test="signTime != null">sign_time,</if>
            <if test="contYear != null">cont_year,</if>
            <if test="contMonth != null">cont_month,</if>
            <if test="contAnniversary != null">cont_anniversary,</if>
            <if test="previousContAnniversary != null">previous_cont_anniversary,</if>
            <if test="contAppFlag != null">cont_app_flag,</if>
            <if test="invalidStateType != null">invalid_state_type,</if>
            <if test="invalidStateReason != null">invalid_state_reason,</if>
            <if test="invalidStartDate != null">invalid_start_date,</if>
            <if test="riskCode != null">risk_code,</if>
            <if test="riskName != null">risk_name,</if>
            <if test="riskAppFlag != null">risk_app_flag,</if>
            <if test="riskValiDate != null">risk_vali_date,</if>
            <if test="riskEndDate != null">risk_end_date,</if>
            <if test="subRiskFlag != null">sub_risk_flag,</if>
            <if test="riskPeriod != null">risk_period,</if>
            <if test="riskType3 != null">risk_type3,</if>
            <if test="planCode != null">plan_code,</if>
            <if test="polRiskType != null">pol_risk_type,</if>
            <if test="getDutyCodes != null">get_duty_codes,</if>
            <if test="getDutyNames != null">get_duty_names,</if>
            <if test="liabilityCode != null">liability_code,</if>
            <if test="liabilityName != null">liability_name,</if>
            <if test="insuYear != null">insu_year,</if>
            <if test="insuYearFlag != null">insu_year_flag,</if>
            <if test="payIntv != null">pay_intv,</if>
            <if test="payendYear != null">payend_year,</if>
            <if test="payendYearFlag != null">payend_year_flag,</if>
            <if test="riskFreeFlag != null">risk_free_flag,</if>
            <if test="payPeriods != null">pay_periods,</if>
            <if test="inPayPeriods != null">in_pay_periods,</if>
            <if test="unPayPeriods != null">un_pay_periods,</if>
            <if test="unPayPremium != null">un_pay_premium,</if>
            <if test="amount != null">amount,</if>
            <if test="availableAmount != null">available_amount,</if>
            <if test="totalPremium != null">total_premium,</if>
            <if test="basePremium != null">base_premium,</if>
            <if test="addPremium != null">add_premium,</if>
            <if test="addScale != null">add_scale,</if>
            <if test="payToDate != null">pay_to_date,</if>
            <if test="payEndDate != null">pay_end_date,</if>
            <if test="sumPayMoney != null">sum_pay_money,</if>
            <if test="sumAddMoney != null">sum_add_money,</if>
            <if test="sumGetMoney != null">sum_get_money,</if>
            <if test="cashValue != null">cash_value,</if>
            <if test="insuaccValue != null">insuacc_value,</if>
            <if test="appntNo != null">appnt_no,</if>
            <if test="appntName != null">appnt_name,</if>
            <if test="appntIdType != null">appnt_id_type,</if>
            <if test="appntIdNo != null">appnt_id_no,</if>
            <if test="appntSex != null">appnt_sex,</if>
            <if test="appntBirthday != null">appnt_birthday,</if>
            <if test="appntOccType != null">appnt_occ_type,</if>
            <if test="appntOccCode != null">appnt_occ_code,</if>
            <if test="insuredPeoples != null">insured_peoples,</if>
            <if test="insuredNo != null">insured_no,</if>
            <if test="insuredName != null">insured_name,</if>
            <if test="insuredIdType != null">insured_id_type,</if>
            <if test="insuredIdNo != null">insured_id_no,</if>
            <if test="insuredSex != null">insured_sex,</if>
            <if test="insuredBirthday != null">insured_birthday,</if>
            <if test="insuredOccType != null">insured_occ_type,</if>
            <if test="insuredOccCode != null">insured_occ_code,</if>
            <if test="insuredSocisec != null">insured_socisec,</if>
            <if test="insuredPassFlag != null">insured_pass_flag,</if>
            <if test="insuredAppAge != null">insured_app_age,</if>
            <if test="edorAcceptNo != null">edor_accept_no,</if>
            <if test="edorType != null">edor_type,</if>
            <if test="edorState != null">edor_state,</if>
            <if test="edorGetMoney != null">edor_get_money,</if>
            <if test="edorGetInterest != null">edor_get_interest,</if>
            <if test="edorAppDate != null">edor_app_date,</if>
            <if test="edorValidate != null">edor_validate,</if>
            <if test="edorConfDate != null">edor_conf_date,</if>
            <if test="edorConfTime != null">edor_conf_time,</if>
            <if test="edorMakeDate != null">edor_make_date,</if>
            <if test="clmNo != null">clm_no,</if>
            <if test="clmState != null">clm_state,</if>
            <if test="clmGiveType != null">clm_give_type,</if>
            <if test="clmStandpay != null">clm_standpay,</if>
            <if test="clmRealpay != null">clm_realpay,</if>
            <if test="clmAccidentDate != null">clm_accident_date,</if>
            <if test="clmAccDate != null">clm_acc_date,</if>
            <if test="clmRptDate != null">clm_rpt_date,</if>
            <if test="clmRgtDate != null">clm_rgt_date,</if>
            <if test="clmCaseEndDate != null">clm_case_end_date,</if>
            <if test="clmEnterAccDate != null">clm_enter_acc_date,</if>
            <if test="clmFeeSum != null">clm_fee_sum,</if>
            <if test="clmFeeType != null">clm_fee_type,</if>
            <if test="clmBalTypeDesc != null">clm_bal_type_desc,</if>
            <if test="clmSubFeeType != null">clm_sub_fee_type,</if>
            <if test="clmSubBalTypeDesc != null">clm_sub_bal_type_desc,</if>
            <if test="clmDefoGrade != null">clm_defo_grade,</if>
            <if test="clmDefoGradeName != null">clm_defo_grade_name,</if>
            <if test="clmDefoType != null">clm_defo_type,</if>
            <if test="clmDefoName != null">clm_defo_name,</if>
            <if test="clmHospitalName != null">clm_hospital_name,</if>
            <if test="clmInHospitalDate != null">clm_in_hospital_date,</if>
            <if test="clmOutHospitalDate != null">clm_out_hospital_date,</if>
            <if test="clmAccidentReason != null">clm_accident_reason,</if>
            <if test="clmAccresult1 != null">clm_accresult_1,</if>
            <if test="clmAccresult2 != null">clm_accresult_2,</if>
            <if test="clmAccresult1Name != null">clm_accresult_1_name,</if>
            <if test="clmAccresult2Name != null">clm_accresult_2_name,</if>
            <if test="clmMakeDate != null">clm_make_date,</if>
            <if test="rsPayIntv != null">rs_pay_intv,</if>
            <if test="calcStatus != null">calc_status,</if>
            <if test="calcType != null">calc_type,</if>
            <if test="calcFailCode != null">calc_fail_code,</if>
            <if test="cedeoutType != null">cedeout_type,</if>
            <if test="facultativeWay != null">facultative_way,</if>
            <if test="reNoticeUrl != null">re_notice_url,</if>
            <if test="cedeoutCount != null">cedeout_count,</if>
            <if test="companyCode != null">company_code,</if>
            <if test="companyName != null">company_name,</if>
            <if test="programmeCode != null">programme_code,</if>
            <if test="programmeName != null">programme_name,</if>
            <if test="contractCode != null">contract_code,</if>
            <if test="contractName != null">contract_name,</if>
            <if test="mainContractCode != null">main_contract_code,</if>
            <if test="mainContractName != null">main_contract_name,</if>
            <if test="programmeSelfAmount != null">programme_self_amount,</if>
            <if test="programmeSelfScale != null">programme_self_scale,</if>
            <if test="cedeoutMode != null">cedeout_mode,</if>
            <if test="cedeoutWay != null">cedeout_way,</if>
            <if test="addupAmountType != null">addup_amount_type,</if>
            <if test="addupRiskCode != null">addup_risk_code,</if>
            <if test="addupRiskName != null">addup_risk_name,</if>
            <if test="initRiskAmount != null">init_risk_amount,</if>
            <if test="occupyRiskAmount != null">occupy_risk_amount,</if>
            <if test="releaseRiskAmount != null">release_risk_amount,</if>
            <if test="cedeoutAmount != null">cedeout_amount,</if>
            <if test="selfAmount != null">self_amount,</if>
            <if test="acceptCopies != null">accept_copies,</if>
            <if test="cedeoutScale != null">cedeout_scale,</if>
            <if test="selfScale != null">self_scale,</if>
            <if test="cedeoutPremium != null">cedeout_premium,</if>
            <if test="cedeoutAddPremium != null">cedeout_add_premium,</if>
            <if test="cedeoutTotalPremium != null">cedeout_total_premium,</if>
            <if test="cedeoutCommission != null">cedeout_commission,</if>
            <if test="taxRate != null">tax_rate,</if>
            <if test="addedTax != null">added_tax,</if>
            <if test="rateCode != null">rate_code,</if>
            <if test="rateDataId != null">rate_data_id,</if>
            <if test="rateDataValue != null">rate_data_value,</if>
            <if test="cedeoutRateDataValue != null">cedeout_rate_data_value,</if>
            <if test="comRateCode != null">com_rate_code,</if>
            <if test="comRateDataId != null">com_rate_data_id,</if>
            <if test="comRateDataValue != null">com_rate_data_value,</if>
            <if test="disRateCode != null">dis_rate_code,</if>
            <if test="disRateDataId != null">dis_rate_data_id,</if>
            <if test="disRateDataValue != null">dis_rate_data_value,</if>
            <if test="reservesType != null">reserves_type,</if>
            <if test="reservesId != null">reserves_id,</if>
            <if test="reserves != null">reserves,</if>
            <if test="returnStatus != null">return_status,</if>
            <if test="returnDate != null">return_date,</if>
            <if test="returnReason != null">return_reason,</if>
            <if test="srcOutTradeId != null">src_out_trade_id,</if>
            <if test="returnPremium != null">return_premium,</if>
            <if test="returnCbPremium != null">return_cb_premium,</if>
            <if test="returnTotalPremium != null">return_total_premium,</if>
            <if test="returnClaimAmount != null">return_claim_amount,</if>
            <if test="returnExpiredGold != null">return_expired_gold,</if>
            <if test="returnCommission != null">return_commission,</if>
            <if test="adjustStatus != null">adjust_status,</if>
            <if test="adjustDate != null">adjust_date,</if>
            <if test="adjuster != null">adjuster,</if>
            <if test="adjustReason != null">adjust_reason,</if>
            <if test="adjustBatchNo != null">adjust_batch_no,</if>
            <if test="billConfirmStatus != null">bill_confirm_status,</if>
            <if test="billConfirmDate != null">bill_confirm_date,</if>
            <if test="billConfirmer != null">bill_confirmer,</if>
            <if test="billNo != null">bill_no,</if>
            <if test="status != null">status,</if>
            <if test="backTrackData != null "> back_track_data ,</if>
            <if test="remark != null">remark,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="batchNo != null and batchNo != ''">#{batchNo},</if>
            <if test="busiType != null">#{busiType},</if>
            <if test="dataType != null">#{dataType},</if>
            <if test="contType != null">#{contType},</if>
            <if test="dataCopy != null">#{dataCopy},</if>
            <if test="dataGroupNo != null">#{dataGroupNo},</if>
            <if test="busiOccurDate != null">#{busiOccurDate},</if>
            <if test="getDate != null">#{getDate},</if>
            <if test="getTime != null and getTime != ''">#{getTime},</if>
            <if test="accountPeriod != null and accountPeriod != ''">#{accountPeriod},</if>
            <if test="accountDate != null">#{accountDate},</if>
            <if test="grpContNo != null">#{grpContNo},</if>
            <if test="grpPolNo != null">#{grpPolNo},</if>
            <if test="contNo != null and contNo != ''">#{contNo},</if>
            <if test="polNo != null and polNo != ''">#{polNo},</if>
            <if test="mainPolNo != null and mainPolNo != ''">#{mainPolNo},</if>
            <if test="saleChnl != null">#{saleChnl},</if>
            <if test="saleChnlName != null">#{saleChnlName},</if>
            <if test="sellType != null">#{sellType},</if>
            <if test="sellTypeName != null">#{sellTypeName},</if>
            <if test="saleComCode != null">#{saleComCode},</if>
            <if test="saleComName != null">#{saleComName},</if>
            <if test="agentComCode != null">#{agentComCode},</if>
            <if test="agentComName != null">#{agentComName},</if>
            <if test="manageComCode != null">#{manageComCode},</if>
            <if test="manageComName != null">#{manageComName},</if>
            <if test="contMakeDate != null">#{contMakeDate},</if>
            <if test="signDate != null">#{signDate},</if>
            <if test="signTime != null">#{signTime},</if>
            <if test="contYear != null">#{contYear},</if>
            <if test="contMonth != null">#{contMonth},</if>
            <if test="contAnniversary != null">#{contAnniversary},</if>
            <if test="previousContAnniversary != null">#{previousContAnniversary},</if>
            <if test="contAppFlag != null">#{contAppFlag},</if>
            <if test="invalidStateType != null">#{invalidStateType},</if>
            <if test="invalidStateReason != null">#{invalidStateReason},</if>
            <if test="invalidStartDate != null">#{invalidStartDate},</if>
            <if test="riskCode != null">#{riskCode},</if>
            <if test="riskName != null">#{riskName},</if>
            <if test="riskAppFlag != null">#{riskAppFlag},</if>
            <if test="riskValiDate != null">#{riskValiDate},</if>
            <if test="riskEndDate != null">#{riskEndDate},</if>
            <if test="subRiskFlag != null">#{subRiskFlag},</if>
            <if test="riskPeriod != null">#{riskPeriod},</if>
            <if test="riskType3 != null">#{riskType3},</if>
            <if test="planCode != null">#{planCode},</if>
            <if test="polRiskType != null">#{polRiskType},</if>
            <if test="getDutyCodes != null">#{getDutyCodes},</if>
            <if test="getDutyNames != null">#{getDutyNames},</if>
            <if test="liabilityCode != null">#{liabilityCode},</if>
            <if test="liabilityName != null">#{liabilityName},</if>
            <if test="insuYear != null">#{insuYear},</if>
            <if test="insuYearFlag != null">#{insuYearFlag},</if>
            <if test="payIntv != null">#{payIntv},</if>
            <if test="payendYear != null">#{payendYear},</if>
            <if test="payendYearFlag != null">#{payendYearFlag},</if>
            <if test="riskFreeFlag != null">#{riskFreeFlag},</if>
            <if test="payPeriods != null">#{payPeriods},</if>
            <if test="inPayPeriods != null">#{inPayPeriods},</if>
            <if test="unPayPeriods != null">#{unPayPeriods},</if>
            <if test="unPayPremium != null">#{unPayPremium},</if>
            <if test="amount != null">#{amount},</if>
            <if test="availableAmount != null">#{availableAmount},</if>
            <if test="totalPremium != null">#{totalPremium},</if>
            <if test="basePremium != null">#{basePremium},</if>
            <if test="addPremium != null">#{addPremium},</if>
            <if test="addScale != null">#{addScale},</if>
            <if test="payToDate != null">#{payToDate},</if>
            <if test="payEndDate != null">#{payEndDate},</if>
            <if test="sumPayMoney != null">#{sumPayMoney},</if>
            <if test="sumAddMoney != null">#{sumAddMoney},</if>
            <if test="sumGetMoney != null">#{sumGetMoney},</if>
            <if test="cashValue != null">#{cashValue},</if>
            <if test="insuaccValue != null">#{insuaccValue},</if>
            <if test="appntNo != null">#{appntNo},</if>
            <if test="appntName != null">#{appntName},</if>
            <if test="appntIdType != null">#{appntIdType},</if>
            <if test="appntIdNo != null">#{appntIdNo},</if>
            <if test="appntSex != null">#{appntSex},</if>
            <if test="appntBirthday != null">#{appntBirthday},</if>
            <if test="appntOccType != null">#{appntOccType},</if>
            <if test="appntOccCode != null">#{appntOccCode},</if>
            <if test="insuredPeoples != null">#{insuredPeoples},</if>
            <if test="insuredNo != null">#{insuredNo},</if>
            <if test="insuredName != null">#{insuredName},</if>
            <if test="insuredIdType != null">#{insuredIdType},</if>
            <if test="insuredIdNo != null">#{insuredIdNo},</if>
            <if test="insuredSex != null">#{insuredSex},</if>
            <if test="insuredBirthday != null">#{insuredBirthday},</if>
            <if test="insuredOccType != null">#{insuredOccType},</if>
            <if test="insuredOccCode != null">#{insuredOccCode},</if>
            <if test="insuredSocisec != null">#{insuredSocisec},</if>
            <if test="insuredPassFlag != null">#{insuredPassFlag},</if>
            <if test="insuredAppAge != null">#{insuredAppAge},</if>
            <if test="edorAcceptNo != null">#{edorAcceptNo},</if>
            <if test="edorType != null">#{edorType},</if>
            <if test="edorState != null">#{edorState},</if>
            <if test="edorGetMoney != null">#{edorGetMoney},</if>
            <if test="edorGetInterest != null">#{edorGetInterest},</if>
            <if test="edorAppDate != null">#{edorAppDate},</if>
            <if test="edorValidate != null">#{edorValidate},</if>
            <if test="edorConfDate != null">#{edorConfDate},</if>
            <if test="edorConfTime != null">#{edorConfTime},</if>
            <if test="edorMakeDate != null">#{edorMakeDate},</if>
            <if test="clmNo != null">#{clmNo},</if>
            <if test="clmState != null">#{clmState},</if>
            <if test="clmGiveType != null">#{clmGiveType},</if>
            <if test="clmStandpay != null">#{clmStandpay},</if>
            <if test="clmRealpay != null">#{clmRealpay},</if>
            <if test="clmAccidentDate != null ">#{clmAccidentDate},</if>
            <if test="clmAccDate != null">#{clmAccDate},</if>
            <if test="clmRptDate != null">#{clmRptDate},</if>
            <if test="clmRgtDate != null">#{clmRgtDate},</if>
            <if test="clmCaseEndDate != null">#{clmCaseEndDate},</if>
            <if test="clmEnterAccDate != null">#{clmEnterAccDate},</if>
            <if test="clmFeeSum != null">#{clmFeeSum},</if>
            <if test="clmFeeType != null">#{clmFeeType},</if>
            <if test="clmBalTypeDesc != null">#{clmBalTypeDesc},</if>
            <if test="clmSubFeeType != null">#{clmSubFeeType},</if>
            <if test="clmSubBalTypeDesc != null">#{clmSubBalTypeDesc},</if>
            <if test="clmDefoGrade != null">#{clmDefoGrade},</if>
            <if test="clmDefoGradeName != null">#{clmDefoGradeName},</if>
            <if test="clmDefoType != null">#{clmDefoType},</if>
            <if test="clmDefoName != null">#{clmDefoName},</if>
            <if test="clmHospitalName != null">#{clmHospitalName},</if>
            <if test="clmInHospitalDate != null">#{clmInHospitalDate},</if>
            <if test="clmOutHospitalDate != null">#{clmOutHospitalDate},</if>
            <if test="clmAccidentReason != null">#{clmAccidentReason},</if>
            <if test="clmAccresult1 != null">#{clmAccresult1},</if>
            <if test="clmAccresult2 != null">#{clmAccresult2},</if>
            <if test="clmAccresult1Name != null">#{clmAccresult1Name},</if>
            <if test="clmAccresult2Name != null">#{clmAccresult2Name},</if>
            <if test="clmMakeDate != null">#{clmMakeDate},</if>
            <if test="rsPayIntv != null">#{rsPayIntv},</if>
            <if test="calcStatus != null">#{calcStatus},</if>
            <if test="calcType != null">#{calcType},</if>
            <if test="calcFailCode != null">#{calcFailCode},</if>
            <if test="cedeoutType != null">#{cedeoutType},</if>
            <if test="facultativeWay != null">#{facultativeWay},</if>
            <if test="reNoticeUrl != null">#{reNoticeUrl},</if>
            <if test="cedeoutCount != null">#{cedeoutCount},</if>
            <if test="companyCode != null">#{companyCode},</if>
            <if test="companyName != null">#{programmeName},</if>
            <if test="programmeCode != null">#{programmeCode},</if>
            <if test="programmeName != null">#{programmeName},</if>
            <if test="contractCode != null">#{contractCode},</if>
            <if test="contractName != null">#{contractName},</if>
            <if test="mainContractCode != null">#{mainContractCode},</if>
            <if test="mainContractName != null">#{mainContractName},</if>
            <if test="programmeSelfAmount != null">#{programmeSelfAmount},</if>
            <if test="programmeSelfScale != null">#{programmeSelfScale},</if>
            <if test="cedeoutMode != null">#{cedeoutMode},</if>
            <if test="cedeoutWay != null">#{cedeoutWay},</if>
            <if test="addupAmountType != null">#{addupAmountType},</if>
            <if test="addupRiskCode != null">#{addupRiskCode},</if>
            <if test="addupRiskName != null">#{addupRiskName},</if>
            <if test="initRiskAmount != null">#{initRiskAmount},</if>
            <if test="occupyRiskAmount != null">#{occupyRiskAmount},</if>
            <if test="releaseRiskAmount != null">#{releaseRiskAmount},</if>
            <if test="cedeoutAmount != null">#{cedeoutAmount},</if>
            <if test="selfAmount != null">#{selfAmount},</if>
            <if test="acceptCopies != null">#{acceptCopies},</if>
            <if test="cedeoutScale != null">#{cedeoutScale},</if>
            <if test="selfScale != null">#{selfScale},</if>
            <if test="cedeoutPremium != null">#{cedeoutPremium},</if>
            <if test="cedeoutAddPremium != null">#{cedeoutAddPremium},</if>
            <if test="cedeoutTotalPremium != null">#{cedeoutTotalPremium},</if>
            <if test="cedeoutCommission != null">#{cedeoutCommission},</if>
            <if test="taxRate != null">#{taxRate},</if>
            <if test="addedTax != null">#{addedTax},</if>
            <if test="rateCode != null">#{rateCode},</if>
            <if test="rateDataId != null">#{rateDataId},</if>
            <if test="rateDataValue != null">#{rateDataValue},</if>
            <if test="cedeoutRateDataValue != null "> #{cedeoutRateDataValue},</if>
            <if test="comRateCode != null">#{comRateCode},</if>
            <if test="comRateDataId != null">#{comRateDataId},</if>
            <if test="comRateDataValue != null">#{comRateDataValue},</if>
            <if test="disRateCode != null">#{disRateCode},</if>
            <if test="disRateDataId != null">#{disRateDataId},</if>
            <if test="disRateDataValue != null">#{disRateDataValue},</if>
            <if test="reservesType != null">#{reservesType},</if>
            <if test="reservesId != null">#{reservesId},</if>
            <if test="reserves != null">#{reserves},</if>
            <if test="returnStatus != null">#{returnStatus},</if>
            <if test="returnDate != null">#{returnDate},</if>
            <if test="returnReason != null">#{returnReason},</if>
            <if test="srcOutTradeId != null">#{srcOutTradeId},</if>
            <if test="returnPremium != null">#{returnPremium},</if>
            <if test="returnCbPremium != null">#{returnCbPremium},</if>
            <if test="returnTotalPremium != null">#{returnTotalPremium},</if>
            <if test="returnClaimAmount != null">#{returnClaimAmount},</if>
            <if test="returnExpiredGold != null">#{returnExpiredGold},</if>
            <if test="returnCommission != null">#{returnCommission},</if>
            <if test="adjustStatus != null">#{adjustStatus},</if>
            <if test="adjustDate != null">#{adjustDate},</if>
            <if test="adjuster != null">#{adjuster},</if>
            <if test="adjustReason != null">#{adjustReason},</if>
            <if test="adjustBatchNo != null">#{adjustBatchNo},</if>
            <if test="billConfirmStatus != null">#{billConfirmStatus},</if>
            <if test="billConfirmDate != null">#{billConfirmDate},</if>
            <if test="billConfirmer != null">#{billConfirmer},</if>
            <if test="billNo != null">#{billNo},</if>
            <if test="status != null">#{status},</if>
            <if test="backTrackData != null ">  #{backTrackData},</if>
            <if test="remark != null">#{remark},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDwsReinsuTrade" parameterType="DwsReinsuTradeEntity">
        update t_dws_reinsu_trade
        <trim prefix="SET" suffixOverrides=",">
            <if test="batchNo != null and batchNo != ''">batch_no = #{batchNo},</if>
            <if test="busiType != null">busi_type = #{busiType},</if>
            <if test="dataType != null">data_type = #{dataType},</if>
            <if test="contType != null">cont_type = #{contType},</if>
            <if test="dataCopy != null">data_copy = #{dataCopy},</if>
            <if test="dataGroupNo != null">data_group_no = #{dataGroupNo},</if>
            <if test="busiOccurDate != null ">busi_occur_date = #{busiOccurDate},</if>
            <if test="getDate != null">get_date = #{getDate},</if>
            <if test="getTime != null and getTime != ''">get_time = #{getTime},</if>
            <if test="accountPeriod != null and accountPeriod != ''">account_period = #{accountPeriod},</if>
            <if test="accountDate != null">account_date = #{accountDate},</if>
            <if test="grpContNo != null">grp_cont_no = #{grpContNo},</if>
            <if test="grpPolNo != null">grp_pol_no = #{grpPolNo},</if>
            <if test="contNo != null and contNo != ''">cont_no = #{contNo},</if>
            <if test="polNo != null and polNo != ''">pol_no = #{polNo},</if>
            <if test="mainPolNo != null and mainPolNo != ''">main_pol_no = #{mainPolNo},</if>
            <if test="saleChnl != null">sale_chnl = #{saleChnl},</if>
            <if test="saleChnlName != null">sale_chnl_name = #{saleChnlName},</if>
            <if test="sellType != null">sell_type = #{sellType},</if>
            <if test="sellTypeName != null">sell_type_name = #{sellTypeName},</if>
            <if test="saleComCode != null">sale_com_code = #{saleComCode},</if>
            <if test="saleComName != null">sale_com_name = #{saleComName},</if>
            <if test="agentComCode != null">agent_com_code = #{agentComCode},</if>
            <if test="agentComName != null">agent_com_name = #{agentComName},</if>
            <if test="manageComCode != null">manage_com_code = #{manageComCode},</if>
            <if test="manageComName != null">manage_com_name = #{manageComName},</if>
            <if test="contMakeDate != null">cont_make_date = #{contMakeDate},</if>
            <if test="signDate != null">sign_date = #{signDate},</if>
            <if test="signTime != null">sign_time = #{signTime},</if>
            <if test="contYear != null">cont_year = #{contYear},</if>
            <if test="contMonth != null">cont_month = #{contMonth},</if>
            <if test="contAnniversary != null">cont_anniversary = #{contAnniversary},</if>
            <if test="previousContAnniversary != null">previous_cont_anniversary = #{previousContAnniversary},</if>
            <if test="contAppFlag != null">cont_app_flag = #{contAppFlag},</if>
            <if test="invalidStateType != null">invalid_state_type = #{invalidStateType},</if>
            <if test="invalidStateReason != null">invalid_state_reason = #{invalidStateReason},</if>
            <if test="invalidStartDate != null">invalid_start_date = #{invalidStartDate},</if>
            <if test="riskCode != null">risk_code = #{riskCode},</if>
            <if test="riskName != null">risk_name = #{riskName},</if>
            <if test="riskAppFlag != null">risk_app_flag = #{riskAppFlag},</if>
            <if test="riskValiDate != null">risk_vali_date = #{riskValiDate},</if>
            <if test="riskEndDate != null">risk_end_date = #{riskEndDate},</if>
            <if test="subRiskFlag != null">sub_risk_flag = #{subRiskFlag},</if>
            <if test="riskPeriod != null">risk_period = #{riskPeriod},</if>
             <if test="riskType3 != null">risk_type3 = #{riskType3},</if>
            <if test="planCode != null">plan_code = #{planCode},</if>
            <if test="polRiskType != null">pol_risk_type = #{polRiskType},</if>
            <if test="getDutyCodes != null">get_duty_codes = #{getDutyCodes},</if>
            <if test="getDutyNames != null">get_duty_names = #{getDutyNames},</if>
            <if test="liabilityCode != null">liability_code = #{liabilityCode},</if>
            <if test="liabilityName != null">liability_name = #{liabilityName},</if>
            <if test="insuYear != null">insu_year = #{insuYear},</if>
            <if test="insuYearFlag != null">insu_year_flag = #{insuYearFlag},</if>
            <if test="payIntv != null">pay_intv = #{payIntv},</if>
            <if test="payendYear != null">payend_year = #{payendYear},</if>
            <if test="payendYearFlag != null">payend_year_flag = #{payendYearFlag},</if>
            <if test="riskFreeFlag != null">risk_free_flag = #{riskFreeFlag},</if>
            <if test="payPeriods != null">pay_periods = #{payPeriods},</if>
            <if test="inPayPeriods != null">in_pay_periods = #{inPayPeriods},</if>
            <if test="unPayPeriods != null">un_pay_periods = #{unPayPeriods},</if>
            <if test="unPayPremium != null">un_pay_premium = #{unPayPremium},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="availableAmount != null">available_amount = #{availableAmount},</if>
            <if test="totalPremium != null">total_premium = #{totalPremium},</if>
            <if test="basePremium != null">base_premium = #{basePremium},</if>
            <if test="addPremium != null">add_premium = #{addPremium},</if>
            <if test="addScale != null">add_scale = #{addScale},</if>
            <if test="payToDate != null">pay_to_date = #{payToDate},</if>
            <if test="payEndDate != null">pay_end_date = #{payEndDate},</if>
            <if test="sumPayMoney != null">sum_pay_money = #{sumPayMoney},</if>
            <if test="sumAddMoney != null">sum_add_money = #{sumAddMoney},</if>
            <if test="sumGetMoney != null">sum_get_money = #{sumGetMoney},</if>
            <if test="cashValue != null">cash_value = #{cashValue},</if>
            <if test="insuaccValue != null">insuacc_value = #{insuaccValue},</if>
            <if test="appntNo != null">appnt_no = #{appntNo},</if>
            <if test="appntName != null">appnt_name = #{appntName},</if>
            <if test="appntIdType != null">appnt_id_type = #{appntIdType},</if>
            <if test="appntIdNo != null">appnt_id_no = #{appntIdNo},</if>
            <if test="appntSex != null">appnt_sex = #{appntSex},</if>
            <if test="appntBirthday != null">appnt_birthday = #{appntBirthday},</if>
            <if test="appntOccType != null">appnt_occ_type = #{appntOccType},</if>
            <if test="appntOccCode != null">appnt_occ_code = #{appntOccCode},</if>
            <if test="insuredPeoples != null">insured_peoples = #{insuredPeoples},</if>
            <if test="insuredNo != null">insured_no = #{insuredNo},</if>
            <if test="insuredName != null">insured_name = #{insuredName},</if>
            <if test="insuredIdType != null">insured_id_type = #{insuredIdType},</if>
            <if test="insuredIdNo != null">insured_id_no = #{insuredIdNo},</if>
            <if test="insuredSex != null">insured_sex = #{insuredSex},</if>
            <if test="insuredBirthday != null">insured_birthday = #{insuredBirthday},</if>
            <if test="insuredOccType != null">insured_occ_type = #{insuredOccType},</if>
            <if test="insuredOccCode != null">insured_occ_code = #{insuredOccCode},</if>
            <if test="insuredSocisec != null">insured_socisec = #{insuredSocisec},</if>
            <if test="insuredPassFlag != null">insured_pass_flag = #{insuredPassFlag},</if>
            <if test="insuredAppAge != null">insured_app_age = #{insuredAppAge},</if>
            <if test="edorAcceptNo != null">edor_accept_no = #{edorAcceptNo},</if>
            <if test="edorType != null">edor_type = #{edorType},</if>
            <if test="edorState != null">edor_state = #{edorState},</if>
            <if test="edorGetMoney != null">edor_get_money = #{edorGetMoney},</if>
            <if test="edorGetInterest != null">edor_get_interest = #{edorGetInterest},</if>
            <if test="edorAppDate != null">edor_app_date = #{edorAppDate},</if>
            <if test="edorValidate != null">edor_validate = #{edorValidate},</if>
            <if test="edorConfDate != null">edor_conf_date = #{edorConfDate},</if>
            <if test="edorConfTime != null">edor_conf_time = #{edorConfTime},</if>
            <if test="edorMakeDate != null">edor_make_date = #{edorMakeDate},</if>
            <if test="clmNo != null">clm_no = #{clmNo},</if>
            <if test="clmState != null">clm_state = #{clmState},</if>
            <if test="clmGiveType != null">clm_give_type = #{clmGiveType},</if>
            <if test="clmStandpay != null">clm_standpay = #{clmStandpay},</if>
            <if test="clmRealpay != null">clm_realpay = #{clmRealpay},</if>
            <if test="clmAccidentDate != null">clm_accident_date = #{clmAccidentDate},</if>
            <if test="clmAccDate != null">clm_acc_date = #{clmAccDate},</if>
            <if test="clmRptDate != null">clm_rpt_date = #{clmRptDate},</if>
            <if test="clmRgtDate != null">clm_rgt_date = #{clmRgtDate},</if>
            <if test="clmCaseEndDate != null">clm_case_end_date = #{clmCaseEndDate},</if>
            <if test="clmEnterAccDate != null">clm_enter_acc_date = #{clmEnterAccDate},</if>
            <if test="clmFeeSum != null">clm_fee_sum = #{clmFeeSum},</if>
            <if test="clmFeeType != null">clm_fee_type = #{clmFeeType},</if>
            <if test="clmBalTypeDesc != null">clm_bal_type_desc = #{clmBalTypeDesc},</if>
            <if test="clmSubFeeType != null">clm_sub_fee_type = #{clmSubFeeType},</if>
            <if test="clmSubBalTypeDesc != null">clm_sub_bal_type_desc = #{clmSubBalTypeDesc},</if>
            <if test="clmDefoGrade != null">clm_defo_grade = #{clmDefoGrade},</if>
            <if test="clmDefoGradeName != null">clm_defo_grade_name = #{clmDefoGradeName},</if>
            <if test="clmDefoType != null">clm_defo_type = #{clmDefoType},</if>
            <if test="clmDefoName != null">clm_defo_name = #{clmDefoName},</if>
            <if test="clmHospitalName != null">clm_hospital_name = #{clmHospitalName},</if>
            <if test="clmInHospitalDate != null">clm_in_hospital_date = #{clmInHospitalDate},</if>
            <if test="clmOutHospitalDate != null">clm_out_hospital_date = #{clmOutHospitalDate},</if>
            <if test="clmAccidentReason != null">clm_accident_reason = #{clmAccidentReason},</if>
            <if test="clmAccresult1 != null">clm_accresult_1 = #{clmAccresult1},</if>
            <if test="clmAccresult2 != null">clm_accresult_2 = #{clmAccresult2},</if>
            <if test="clmAccresult1Name != null">clm_accresult_1_name = #{clmAccresult1Name},</if>
            <if test="clmAccresult2Name != null">clm_accresult_2_name = #{clmAccresult2Name},</if>
            <if test="clmMakeDate != null">clm_make_date = #{clmMakeDate},</if>
            <if test="rsPayIntv != null">rs_pay_intv = #{rsPayIntv},</if>
            <if test="calcStatus != null">calc_status = #{calcStatus},</if>
            <if test="calcType != null">calc_type = #{calcType},</if>
            <if test="calcFailCode != null">calc_fail_code = #{calcFailCode},</if>
            <if test="cedeoutType != null">cedeout_type = #{cedeoutType},</if>
            <if test="facultativeWay != null">facultative_way = #{facultativeWay},</if>
            <if test="reNoticeUrl != null">re_notice_url = #{reNoticeUrl},</if>
            <if test="cedeoutCount != null">cedeout_count = #{cedeoutCount},</if>
            <if test="companyCode != null">company_code = #{companyCode},</if>
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="programmeCode != null">programme_code = #{programmeCode},</if>
            <if test="programmeName != null">programme_name = #{programmeName},</if>
            <if test="contractCode != null">contract_code = #{contractCode},</if>
            <if test="contractName != null">contract_name = #{contractName},</if>
            <if test="mainContractCode != null">main_contract_code = #{mainContractCode},</if>
            <if test="mainContractName != null">main_contract_name = #{mainContractName},</if>
            <if test="programmeSelfAmount != null">programme_self_amount = #{programmeSelfAmount},</if>
            <if test="programmeSelfScale != null">programme_self_scale = #{programmeSelfScale},</if>
            <if test="cedeoutMode != null">cedeout_mode = #{cedeoutMode},</if>
            <if test="cedeoutWay != null">cedeout_way = #{cedeoutWay},</if>
            <if test="addupAmountType != null">addup_amount_type = #{addupAmountType},</if>
            <if test="addupRiskCode != null">addup_risk_code = #{addupRiskCode},</if>
            <if test="addupRiskName != null">addup_risk_name = #{addupRiskName},</if>
            <if test="initRiskAmount != null">init_risk_amount = #{initRiskAmount},</if>
            <if test="occupyRiskAmount != null">occupy_risk_amount = #{occupyRiskAmount},</if>
            <if test="releaseRiskAmount != null">release_risk_amount = #{releaseRiskAmount},</if>
            <if test="cedeoutAmount != null">cedeout_amount = #{cedeoutAmount},</if>
            <if test="selfAmount != null"> self_amount = #{selfAmount},</if>
            <if test="acceptCopies != null">accept_copies = #{acceptCopies},</if>
            <if test="cedeoutScale != null">cedeout_scale = #{cedeoutScale},</if>
            <if test="selfScale != null">self_scale = #{selfScale},</if>
            <if test="cedeoutPremium != null">cedeout_premium = #{cedeoutPremium},</if>
            <if test="cedeoutAddPremium != null">cedeout_add_premium = #{cedeoutAddPremium},</if>
            <if test="cedeoutTotalPremium != null">cedeout_total_premium = #{cedeoutTotalPremium},</if>
            <if test="cedeoutCommission != null">cedeout_commission = #{cedeoutCommission},</if>
            <if test="taxRate != null">tax_rate = #{taxRate},</if>
            <if test="addedTax != null">added_tax = #{addedTax},</if>
            <if test="rateCode != null">rate_code = #{rateCode},</if>
            <if test="rateDataId != null">rate_data_id = #{rateDataId},</if>
            <if test="rateDataValue != null">rate_data_value = #{rateDataValue},</if>
            <if test="cedeoutRateDataValue != null">cedeout_rate_data_value = #{cedeoutRateDataValue},</if>
            <if test="comRateCode != null">com_rate_code = #{comRateCode},</if>
            <if test="comRateDataId != null">com_rate_data_id = #{comRateDataId},</if>
            <if test="comRateDataValue != null">com_rate_data_value = #{comRateDataValue},</if>
            <if test="disRateCode != null">dis_rate_code = #{disRateCode},</if>
            <if test="disRateDataId != null">dis_rate_data_id = #{disRateDataId},</if>
            <if test="disRateDataValue != null">dis_rate_data_value = #{disRateDataValue},</if>
            <if test="reservesType != null">reserves_type = #{reservesType},</if>
            <if test="reservesId != null">reserves_id = #{reservesId},</if>
            <if test="reserves != null">reserves = #{reserves},</if>
            <if test="returnStatus != null">return_status = #{returnStatus},</if>
            <if test="returnDate != null">return_date = #{returnDate},</if>
            <if test="returnReason != null">return_reason = #{returnReason},</if>
            <if test="srcOutTradeId != null">src_out_trade_id = #{srcOutTradeId},</if>
            <if test="returnPremium != null">return_premium = #{returnPremium},</if>
            <if test="returnCbPremium != null">return_cb_premium = #{returnCbPremium},</if>
            <if test="returnTotalPremium != null">return_total_premium = #{returnTotalPremium},</if>
            <if test="returnClaimAmount != null">return_claim_amount = #{returnClaimAmount},</if>
            <if test="returnExpiredGold != null">return_expired_gold = #{returnExpiredGold},</if>
            <if test="returnCommission != null">return_commission = #{returnCommission},</if>
            <if test="adjustStatus != null">adjust_status = #{adjustStatus},</if>
            <if test="adjustDate != null">adjust_date = #{adjustDate},</if>
            <if test="adjuster != null">adjuster = #{adjuster},</if>
            <if test="adjustReason != null">adjust_reason = #{adjustReason},</if>
            <if test="adjustBatchNo != null">adjust_batch_no = #{adjustBatchNo},</if>
            <if test="billConfirmStatus != null">bill_confirm_status = #{billConfirmStatus},</if>
            <if test="billConfirmDate != null">bill_confirm_date = #{billConfirmDate},</if>
            <if test="billConfirmer != null">bill_confirmer = #{billConfirmer},</if>
            <if test="billNo != null">bill_no = #{billNo},</if>
            <if test="status != null">status = #{status},</if>
            <if test="backTrackData != null "> back_track_data = #{backTrackData},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDwsReinsuTradeById" parameterType="Long">
        delete from t_dws_reinsu_trade where id = #{id}
    </delete>

    <delete id="deleteDwsReinsuTradeByIds" parameterType="String">
        delete from t_dws_reinsu_trade where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateReleasePreviousDwsReinsuTrade" parameterType="DwsReinsuTradeQuery">
        update t_dws_reinsu_trade
        set return_status = 2, return_date = now(), update_time = #{updateTime}, update_by = #{updateBy}, release_risk_amount = -occupy_risk_amount
        <where>
        	<if test="polNo != null"> and pol_no = #{polNo}</if>
            <if test="busiType != null"> and busi_type = #{busiType}</if>
            <if test="contNo != null"> and cont_no = #{contNo}</if>
            <if test="insuredNo != null"> and insured_no = #{insuredNo}</if>
        	<if test="dataType != null"> and data_type = #{dataType}</if>
        	<if test="rsPayIntv != null"> and rs_pay_intv = #{rsPayIntv}</if>
            <if test="calcStatus != null"> and calc_status = #{calcStatus}</if>
            <if test="returnStatus != null"> and return_status = #{returnStatus}</if>
            <if test="programmeCode != null and programmeCode != ''"> and programme_code = #{programmeCode}</if>
            <if test="params.startDate != null">
                and cont_anniversary &gt;= date_format(#{params.startDate}, '%Y-%m-%d')
            </if>
            <if test="params.endDate != null">
            	and cont_anniversary &lt;= date_format(#{params.endDate}, '%Y-%m-%d')
            </if>
            <if test="params.calcStatus != null and params.calcStatus.size() > 0"> 
            	and calc_status in 
            	<foreach item="itemStatus" collection="params.calcStatus" open="(" separator="," close=")">
		            #{itemStatus}
				</foreach>
            </if>
        </where>
    </update>
    
    <select id="selectWaitCedeoutDwsReinsuTrade" parameterType="DwsReinsuTradeQuery" resultMap="DwsReinsuTradeResult">
        <include refid="selectDwsReinsuTradeVo"/>
        <where>
            <if test="dataType != null"> and data_type = #{dataType}</if>
            <if test="rsPayIntv != null"> and rs_pay_intv = #{rsPayIntv}</if>
            <if test="contType != null"> and cont_type = #{contType}</if>
            <if test="dataCopy != null"> and data_copy = #{dataCopy}</if>
            <if test="calcStatus != null"> and calc_status = #{calcStatus}</if>
            and is_del=0 and status=0 
        </where>
        order by busi_occur_date, risk_vali_date, get_date, get_time, insured_no, addup_risk_code, cont_no, busi_type, risk_code, liability_code
        <if test="params.limit != null">
        	limit #{params.limit}
        </if>
     </select>
     
     <select id="selectOccupyRiskAmountByInsuredNo" parameterType="DwsReinsuTradeQuery" resultType="java.math.BigDecimal">
        select ifnull(sum(ifnull(occupy_risk_amount, 0) + ifnull(release_risk_amount, 0)), 0) as occupy_risk_amount 
        from t_dws_reinsu_trade where is_del = 0 and status = #{status} and (calc_status = #{calcStatus} or calc_status = #{params.calcStatus})
        and insured_no = #{insuredNo} and addup_risk_code = #{addupRiskCode}
     </select>

    <select id="selectAnomalousDataCalcList" parameterType="CedeoutBatchLogQuery" resultType="com.reinsurance.dto.AnomalousDataDTO">
        <include refid="selectDwsReinsuTradeVo"/>
        <where>
            <if test="batchNo != null  and batchNo != ''"> and batch_no = #{batchNo}</if>
            <if test="insuredNo != null  and insuredNo != ''"> and insured_no = #{insuredNo}</if>
            <if test="calcStartTime != null and calcStartTime == null "> and create_time &gt;= #{calcStartTime}</if>
            <if test="calcEndTime != null and calcStartTime == null"> and update_time &lt;= #{calcEndTime}</if>
            <if test="calcEndTime != null and calcStartTime != null"> and create_time &gt;= #{calcStartTime} and update_time &lt;= #{calcEndTime}</if>
            and is_del=0 and status=0 and calc_status = 2 order by id desc
        </where>
    </select>
    
    <resultMap type="DwsReinsuTradeBillDetailDTO" id="DwsReinsuTradeBillDetailResult">
        <result property="id"    column="id"    />
        <result property="companyCode"    column="company_code"    />
        <result property="companyName"    column="company_name"    />
        <result property="accountPeriod"    column="account_period"    />
        <result property="contNo"    column="cont_no"    />
        <result property="polNo"    column="pol_no"    />
        <result property="riskCode"    column="risk_code"    />
        <result property="riskName"    column="risk_name"    />
        <result property="liabilityCode"    column="liability_code"    />
        <result property="liabilityName"    column="liability_name"    />
        <result property="contType"    column="cont_type"    />
        <result property="busiType"    column="busi_type"    />
        <result property="insuredNo"    column="insured_no"    />
        <result property="insuredName"    column="insured_name"    />
        <result property="cedeoutPremium"    column="cedeout_premium"    />
        <result property="cedeoutAddPremium"    column="cedeout_add_premium"    />
        <result property="cedeoutTotalPremium"    column="cedeout_total_premium"    />
        <result property="cedeoutCommission"    column="cedeout_commission"    />
        <result property="returnPremium"    column="return_premium"    />
        <result property="returnCbPremium"    column="return_cb_premium"    />
        <result property="returnTotalPremium"    column="return_total_premium"    />
        <result property="returnClaimAmount"    column="return_claim_amount"    />
        <result property="returnExpiredGold"    column="return_expired_gold"    />
        <result property="returnCommission"    column="return_commission"    />
        <result property="accountDate"    column="account_date"    />
        <result property="billConfirmStatus"    column="bill_confirm_status"    />
        <result property="billConfirmDate"    column="bill_confirm_date"    />
        <result property="billConfirmer"    column="bill_confirmer"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="calcStatus"    column="calc_status"    />
        <result property="adjustStatus"    column="adjust_status"    />
        <result property="adjustDate"    column="adjust_date"    />
        <result property="adjuster"    column="adjuster"    />
        <result property="cedeoutWay"    column="cedeout_way"    />
    </resultMap>
    
    <select id="selectReinsuTradeBillList" parameterType="DwsReinsuTradeBillDetailQuery" resultMap="DwsReinsuTradeBillDetailResult">
        select id, company_code, company_name, account_period, cont_no, pol_no, risk_code, risk_name, liability_code, liability_name, cont_type, busi_type, insured_no, insured_name, 
        cedeout_premium, cedeout_add_premium, cedeout_commission, return_premium, return_cb_premium, return_commission, return_claim_amount, return_expired_gold, account_date, 
        bill_confirm_status, bill_confirm_date, bill_confirmer, remark, status, calc_status, adjust_status, adjust_date, adjuster, cedeout_way from t_dws_reinsu_trade 
        <where>
            <if test="contType != null "> and cont_type = #{contType}</if>
            <if test="contNo != null  and contNo != ''"> and cont_no = #{contNo}</if>
            <if test="polNo != null  and polNo != ''"> and pol_no = #{polNo}</if>
            <if test="insuredNo != null  and insuredNo != ''"> and insured_no = #{insuredNo}</if>
            <if test="insuredName != null  and insuredName != ''"> and insured_name like concat('%', #{insuredName}, '%')</if>
            <if test="calcStatus != null "> and calc_status = #{calcStatus}</if>
            <if test="adjustStatus != null "> and adjust_status = #{adjustStatus}</if>
            <if test="billConfirmStatus != null "> and bill_confirm_status = #{billConfirmStatus}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="params.startAccountDate != null">
			    and account_date &gt;= date_format(#{params.startAccountDate}, '%Y-%m-%d')
			</if>
			<if test="params.endAccountDate != null">
			    and account_date &lt;= date_format(#{params.endAccountDate}, '%Y-%m-%d')
			</if>
        </where>
        and is_del=0 and status=0 order by id desc
    </select>
    
    <update id="updateAdjustReinsuTradeBill" parameterType="DwsReinsuTradeBillDetailDTO">
        update t_dws_reinsu_trade set adjust_batch_no = #{adjustBatchNo}, adjust_status = #{adjustStatus}, adjust_date = #{adjustDate}, 
        adjuster = #{adjuster}, adjust_reason = #{adjustReason}, update_by = #{updateBy}, update_time = #{updateTime},
        <trim prefix="" suffixOverrides=",">
        	<if test="cedeoutPremium != null">cedeout_premium = #{cedeoutPremium},</if>
            <if test="cedeoutAddPremium != null">cedeout_add_premium = #{cedeoutAddPremium},</if>
            <if test="cedeoutTotalPremium != null">cedeout_total_premium = #{cedeoutTotalPremium},</if>
            <if test="cedeoutCommission != null">cedeout_commission = #{cedeoutCommission},</if>
            <if test="returnPremium != null">return_premium = #{returnPremium},</if>
            <if test="returnCbPremium != null">return_cb_premium = #{returnCbPremium},</if>
            <if test="returnTotalPremium != null">return_total_premium = #{returnTotalPremium},</if>
            <if test="returnClaimAmount != null">return_claim_amount = #{returnClaimAmount},</if>
            <if test="returnExpiredGold != null">return_expired_gold = #{returnExpiredGold},</if>
            <if test="returnCommission != null">return_commission = #{returnCommission},</if>
        </trim>
         where id = #{id}
    </update>

    <select id="selectReportDataCount" parameterType="ReInsuranceReportDataQuery" resultType="java.lang.Integer">
        select count(1) from t_dws_reinsu_trade
        <where>
            is_del=0 and status=0
            <if test="accountStartDate != null">
                and account_date &gt;= date_format(#{accountStartDate}, '%Y-%m-%d')
            </if>
            <if test="accountEndDate != null">
                and account_date &lt;= date_format(#{accountEndDate}, '%Y-%m-%d')
            </if>
            <if test="riskCode != null and riskCode != ''">
                and risk_code = #{riskCode}
            </if>
            <if test="dataType != null">
                and data_type = #{dataType}
            </if>
            <if test="busiType != null">
                and busi_type = #{busiType}
            </if>
            <if test="params.busiTypes != null and params.busiTypes.size() > 0"> 
				and busi_type in 
				<foreach item="itemValue" collection="params.busiTypes" open="(" separator="," close=")">
					#{itemValue}
				</foreach>
			</if>
            and calc_status in(1, 3)
        </where>
    </select>
    
    <select id="selectReportDataList" parameterType="ReInsuranceReportDataQuery" resultType="java.util.Map">
        select 
        <foreach item="column" collection="outputColumnList" open="" separator="," close="">
            ${column}
        </foreach>
         from t_dws_reinsu_trade 
        <where>
            is_del=0 and status=0
            <if test="accountStartDate != null">
			    and account_date &gt;= date_format(#{accountStartDate}, '%Y-%m-%d')
			</if>
			<if test="accountEndDate != null">
			    and account_date &lt;= date_format(#{accountEndDate}, '%Y-%m-%d')
			</if>
            <if test="riskCode != null and riskCode != ''">
                and risk_code = #{riskCode}
            </if>
            <if test="dataType != null">
                and data_type = #{dataType}
            </if>
            <if test="busiType != null">
                and busi_type = #{busiType}
            </if>
            <if test="params.busiTypes != null and params.busiTypes.size() > 0"> 
				and busi_type in 
				<foreach item="itemValue" collection="params.busiTypes" open="(" separator="," close=")">
					#{itemValue}
				</foreach>
			</if>
			and calc_status in(1, 3)
        </where>
        <if test="params.startRows != null and params.pageSize != null">order by id limit #{params.startRows}, #{params.pageSize}</if>
    </select>
	
	<select id="selectSummaryReportData" parameterType="ReInsuranceReportDataQuery" resultType="java.util.Map">
        select 
        <foreach item="column" collection="groupColumnList" open="" separator="," close="">
            ${column}
        </foreach>
        <if test="groupColumnList != null and groupColumnList.size() > 0 and summaryColumnList != null and summaryColumnList.size() > 0">,</if>
        <foreach item="column" collection="summaryColumnList" open="" separator="," close="">
            ifnull(sum(${column}), 0) as ${column}
        </foreach>
        from t_dws_reinsu_trade 
        <where>
            is_del=0 and status=0
            <if test="accountStartDate != null">
			    and account_date &gt;= date_format(#{accountStartDate}, '%Y-%m-%d')
			</if>
			<if test="accountEndDate != null">
			    and account_date &lt;= date_format(#{accountEndDate}, '%Y-%m-%d')
			</if>
            <if test="riskCode != null and riskCode != ''">
                and risk_code = #{riskCode}
            </if>
            <if test="dataType != null">
                and data_type = #{dataType}
            </if>
            <if test="busiType != null">
                and busi_type = #{busiType}
            </if>
            <if test="params.busiTypes != null and params.busiTypes.size() > 0"> 
				and busi_type in 
				<foreach item="itemValue" collection="params.busiTypes" open="(" separator="," close=")">
					#{itemValue}
				</foreach>
			</if>
			and calc_status in(1, 3)
        </where>
        <if test="groupColumnList != null and groupColumnList.size() > 0">
        group by
	        <foreach item="column" collection="groupColumnList" open="" separator="," close="">
	            ${column} 
			</foreach>
        </if>
    </select>
    
    <update id="updateCopyDwsReinsuTradeBySrcDwsReinsuTrade">
    	update
			t_dws_reinsu_trade
		set
				 reserves = src.reserves,
				 reserves_id = src.reserves_id,
				 init_risk_amount = src.init_risk_amount,
				 cedeout_amount = src.cedeout_amount,
				 self_amount = src.self_amount,
				 cedeout_scale = src.cedeout_scale,
				 self_scale = src.self_scale
		from
				 (
			select
				  data_group_no,
				  reserves,
				  reserves_id,
				  init_risk_amount,
				  cedeout_amount,
				  self_amount,
				  cedeout_scale,
				  self_scale
			from
				  t_dws_reinsu_trade
			where
				  is_del = 0
				and status = 0
				and data_copy = 0
				and data_type = 0
				and calc_status in(1, 3)
				) as src
		where
		    t_dws_reinsu_trade.data_group_no = src.data_group_no
			and t_dws_reinsu_trade.is_del = 0
			and t_dws_reinsu_trade.status = 0
			and t_dws_reinsu_trade.data_copy = 1
			and t_dws_reinsu_trade.data_type = 0
			and t_dws_reinsu_trade.calc_status = 0
    </update>
    
    <select id="selectWaitReturnOnCedeoutReinsuTrade" parameterType="DwsReinsuTradeQuery" resultMap="DwsReinsuTradeResult">
        <include refid="selectDwsReinsuTradeVo"/> where is_del=0 and status = #{status} and data_type = #{dataType} and cont_no = #{contNo}
       	<if test="rsPayIntv != null "> and rs_pay_intv = #{rsPayIntv}</if>
       	<if test="polNo != null  and polNo != ''"> and pol_no = #{polNo}</if>
       	<if test="insuredNo != null  and insuredNo != ''"> and insured_no = #{insuredNo}</if>
       	<if test="liabilityCode != null and liabilityCode != ''"> and liability_code = #{liabilityCode}</if>
       	<if test="backTrackData != null"> and back_track_data = #{backTrackData}</if>
       	<if test="params.calcStatus != null and params.calcStatus.size() > 0"> 
			and calc_status in 
			<foreach item="itemValue" collection="params.calcStatus" open="(" separator="," close=")">
				#{itemValue}
			</foreach>
		</if>
		
		<if test="params.returnStatus != null and params.returnStatus.size() > 0"> 
			and return_status in 
			<foreach item="itemValue" collection="params.returnStatus" open="(" separator="," close=")">
				#{itemValue}
			</foreach>
		</if>
		<if test="busiOccurDate != null"> and busi_occur_date &lt;= date_format(#{busiOccurDate}, '%Y-%m-%d')</if>
    </select>
    
    <insert id="insertBatchDwsReinsuTrade" useGeneratedKeys="true" keyProperty="id">
        insert into t_dws_reinsu_trade(batch_no, busi_type, data_type, data_copy, data_group_no, busi_occur_date, get_date, get_time, account_period, account_date, 
        cont_type, grp_cont_no, grp_pol_no, cont_no, pol_no, main_pol_no, sale_chnl, sale_chnl_name, sell_type, sell_type_name, sale_com_code, sale_com_name, agent_com_code, agent_com_name,
        manage_com_code, manage_com_name, cont_make_date, sign_date, sign_time, cont_year, cont_month, cont_anniversary, previous_cont_anniversary, cont_app_flag, invalid_state_type, 
        invalid_state_reason, invalid_start_date, risk_code, risk_name, risk_app_flag, risk_vali_date, risk_end_date, sub_risk_flag, risk_period, risk_type3, plan_code, 
        pol_risk_type, get_duty_codes, get_duty_names, liability_code, liability_name, insu_year, insu_year_flag, pay_intv, payend_year, payend_year_flag, risk_free_flag, 
        pay_periods, in_pay_periods, un_pay_periods, un_pay_premium, amount, available_amount, total_premium, base_premium, add_premium, add_scale, pay_to_date, pay_end_date, 
        sum_pay_money, sum_add_money, sum_get_money, cash_value, insuacc_value, appnt_no, appnt_name, appnt_id_type, appnt_id_no, appnt_sex, appnt_birthday, appnt_occ_type, appnt_occ_code, 
        insured_peoples, insured_no, insured_name, insured_id_type, insured_id_no, insured_sex, insured_birthday, insured_occ_type, insured_occ_code, insured_socisec, insured_pass_flag, insured_app_age, 
        edor_accept_no, edor_type, edor_state, edor_get_money, edor_get_interest, edor_app_date, edor_validate, edor_conf_date, edor_conf_time, edor_make_date, clm_no, clm_state, clm_give_type, 
        clm_standpay, clm_realpay, clm_accident_date, clm_acc_date, clm_rpt_date, clm_rgt_date, clm_case_end_date, clm_enter_acc_date, clm_fee_sum, clm_fee_type, clm_bal_type_desc, clm_sub_fee_type, 
        clm_sub_bal_type_desc, clm_defo_grade, clm_defo_grade_name, clm_defo_type, clm_defo_name, clm_hospital_name, clm_in_hospital_date, clm_out_hospital_date, clm_accident_reason, clm_accresult_1, 
        clm_accresult_2, clm_accresult_1_name, clm_accresult_2_name, clm_make_date, rs_pay_intv, back_track_data, calc_status, calc_type, calc_fail_code, cedeout_type, re_notice_url, cedeout_count, company_code, company_name, 
        contract_code, contract_name, main_contract_code, main_contract_name, programme_code, programme_name, programme_self_amount, programme_self_scale, cedeout_mode, cedeout_way, addup_amount_type, 
        addup_risk_code, addup_risk_name, init_risk_amount, occupy_risk_amount, release_risk_amount, cedeout_amount, self_amount, accept_copies, cedeout_scale, self_scale, cedeout_premium, cedeout_add_premium, cedeout_total_premium, 
        cedeout_commission, tax_rate, added_tax, rate_code, rate_data_id, rate_data_value, cedeout_rate_data_value, com_rate_code, com_rate_data_id, com_rate_data_value, dis_rate_code, dis_rate_data_id, dis_rate_data_value, 
        reserves_type, reserves_id, reserves, return_status, return_date, return_reason, src_out_trade_id, return_premium, return_cb_premium, return_total_premium, return_claim_amount, return_expired_gold, return_commission, bill_confirm_status, bill_confirm_date, bill_confirmer) values 
        <foreach collection="dwsReinsuTradeList" item="item" separator=",">
	        (#{item.batchNo}, #{item.busiType}, #{item.dataType}, #{item.dataCopy}, #{item.dataGroupNo}, #{item.busiOccurDate}, #{item.getDate}, #{item.getTime}, #{item.accountPeriod}, #{item.accountDate}, 
	        #{item.contType}, #{item.grpContNo}, #{item.grpPolNo}, #{item.contNo}, #{item.polNo}, #{item.mainPolNo}, #{item.saleChnl}, #{item.saleChnlName}, #{item.sellType}, #{item.sellTypeName}, #{item.saleComCode}, #{item.saleComName}, #{item.agentComCode}, #{item.agentComName},
	        #{item.manageComCode}, #{item.manageComName}, #{item.contMakeDate}, #{item.signDate}, #{item.signTime}, #{item.contYear}, #{item.contMonth}, #{item.contAnniversary}, #{item.previousContAnniversary}, #{item.contAppFlag}, #{item.invalidStateType}, 
	        #{item.invalidStateReason}, #{item.invalidStartDate}, #{item.riskCode}, #{item.riskName}, #{item.riskAppFlag}, #{item.riskValiDate}, #{item.riskEndDate}, #{item.subRiskFlag}, #{item.riskPeriod}, #{item.riskType3}, #{item.planCode}, 
	        #{item.polRiskType}, #{item.getDutyCodes}, #{item.getDutyNames}, #{item.liabilityCode}, #{item.liabilityName}, #{item.insuYear}, #{item.insuYearFlag}, #{item.payIntv}, #{item.payendYear}, #{item.payendYearFlag}, #{item.riskFreeFlag}, 
	        #{item.payPeriods}, #{item.inPayPeriods}, #{item.unPayPeriods}, #{item.unPayPremium}, #{item.amount}, #{item.availableAmount}, #{item.totalPremium}, #{item.basePremium}, #{item.addPremium}, #{item.addScale}, #{item.payToDate}, #{item.payEndDate}, 
	        #{item.sumPayMoney}, #{item.sumAddMoney}, #{item.sumGetMoney}, #{item.cashValue}, #{item.insuaccValue}, #{item.appntNo}, #{item.appntName}, #{item.appntIdType}, #{item.appntIdNo}, #{item.appntSex}, #{item.appntBirthday}, #{item.appntOccType}, #{item.appntOccCode}, 
	        #{item.insuredPeoples}, #{item.insuredNo}, #{item.insuredName}, #{item.insuredIdType}, #{item.insuredIdNo}, #{item.insuredSex}, #{item.insuredBirthday}, #{item.insuredOccType}, #{item.insuredOccCode}, #{item.insuredSocisec}, #{item.insuredPassFlag}, #{item.insuredAppAge}, 
	        #{item.edorAcceptNo}, #{item.edorType}, #{item.edorState}, #{item.edorGetMoney}, #{item.edorGetInterest}, #{item.edorAppDate}, #{item.edorValidate}, #{item.edorConfDate}, #{item.edorConfTime}, #{item.edorMakeDate}, #{item.clmNo}, #{item.clmState}, #{item.clmGiveType}, 
	        #{item.clmStandpay}, #{item.clmRealpay}, #{item.clmAccidentDate}, #{item.clmAccDate}, #{item.clmRptDate}, #{item.clmRgtDate}, #{item.clmCaseEndDate}, #{item.clmEnterAccDate}, #{item.clmFeeSum}, #{item.clmFeeType}, #{item.clmBalTypeDesc}, #{item.clmSubFeeType}, 
	        #{item.clmSubBalTypeDesc}, #{item.clmDefoGrade}, #{item.clmDefoGradeName}, #{item.clmDefoType}, #{item.clmDefoName}, #{item.clmHospitalName}, #{item.clmInHospitalDate}, #{item.clmOutHospitalDate}, #{item.clmAccidentReason}, #{item.clmAccresult1}, 
	        #{item.clmAccresult2}, #{item.clmAccresult1Name}, #{item.clmAccresult2Name}, #{item.clmMakeDate}, #{item.rsPayIntv}, #{item.backTrackData}, #{item.calcStatus}, #{item.calcType}, #{item.calcFailCode}, #{item.cedeoutType}, #{item.reNoticeUrl}, #{item.cedeoutCount}, #{item.companyCode}, #{item.companyName}, 
	        #{item.contractCode}, #{item.contractName}, #{item.mainContractCode}, #{item.mainContractName}, #{item.programmeCode}, #{item.programmeName}, #{item.programmeSelfAmount}, #{item.programmeSelfScale}, #{item.cedeoutMode}, #{item.cedeoutWay}, #{item.addupAmountType}, 
	        #{item.addupRiskCode}, #{item.addupRiskName}, #{item.initRiskAmount}, #{item.occupyRiskAmount}, #{item.releaseRiskAmount}, #{item.cedeoutAmount}, #{item.selfAmount}, #{item.acceptCopies}, #{item.cedeoutScale}, #{item.selfScale}, #{item.cedeoutPremium}, #{item.cedeoutAddPremium}, #{item.cedeoutTotalPremium}, 
	        #{item.cedeoutCommission}, #{item.taxRate}, #{item.addedTax}, #{item.rateCode}, #{item.rateDataId}, #{item.rateDataValue}, #{item.cedeoutRateDataValue}, #{item.comRateCode}, #{item.comRateDataId}, #{item.comRateDataValue}, #{item.disRateCode}, #{item.disRateDataId}, #{item.disRateDataValue}, 
	        #{item.reservesType}, #{item.reservesId}, #{item.reserves}, #{item.returnStatus}, #{item.returnDate}, #{item.returnReason}, #{item.srcOutTradeId}, #{item.returnPremium}, #{item.returnCbPremium}, #{item.returnTotalPremium}, #{item.returnClaimAmount}, #{item.returnExpiredGold}, #{item.returnCommission}, #{item.billConfirmStatus}, #{item.billConfirmDate}, #{item.billConfirmer})
        </foreach>
    </insert>

    <update id="updateCedeoutTradeReturnStatus">
       	update t_dws_reinsu_trade
       	<trim prefix="set" suffixOverrides=",">
        	<if test="returnStatus != null">return_status = #{returnStatus},</if>
        	<if test="returnDate != null">return_date = #{returnDate},</if>
        	<if test="returnReason != null">return_reason = #{returnReason},</if>
        	<if test="returnDate != null">update_time = #{returnDate},</if>
        	<if test="releaseRiskAmount != null">release_risk_amount = #{releaseRiskAmount},</if>
        </trim>
       	where id = #{id}
    </update>
    
    <select id="selectIncludedContractByCommpanyCode" parameterType="DwsReinsuSettleBillQuery" resultType="java.util.Map">
		select main_contract_code as contractCode, main_contract_name as contractName, count(*) as totalRows from t_dws_reinsu_trade where is_del=0 and status=0 and calc_status in (1, 3) 
		and main_contract_code is not null and bill_no is null and company_code=#{companyCode} and account_date &gt;= date_format(#{actualStartDate}, '%Y-%m-%d') 
		and account_date &lt;= date_format(#{actualEndDate}, '%Y-%m-%d') group by main_contract_code, main_contract_name
    </select>
    
    <update id="updateDwsReinsuTradeBillNo" parameterType="DwsReinsuSettleBillEntity">
    	update t_dws_reinsu_trade set bill_no=#{billNo}, update_by=#{updateBy}, update_time=#{updateTime} where is_del=0 and status=0 and calc_status in (1, 3) and bill_no is null 
    	and company_code=#{companyCode} and main_contract_code=#{contractCode} and account_date &gt;= date_format(#{actualStartDate}, '%Y-%m-%d') and account_date &lt;= date_format(#{actualEndDate}, '%Y-%m-%d')
    </update>
    
    <update id="updateDwsReinsuTradeConfirmStatus" parameterType="DwsReinsuSettleBillEntity">
    	update t_dws_reinsu_trade set bill_confirm_status=#{confirmStatus}, bill_confirmer=#{confirmer}, bill_confirm_date=#{confirmDate}, update_by=#{updateBy}, update_time=#{updateTime} where bill_no=#{billNo}
    </update>
    
    <select id="selectDwsReinsuTradeCountByBillNo" resultType="java.lang.Integer">
        select count(*) as totalRows from t_dws_reinsu_trade where is_del=0 and status=0 and bill_no=#{billNo}
    </select>
    
    <select id="selectDwsReinsuTradeListByBillNo" resultType="java.util.Map">
        select <foreach item="column" collection="outColumns" open="" separator="," close="">${column}</foreach> from t_dws_reinsu_trade where is_del=0 and bill_no=#{billNo} order by id limit #{startRows}, #{pageSize}
    </select>
    
    
    <resultMap type="DwsEastZbzdxxbDTO" id="DwsEastZbzdxxbMoneyResult">
    	<result property="settleBillNo"    column="bill_no"    />
	    <result property="fbf"    column="fbf"    />
	    <result property="fbyj"    column="fbyj"    />
	    <result property="thfbf"    column="thfbf"    />
	    <result property="thfbyj"    column="thfbyj"    />
	    <result property="thlpk"    column="thlpk"    />
	    <result property="thmqj"    column="thmqj"    />
	    <result property="totalRows"    column="totalRows"    />
    </resultMap>
    
    <resultMap type="DwsEastZbzdxxbDTO" id="DwsEastZbzdxxbResult">
    	<result property="zdqq"    column="zdqq"    />
	    <result property="zdzq"    column="zdzq"    />
	    <result property="zbxgsdm"    column="zbxgsdm"    />
	    <result property="zbxgsmc"    column="zbxgsmc"    />
	    <result property="xnhtbz"    column="xnhtbz"    />
	    <result property="zbxhthm"    column="zbxhthm"    />
	    <result property="zbxhtmc"    column="zbxhtmc"    />
	    <result property="fbf"    column="fbf"    />
	    <result property="fbyj"    column="fbyj"    />
	    <result property="thfbf"    column="thfbf"    />
	    <result property="thfbyj"    column="thfbyj"    />
	    <result property="thlpk"    column="thlpk"    />
	    <result property="thmqj"    column="thmqj"    />
	    <result property="totalRows"    column="totalRows"    />
    </resultMap>
    
    <select id="selectDwsReinsuTradeMoneyByBillNos" resultMap="DwsEastZbzdxxbMoneyResult">
        select bill_no, count(1) as totalRows, ifnull(sum(cedeout_total_premium), 0) as fbf, ifnull(sum(cedeout_commission), 0) as fbyj, ifnull(sum(return_total_premium), 0) as thfbf, ifnull(sum(return_commission), 0) as thfbyj, 
        ifnull(sum(return_claim_amount), 0) as thlpk, ifnull(sum(return_expired_gold), 0) as thmqj from t_dws_reinsu_trade where is_del=0 and status=0 and calc_status in (1, 3) and bill_no in 
        <foreach item="billNo" collection="billNos" open="(" separator="," close=")">#{billNo}</foreach> group by bill_no
    </select>
    
    <select id="selectDwsReinsuTradeGroupSummaryAsZbzdxxb" resultMap="DwsEastZbzdxxbResult">
        select count(1) as totalRows, date_format(#{params.startDate}, '%Y%m%d') as zdqq, date_format(#{params.endDate}, '%Y%m%d')as zdzq, t.company_code as zbxgsdm, t.company_name as zbxgsmc, t.main_contract_code as zbxhthm, 
        t.main_contract_name as zbxhtmc, (select c.cedeout_type from ${params.mysqlBaseName}.t_cedeout_contract c where c.contract_code=t.main_contract_code) as xnhtbz, ifnull(sum(cedeout_total_premium), 0) as fbf, 
        ifnull(sum(cedeout_commission), 0) as fbyj, ifnull(sum(return_total_premium), 0) as thfbf, ifnull(sum(return_commission), 0) as thfbyj, ifnull(sum(return_claim_amount), 0) as thlpk, ifnull(sum(return_expired_gold), 0) as thmqj 
        from t_dws_reinsu_trade t where t.is_del=0 and t.status=0 and t.calc_status in (1, 3) and t.account_date &gt;= date_format(#{params.startDate}, '%Y-%m-%d') and t.account_date &lt;= date_format(#{params.endDate}, '%Y-%m-%d') 
        group by t.company_code, t.company_name, t.main_contract_code, t.main_contract_name
    </select>
</mapper>