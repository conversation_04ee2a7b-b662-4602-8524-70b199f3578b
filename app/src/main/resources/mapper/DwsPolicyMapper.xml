<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reinsurance.mapper.DwsPolicyMapper">
    <resultMap type="DwsPolicyEntity" id="DwsPolicyResult">
    	<result property="uniqueKey"    column="unique_key"    />
    	<result property="contType"    column="cont_type"    />
    	<result property="grpContNo"    column="grp_cont_no"    />
        <result property="grpPolNo"    column="grp_pol_no"    />
        <result property="contNo"    column="cont_no"    />
        <result property="polNo"    column="pol_no"    />
        <result property="mainPolNo"    column="main_pol_no"    />
        <result property="saleChnl"    column="sale_chnl"    />
        <result property="saleChnlName"    column="sale_chnl_name"    />
        <result property="sellType"    column="sell_type"    />
        <result property="sellTypeName"    column="sell_type_name"    />
        <result property="saleComCode"    column="sale_com_code"    />
        <result property="saleComName"    column="sale_com_name"    />
        <result property="agentComCode"    column="agent_com_code"    />
        <result property="agentComName"    column="agent_com_name"    />
        <result property="manageComCode"    column="manage_com_code"    />
        <result property="manageComName"    column="manage_com_name"    />
        <result property="signDate"    column="sign_date"    />
        <result property="signTime"    column="sign_time"    />
        <result property="riskValiDate"    column="risk_vali_date"    />
        <result property="riskEndDate"    column="risk_end_date"    />
        <result property="contAppFlag"    column="cont_app_flag"    />
        <result property="contMakeDate"    column="cont_make_date"    />
        <result property="contMakeTime"    column="cont_make_time"    />
        <result property="contYear"    column="cont_year"    />
        <result property="contAnniversary"    column="cont_anniversary"    />
        <result property="previousContAnniversary"    column="previous_cont_anniversary"    />
        <result property="riskCode"    column="risk_code"    />
        <result property="riskName"    column="risk_name"    />
        <result property="riskType3"    column="risk_type3"    />
        <result property="planCode"    column="plan_code"    />
        <result property="polRiskType"    column="pol_risk_type"    />
        <result property="riskAppFlag"    column="risk_app_flag"    />
        <result property="subRiskFlag"    column="sub_risk_flag"    />
        <result property="riskPeriod"    column="risk_period"    />
        <result property="dutyCode"    column="duty_code"    />
        <result property="dutyName"    column="duty_name"    />
        <result property="getDutyCode"    column="get_duty_code"    />
        <result property="getDutyName"    column="get_duty_name"    />
        <result property="getDutyState"    column="get_duty_state"    />
        <result property="liabilityCode"    column="liability_code"    />
        <result property="liabilityName"    column="liability_name"    />
        <result property="insuYear"    column="insu_year"    />
        <result property="insuYearFlag"    column="insu_year_flag"    />
        <result property="payIntv"    column="pay_intv"    />
        <result property="payendYear"    column="payend_year"    />
        <result property="payendYearFlag"    column="payend_year_flag"    />
        <result property="payEndDate"    column="pay_end_date"    />
        <result property="payToDate"    column="pay_to_date"    />
        <result property="busiOccurDate"    column="busi_occur_date"    />
        <result property="amount"    column="amount"    />
        <result property="sumPayMoney"    column="sum_pay_money"    />
        <result property="sumAddMoney"    column="sum_add_money"    />
        <result property="sumGetMoney"    column="sum_get_money"    />
        <result property="basePremium"    column="base_premium"    />
        <result property="addPremium"    column="add_premium"    />
        <result property="addScale"    column="add_scale"    />
        <result property="riskFreeFlag"    column="risk_free_flag"    />
        <result property="appntNo"    column="appnt_no"    />
        <result property="appntName"    column="appnt_name"    />
        <result property="appntIdType"    column="appnt_id_type"    />
        <result property="appntIdNo"    column="appnt_id_no"    />
        <result property="appntSex"    column="appnt_sex"    />
        <result property="appntBirthday"    column="appnt_birthday"    />
        <result property="appntOccType"    column="appnt_occ_type"    />
        <result property="appntOccCode"    column="appnt_occ_code"    />
        <result property="insuredPeoples"    column="insured_peoples"    />
        <result property="insuredSequenceNo"    column="insured_sequence_no"    />
        <result property="mainInsuredNo"    column="main_insured_no"    />
        <result property="insuredAppAge"    column="insured_app_age"    />
        <result property="insuredNo"    column="insured_no"    />
        <result property="insuredName"    column="insured_name"    />
        <result property="insuredIdType"    column="insured_id_type"    />
        <result property="insuredIdNo"    column="insured_id_no"    />
        <result property="insuredSex"    column="insured_sex"    />
        <result property="insuredBirthday"    column="insured_birthday"    />
        <result property="insuredOccType"    column="insured_occ_type"    />
        <result property="insuredOccCode"    column="insured_occ_code"    />
        <result property="insuredPassFlag"    column="insured_pass_flag"    />
        <result property="cedeoutType"    column="cedeout_type"    />
        <result property="coreConclusion"    column="core_conclusion"    />
        <result property="reinsuAddScale"    column="reinsu_add_scale"    />
        <result property="cedeoutCount"    column="cedeout_count"    />
        <result property="insuaccValue"    column="insuacc_value"    />
        <result property="cashValue"    column="cash_value"    />
    </resultMap>

    <sql id="selectDwsPolicyVo">
        select unique_key, cont_type, grp_cont_no, grp_pol_no, cont_no, pol_no, main_pol_no, sale_chnl, sale_chnl_name, sell_type, sell_type_name, sale_com_code, sale_com_name, agent_com_code, agent_com_name, manage_com_code, manage_com_name, sign_date, sign_time, risk_vali_date, risk_end_date, cont_make_date, cont_make_time, cont_year, cont_anniversary, previous_cont_anniversary, cont_app_flag, risk_code, risk_name, risk_type3, plan_code, pol_risk_type, risk_app_flag, sub_risk_flag, risk_period, duty_code, duty_name, get_duty_code, get_duty_name, get_duty_state, liability_code, liability_name, insu_year, insu_year_flag, pay_intv, payend_year, payend_year_flag, pay_end_date, pay_to_date, busi_occur_date, amount, sum_pay_money, sum_add_money, sum_get_money, base_premium, add_premium, add_scale, risk_free_flag, appnt_no, appnt_name, appnt_id_type, appnt_id_no, appnt_sex, appnt_birthday, appnt_occ_type, appnt_occ_code, insured_peoples, main_insured_no, insured_sequence_no, insured_app_age, insured_no, insured_name, insured_id_type, insured_id_no, insured_sex, insured_birthday, insured_occ_type, insured_occ_code, insured_pass_flag, cedeout_type, core_conclusion, reinsu_add_scale, cedeout_count, insuacc_value, cash_value from v_dws_policy
    </sql>
	
	<select id="selectOneDwsPolicy" parameterType="DwsPolicyEntity" resultMap="DwsPolicyResult">
        <include refid="selectDwsPolicyVo"/>where pol_no=#{polNo} and cont_year=#{contYear} limit 1
    </select>
    
    <select id="selectOneDwsPolicyFormDwsContLiability" parameterType="DwsPolicyEntity" resultMap="DwsPolicyResult">
        select *, null as liability_code, null as liability_name from v_dws_cont_liability where pol_no=#{polNo} and cont_year=#{contYear} limit 1
    </select>
    
    <select id="selectDwsPolicyList" parameterType="DwsPolicyEntity" resultMap="DwsPolicyResult">
        <include refid="selectDwsPolicyVo"/>
        <where>
        	(insured_peoples=1 or (insured_peoples=2 and main_insured_no != insured_no))
        	<if test="contType != null"> and cont_type = #{contType}</if>
        	<if test="grpContNo != null  and grpContNo != ''"> and grp_cont_no = #{grpContNo}</if>
            <if test="grpPolNo != null  and grpPolNo != ''"> and grp_pol_no = #{grpPolNo}</if>
            <if test="contNo != null  and contNo != ''"> and cont_no = #{contNo}</if>
            <if test="polNo != null  and polNo != ''"> and pol_no = #{polNo}</if>
            <if test="mainPolNo != null  and mainPolNo != ''"> and main_pol_no = #{mainPolNo}</if>
            <if test="saleChnl != null  and saleChnl != ''"> and sale_chnl = #{saleChnl}</if>
            <if test="saleChnlName != null  and saleChnlName != ''"> and sale_chnl_name = #{saleChnlName}</if>
            <if test="sellType != null  and sellType != ''"> and sell_type = #{sellType}</if>
            <if test="sellTypeName != null  and sellTypeName != ''"> and sell_type_name = #{sellTypeName}</if>
            <if test="saleComCode != null  and saleComCode != ''"> and sale_com_code = #{saleComCode}</if>
            <if test="saleComName != null  and saleComName != ''"> and sale_com_name like concat('%', #{saleComName}, '%')</if>
            <if test="agentComCode != null  and agentComCode != ''"> and agent_com_code = #{agentComCode}</if>
            <if test="agentComName != null  and agentComName != ''"> and agent_com_name like concat('%', #{agentComName}, '%')</if>
            <if test="manageComCode != null  and manageComCode != ''"> and manage_com_code = #{manageComCode}</if>
            <if test="manageComName != null  and manageComName != ''"> and manage_com_name like concat('%', #{manageComName}, '%')</if>
            <if test="signDate != null "> and sign_date = #{signDate}</if>
            <if test="signTime != null  and signTime != ''"> and sign_time = #{signTime}</if>
            <if test="riskValiDate != null "> and risk_vali_date = #{riskValiDate}</if>
            <if test="riskEndDate != null "> and risk_end_date = #{riskEndDate}</if>
            <if test="contMakeDate != null "> and cont_make_date = #{contMakeDate}</if>
            <if test="contMakeTime != null  and contMakeTime != ''"> and cont_make_time = #{contMakeTime}</if>
            <if test="contYear != null "> and cont_year = #{contYear}</if>
            <if test="contAnniversary != null "> and cont_anniversary = #{contAnniversary}</if>
            <if test="previousContAnniversary != null "> and previous_cont_anniversary = #{previousContAnniversary}</if>
            <if test="contAppFlag != null  and contAppFlag != ''"> and cont_app_flag = #{contAppFlag}</if>
            <if test="riskCode != null  and riskCode != ''"> and risk_code = #{riskCode}</if>
            <if test="riskName != null  and riskName != ''"> and risk_name like concat('%', #{riskName}, '%')</if>
            <if test="riskType3 != null  and riskType3 != ''"> and risk_type3 = #{riskType3}</if>
            <if test="planCode != null  and planCode != ''"> and plan_code = #{planCode}</if>
            <if test="polRiskType != null and polRiskType != ''"> and pol_risk_type = #{polRiskType}</if>
            <if test="riskAppFlag != null  and riskAppFlag != ''"> and risk_app_flag = #{riskAppFlag}</if>
            <if test="subRiskFlag != null  and subRiskFlag != ''"> and sub_risk_flag = #{subRiskFlag}</if>
            <if test="riskPeriod != null  and riskPeriod != ''"> and risk_period = #{riskPeriod}</if>
            <if test="dutyCode != null  and dutyCode != ''"> and duty_code = #{dutyCode}</if>
            <if test="dutyName != null  and dutyName != ''"> and duty_name like concat('%', #{dutyName}, '%')</if>
            <if test="getDutyCode != null  and getDutyCode != ''"> and get_duty_code = #{getDutyCode}</if>
            <if test="getDutyName != null  and getDutyName != ''"> and get_duty_name like concat('%', #{getDutyName}, '%')</if>
            <if test="getDutyState != null  and getDutyState != ''"> and get_duty_state = #{getDutyState}</if>
            <if test="liabilityCode != null  and liabilityCode != ''"> and liability_code = #{liabilityCode}</if>
            <if test="liabilityName != null  and liabilityName != ''"> and liability_name like concat('%', #{liabilityName}, '%')</if>
            <if test="insuYear != null "> and insu_year = #{insuYear}</if>
            <if test="insuYearFlag != null  and insuYearFlag != ''"> and insu_year_flag = #{insuYearFlag}</if>
            <if test="payIntv != null "> and pay_intv = #{payIntv}</if>
            <if test="payendYear != null "> and payend_year = #{payendYear}</if>
            <if test="payendYearFlag != null  and payendYearFlag != ''"> and payend_year_flag = #{payendYearFlag}</if>
            <if test="payEndDate != null "> and pay_end_date = #{payEndDate}</if>
            <if test="payToDate != null "> and pay_to_date = #{payToDate}</if>
            <if test="busiOccurDate != null "> and busi_occur_date = #{busiOccurDate}</if>
            <if test="amount != null "> and amount = #{amount}</if>
            <if test="sumPayMoney != null "> and sum_pay_money = #{sumPayMoney}</if>
            <if test="sumAddMoney != null "> and sum_add_money = #{sumAddMoney}</if>
            <if test="sumGetMoney != null "> and sum_get_money = #{sumGetMoney}</if>
            <if test="basePremium != null "> and base_premium = #{basePremium}</if>
            <if test="addPremium != null "> and add_premium = #{addPremium}</if>
            <if test="addScale != null "> and add_scale = #{addScale}</if>
            <if test="riskFreeFlag != null  and riskFreeFlag != ''"> and risk_free_flag = #{riskFreeFlag}</if>
            <if test="appntNo != null  and appntNo != ''"> and appnt_no = #{appntNo}</if>
            <if test="appntName != null  and appntName != ''"> and appnt_name like concat('%', #{appntName}, '%')</if>
            <if test="appntIdType != null  and appntIdType != ''"> and appnt_id_type = #{appntIdType}</if>
            <if test="appntIdNo != null  and appntIdNo != ''"> and appnt_id_no = #{appntIdNo}</if>
            <if test="appntSex != null  and appntSex != ''"> and appnt_sex = #{appntSex}</if>
            <if test="appntBirthday != null "> and appnt_birthday = #{appntBirthday}</if>
            <if test="appntOccType != null  and appntOccType != ''"> and appnt_occ_type = #{appntOccType}</if>
            <if test="appntOccCode != null  and appntOccCode != ''"> and appnt_occ_code = #{appntOccCode}</if>
            <if test="insuredPeoples != null "> and insured_peoples = #{insuredPeoples}</if>
            <if test="insuredAppAge != null "> and insured_app_age = #{insuredAppAge}</if>
            <if test="insuredNo != null  and insuredNo != ''"> and insured_no = #{insuredNo}</if>
            <if test="insuredName != null  and insuredName != ''"> and insured_name like concat('%', #{insuredName}, '%')</if>
            <if test="insuredIdType != null  and insuredIdType != ''"> and insured_id_type = #{insuredIdType}</if>
            <if test="insuredIdNo != null  and insuredIdNo != ''"> and insured_id_no = #{insuredIdNo}</if>
            <if test="insuredSex != null  and insuredSex != ''"> and insured_sex = #{insuredSex}</if>
            <if test="insuredBirthday != null "> and insured_birthday = #{insuredBirthday}</if>
            <if test="insuredOccType != null  and insuredOccType != ''"> and insured_occ_type = #{insuredOccType}</if>
            <if test="insuredOccCode != null  and insuredOccCode != ''"> and insured_occ_code = #{insuredOccCode}</if>
            <if test="insuredPassFlag != null  and insuredPassFlag != ''"> and insured_pass_flag = #{insuredPassFlag}</if>
            <if test="cedeoutType != null"> and cedeout_type = #{cedeoutType}</if>
            <if test="coreConclusion != null and coreConclusion != ''"> and core_conclusion = #{coreConclusion}</if>
            <if test="reinsuAddScale != null"> and reinsu_add_scale = #{reinsuAddScale}</if>
            <if test="cedeoutCount != null "> and cedeout_count = #{cedeoutCount}</if>
            <if test="insuaccValue != null "> and insuacc_value = #{insuaccValue}</if>
            <if test="cashValue != null "> and cash_value = #{cashValue}</if>
        </where>
        order by cont_make_date, cont_make_time
    </select>

    <select id="selectDwsPolicyNotCedeoutList" parameterType="DwsReinsuTradeQuery" resultMap="DwsPolicyResult">
        select v.* from v_dws_cont_liability v where not exists (select 1 from t_dws_reinsu_trade d where d.pol_no=v.pol_no)
        <where>
            <if test="insuredNo != null  and insuredNo != ''"> and v.insured_no = #{insuredNo}</if>
            <if test="contNo != null  and contNo != ''"> and v.cont_no = #{contNo}</if>
            <if test="insuredIdNo != null  and insuredIdNo != ''"> and v.insured_id_no = #{insuredIdNo}</if>
            <if test="busiOccurDate != null "> and v.busi_occur_date = #{busiOccurDate}</if>
        </where>
    </select>
    
</mapper>