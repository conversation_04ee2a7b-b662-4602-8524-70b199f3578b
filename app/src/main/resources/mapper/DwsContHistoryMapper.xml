<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reinsurance.mapper.DwsContHistoryMapper">

    <resultMap type="DwsContHistoryEntity" id="DwsContHistoryResult">
        <result property="id"    column="id"    />
        <result property="batchNo"    column="batch_no"    />
        <result property="batchDate"    column="batch_date"    />
        <result property="uniqueKey"    column="unique_key"    />
        <result property="busiType"    column="busi_type"    />
        <result property="busiOccurTime"    column="busi_occur_time"    />
        <result property="busiOccurDate"    column="busi_occur_date"    />
        <result property="contType"    column="cont_type"    />
        <result property="grpContNo"    column="grp_cont_no"    />
        <result property="grpPolNo"    column="grp_pol_no"    />
        <result property="contNo"    column="cont_no"    />
        <result property="polNo"    column="pol_no"    />
        <result property="mainPolNo"    column="main_pol_no"    />
        <result property="saleChnl"    column="sale_chnl"    />
        <result property="saleChnlName"    column="sale_chnl_name"    />
        <result property="sellType"    column="sell_type"    />
        <result property="sellTypeName"    column="sell_type_name"    />
        <result property="saleComCode"    column="sale_com_code"    />
        <result property="saleComName"    column="sale_com_name"    />
        <result property="agentComCode"    column="agent_com_code"    />
        <result property="agentComName"    column="agent_com_name"    />
        <result property="manageComCode"    column="manage_com_code"    />
        <result property="manageComName"    column="manage_com_name"    />
        <result property="bankBranchName"    column="bank_branch_name"    />
        <result property="signDate"    column="sign_date"    />
        <result property="signTime"    column="sign_time"    />
        <result property="riskValiDate"    column="risk_vali_date"    />
        <result property="riskEndDate"    column="risk_end_date"    />
        <result property="contMakeDate"    column="cont_make_date"    />
        <result property="contMakeTime"    column="cont_make_time"    />
        <result property="contYear"    column="cont_year"    />
        <result property="contAnniversary"    column="cont_anniversary"    />
        <result property="previousContAnniversary"    column="previous_cont_anniversary"    />
        <result property="contAppFlag"    column="cont_app_flag"    />
        <result property="riskCode"    column="risk_code"    />
        <result property="riskName"    column="risk_name"    />
        <result property="riskType3"    column="risk_type3"    />
        <result property="planCode"    column="plan_code"    />
        <result property="polRiskType"    column="pol_risk_type"    />
        <result property="riskAppFlag"    column="risk_app_flag"    />
        <result property="subRiskFlag"    column="sub_risk_flag"    />
        <result property="riskPeriod"    column="risk_period"    />
        <result property="insuYear"    column="insu_year"    />
        <result property="insuYearFlag"    column="insu_year_flag"    />
        <result property="payIntv"    column="pay_intv"    />
        <result property="payendYear"    column="payend_year"    />
        <result property="payendYearFlag"    column="payend_year_flag"    />
        <result property="payToDate"    column="pay_to_date"    />
        <result property="payEndDate"    column="pay_end_date"    />
        <result property="dutyCode"    column="duty_code"    />
        <result property="dutyName"    column="duty_name"    />
        <result property="getDutyCode"    column="get_duty_code"    />
        <result property="getDutyName"    column="get_duty_name"    />
        <result property="getDutyState"    column="get_duty_state"    />
        <result property="amount"    column="amount"    />
        <result property="sumPayMoney"    column="sum_pay_money"    />
        <result property="sumAddMoney"    column="sum_add_money"    />
        <result property="sumGetMoney"    column="sum_get_money"    />
        <result property="basePremium"    column="base_premium"    />
        <result property="addPremium"    column="add_premium"    />
        <result property="addScale"    column="add_scale"    />
        <result property="riskFreeFlag"    column="risk_free_flag"    />
        <result property="appntNo"    column="appnt_no"    />
        <result property="appntName"    column="appnt_name"    />
        <result property="appntIdType"    column="appnt_id_type"    />
        <result property="appntIdNo"    column="appnt_id_no"    />
        <result property="appntSex"    column="appnt_sex"    />
        <result property="appntBirthday"    column="appnt_birthday"    />
        <result property="appntOccType"    column="appnt_occ_type"    />
        <result property="appntOccCode"    column="appnt_occ_code"    />
        <result property="insuredPeoples"    column="insured_peoples"    />
        <result property="insuredSequenceNo"    column="insured_sequence_no"    />
        <result property="mainInsuredNo"    column="main_insured_no"    />
        <result property="insuredAppAge"    column="insured_app_age"    />
        <result property="insuredNo"    column="insured_no"    />
        <result property="insuredName"    column="insured_name"    />
        <result property="insuredIdType"    column="insured_id_type"    />
        <result property="insuredIdNo"    column="insured_id_no"    />
        <result property="insuredSex"    column="insured_sex"    />
        <result property="insuredBirthday"    column="insured_birthday"    />
        <result property="insuredOccType"    column="insured_occ_type"    />
        <result property="insuredOccCode"    column="insured_occ_code"    />
        <result property="insuredPassFlag"    column="insured_pass_flag"    />
        <result property="cedeoutType"    column="cedeout_type"    />
        <result property="coreConclusion"    column="core_conclusion"    />
        <result property="reinsuAddScale"    column="reinsu_add_scale"    />
        <result property="cedeoutCount"    column="cedeout_count"    />
        <result property="insuaccValue"    column="insuacc_value"    />
        <result property="cashValue"    column="cash_value"    />
        <result property="edorAcceptNo"    column="edor_accept_no"    />
        <result property="edorNo"    column="edor_no"    />
        <result property="edorType"    column="edor_type"    />
        <result property="edorState"    column="edor_state"    />
        <result property="edorAppType"    column="edor_app_type"    />
        <result property="edorMakeDate"    column="edor_make_date"    />
        <result property="edorMakeTime"    column="edor_make_time"    />
        <result property="edorGetMoney"    column="edor_get_money"    />
        <result property="edorGetInterest"    column="edor_get_interest"    />
        <result property="edorAppDate"    column="edor_app_date"    />
        <result property="edorValidate"    column="edor_validate"    />
        <result property="edorConfDate"    column="edor_conf_date"    />
        <result property="edorConfTime"    column="edor_conf_time"    />
        <result property="edorAppName"    column="edor_app_name"    />
        <result property="edorStandbyFlag1"    column="edor_standby_flag1"    />
        <result property="edorStandbyFlag2"    column="edor_standby_flag2"    />
        <result property="edorStandbyFlag3"    column="edor_standby_flag3"    />
        <result property="edorStandbyFlag4"    column="edor_standby_flag4"    />
        <result property="edorStandbyFlag5"    column="edor_standby_flag5"    />
        <result property="clmNo"    column="clm_no"    />
        <result property="clmRgtNo"    column="clm_rgt_no"    />
        <result property="clmCaseNo"    column="clm_case_no"    />
        <result property="clmState"    column="clm_state"    />
        <result property="clmStandpay"    column="clm_standpay"    />
        <result property="clmBeforepay"    column="clm_beforepay"    />
        <result property="clmBalancepay"    column="clm_balancepay"    />
        <result property="clmRealpay"    column="clm_realpay"    />
        <result property="clmGiveType"    column="clm_give_type"    />
        <result property="clmGiveTypeDesc"    column="clm_give_type_desc"    />
        <result property="clmAccDate"    column="clm_acc_date"    />
        <result property="clmRptDate"    column="clm_rpt_date"    />
        <result property="clmRgtDate"    column="clm_rgt_date"    />
        <result property="clmCaseEndDate"    column="clm_case_end_date"    />
        <result property="clmEnterAccDate"    column="clm_enter_acc_date"    />
        <result property="clmFeeSum"    column="clm_fee_sum"    />
        <result property="clmFeeFinaType"    column="clm_fee_fina_type"    />
        <result property="clmAccidentReason"    column="clm_accident_reason"    />
        <result property="clmAccresult1"    column="clm_accresult_1"    />
        <result property="clmAccresult2"    column="clm_accresult_2"    />
        <result property="clmAccresult1Name"    column="clm_accresult_1_name"    />
        <result property="clmAccresult2Name"    column="clm_accresult_2_name"    />
        <result property="clmDefoType"    column="clm_defo_type"    />
        <result property="clmDefoGrade"    column="clm_defo_grade"    />
        <result property="clmDefoName"    column="clm_defo_name"    />
        <result property="clmDefoGradeName"    column="clm_defo_grade_name"    />
        <result property="clmFeeType"    column="clm_fee_type"    />
        <result property="clmBalTypeDesc"    column="clm_bal_type_desc"    />
        <result property="clmSubFeeType"    column="clm_sub_fee_type"    />
        <result property="clmSubBalTypeDesc"    column="clm_sub_bal_type_desc"    />
        <result property="clmHospitalCode"    column="clm_hospital_code"    />
        <result property="clmHospitalName"    column="clm_hospital_name"    />
        <result property="clmInHospitalDate"    column="clm_in_hospital_date"    />
        <result property="clmOutHospitalDate"    column="clm_out_hospital_date"    />
        <result property="clmMakeDate"    column="clm_make_date"    />
        <result property="clmMakeTime"    column="clm_make_time"    />
        <result property="stateType"    column="state_type"    />
        <result property="stateState"    column="state_state"    />
        <result property="stateReason"    column="state_reason"    />
        <result property="stateStartDate"    column="state_start_date"    />
        <result property="stateEndDate"    column="state_end_date"    />
        <result property="stateMakeDate"    column="state_make_date"    />
        <result property="stateMakeTime"    column="state_make_time"    />
        <result property="subTrackStatus"    column="sub_track_status"    />
        <result property="handleStatus"    column="handle_status"    />
        <result property="handleDate"    column="handle_date"    />
        <result property="handleErrorNum"    column="handle_error_num"    />
        <result property="handleErrorMsg"    column="handle_error_msg"    />
        <result property="liabilityCode"    column="liability_code"    />
        <result property="liabilityName"    column="liability_name"    />
        <result property="programmeCode"    column="programme_code"    />
        <result property="contMonth"    column="cont_month"    />
    </resultMap>

	<insert id="insertBatchDwsContHistoryFromCont">
		insert into t_dws_cont_history(batch_no, batch_date, busi_type, unique_key, busi_occur_time, busi_occur_date, cont_type, grp_cont_no, grp_pol_no, cont_no, pol_no, main_pol_no, sale_chnl, sell_type, sale_chnl_name, sell_type_name, sale_com_code, sale_com_name, manage_com_code, manage_com_name, agent_com_code, agent_com_name, bank_branch_name, sign_date, sign_time, risk_vali_date, risk_end_date, cont_make_date, cont_make_time, cont_year, cont_anniversary, previous_cont_anniversary, cont_app_flag, risk_code, risk_name, risk_type3, plan_code, pol_risk_type, risk_app_flag, sub_risk_flag, risk_period, insu_year, insu_year_flag, pay_intv, payend_year, payend_year_flag, pay_to_date, pay_end_date, duty_code, duty_name, get_duty_code, get_duty_name, get_duty_state, amount, sum_pay_money, sum_add_money, sum_get_money, base_premium, add_premium, add_scale, risk_free_flag, appnt_no, appnt_name, appnt_id_type, appnt_id_no, appnt_sex, appnt_birthday, appnt_occ_type, appnt_occ_code, main_insured_no, insured_sequence_no, insured_peoples, insured_app_age, insured_no, insured_name, insured_id_type, insured_id_no, insured_sex, insured_birthday, insured_occ_type, insured_occ_code, insured_pass_flag, cedeout_type, core_conclusion, reinsu_add_scale, insuacc_value, cash_value, create_time, update_time)
		select #{batchNo}, #{batchDate}, if(cont_year>1, 1, 0) as busi_type, unique_key, busi_occur_time, busi_occur_date, cont_type, grp_cont_no, grp_pol_no, cont_no, pol_no, main_pol_no, sale_chnl, sell_type, sale_chnl_name, sell_type_name, sale_com_code, sale_com_name, manage_com_code, manage_com_name, agent_com_code, agent_com_name, bank_branch_name, sign_date, sign_time, risk_vali_date, risk_end_date, cont_make_date, cont_make_time, cont_year, cont_anniversary, previous_cont_anniversary, cont_app_flag, risk_code, risk_name, risk_type3, plan_code, pol_risk_type, risk_app_flag, sub_risk_flag, risk_period, insu_year, insu_year_flag, pay_intv, payend_year, payend_year_flag, pay_to_date, pay_end_date, duty_code, duty_name, get_duty_code, get_duty_name, get_duty_state, amount, sum_pay_money, sum_add_money, sum_get_money, base_premium, add_premium, add_scale, risk_free_flag, appnt_no, appnt_name, appnt_id_type, appnt_id_no, appnt_sex, appnt_birthday, appnt_occ_type, appnt_occ_code, main_insured_no, insured_sequence_no, insured_peoples, insured_app_age, insured_no, insured_name, insured_id_type, insured_id_no, insured_sex, insured_birthday, insured_occ_type, insured_occ_code, insured_pass_flag, cedeout_type, core_conclusion, reinsu_add_scale, insuacc_value, cash_value, now(), now()
		from v_dws_cont_liability vdcl where not exists(select 1 from t_dws_cont_history tdch where tdch.busi_type in (0, 1) and tdch.unique_key=vdcl.unique_key) and vdcl.busi_occur_date &gt;= #{trackStartDate} and vdcl.busi_occur_date &lt;= #{trackEndDate}
	</insert>

	<insert id="insertBatchDwsContHistoryFromEdor">
		insert into t_dws_cont_history(batch_no, batch_date, busi_type, unique_key, busi_occur_time, busi_occur_date, cont_no, pol_no, insured_no, edor_accept_no, edor_no, edor_type, edor_state, edor_app_type, edor_make_date, edor_make_time, edor_get_money, edor_get_interest, edor_app_date, edor_validate, edor_conf_date, edor_conf_time, edor_app_name, edor_standby_flag1, edor_standby_flag2, edor_standby_flag3, edor_standby_flag4, edor_standby_flag5, sub_track_status, create_time, update_time)
		select #{batchNo}, #{batchDate}, 2 as busi_type, unique_key, busi_occur_time, busi_occur_date, cont_no, pol_no, insured_no, edor_accept_no, edor_no, edor_type, edor_state, edor_app_type, edor_make_date, edor_make_time, edor_get_money, edor_get_interest, edor_app_date, edor_validate, edor_conf_date, edor_conf_time, edor_app_name, edor_standby_flag1, edor_standby_flag2, edor_standby_flag3, edor_standby_flag4, edor_standby_flag5,
		(case when edor_type='AI' or edor_type='RE' or edor_type='NS' or edor_type='RB' then '0' else '1' end) as sub_track_status, now(), now() 
		from v_dws_edor vde where not exists(select 1 from t_dws_cont_history tdch where tdch.busi_type=2 and tdch.unique_key=vde.unique_key) and vde.busi_occur_date &gt;= #{trackStartDate} and vde.busi_occur_date &lt;= #{trackEndDate}
		<if test="edorTypes != null and edorTypes.size > 0">
			and vde.edor_type in
			<foreach item="edorType" collection="edorTypes" open="(" separator="," close=")">
                 #{edorType}
            </foreach>
		</if>
	</insert>

	<insert id="insertBatchDwsContHistoryFromClaim">
		insert into t_dws_cont_history(batch_no, batch_date, busi_type, unique_key, busi_occur_time, busi_occur_date, cont_no, pol_no, insured_no, risk_code, risk_name, sub_risk_flag, risk_period, duty_code, duty_name, get_duty_code, get_duty_name, clm_no, clm_rgt_no, clm_case_no, clm_state, clm_standpay, clm_beforepay, clm_balancepay, clm_realpay, clm_give_type, clm_give_type_desc, clm_acc_date, clm_rpt_date, clm_rgt_date, clm_case_end_date, clm_enter_acc_date, clm_accident_date, clm_fee_fina_type, clm_accident_reason, clm_fee_sum, clm_accresult_1, clm_accresult_1_name, clm_accresult_2, clm_accresult_2_name, clm_defo_type, clm_defo_name, clm_defo_grade, clm_defo_grade_name, clm_fee_type, clm_bal_type_desc, clm_sub_fee_type, clm_sub_bal_type_desc, clm_hospital_code, clm_hospital_name, clm_in_hospital_date, clm_out_hospital_date, clm_make_date, clm_make_time, create_time, update_time)
		select #{batchNo}, #{batchDate}, 3 as busi_type, unique_key, busi_occur_time, busi_occur_date, cont_no, pol_no, insured_no, risk_code, risk_name, sub_risk_flag, risk_period, duty_code, duty_name, get_duty_code, get_duty_name, clm_no, clm_rgt_no, clm_case_no, clm_state, clm_standpay, clm_beforepay, clm_balancepay, clm_realpay, clm_give_type, clm_give_type_desc, clm_acc_date, clm_rpt_date, clm_rgt_date, clm_case_end_date, clm_enter_acc_date, clm_accident_date, clm_fee_fina_type, clm_accident_reason, clm_fee_sum, clm_accresult_1, clm_accresult_1_name, clm_accresult_2, clm_accresult_2_name, clm_defo_type, clm_defo_name, clm_defo_grade, clm_defo_grade_name, clm_fee_type, clm_bal_type_desc, clm_sub_fee_type, clm_sub_bal_type_desc, clm_hospital_code, clm_hospital_name, clm_in_hospital_date, clm_out_hospital_date, clm_make_date, clm_make_time, now(), now() 
		from v_dws_claim vdc where not exists(select 1 from t_dws_cont_history tdch where tdch.busi_type=3 and tdch.unique_key=vdc.unique_key) and vdc.busi_occur_date &gt;= #{trackStartDate} and vdc.busi_occur_date &lt;= #{trackEndDate}
	</insert>

	<insert id="insertBatchDwsContHistoryFromExpired">
		insert into t_dws_cont_history(batch_no, batch_date, busi_type, unique_key, busi_occur_time, busi_occur_date, cont_no, pol_no, insured_no, state_type, state_state, state_reason, state_start_date, state_end_date, state_make_date, state_make_time, create_time, update_time)
		select #{batchNo}, #{batchDate}, 4 as busi_type, unique_key, busi_occur_time, busi_occur_date, cont_no, pol_no, insured_no, state_type, state_state, state_reason, state_start_date, state_end_date, state_make_date, state_make_time, now(), now()  
		from v_dws_policy_state vdps where not exists(select 1 from t_dws_cont_history tdch where tdch.busi_type=4 and tdch.unique_key=vdps.unique_key) and state_type='Terminate' and state_reason='01' and vdps.busi_occur_date &gt;= #{trackStartDate} and vdps.busi_occur_date &lt;= #{trackEndDate}
	</insert>

	<insert id="insertBatchDwsContHistoryFromInvalid">
		insert into t_dws_cont_history(batch_no, batch_date, busi_type, unique_key, busi_occur_time, busi_occur_date, cont_no, pol_no, insured_no, state_type, state_state, state_reason, state_start_date, state_end_date, state_make_date, state_make_time, create_time, update_time)
		select #{batchNo}, #{batchDate}, 5 as busi_type, unique_key, busi_occur_time, busi_occur_date, cont_no, pol_no, insured_no, state_type, state_state, state_reason, state_start_date, state_end_date, state_make_date, state_make_time, now(), now() 
		from v_dws_policy_state vdps where not exists(select 1 from t_dws_cont_history tdch where tdch.busi_type=5 and tdch.unique_key=vdps.unique_key) and ((state_type='Terminate' and state_reason!='01') or state_type='Available') and vdps.busi_occur_date &gt;= #{trackStartDate} and vdps.busi_occur_date &lt;= #{trackEndDate}
	</insert>
    
    <select id="selectWiatSubDataHistoryCount" resultType="java.lang.Integer">
		select count(*) from t_dws_cont_history where busi_type=#{busiType} and sub_track_status=#{subTrackStatus}
	</select>
	
    <select id="selectWiatSubDataHistoryList" parameterType="DwsContHistoryEntity" resultMap="DwsContHistoryResult">
		select id, busi_type, unique_key, busi_occur_time, busi_occur_date, cont_no, pol_no, insured_no, edor_accept_no, edor_no, edor_type, edor_state, edor_app_type, edor_make_date, edor_make_time, edor_get_money, edor_get_interest, edor_app_date, edor_validate, edor_conf_date, edor_conf_time, edor_app_name, edor_standby_flag1, edor_standby_flag2, edor_standby_flag3, edor_standby_flag4, edor_standby_flag5, sub_track_status 
		from t_dws_cont_history where busi_type=#{busiType} and sub_track_status=#{subTrackStatus} order by busi_occur_time, id limit #{startRows}, #{pageSize} 
	</select>
	
	<select id="selectEdorCauseContLiabilityList" resultMap="DwsContHistoryResult">
		select * from v_dws_cont_liability vdcl where cont_no in 
		<foreach item="contNo" collection="contNos" open="(" separator="," close=")">
             #{contNo}
        </foreach>
	</select>
	
	<select id="selectDwsContReLiabilityCount" parameterType="DwsContHistoryEntity" resultType="java.lang.Integer">
        select sum(total) from (
			select count(*) as total from t_dws_reinsu_policy_liability where is_del=0 and status=0 
			and busi_type in(0, 1) and back_track_data=1 and handle_status=#{handleStatus} and batch_no=#{batchNo}
			union all 
			select count(*) as total from t_dws_cont_history t where t.is_del=0 and t.busi_type in(2, 4, 5) 
			and exists(select 1 from ${dataWarehouseName}.t_ods_core_lcpol_prod toclp where toclp.contno=t.cont_no and (
			<foreach item="programme" collection="params.programmes" open="" separator="or" close="">
				(t.busi_occur_date &gt;= #{programme.startBusiOccurDate} and (
				<foreach item="liability" collection="programme.programmeLiabilityDTOList" open="" separator="or" close="">
                 (toclp.riskcode=#{liability.riskCode} 
                 and toclp.cvalidate &gt;= date_format(#{liability.startDate}, '%Y-%m-%d') 
                 and toclp.cvalidate &lt;= date_format(#{liability.endDate}, '%Y-%m-%d'))
	            </foreach>
				))
			</foreach>
			)) 
			union all 
			select count(*) from t_dws_cont_history t inner join ${mysqlBaseName}.t_liability_mapping tlm 
			on t.risk_code=tlm.risk_code and t.get_duty_code=tlm.lis_liability_code 
			where t.is_del=0 and tlm.is_del=0 and tlm.status=0 and t.busi_type=3 
			and exists(select 1 from ${dataWarehouseName}.t_ods_core_lcpol_prod toclp where toclp.contno=t.cont_no and (
			<foreach item="programme" collection="params.programmes" open="" separator="or" close="">
				(t.busi_occur_date &gt;= #{programme.startBusiOccurDate} and (
				<foreach item="liability" collection="programme.programmeLiabilityDTOList" open="" separator="or" close="">
					(toclp.riskcode=#{liability.riskCode} and tlm.rs_liability_code=#{liability.liabilityCode} 
					and toclp.cvalidate &gt;= date_format(#{liability.startDate}, '%Y-%m-%d') 
                 	and toclp.cvalidate &lt;= date_format(#{liability.endDate}, '%Y-%m-%d'))
				</foreach>
				))
			</foreach>
			))
		)vtdch
    </select>
    
    <select id="selectDwsContReLiabilityList" parameterType="DwsContHistoryEntity" resultMap="DwsContHistoryResult">
        select * from (
			select id, unique_key, busi_type, cont_type, grp_cont_no, grp_pol_no, cont_no, pol_no, main_pol_no, sale_chnl, sale_chnl_name, sell_type, sell_type_name, sale_com_code, sale_com_name, agent_com_code, agent_com_name,
			manage_com_code, manage_com_name, bank_branch_name, busi_occur_time, busi_occur_date, sign_date,sign_time, risk_vali_date, risk_end_date, cont_make_date, cont_make_time, cont_year, cont_anniversary, 
			previous_cont_anniversary, cont_app_flag, risk_code, risk_name, risk_type3, plan_code, pol_risk_type, risk_app_flag, sub_risk_flag, risk_period, insu_year, insu_year_flag, pay_intv, payend_year, payend_year_flag, 
			pay_to_date, pay_end_date, amount, base_premium, add_premium, add_scale, sum_pay_money, sum_add_money, sum_get_money, risk_free_flag, appnt_no, appnt_name, appnt_id_type, appnt_id_no, appnt_sex, appnt_birthday, 
			appnt_occ_type, appnt_occ_code, insured_peoples, null as insured_sequence_no, null as main_insured_no, insured_app_age, insured_no, insured_name, insured_id_type, insured_id_no, insured_sex, insured_birthday, insured_occ_type, 
			insured_occ_code, insured_pass_flag, cash_value, insuacc_value, cedeout_type, core_conclusion, reinsu_add_scale, cedeout_count, liability_code, liability_name, null as edor_accept_no, null as edor_no, null as edor_type, 
			null as edor_state, null as edor_app_type, null as edor_make_date, null as edor_make_time, null as edor_get_money, null as edor_get_interest, null as edor_app_date, null as edor_validate, null as edor_conf_date, 
			null as edor_conf_time, null as edor_app_name, null as edor_standby_flag1, null as edor_standby_flag2, null as edor_standby_flag3, null as edor_standby_flag4, null as edor_standby_flag5, null as clm_no, null as clm_rgt_no, 
			null as clm_case_no, null as clm_state, null as clm_standpay, null as clm_beforepay, null as clm_balancepay, null as clm_realpay, null as clm_give_type, null as clm_give_type_desc, null as clm_acc_date, null as clm_rpt_date, 
			null as clm_rgt_date, null as clm_case_end_date, null as clm_enter_acc_date, null as clm_accident_date, null as clm_fee_fina_type, null as clm_accident_reason, null as clm_fee_sum, null as clm_accresult_1, null as clm_accresult_1_name, 
			null as clm_accresult_2, null as clm_accresult_2_name, null as clm_defo_type, null as clm_defo_name, null as clm_defo_grade, null as clm_defo_grade_name, null as clm_fee_type, null as clm_bal_type_desc, null as clm_sub_fee_type, 
			null as clm_sub_bal_type_desc, null as clm_hospital_code, null as clm_hospital_name, null as clm_in_hospital_date, null as clm_out_hospital_date, null as clm_make_date, null as clm_make_time, null as state_type, null as state_state, 
			null as state_reason, null as state_start_date, null as state_end_date, null as state_make_date, null as state_make_time, get_duty_codes, get_duty_names, handle_error_num, programme_code, cont_month 
			from t_dws_reinsu_policy_liability where is_del=0 and status=0 and busi_type in(0, 1) and back_track_data=1 and handle_status=#{handleStatus} and batch_no=#{batchNo}
			union all 
			select t.id, t.unique_key, t.busi_type, t.cont_type, t.grp_cont_no, t.grp_pol_no, t.cont_no, t.pol_no, t.main_pol_no, t.sale_chnl, t.sale_chnl_name, t.sell_type, t.sell_type_name, t.sale_com_code, t.sale_com_name,
			t.agent_com_code, t.agent_com_name, t.manage_com_code, t.manage_com_name, t.bank_branch_name, t.busi_occur_time, t.busi_occur_date, t.sign_date,t.sign_time, t.risk_vali_date, t.risk_end_date, t.cont_make_date, 
			t.cont_make_time, t.cont_year, t.cont_anniversary, t.previous_cont_anniversary, t.cont_app_flag, t.risk_code, t.risk_name, t.risk_type3, t.plan_code, t.pol_risk_type, t.risk_app_flag, t.sub_risk_flag, 
			t.risk_period, t.insu_year, t.insu_year_flag, t.pay_intv, t.payend_year, t.payend_year_flag, t.pay_to_date, t.pay_end_date, t.amount, t.base_premium, t.add_premium, t.add_scale, t.sum_pay_money, t.sum_add_money, 
			t.sum_get_money, t.risk_free_flag, t.appnt_no, t.appnt_name, t.appnt_id_type, t.appnt_id_no, t.appnt_sex, t.appnt_birthday, t.appnt_occ_type, t.appnt_occ_code, t.insured_peoples, t.insured_sequence_no, t.main_insured_no, 
			t.insured_app_age, t.insured_no, t.insured_name, t.insured_id_type, t.insured_id_no, t.insured_sex, t.insured_birthday, t.insured_occ_type, t.insured_occ_code, t.insured_pass_flag, t.cash_value, t.insuacc_value, 
			t.cedeout_type, t.core_conclusion, t.reinsu_add_scale, t.cedeout_count, null as liability_code, null as liability_name, t.edor_accept_no, t.edor_no, t.edor_type, t.edor_state, t.edor_app_type, t.edor_make_date, t.edor_make_time, 
			t.edor_get_money, t.edor_get_interest, t.edor_app_date, t.edor_validate, t.edor_conf_date, t.edor_conf_time, t.edor_app_name, t.edor_standby_flag1, t.edor_standby_flag2, t.edor_standby_flag3, t.edor_standby_flag4, t.edor_standby_flag5, 
			t.clm_no, t.clm_rgt_no, t.clm_case_no, t.clm_state, t.clm_standpay, t.clm_beforepay, t.clm_balancepay, t.clm_realpay, t.clm_give_type, t.clm_give_type_desc, t.clm_acc_date, t.clm_rpt_date, t.clm_rgt_date, t.clm_case_end_date, 
			t.clm_enter_acc_date, t.clm_accident_date, t.clm_fee_fina_type, t.clm_accident_reason, t.clm_fee_sum, t.clm_accresult_1, t.clm_accresult_1_name, t.clm_accresult_2, t.clm_accresult_2_name, t.clm_defo_type, t.clm_defo_name, t.clm_defo_grade, 
			t.clm_defo_grade_name, t.clm_fee_type, t.clm_bal_type_desc, t.clm_sub_fee_type, t.clm_sub_bal_type_desc, t.clm_hospital_code, t.clm_hospital_name, t.clm_in_hospital_date, t.clm_out_hospital_date, t.clm_make_date, t.clm_make_time, 
			t.state_type, t.state_state, t.state_reason, t.state_start_date, t.state_end_date, t.state_make_date, t.state_make_time, t.get_duty_code as get_duty_codes, t.get_duty_name as get_duty_names, handle_error_num, null as programme_code, null as cont_month  
			from t_dws_cont_history t where t.is_del=0 and t.busi_type in(2, 4, 5) and exists(select 1 from ${dataWarehouseName}.t_ods_core_lcpol_prod toclp where toclp.contno=t.cont_no and (
			<foreach item="programme" collection="params.programmes" open="" separator="or" close="">
				(t.busi_occur_date &gt;= #{programme.startBusiOccurDate} and (
				<foreach item="liability" collection="programme.programmeLiabilityDTOList" open="" separator="or" close="">
                 (toclp.riskcode=#{liability.riskCode} 
                 and toclp.cvalidate &gt;= date_format(#{liability.startDate}, '%Y-%m-%d') 
                 and toclp.cvalidate &lt;= date_format(#{liability.endDate}, '%Y-%m-%d'))
	            </foreach>
				))
			</foreach>
			)) 
			union all 
			select t.id, t.unique_key, t.busi_type, t.cont_type, t.grp_cont_no, t.grp_pol_no, t.cont_no, t.pol_no, t.main_pol_no, t.sale_chnl, t.sale_chnl_name, t.sell_type, t.sell_type_name, t.sale_com_code, t.sale_com_name, t.agent_com_code, t.agent_com_name,
			t.manage_com_code, t.manage_com_name, t.bank_branch_name, t.busi_occur_time, t.busi_occur_date, t.sign_date,t.sign_time, t.risk_vali_date, t.risk_end_date, t.cont_make_date, t.cont_make_time, t.cont_year, t.cont_anniversary, 
			t.previous_cont_anniversary, t.cont_app_flag, t.risk_code, t.risk_name, t.risk_type3, t.plan_code, t.pol_risk_type, t.risk_app_flag, t.sub_risk_flag, t.risk_period, t.insu_year, t.insu_year_flag, t.pay_intv, t.payend_year, t.payend_year_flag, 
			t.pay_to_date, t.pay_end_date, t.amount, t.base_premium, t.add_premium, t.add_scale, t.sum_pay_money, t.sum_add_money, t.sum_get_money, t.risk_free_flag, t.appnt_no, t.appnt_name, t.appnt_id_type, t.appnt_id_no, t.appnt_sex, t.appnt_birthday, 
			t.appnt_occ_type, t.appnt_occ_code, t.insured_peoples, t.insured_sequence_no, t.main_insured_no, t.insured_app_age, t.insured_no, t.insured_name, t.insured_id_type, t.insured_id_no, t.insured_sex, t.insured_birthday, t.insured_occ_type, 
			t.insured_occ_code, t.insured_pass_flag, t.cash_value, t.insuacc_value, t.cedeout_type, t.core_conclusion, t.reinsu_add_scale, t.cedeout_count, tlm.rs_liability_code as liability_code, tlm.rs_liability_name as liability_name, t.edor_accept_no, 
			t.edor_no, t.edor_type, t.edor_state, t.edor_app_type, t.edor_make_date, t.edor_make_time, t.edor_get_money, t.edor_get_interest, t.edor_app_date, t.edor_validate, t.edor_conf_date, t.edor_conf_time, t.edor_app_name, t.edor_standby_flag1, 
			t.edor_standby_flag2, t.edor_standby_flag3, t.edor_standby_flag4, t.edor_standby_flag5, t.clm_no, t.clm_rgt_no, t.clm_case_no, t.clm_state, t.clm_standpay, t.clm_beforepay, t.clm_balancepay, t.clm_realpay, t.clm_give_type, t.clm_give_type_desc, 
			t.clm_acc_date, t.clm_rpt_date, t.clm_rgt_date, t.clm_case_end_date, t.clm_enter_acc_date, t.clm_accident_date, t.clm_fee_fina_type, t.clm_accident_reason, t.clm_fee_sum, t.clm_accresult_1, t.clm_accresult_1_name, t.clm_accresult_2, t.clm_accresult_2_name, 
			t.clm_defo_type, t.clm_defo_name, t.clm_defo_grade, t.clm_defo_grade_name, t.clm_fee_type, t.clm_bal_type_desc, t.clm_sub_fee_type, t.clm_sub_bal_type_desc, t.clm_hospital_code, t.clm_hospital_name, t.clm_in_hospital_date, t.clm_out_hospital_date, 
			t.clm_make_date, t.clm_make_time, t.state_type, t.state_state, t.state_reason, t.state_start_date, t.state_end_date, t.state_make_date, t.state_make_time, t.get_duty_code as get_duty_codes, t.get_duty_name as get_duty_names, handle_error_num, 
			null as programme_code, null as cont_month from t_dws_cont_history t inner join ${mysqlBaseName}.t_liability_mapping tlm on t.risk_code=tlm.risk_code and t.get_duty_code=tlm.lis_liability_code 
			where t.is_del=0 and tlm.is_del=0 and tlm.status=0 and t.busi_type=3 and exists(select 1 from ${dataWarehouseName}.t_ods_core_lcpol_prod toclp where toclp.contno=t.cont_no and (
			<foreach item="programme" collection="params.programmes" open="" separator="or" close="">
				(t.busi_occur_date &gt;= #{programme.startBusiOccurDate} and (
				<foreach item="liability" collection="programme.programmeLiabilityDTOList" open="" separator="or" close="">
					(toclp.riskcode=#{liability.riskCode} and tlm.rs_liability_code=#{liability.liabilityCode} 
					and toclp.cvalidate &gt;= date_format(#{liability.startDate}, '%Y-%m-%d') 
                 	and toclp.cvalidate &lt;= date_format(#{liability.endDate}, '%Y-%m-%d'))
				</foreach>
				))
			</foreach>
			))
		)vtdch order by busi_occur_time, busi_type, cont_no, pol_no, insured_no 
		<if test="startRows != null and pageSize != null">
			limit #{startRows}, #{pageSize}
		</if>
    </select>
    
    <select id="selectEdorContReLiabilityByUniqueKeys" resultMap="DwsContHistoryResult">	
		select tdch.busi_type, tdch.unique_key, tdch.busi_occur_time, tdch.busi_occur_date, tdecl.cont_type, tdecl.grp_cont_no, tdecl.grp_pol_no, tdecl.cont_no, tdecl.pol_no, tdecl.main_pol_no, tdecl.sale_chnl, tdecl.sell_type, tdecl.sale_chnl_name, tdecl.sell_type_name, tdecl.sale_com_code, tdecl.sale_com_name,
		tdecl.manage_com_code, tdecl.manage_com_name, tdecl.agent_com_code, tdecl.agent_com_name, tdecl.bank_branch_name, tdecl.sign_date, tdecl.sign_time, tdecl.risk_vali_date, tdecl.risk_end_date, tdecl.cont_make_date, tdecl.cont_make_time, tdecl.cont_year, tdecl.cont_anniversary, tdecl.previous_cont_anniversary, 
		tdecl.cont_app_flag, tdecl.risk_code, tdecl.risk_name, tdecl.risk_type3, tdecl.plan_code, tdecl.pol_risk_type, tdecl.risk_app_flag, tdecl.sub_risk_flag, tdecl.risk_period, tdecl.insu_year, tdecl.insu_year_flag, tdecl.pay_intv, tdecl.payend_year, tdecl.payend_year_flag, 
		tdecl.pay_to_date, tdecl.pay_end_date, group_concat(tdecl.get_duty_code) as get_duty_codes, group_concat(tdecl.get_duty_name) as get_duty_names, tdecl.amount, tdecl.sum_pay_money, tdecl.sum_add_money, tdecl.sum_get_money, tdecl.base_premium, tdecl.add_premium, tdecl.add_scale, tdecl.risk_free_flag, 
		tdecl.appnt_no, tdecl.appnt_name, tdecl.appnt_id_type, tdecl.appnt_id_no, tdecl.appnt_sex, tdecl.appnt_birthday, tdecl.appnt_occ_type, tdecl.appnt_occ_code, tdecl.main_insured_no, tdecl.insured_sequence_no, tdecl.insured_peoples, tdecl.insured_app_age, 
		tdecl.insured_no, tdecl.insured_name, tdecl.insured_id_type, tdecl.insured_id_no, tdecl.insured_sex, tdecl.insured_birthday, tdecl.insured_occ_type, tdecl.insured_occ_code, tdecl.insured_pass_flag, tdecl.cedeout_type, tdecl.core_conclusion, tdecl.reinsu_add_scale, tdecl.insuacc_value, 
		tdecl.cash_value, tlm.rs_liability_code as liability_code, tlm.rs_liability_name as liability_name, group_concat(tdecl.id) as ids
		from (select * from t_dws_cont_history where busi_type=2 and is_del=0 and unique_key in 
		<foreach item="uniqueKey" collection="uniqueKeyList" open="(" separator="," close=")">
        	#{uniqueKey}
        </foreach>
		) tdch 
		inner join t_dws_edor_cont_liability tdecl on tdch.unique_key = tdecl.unique_key 
		inner join ${mysqlBaseName}.t_liability_mapping tlm on tdecl.get_duty_code = tlm.lis_liability_code 
		group by tdch.busi_type, tdch.unique_key, tdch.busi_occur_time, tdch.busi_occur_date, tdecl.cont_type, tdecl.grp_cont_no, tdecl.grp_pol_no, tdecl.cont_no, tdecl.pol_no, tdecl.main_pol_no, tdecl.sale_chnl, tdecl.sell_type, tdecl.sale_chnl_name, tdecl.sell_type_name, tdecl.sale_com_code, tdecl.sale_com_name,
		tdecl.manage_com_code, tdecl.manage_com_name, tdecl.agent_com_code, tdecl.agent_com_name, tdecl.bank_branch_name, tdecl.sign_date, tdecl.sign_time, tdecl.risk_vali_date, tdecl.risk_end_date, tdecl.cont_make_date, tdecl.cont_make_time, tdecl.cont_year, tdecl.cont_anniversary, tdecl.previous_cont_anniversary, 
		tdecl.cont_app_flag, tdecl.risk_code, tdecl.risk_name, tdecl.risk_type3, tdecl.plan_code, tdecl.pol_risk_type, tdecl.risk_app_flag, tdecl.sub_risk_flag, tdecl.risk_period, tdecl.insu_year, tdecl.insu_year_flag, tdecl.pay_intv, tdecl.payend_year, tdecl.payend_year_flag, 
		tdecl.pay_to_date, tdecl.pay_end_date, tdecl.amount, tdecl.sum_pay_money, tdecl.sum_add_money, tdecl.sum_get_money, tdecl.base_premium, tdecl.add_premium, tdecl.add_scale, tdecl.risk_free_flag, 
		tdecl.appnt_no, tdecl.appnt_name, tdecl.appnt_id_type, tdecl.appnt_id_no, tdecl.appnt_sex, tdecl.appnt_birthday, tdecl.appnt_occ_type, tdecl.appnt_occ_code, tdecl.main_insured_no, tdecl.insured_sequence_no, tdecl.insured_peoples, tdecl.insured_app_age, 
		tdecl.insured_no, tdecl.insured_name, tdecl.insured_id_type, tdecl.insured_id_no, tdecl.insured_sex, tdecl.insured_birthday, tdecl.insured_occ_type, tdecl.insured_occ_code, tdecl.insured_pass_flag, tdecl.cedeout_type, tdecl.core_conclusion, tdecl.reinsu_add_scale, tdecl.insuacc_value, 
		tdecl.cash_value, tlm.rs_liability_code, tlm.rs_liability_name
    </select>
    
    <select id="selectContAlreadyClamCountByExpiryDate" parameterType="DwsContHistoryEntity" resultType="java.lang.Integer">
    	select count(1) from v_dws_claim
    	<where>
	        <if test="contNo != null and contNo != ''"> and cont_no = #{contNo}</if>
	        <if test="polNo != null and polNo != ''"> and pol_no = #{polNo}</if>
	        <if test="insuredNo != null and insuredNo != ''"> and insured_no = #{insuredNo}</if>
	        <if test="riskCode != null and riskCode != ''"> and risk_code = #{riskCode}</if>
	        <if test="liabilityCode != null and liabilityCode != ''"> and liability_code = #{liabilityCode}</if>
	        <if test="params.startDate != null">
	            and busi_occur_date &gt;= date_format(#{params.startDate}, '%Y-%m-%d')
	        </if>
	        <if test="params.endDate != null">
	            and busi_occur_date &lt;= date_format(#{params.endDate}, '%Y-%m-%d')
	        </if>
        </where>
    </select>
    
    <select id="selectEdorList" resultMap="DwsContHistoryResult">
    	select id, busi_type, unique_key, busi_occur_time, busi_occur_date, cont_no, pol_no, insured_no, edor_accept_no, edor_no, edor_type, edor_state, edor_app_type, edor_make_date, edor_make_time, edor_get_money, edor_get_interest, edor_app_date, edor_validate, edor_conf_date, edor_conf_time, edor_app_name, edor_standby_flag1, edor_standby_flag2, edor_standby_flag3, edor_standby_flag4, edor_standby_flag5, sub_track_status, handle_status, handle_date, handle_error_num, handle_error_msg  
    	from t_dws_cont_history where busi_type=#{busiType} and handle_status=#{handleStatus} and busi_occur_date &lt;= date_format(#{endDate}, '%Y-%m-%d') order by busi_occur_time, id limit #{limit}
    </select>
    
    <select id="selectCalimList" resultMap="DwsContHistoryResult">
    	select t.id, t.busi_type, t.unique_key, t.busi_occur_time, t.busi_occur_date, t.cont_no, t.pol_no, t.insured_no, t.risk_code, t.risk_name, t.sub_risk_flag, t.risk_period, t.duty_code, t.duty_name, t.get_duty_code, t.get_duty_name, tlm.rs_liability_code as liability_code, tlm.rs_liability_name as liability_name, 
    	t.clm_no, t.clm_rgt_no, t.clm_case_no, t.clm_state, t.clm_standpay, t.clm_beforepay, t.clm_balancepay, t.clm_realpay, t.clm_give_type, t.clm_give_type_desc, t.clm_acc_date, t.clm_rpt_date, t.clm_rgt_date, t.clm_case_end_date, t.clm_enter_acc_date, t.clm_accident_date, 
    	t.clm_fee_fina_type, t.clm_accident_reason, t.clm_fee_sum, t.clm_accresult_1, t.clm_accresult_1_name, t.clm_accresult_2, t.clm_accresult_2_name, t.clm_defo_type, t.clm_defo_name, t.clm_defo_grade, t.clm_defo_grade_name, t.clm_fee_type, t.clm_bal_type_desc, t.clm_sub_fee_type, 
    	t.clm_sub_bal_type_desc, t.clm_hospital_code, t.clm_hospital_name, t.clm_in_hospital_date, t.clm_out_hospital_date, t.clm_make_date, t.clm_make_time, t.handle_status, t.handle_date, t.handle_error_num, t.handle_error_msg 
    	from t_dws_cont_history t inner join ${mysqlBaseName}.t_liability_mapping tlm on t.risk_code=tlm.risk_code and t.get_duty_code=tlm.lis_liability_code where t.is_del=0 and tlm.is_del=0 and tlm.status=0 and t.busi_type=#{busiType} and t.handle_status=#{handleStatus} 
    	and t.busi_occur_date &lt;= date_format(#{endDate}, '%Y-%m-%d') order by t.busi_occur_time, t.id limit #{limit}
    </select>
    
    <select id="selectExpiredList" resultMap="DwsContHistoryResult">
    	select id, busi_type, unique_key, busi_occur_time, busi_occur_date, cont_no, pol_no, insured_no, state_type, state_state, state_reason, state_start_date, state_end_date, state_make_date, state_make_time, handle_status, handle_date, handle_error_num, handle_error_msg  
    	from t_dws_cont_history where busi_type=#{busiType} and handle_status=#{handleStatus} and busi_occur_date &lt;= date_format(#{endDate}, '%Y-%m-%d') order by busi_occur_time, id limit #{limit}
    </select>
    
    <select id="selectInvalidList" resultMap="DwsContHistoryResult">
    	select id, busi_type, unique_key, busi_occur_time, busi_occur_date, cont_no, pol_no, insured_no, state_type, state_state, state_reason, state_start_date, state_end_date, state_make_date, state_make_time, handle_status, handle_date, handle_error_num, handle_error_msg  
    	from t_dws_cont_history where busi_type=#{busiType} and handle_status=#{handleStatus} and busi_occur_date &lt;= date_format(#{endDate}, '%Y-%m-%d') order by busi_occur_time, id limit #{limit}
    </select>
    
    <select id="selectInvalidExpiredList" resultMap="DwsContHistoryResult">
    	select id, busi_type, unique_key, busi_occur_time, busi_occur_date, cont_no, pol_no, insured_no, state_type, state_state, state_reason, state_start_date, state_end_date, state_make_date, state_make_time, handle_status, handle_date, handle_error_num, handle_error_msg 
    	from t_dws_cont_history where busi_type in 
    	<foreach item="busiType" collection="busiTypes" open="(" separator="," close=")">
        	#{busiType}
        </foreach>
    	and handle_status=#{handleStatus} and busi_occur_date &lt;= date_format(#{endDate}, '%Y-%m-%d') order by busi_occur_time, id limit #{limit}
    </select>
    
    <select id="selectOneDwsContHistoryByPol" parameterType="DwsContHistoryEntity" resultMap="DwsContHistoryResult">
    	select id, batch_no, batch_date, busi_type, unique_key, busi_occur_time, busi_occur_date, cont_type, grp_cont_no, cont_no, pol_no, main_pol_no, sale_chnl, sell_type, sale_chnl_name, sell_type_name, sale_com_code, sale_com_name, manage_com_code, manage_com_name, agent_com_code, agent_com_name, bank_branch_name, sign_date, sign_time, risk_vali_date, risk_end_date, cont_make_date, cont_make_time, cont_year, cont_anniversary, previous_cont_anniversary, cont_app_flag, risk_code, risk_name, risk_type3, plan_code, pol_risk_type, risk_app_flag, sub_risk_flag, risk_period, insu_year, insu_year_flag, pay_intv, payend_year, payend_year_flag, pay_to_date, pay_end_date, duty_code, duty_name, get_duty_code, get_duty_name, get_duty_state, amount, sum_pay_money, sum_add_money, sum_get_money, base_premium, add_premium, add_scale, risk_free_flag, appnt_no, appnt_name, appnt_id_type, appnt_id_no, appnt_sex, appnt_birthday, appnt_occ_type, appnt_occ_code, main_insured_no, insured_sequence_no, insured_peoples, insured_app_age, insured_no, insured_name, insured_id_type, insured_id_no, insured_sex, insured_birthday, insured_occ_type, insured_occ_code, insured_pass_flag, cedeout_type, core_conclusion, reinsu_add_scale, insuacc_value, cash_value 
    	from t_dws_cont_history where is_del=0 and status=0 and busi_type=#{busiType} and pol_no=#{polNo} and cont_year=#{contYear} limit 1
    </select>
</mapper>