<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reinsurance.mapper.DwsEastBlzbbdmxbMapper">
    
    <resultMap type="DwsEastBlzbbdmxbEntity" id="DwsEastBlzbbdmxbResult">
        <result property="id"    column="ID"    />
        <result property="billLsh"    column="BILL_LSH"    />
        <result property="lsh"    column="LSH"    />
        <result property="bxjgdm"    column="BXJGDM"    />
        <result property="bxjgmc"    column="BXJGMC"    />
        <result property="lfbdbz"    column="LFBDBZ"    />
        <result property="xnhtbz"    column="XNHTBZ"    />
        <result property="zbxhthm"    column="ZBXHTHM"    />
        <result property="zbxgsdm"    column="ZBXGSDM"    />
        <result property="zbxgsmc"    column="ZBXGSMC"    />
        <result property="zbhzywbz"    column="ZBHZYWBZ"    />
        <result property="bdtgxz"    column="BDTGXZ"    />
        <result property="ttbdh"    column="TTBDH"    />
        <result property="tdbxxzhm"    column="TDBXXZHM"    />
        <result property="grbdh"    column="GRBDH"    />
        <result property="gdbxxzhm"    column="GDBXXZHM"    />
        <result property="xzbm"    column="XZBM"    />
        <result property="zrdm"    column="ZRDM"    />
        <result property="zrmc"    column="ZRMC"    />
        <result property="zrfl"    column="ZRFL"    />
        <result property="ywlx"    column="YWLX"    />
        <result property="bdnd"    column="BDND"    />
        <result property="fbfs"    column="FBFS"    />
        <result property="fxbe"    column="FXBE"    />
        <result property="fbfxbe"    column="FBFXBE"    />
        <result property="zle"    column="ZLE"    />
        <result property="fbbl"    column="FBBL"    />
        <result property="zbrcyfebl"    column="ZBRCYFEBL"    />
        <result property="fbhthjsbh"    column="FBHTHJSBH"    />
        <result property="fbf"    column="FBF"    />
        <result property="fbyj"    column="FBYJ"    />
        <result property="thfbf"    column="THFBF"    />
        <result property="thfbyj"    column="THFBYJ"    />
        <result property="thtbj"    column="THTBJ"    />
        <result property="thlpk"    column="THLPK"    />
        <result property="pah"    column="PAH"    />
        <result property="thmqj"    column="THMQJ"    />
        <result property="thscj"    column="THSCJ"    />
        <result property="jsrq"    column="JSRQ"    />
        <result property="hbdm"    column="HBDM"    />
        <result property="bdzt"    column="BDZT"    />
        <result property="bz"    column="BZ"    />
        <result property="cjrq"    column="CJRQ"    />
        <result property="sjbspch"    column="SJBSPCH"    />
        <result property="managecom"    column="MANAGECOM"    />
        <result property="dataSource"    column="DATA_SOURCE"    />
        <result property="pushStatus"    column="PUSH_STATUS"    />
        <result property="pushDate"    column="PUSH_DATE"    />
        <result property="pushBy"    column="PUSH_BY"    />
        <result property="remark"    column="REMARK"    />
        <result property="isDel"    column="IS_DEL"    />
        <result property="createBy"    column="CREATE_BY"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updateBy"    column="UPDATE_BY"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
    </resultMap>

    <sql id="selectDwsEastBlzbbdmxbVo">
        select ID, BILL_LSH, LSH, BXJGDM, BXJGMC, LFBDBZ, XNHTBZ, ZBXHTHM, ZBXGSDM, ZBXGSMC, ZBHZYWBZ, BDTGXZ, TTBDH, TDBXXZHM, GRBDH, GDBXXZHM, XZBM, ZRDM, ZRMC, ZRFL, YWLX, BDND, FBFS, FXBE, FBFXBE, ZLE, FBBL, ZBRCYFEBL, FBHTHJSBH, FBF, FBYJ, THFBF, THFBYJ, THTBJ, THLPK, PAH, THMQJ, THSCJ, JSRQ, HBDM, BDZT, BZ, CJRQ, SJBSPCH, MANAGECOM, DATA_SOURCE, PUSH_STATUS, PUSH_DATE, PUSH_BY, REMARK, IS_DEL, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME from T_DWS_EAST_BLZBBDMXB
    </sql>

    <select id="selectDwsEastBlzbbdmxbList" parameterType="DwsEastBlzbbdmxbQuery" resultMap="DwsEastBlzbbdmxbResult">
        <include refid="selectDwsEastBlzbbdmxbVo"/>
        <where>  
            <if test="billLsh != null  and billLsh != ''"> and BILL_LSH = #{billLsh}</if>
            <if test="lsh != null  and lsh != ''"> and LSH = #{lsh}</if>
            <if test="bxjgdm != null  and bxjgdm != ''"> and BXJGDM = #{bxjgdm}</if>
            <if test="bxjgmc != null  and bxjgmc != ''"> and BXJGMC = #{bxjgmc}</if>
            <if test="lfbdbz != null  and lfbdbz != ''"> and LFBDBZ = #{lfbdbz}</if>
            <if test="xnhtbz != null  and xnhtbz != ''"> and XNHTBZ = #{xnhtbz}</if>
            <if test="zbxhthm != null  and zbxhthm != ''"> and ZBXHTHM = #{zbxhthm}</if>
            <if test="zbxgsdm != null  and zbxgsdm != ''"> and ZBXGSDM = #{zbxgsdm}</if>
            <if test="zbxgsmc != null  and zbxgsmc != ''"> and ZBXGSMC = #{zbxgsmc}</if>
            <if test="zbhzywbz != null  and zbhzywbz != ''"> and ZBHZYWBZ = #{zbhzywbz}</if>
            <if test="bdtgxz != null  and bdtgxz != ''"> and BDTGXZ = #{bdtgxz}</if>
            <if test="ttbdh != null  and ttbdh != ''"> and TTBDH = #{ttbdh}</if>
            <if test="tdbxxzhm != null  and tdbxxzhm != ''"> and TDBXXZHM = #{tdbxxzhm}</if>
            <if test="grbdh != null  and grbdh != ''"> and GRBDH = #{grbdh}</if>
            <if test="gdbxxzhm != null  and gdbxxzhm != ''"> and GDBXXZHM = #{gdbxxzhm}</if>
            <if test="xzbm != null  and xzbm != ''"> and XZBM = #{xzbm}</if>
            <if test="zrdm != null  and zrdm != ''"> and ZRDM = #{zrdm}</if>
            <if test="zrmc != null  and zrmc != ''"> and ZRMC = #{zrmc}</if>
            <if test="zrfl != null  and zrfl != ''"> and ZRFL = #{zrfl}</if>
            <if test="ywlx != null  and ywlx != ''"> and YWLX = #{ywlx}</if>
            <if test="bdnd != null "> and BDND = #{bdnd}</if>
            <if test="fbfs != null  and fbfs != ''"> and FBFS = #{fbfs}</if>
            <if test="fxbe != null "> and FXBE = #{fxbe}</if>
            <if test="fbfxbe != null "> and FBFXBE = #{fbfxbe}</if>
            <if test="zle != null "> and ZLE = #{zle}</if>
            <if test="fbbl != null "> and FBBL = #{fbbl}</if>
            <if test="zbrcyfebl != null "> and ZBRCYFEBL = #{zbrcyfebl}</if>
            <if test="fbhthjsbh != null  and fbhthjsbh != ''"> and FBHTHJSBH = #{fbhthjsbh}</if>
            <if test="fbf != null "> and FBF = #{fbf}</if>
            <if test="fbyj != null "> and FBYJ = #{fbyj}</if>
            <if test="thfbf != null "> and THFBF = #{thfbf}</if>
            <if test="thfbyj != null "> and THFBYJ = #{thfbyj}</if>
            <if test="thtbj != null "> and THTBJ = #{thtbj}</if>
            <if test="thlpk != null "> and THLPK = #{thlpk}</if>
            <if test="pah != null  and pah != ''"> and PAH = #{pah}</if>
            <if test="thmqj != null "> and THMQJ = #{thmqj}</if>
            <if test="thscj != null "> and THSCJ = #{thscj}</if>
            <if test="jsrq != null  and jsrq != ''"> and JSRQ = #{jsrq}</if>
            <if test="hbdm != null  and hbdm != ''"> and HBDM = #{hbdm}</if>
            <if test="bdzt != null  and bdzt != ''"> and BDZT = #{bdzt}</if>
            <if test="bz != null  and bz != ''"> and BZ = #{bz}</if>
            <if test="cjrq != null  and cjrq != ''"> and CJRQ = #{cjrq}</if>
            <if test="sjbspch != null  and sjbspch != ''"> and SJBSPCH = #{sjbspch}</if>
            <if test="managecom != null  and managecom != ''"> and MANAGECOM = #{managecom}</if>
            <if test="dataSource != null "> and DATA_SOURCE = #{dataSource}</if>
            <if test="pushStatus != null "> and PUSH_STATUS = #{pushStatus}</if>
            <if test="pushDate != null "> and PUSH_DATE = date_format(#{pushDate}, '%Y-%m-%d')</if>
            <if test="pushBy != null  and pushBy != ''"> and PUSH_BY = #{pushBy}</if>
            and IS_DEL = 0
        </where>
    </select>
    
    <select id="selectDwsEastBlzbbdmxbById" parameterType="Long" resultMap="DwsEastBlzbbdmxbResult">
        <include refid="selectDwsEastBlzbbdmxbVo"/>
        where ID = #{id}
    </select>
    
    <select id="selectBlzbbdmxbCounttByBillLsh" resultType="Integer">
        select count(*) from T_DWS_EAST_BLZBBDMXB where IS_DEL = 0 and BILL_LSH = #{billLsh}
    </select>
    
    <select id="selectBlzbbdmxbByBillLsh" resultMap="DwsEastBlzbbdmxbResult">
        <include refid="selectDwsEastBlzbbdmxbVo"/> where IS_DEL = 0 and BILL_LSH = #{billLsh} order by LSH limit #{startRows}, #{pageSize}
    </select>
    
    <insert id="insertDwsEastBlzbbdmxb" parameterType="DwsEastBlzbbdmxbEntity" useGeneratedKeys="true" keyProperty="id">
        insert into T_DWS_EAST_BLZBBDMXB
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="billLsh != null and billLsh != ''">BILL_LSH,</if>
            <if test="lsh != null and lsh != ''">LSH,</if>
            <if test="bxjgdm != null">BXJGDM,</if>
            <if test="bxjgmc != null">BXJGMC,</if>
            <if test="lfbdbz != null">LFBDBZ,</if>
            <if test="xnhtbz != null">XNHTBZ,</if>
            <if test="zbxhthm != null">ZBXHTHM,</if>
            <if test="zbxgsdm != null">ZBXGSDM,</if>
            <if test="zbxgsmc != null">ZBXGSMC,</if>
            <if test="zbhzywbz != null">ZBHZYWBZ,</if>
            <if test="bdtgxz != null">BDTGXZ,</if>
            <if test="ttbdh != null">TTBDH,</if>
            <if test="tdbxxzhm != null">TDBXXZHM,</if>
            <if test="grbdh != null">GRBDH,</if>
            <if test="gdbxxzhm != null">GDBXXZHM,</if>
            <if test="xzbm != null">XZBM,</if>
            <if test="zrdm != null">ZRDM,</if>
            <if test="zrmc != null">ZRMC,</if>
            <if test="zrfl != null">ZRFL,</if>
            <if test="ywlx != null">YWLX,</if>
            <if test="bdnd != null">BDND,</if>
            <if test="fbfs != null">FBFS,</if>
            <if test="fxbe != null">FXBE,</if>
            <if test="fbfxbe != null">FBFXBE,</if>
            <if test="zle != null">ZLE,</if>
            <if test="fbbl != null">FBBL,</if>
            <if test="zbrcyfebl != null">ZBRCYFEBL,</if>
            <if test="fbhthjsbh != null">FBHTHJSBH,</if>
            <if test="fbf != null">FBF,</if>
            <if test="fbyj != null">FBYJ,</if>
            <if test="thfbf != null">THFBF,</if>
            <if test="thfbyj != null">THFBYJ,</if>
            <if test="thtbj != null">THTBJ,</if>
            <if test="thlpk != null">THLPK,</if>
            <if test="pah != null">PAH,</if>
            <if test="thmqj != null">THMQJ,</if>
            <if test="thscj != null">THSCJ,</if>
            <if test="jsrq != null">JSRQ,</if>
            <if test="hbdm != null">HBDM,</if>
            <if test="bdzt != null">BDZT,</if>
            <if test="bz != null">BZ,</if>
            <if test="cjrq != null">CJRQ,</if>
            <if test="sjbspch != null">SJBSPCH,</if>
            <if test="managecom != null">MANAGECOM,</if>
            <if test="dataSource != null">DATA_SOURCE,</if>
            <if test="pushStatus != null">PUSH_STATUS,</if>
            <if test="pushDate != null">PUSH_DATE,</if>
            <if test="pushBy != null">PUSH_BY,</if>
            <if test="remark != null">REMARK,</if>
            <if test="isDel != null">IS_DEL,</if>
            <if test="createBy != null">CREATE_BY,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updateBy != null">UPDATE_BY,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="billLsh != null and billLsh != ''">#{billLsh},</if>
            <if test="lsh != null and lsh != ''">#{lsh},</if>
            <if test="bxjgdm != null">#{bxjgdm},</if>
            <if test="bxjgmc != null">#{bxjgmc},</if>
            <if test="lfbdbz != null">#{lfbdbz},</if>
            <if test="xnhtbz != null">#{xnhtbz},</if>
            <if test="zbxhthm != null">#{zbxhthm},</if>
            <if test="zbxgsdm != null">#{zbxgsdm},</if>
            <if test="zbxgsmc != null">#{zbxgsmc},</if>
            <if test="zbhzywbz != null">#{zbhzywbz},</if>
            <if test="bdtgxz != null">#{bdtgxz},</if>
            <if test="ttbdh != null">#{ttbdh},</if>
            <if test="tdbxxzhm != null">#{tdbxxzhm},</if>
            <if test="grbdh != null">#{grbdh},</if>
            <if test="gdbxxzhm != null">#{gdbxxzhm},</if>
            <if test="xzbm != null">#{xzbm},</if>
            <if test="zrdm != null">#{zrdm},</if>
            <if test="zrmc != null">#{zrmc},</if>
            <if test="zrfl != null">#{zrfl},</if>
            <if test="ywlx != null">#{ywlx},</if>
            <if test="bdnd != null">#{bdnd},</if>
            <if test="fbfs != null">#{fbfs},</if>
            <if test="fxbe != null">#{fxbe},</if>
            <if test="fbfxbe != null">#{fbfxbe},</if>
            <if test="zle != null">#{zle},</if>
            <if test="fbbl != null">#{fbbl},</if>
            <if test="zbrcyfebl != null">#{zbrcyfebl},</if>
            <if test="fbhthjsbh != null">#{fbhthjsbh},</if>
            <if test="fbf != null">#{fbf},</if>
            <if test="fbyj != null">#{fbyj},</if>
            <if test="thfbf != null">#{thfbf},</if>
            <if test="thfbyj != null">#{thfbyj},</if>
            <if test="thtbj != null">#{thtbj},</if>
            <if test="thlpk != null">#{thlpk},</if>
            <if test="pah != null">#{pah},</if>
            <if test="thmqj != null">#{thmqj},</if>
            <if test="thscj != null">#{thscj},</if>
            <if test="jsrq != null">#{jsrq},</if>
            <if test="hbdm != null">#{hbdm},</if>
            <if test="bdzt != null">#{bdzt},</if>
            <if test="bz != null">#{bz},</if>
            <if test="cjrq != null">#{cjrq},</if>
            <if test="sjbspch != null">#{sjbspch},</if>
            <if test="managecom != null">#{managecom},</if>
            <if test="dataSource != null">#{dataSource},</if>
            <if test="pushStatus != null">#{pushStatus},</if>
            <if test="pushDate != null">#{pushDate},</if>
            <if test="pushBy != null">#{pushBy},</if>
            <if test="remark != null">#{remark},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDwsEastBlzbbdmxb" parameterType="DwsEastBlzbbdmxbEntity">
        update T_DWS_EAST_BLZBBDMXB
        <trim prefix="SET" suffixOverrides=",">
            <if test="billLsh != null and billLsh != ''">BILL_LSH = #{billLsh},</if>
            <if test="lsh != null and lsh != ''">LSH = #{lsh},</if>
            <if test="bxjgdm != null">BXJGDM = #{bxjgdm},</if>
            <if test="bxjgmc != null">BXJGMC = #{bxjgmc},</if>
            <if test="lfbdbz != null">LFBDBZ = #{lfbdbz},</if>
            <if test="xnhtbz != null">XNHTBZ = #{xnhtbz},</if>
            <if test="zbxhthm != null">ZBXHTHM = #{zbxhthm},</if>
            <if test="zbxgsdm != null">ZBXGSDM = #{zbxgsdm},</if>
            <if test="zbxgsmc != null">ZBXGSMC = #{zbxgsmc},</if>
            <if test="zbhzywbz != null">ZBHZYWBZ = #{zbhzywbz},</if>
            <if test="bdtgxz != null">BDTGXZ = #{bdtgxz},</if>
            <if test="ttbdh != null">TTBDH = #{ttbdh},</if>
            <if test="tdbxxzhm != null">TDBXXZHM = #{tdbxxzhm},</if>
            <if test="grbdh != null">GRBDH = #{grbdh},</if>
            <if test="gdbxxzhm != null">GDBXXZHM = #{gdbxxzhm},</if>
            <if test="xzbm != null">XZBM = #{xzbm},</if>
            <if test="zrdm != null">ZRDM = #{zrdm},</if>
            <if test="zrmc != null">ZRMC = #{zrmc},</if>
            <if test="zrfl != null">ZRFL = #{zrfl},</if>
            <if test="ywlx != null">YWLX = #{ywlx},</if>
            <if test="bdnd != null">BDND = #{bdnd},</if>
            <if test="fbfs != null">FBFS = #{fbfs},</if>
            <if test="fxbe != null">FXBE = #{fxbe},</if>
            <if test="fbfxbe != null">FBFXBE = #{fbfxbe},</if>
            <if test="zle != null">ZLE = #{zle},</if>
            <if test="fbbl != null">FBBL = #{fbbl},</if>
            <if test="zbrcyfebl != null">ZBRCYFEBL = #{zbrcyfebl},</if>
            <if test="fbhthjsbh != null">FBHTHJSBH = #{fbhthjsbh},</if>
            <if test="fbf != null">FBF = #{fbf},</if>
            <if test="fbyj != null">FBYJ = #{fbyj},</if>
            <if test="thfbf != null">THFBF = #{thfbf},</if>
            <if test="thfbyj != null">THFBYJ = #{thfbyj},</if>
            <if test="thtbj != null">THTBJ = #{thtbj},</if>
            <if test="thlpk != null">THLPK = #{thlpk},</if>
            <if test="pah != null">PAH = #{pah},</if>
            <if test="thmqj != null">THMQJ = #{thmqj},</if>
            <if test="thscj != null">THSCJ = #{thscj},</if>
            <if test="jsrq != null">JSRQ = #{jsrq},</if>
            <if test="hbdm != null">HBDM = #{hbdm},</if>
            <if test="bdzt != null">BDZT = #{bdzt},</if>
            <if test="bz != null">BZ = #{bz},</if>
            <if test="cjrq != null">CJRQ = #{cjrq},</if>
            <if test="sjbspch != null">SJBSPCH = #{sjbspch},</if>
            <if test="managecom != null">MANAGECOM = #{managecom},</if>
            <if test="dataSource != null">DATA_SOURCE = #{dataSource},</if>
            <if test="pushStatus != null">PUSH_STATUS = #{pushStatus},</if>
            <if test="pushDate != null">PUSH_DATE = #{pushDate},</if>
            <if test="pushBy != null">PUSH_BY = #{pushBy},</if>
            <if test="remark != null">REMARK = #{remark},</if>
            <if test="isDel != null">IS_DEL = #{isDel},</if>
            <if test="createBy != null">CREATE_BY = #{createBy},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updateBy != null">UPDATE_BY = #{updateBy},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
        </trim>
        where ID = #{id}
    </update>
    
    <update id="updateDwsEastZbzdxxbPushStatus">
        update T_DWS_EAST_BLZBBDMXB set PUSH_STATUS = #{pushStatus}, PUSH_BY = #{pushBy}, PUSH_DATE = now() where BILL_LSH in
        <foreach item="billLsh" collection="billLshs" open="(" separator="," close=")">
        	#{billLsh}
        </foreach>
    </update>

    <delete id="deleteDwsEastBlzbbdmxbById" parameterType="Long">
        delete from T_DWS_EAST_BLZBBDMXB where ID = #{id}
    </delete>

    <delete id="deleteDwsEastBlzbbdmxbByIds" parameterType="String">
        delete from T_DWS_EAST_BLZBBDMXB where ID in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <delete id="deleteDwsEastBlzbbdmxbByBillLsh" parameterType="String">
        delete from T_DWS_EAST_BLZBBDMXB where BILL_LSH = #{billLsh}
    </delete>
    
    
    <resultMap type="Long" id="DwsEastBlzbbdmxbIdResult">
        <result property="id"    column="ID"    />
    </resultMap>
    <select id="selectWaitSetLshBlzbbdmxbListByBillLsh" resultMap="DwsEastBlzbbdmxbIdResult">
        select ID from T_DWS_EAST_BLZBBDMXB where IS_DEL = 0 and BILL_LSH = #{billLsh} and LSH is null limit #{limit}
    </select>
    
    <resultMap type="String" id="DwsEastBlzbbdmxbZdbhResult">
        <result property="zdbh"    column="ZDBH"    />
    </resultMap>
    <select id="selectPushBeforeCheckEmptyByBillLshs" resultMap="DwsEastBlzbbdmxbZdbhResult">
	    select (select ZDBH from T_DWS_EAST_ZBZDXXB where LSH=BILL_LSH) as ZDBH, count(*) as nullTotalRows from T_DWS_EAST_BLZBBDMXB where BILL_LSH in <foreach item="billLsh" collection="billLshs" open="(" separator="," close=")">#{billLsh}</foreach>
	    and (LSH is null or BXJGDM is null or BXJGMC is null or LFBDBZ is null or XNHTBZ is null or ZBXHTHM is null or ZBXGSDM is null or ZBXGSMC is null or ZBHZYWBZ is null or BDTGXZ is null or TTBDH is null or TDBXXZHM is null or GRBDH is null or GDBXXZHM is null or XZBM is null or ZRDM is null or ZRMC is null or ZRFL is null or YWLX is null or BDND is null or FBFS is null or FXBE is null or FBFXBE is null or ZLE is null or FBBL is null or ZBRCYFEBL is null or FBHTHJSBH is null or FBF is null or FBYJ is null or THFBF is null or THFBYJ is null or THTBJ is null or THLPK is null or THMQJ is null or THSCJ is null or JSRQ is null or HBDM is null or BDZT is null or CJRQ is null) group by BILL_LSH
    </select>
    
    <insert id="insertDwsEastBlzbbdmxbFormTrade" parameterType="DwsEastZbzdxxbDTO">
    	insert into T_DWS_EAST_BLZBBDMXB (BILL_LSH, BXJGDM, BXJGMC, LFBDBZ, XNHTBZ, ZBXHTHM, ZBXGSDM, ZBXGSMC, ZBHZYWBZ, BDTGXZ, TTBDH, TDBXXZHM, GRBDH, GDBXXZHM, XZBM, ZRDM, ZRMC, ZRFL, YWLX, BDND, FBFS, FXBE, FBFXBE, ZLE, FBBL, ZBRCYFEBL, FBHTHJSBH, FBF, FBYJ, THFBF, THFBYJ, THTBJ, THLPK, PAH, THMQJ, THSCJ, JSRQ, HBDM, BDZT, CJRQ, SJBSPCH, MANAGECOM, DATA_SOURCE, PUSH_STATUS, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME)
    	select #{lsh} as BILL_LSH, #{bxjgdm} as BXJGDM, #{bxjgmc} as BXJGMC, t.cedeout_type as LFBDBZ, c.cedeout_type as XNHTBZ, t.contract_code as ZBXHTHM, t.company_code as ZBXGSDM, t.company_name as ZBXGSMC, l.ins_gov_flag ZBHZYWBZ, (case when t.cont_type = 1 then '个人' else '团体' end)as BDTGXZ, (case when t.cont_type = 1 then '000000' else t.grp_cont_no end)as TTBDH, (case when t.cont_type = 1 then '000000' else t.grp_pol_no end) as TDBXXZHM,
    	t.cont_no as GRBDH, t.pol_no as GDBXXZHM, t.risk_code as XZBM, t.liability_code as ZRDM, t.liability_name as ZRMC, l.ins_liability_type_name as ZRFL, (case when t.busi_type=0 then '新单' when t.busi_type=1 then '续期' when t.busi_type=2 then '保全' when t.busi_type=3 then '理赔' else '其他' end) as YWLX, t.cont_year as BDND, (case when t.cedeout_mode=1 then '共保' else (case when t.cedeout_way=0 then '溢额（YRT）' when t.cedeout_way=1 then '成数（YRT）' else '成数溢额混合（YRT）' end) end) as FBFS, 
    	(case when t.cedeout_mode=1 then -1 else t.init_risk_amount end) as FXBE, (case when t.cedeout_mode=1 then -1 else t.cedeout_amount end) as FBFXBE, (case when t.cedeout_way=1 then -1 else t.self_amount end) as ZLE, cast(round((t.cedeout_scale * 100), 2) as varchar(32)) as FBBL, cast(round((t.accept_copies * 100), 2) as varchar(32)) as ZBRCYFEBL, t.id as FBHTHJSBH, t.cedeout_total_premium as FBF, t.cedeout_commission as FBYJ, t.return_total_premium as THFBF, t.return_commission as THFBYJ, 0 as THTBJ, t.return_claim_amount as THLPK, t.clm_no as PAH, t.return_expired_gold as THMQJ, 0 as THSCJ, date_format(t.account_date, '%Y%m%d') as JSRQ, 
    	b.currency as HBDM, (case when p.appflag='4' then '终止' when p.appflag='1' then if((select count(*) from ${params.dataWarehouseName}.t_ods_core_lccontstate_prod s where s.statetype='Available' and s.state=1 and s.enddate is null and s.contno=t.cont_no)>0, '中止', '有效') else '有效' end) as BDZT, #{cjrq}, #{sjbspch}, #{managecom}, #{dataSource}, #{pushStatus}, #{createBy}, now(), #{updateBy}, now()
    	from t_dws_reinsu_trade t inner join t_dws_reinsu_settle_bill b on b.bill_no=t.bill_no inner join ${params.mysqlBaseName}.t_risk_liability l on l.risk_code=t.risk_code and l.liability_code=t.liability_code inner join ${params.mysqlBaseName}.t_cedeout_contract c on c.contract_code=t.main_contract_code inner join ${params.dataWarehouseName}.t_ods_core_lccont_prod p on p.contno=t.cont_no 
    	where l.is_del=0 and l.status=0 and t.is_del=0 and t.status=0 and t.calc_status in (1, 3) and t.bill_no=#{settleBillNo} 
    </insert>
    
    <insert id="insertDwsEastPreBlzbbdmxbFormTrade" parameterType="DwsEastZbzdxxbDTO">
    	insert into T_DWS_EAST_BLZBBDMXB (BILL_LSH, BXJGDM, BXJGMC, LFBDBZ, XNHTBZ, ZBXHTHM, ZBXGSDM, ZBXGSMC, ZBHZYWBZ, BDTGXZ, TTBDH, TDBXXZHM, GRBDH, GDBXXZHM, XZBM, ZRDM, ZRMC, ZRFL, YWLX, BDND, FBFS, FXBE, FBFXBE, ZLE, FBBL, ZBRCYFEBL, FBHTHJSBH, FBF, FBYJ, THFBF, THFBYJ, THTBJ, THLPK, PAH, THMQJ, THSCJ, JSRQ, HBDM, BDZT, CJRQ, SJBSPCH, MANAGECOM, DATA_SOURCE, PUSH_STATUS, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME)
    	select #{lsh} as BILL_LSH, #{bxjgdm} as BXJGDM, #{bxjgmc} as BXJGMC, t.cedeout_type as LFBDBZ, #{xnhtbz} as XNHTBZ, t.contract_code as ZBXHTHM, t.company_code as ZBXGSDM, t.company_name as ZBXGSMC, l.ins_gov_flag ZBHZYWBZ, (case when t.cont_type = 1 then '个人' else '团体' end)as BDTGXZ, (case when t.cont_type = 1 then '000000' else t.grp_cont_no end) as TTBDH, (case when t.cont_type = 1 then '000000' else t.grp_pol_no end)as TDBXXZHM,
    	t.cont_no as GRBDH, t.pol_no as GDBXXZHM, t.risk_code as XZBM, t.liability_code as ZRDM, t.liability_name as ZRMC, l.ins_liability_type_name as ZRFL, (case when t.busi_type=0 then '新单' when t.busi_type=1 then '续期' when t.busi_type=2 then '保全' when t.busi_type=3 then '理赔' else '其他' end) as YWLX, t.cont_year as BDND, (case when t.cedeout_mode=1 then '共保' else (case when t.cedeout_way=0 then '溢额（YRT）' when t.cedeout_way=1 then '成数（YRT）' else '成数溢额混合（YRT）' end) end)as FBFS, 
    	(case when t.cedeout_mode=1 then -1 else t.init_risk_amount end) as FXBE, (case when t.cedeout_mode=1 then -1 else t.cedeout_amount end) as FBFXBE, (case when t.cedeout_way=1 then -1 else t.self_amount end) as ZLE, cast(round((t.cedeout_scale * 100), 2) as varchar(32)) as FBBL, cast(round((t.accept_copies * 100), 2) as varchar(32)) as ZBRCYFEBL, t.id as FBHTHJSBH, t.cedeout_total_premium as FBF, t.cedeout_commission as FBYJ, t.return_total_premium as THFBF, t.return_commission as THFBYJ, 0 as THTBJ, t.return_claim_amount as THLPK, t.clm_no as PAH, t.return_expired_gold as THMQJ, 0 as THSCJ, date_format(t.account_date, '%Y%m%d') as JSRQ, 
    	#{hbdm} as HBDM, (case when p.appflag='4' then '终止' when p.appflag='1' then if((select count(*) from ${params.dataWarehouseName}.t_ods_core_lccontstate_prod s where s.statetype='Available' and s.state=1 and s.enddate is null and s.contno=t.cont_no)>0, '中止', '有效') else '有效' end) as BDZT, #{cjrq}, #{sjbspch}, #{managecom}, #{dataSource}, #{pushStatus}, #{createBy}, now(), #{updateBy}, now()
    	from t_dws_reinsu_trade t inner join ${params.mysqlBaseName}.t_risk_liability l on t.risk_code=l.risk_code and t.liability_code=l.liability_code inner join ${params.dataWarehouseName}.t_ods_core_lccont_prod p on p.contno=t.cont_no where t.is_del=0 and t.status=0 and l.is_del=0 and l.status=0 and t.calc_status in (1, 3) and t.company_code=#{zbxgsdm} and t.main_contract_code=#{zbxhthm} 
    	and t.account_date &gt;= date_format(#{params.startDate}, '%Y-%m-%d') and t.account_date &lt;= date_format(#{params.endDate}, '%Y-%m-%d')
    </insert>
</mapper>