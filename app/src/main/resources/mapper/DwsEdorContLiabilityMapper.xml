<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reinsurance.mapper.DwsEdorContLiabilityMapper">

    <resultMap type="DwsEdorContLiabilityEntity" id="DwsEdorContLiabilityResult">
        <result property="id"    column="id"    />
        <result property="uniqueKey"    column="unique_key"    />
        <result property="contType"    column="cont_type"    />
        <result property="grpContNo"    column="grp_cont_no"    />
        <result property="grpPolNo"    column="grp_pol_no"    />
        <result property="contNo"    column="cont_no"    />
        <result property="polNo"    column="pol_no"    />
        <result property="mainPolNo"    column="main_pol_no"    />
        <result property="saleChnl"    column="sale_chnl"    />
        <result property="saleChnlName"    column="sale_chnl_name"    />
        <result property="sellType"    column="sell_type"    />
        <result property="sellTypeName"    column="sell_type_name"    />
        <result property="saleComCode"    column="sale_com_code"    />
        <result property="saleComName"    column="sale_com_name"    />
        <result property="agentComCode"    column="agent_com_code"    />
        <result property="agentComName"    column="agent_com_name"    />
        <result property="manageComCode"    column="manage_com_code"    />
        <result property="manageComName"    column="manage_com_name"    />
        <result property="bankBranchName"    column="bank_branch_name"    />
        <result property="signDate"    column="sign_date"    />
        <result property="signTime"    column="sign_time"    />
        <result property="riskValiDate"    column="risk_vali_date"    />
        <result property="riskEndDate"    column="risk_end_date"    />
        <result property="contMakeDate"    column="cont_make_date"    />
        <result property="contMakeTime"    column="cont_make_time"    />
        <result property="contYear"    column="cont_year"    />
        <result property="contAnniversary"    column="cont_anniversary"    />
        <result property="previousContAnniversary"    column="previous_cont_anniversary"    />
        <result property="contAppFlag"    column="cont_app_flag"    />
        <result property="riskCode"    column="risk_code"    />
        <result property="riskName"    column="risk_name"    />
        <result property="riskType3"    column="risk_type3"    />
        <result property="planCode"    column="plan_code"    />
        <result property="polRiskType"    column="pol_risk_type"    />
        <result property="riskAppFlag"    column="risk_app_flag"    />
        <result property="subRiskFlag"    column="sub_risk_flag"    />
        <result property="riskPeriod"    column="risk_period"    />
        <result property="insuYear"    column="insu_year"    />
        <result property="insuYearFlag"    column="insu_year_flag"    />
        <result property="payIntv"    column="pay_intv"    />
        <result property="payendYear"    column="payend_year"    />
        <result property="payendYearFlag"    column="payend_year_flag"    />
        <result property="payToDate"    column="pay_to_date"    />
        <result property="payEndDate"    column="pay_end_date"    />
        <result property="dutyCode"    column="duty_code"    />
        <result property="dutyName"    column="duty_name"    />
        <result property="getDutyCode"    column="get_duty_code"    />
        <result property="getDutyName"    column="get_duty_name"    />
        <result property="getDutyState"    column="get_duty_state"    />
        <result property="amount"    column="amount"    />
        <result property="sumPayMoney"    column="sum_pay_money"    />
        <result property="sumAddMoney"    column="sum_add_money"    />
        <result property="sumGetMoney"    column="sum_get_money"    />
        <result property="basePremium"    column="base_premium"    />
        <result property="addPremium"    column="add_premium"    />
        <result property="addScale"    column="add_scale"    />
        <result property="riskFreeFlag"    column="risk_free_flag"    />
        <result property="appntNo"    column="appnt_no"    />
        <result property="appntName"    column="appnt_name"    />
        <result property="appntIdType"    column="appnt_id_type"    />
        <result property="appntIdNo"    column="appnt_id_no"    />
        <result property="appntSex"    column="appnt_sex"    />
        <result property="appntBirthday"    column="appnt_birthday"    />
        <result property="appntOccType"    column="appnt_occ_type"    />
        <result property="appntOccCode"    column="appnt_occ_code"    />
        <result property="insuredPeoples"    column="insured_peoples"    />
        <result property="insuredSequenceNo"    column="insured_sequence_no"    />
        <result property="mainInsuredNo"    column="main_insured_no"    />
        <result property="insuredAppAge"    column="insured_app_age"    />
        <result property="insuredNo"    column="insured_no"    />
        <result property="insuredName"    column="insured_name"    />
        <result property="insuredIdType"    column="insured_id_type"    />
        <result property="insuredIdNo"    column="insured_id_no"    />
        <result property="insuredSex"    column="insured_sex"    />
        <result property="insuredBirthday"    column="insured_birthday"    />
        <result property="insuredOccType"    column="insured_occ_type"    />
        <result property="insuredOccCode"    column="insured_occ_code"    />
        <result property="insuredPassFlag"    column="insured_pass_flag"    />
        <result property="cedeoutType"    column="cedeout_type"    />
        <result property="coreConclusion"    column="core_conclusion"    />
        <result property="reinsuAddScale"    column="reinsu_add_scale"    />
        <result property="cedeoutCount"    column="cedeout_count"    />
        <result property="insuaccValue"    column="insuacc_value"    />
        <result property="cashValue"    column="cash_value"    />
        <result property="liabilityCode"    column="liability_code"    />
        <result property="liabilityName"    column="liability_name"    />
    </resultMap>
    
    <insert id="insertBatchEdorContLiability" keyProperty="id" useGeneratedKeys="true">
    	insert into t_dws_edor_cont_liability(unique_key, cont_type, grp_cont_no, grp_pol_no, cont_no, pol_no, main_pol_no, sale_chnl, sell_type, sale_chnl_name, sell_type_name, sale_com_code, sale_com_name,
    	manage_com_code, manage_com_name, agent_com_code, agent_com_name, bank_branch_name, sign_date, sign_time, risk_vali_date, risk_end_date, cont_make_date, cont_make_time, cont_year, 
    	cont_anniversary, previous_cont_anniversary, cont_app_flag, risk_code, risk_name, risk_type3, plan_code, pol_risk_type, risk_app_flag, sub_risk_flag, risk_period, insu_year, insu_year_flag, 
    	pay_intv, payend_year, payend_year_flag, pay_to_date, pay_end_date, duty_code, duty_name, get_duty_code, get_duty_name, get_duty_state, amount, sum_pay_money, sum_add_money, sum_get_money, 
    	base_premium, add_premium, add_scale, risk_free_flag, appnt_no, appnt_name, appnt_id_type, appnt_id_no, appnt_sex, appnt_birthday, appnt_occ_type, appnt_occ_code, insured_peoples, main_insured_no, 
    	insured_sequence_no, insured_app_age, insured_no, insured_name, insured_id_type, insured_id_no, insured_sex, insured_birthday, insured_occ_type, insured_occ_code, insured_pass_flag, cedeout_type, 
    	core_conclusion, reinsu_add_scale, insuacc_value, cash_value, create_time, update_time) values
    	<foreach collection="contLiabilityList" item="item" separator=",">
    		(#{item.uniqueKey}, #{item.contType}, #{item.grpContNo}, #{item.grpPolNo}, #{item.contNo}, #{item.polNo}, #{item.mainPolNo}, #{item.saleChnl}, #{item.sellType}, #{item.saleChnlName}, #{item.sellTypeName}, #{item.saleComCode}, #{item.saleComName},
    		 #{item.manageComCode}, #{item.manageComName}, #{item.agentComCode}, #{item.agentComName}, #{item.bankBranchName}, #{item.signDate}, #{item.signTime}, #{item.riskValiDate}, #{item.riskEndDate}, #{item.contMakeDate}, #{item.contMakeTime}, #{item.contYear}, 
    		 #{item.contAnniversary}, #{item.previousContAnniversary}, #{item.contAppFlag}, #{item.riskCode}, #{item.riskName}, #{item.riskType3}, #{item.planCode}, #{item.polRiskType}, #{item.riskAppFlag}, #{item.subRiskFlag}, #{item.riskPeriod}, #{item.insuYear}, #{item.insuYearFlag}, 
    		 #{item.payIntv}, #{item.payendYear}, #{item.payendYearFlag}, #{item.payToDate}, #{item.payEndDate}, #{item.dutyCode}, #{item.dutyName}, #{item.getDutyCode}, #{item.getDutyName}, #{item.getDutyState}, #{item.amount}, #{item.sumPayMoney}, #{item.sumAddMoney}, #{item.sumGetMoney}, 
    		 #{item.basePremium}, #{item.addPremium}, #{item.addScale}, #{item.riskFreeFlag}, #{item.appntNo}, #{item.appntName}, #{item.appntIdType}, #{item.appntIdNo}, #{item.appntSex}, #{item.appntBirthday}, #{item.appntOccType}, #{item.appntOccCode}, #{item.insuredPeoples}, #{item.mainInsuredNo}, 
    		 #{item.insuredSequenceNo}, #{item.insuredAppAge}, #{item.insuredNo}, #{item.insuredName}, #{item.insuredIdType}, #{item.insuredIdNo}, #{item.insuredSex}, #{item.insuredBirthday}, #{item.insuredOccType}, #{item.insuredOccCode}, #{item.insuredPassFlag}, #{item.cedeoutType}, 
    		 #{item.coreConclusion}, #{item.reinsuAddScale}, #{item.insuaccValue}, #{item.cashValue}, now(), now())
    	</foreach>
    </insert>
    
    <select id="selectOneDwsEdorContLiability" parameterType="DwsEdorContLiabilityEntity" resultMap="DwsEdorContLiabilityResult">
        select *, null as liability_code, null as liability_name from t_dws_edor_cont_liability where unique_key=#{uniqueKey} and pol_no=#{polNo} and cont_year=#{contYear} limit 1
    </select>
</mapper>