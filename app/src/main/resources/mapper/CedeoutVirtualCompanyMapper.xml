<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reinsurance.mapper.CedeoutVirtualCompanyMapper">

    <resultMap type="CedeoutVirtualCompanyEntity" id="CedeoutVirtualCompanyResult">
        <result property="id"    column="id"    />
        <result property="batchNo"    column="batch_no"    />
        <result property="version"    column="version"    />
        <result property="virtualId"    column="virtual_id"    />
        <result property="virtualCode"    column="virtual_code"    />
        <result property="companyId"    column="company_id"    />
        <result property="companyCode"    column="company_code"    />
        <result property="companyName"    column="company_name"    />
        <result property="cedeoutScale"    column="cedeout_scale"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCedeoutVirtualCompanyVo">
        select id, batch_no, version, virtual_id, virtual_code, company_id, company_code, company_name, cedeout_scale, status, remark, is_del, create_by, create_time, update_by, update_time from t_cedeout_virtual_company
    </sql>

    <select id="selectCedeoutVirtualCompanyList" parameterType="CedeoutVirtualCompanyEntity" resultMap="CedeoutVirtualCompanyResult">
        <include refid="selectCedeoutVirtualCompanyVo"/>
        <where>  
            <if test="batchNo != null  and batchNo != ''"> and batch_no = #{batchNo}</if>
            <if test="version != null "> and version = #{version}</if>
            <if test="virtualId != null"> and virtual_id = #{virtualId}</if>
            <if test="virtualCode != null  and virtualCode != ''"> and virtual_code = #{virtualCode}</if>
            <if test="companyCode != null  and companyCode != ''"> and company_code = #{companyCode}</if>
            <if test="companyName != null  and companyName != ''"> and company_name = #{companyName}</if>
            <if test="cedeoutScale != null "> and cedeout_scale = #{cedeoutScale}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="params.virtualCodes != null and params.virtualCodes.size > 0">
            	and virtual_code in
	            <foreach item="virtualCode" collection="params.virtualCodes" open="(" separator="," close=")">
	            	#{virtualCode}
	            </foreach>
            </if>
            and is_del = 0
        </where>
    </select>


    <select id="selectCedeoutVirtualCompanyListByVirtualId" parameterType="Long" resultType="com.reinsurance.dto.CedeoutVirtualCompanyDTO">
        <include refid="selectCedeoutVirtualCompanyVo"/>
        where virtual_id = #{virtualId}
    </select>


    <select id="selectCedeoutVirtualCompanyById" parameterType="Long" resultMap="CedeoutVirtualCompanyResult">
        <include refid="selectCedeoutVirtualCompanyVo"/>
        where id = #{id}
    </select>

    <!-- 批量新增数据 -->
    <insert id="insertCedeoutVirtualCompanyBatch" keyProperty="id" useGeneratedKeys="true">
        insert into t_cedeout_virtual_company(batch_no, version, virtual_id, virtual_code, company_id, company_code, company_name, cedeout_scale ,status,
                                              remark, is_del,create_by,create_time,update_by,update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.batchNo},#{entity.version},#{entity.virtualId},#{entity.virtualCode},#{entity.companyId},#{entity.companyCode},#{entity.companyName},#{entity.cedeoutScale},#{entity.status},
             #{entity.remark},#{entity.isDel},#{entity.createBy},#{entity.createTime},#{entity.updateBy},#{entity.updateTime})
        </foreach>
    </insert>

    <insert id="insertCedeoutVirtualCompany" parameterType="CedeoutVirtualCompanyEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_cedeout_virtual_company
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="batchNo != null and batchNo != ''">batch_no,</if>
            <if test="version != null">version,</if>
            <if test="virtualId != null">virtual_id,</if>
            <if test="virtualCode != null and virtualCode != ''">virtual_code,</if>
            <if test="companyId != null ">company_id,</if>
            <if test="companyCode != null and companyCode != ''">company_code,</if>
            <if test="companyName != null and companyName != ''">company_name,</if>
            <if test="cedeoutScale != null">cedeout_scale,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="batchNo != null and batchNo != ''">#{batchNo},</if>
            <if test="version != null">#{version},</if>
            <if test="virtualId != null">#{virtualId},</if>
            <if test="virtualCode != null and virtualCode != ''">#{virtualCode},</if>
            <if test="companyId != null">#{companyId},</if>
            <if test="companyCode != null and companyCode != ''">#{companyCode},</if>
            <if test="companyName != null and companyName != ''">#{companyName},</if>
            <if test="cedeoutScale != null">#{cedeoutScale},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCedeoutVirtualCompany" parameterType="CedeoutVirtualCompanyEntity">
        update t_cedeout_virtual_company
        <trim prefix="SET" suffixOverrides=",">
            <if test="batchNo != null and batchNo != ''">batch_no = #{batchNo},</if>
            <if test="version != null">version = #{version},</if>
            <if test="virtualId != null">virtual_id = #{virtualId},</if>
            <if test="virtualCode != null and virtualCode != ''">virtual_code = #{virtualCode},</if>
            <if test="companyId != null ">company_id = #{companyId},</if>
            <if test="companyCode != null and companyCode != ''">company_code = #{companyCode},</if>
            <if test="companyName != null and companyName != ''">company_name = #{companyName},</if>
            <if test="cedeoutScale != null">cedeout_scale = #{cedeoutScale},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteCedeoutVirtualCompanyById" parameterType="Long">
        update t_cedeout_virtual_company set is_del = 0 where id = #{id}
    </update>

    <delete id="deleteCedeoutVirtualCompanyByIds" parameterType="String">
        delete from t_cedeout_virtual_company where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteCedeoutVirtualCompanyByVirtualId" parameterType="Long">
        delete from t_cedeout_virtual_company where virtual_id = #{virtualId}
    </delete>
</mapper>