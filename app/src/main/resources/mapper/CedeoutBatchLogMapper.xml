<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reinsurance.mapper.CedeoutBatchLogMapper">
    
    <resultMap type="CedeoutBatchLogEntity" id="CedeoutBatchLogResult">
        <result property="id"    column="id"    />
        <result property="batchNo"    column="batch_no"    />
        <result property="calcType"    column="calc_type"    />
        <result property="inputParams"    column="input_params"    />
        <result property="passCount"    column="pass_count"    />
        <result property="failCount"    column="fail_count"    />
        <result property="executor"    column="executor"    />
        <result property="progress"    column="progress"    />
        <result property="executeDate"    column="execute_date"    />
        <result property="isBackTrack"    column="is_back_track"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCedeoutBatchLogVo">
        select id, batch_no, calc_type, input_params, pass_count, fail_count, executor, progress, execute_date,is_back_track, status, remark, is_del, create_by, create_time, update_by, update_time from t_cedeout_batch_log
    </sql>

    <select id="selectCedeoutBatchLogList" parameterType="CedeoutBatchLogQuery" resultMap="CedeoutBatchLogResult">
        <include refid="selectCedeoutBatchLogVo"/>
        <where>  
            <if test="batchNo != null  and batchNo != ''"> and batch_no = #{batchNo}</if>
            <if test="calcType != null "> and calc_type = #{calcType}</if>
            <if test="inputParams != null  and inputParams != ''"> and input_params = #{inputParams}</if>
            <if test="passCount != null "> and pass_count = #{passCount}</if>
            <if test="failCount != null "> and fail_count = #{failCount}</if>
            <if test="executor != null  and executor != ''"> and executor = #{executor}</if>
            <if test="progress != null "> and progress = #{progress}</if>
            <if test="executeDate != null "> and execute_date = #{executeDate}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="isBackTrack != null "> and is_back_track = #{isBackTrack}</if>
            <if test="calcStartTime != null"> and create_time &gt;= date_format(#{calcStartTime}, '%Y-%m-%d')</if>
            <if test="calcEndTime != null"> and create_time &lt;= date_format(#{calcEndTime}, '%Y-%m-%d')</if>
            and is_del = 0 order by create_time desc
        </where>
    </select>
    
    <select id="selectCedeoutBatchLogById" parameterType="Long" resultMap="CedeoutBatchLogResult">
        <include refid="selectCedeoutBatchLogVo"/>
        where id = #{id}
    </select>
    
    <select id="selectCedeoutBatchLogByBatchNo" parameterType="String" resultMap="CedeoutBatchLogResult">
        <include refid="selectCedeoutBatchLogVo"/> where batch_no = #{batchNo}
    </select>
    
    <select id="selectRunningBatchCount" parameterType="CedeoutBatchLogQuery" resultType="java.lang.Integer">
        select count(*) from t_cedeout_batch_log
        <where>
            <if test="isDel != null "> and is_del = #{isDel}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="progress != null "> and progress = #{progress}</if>
            <if test="isBackTrack != null "> and is_back_track = #{isBackTrack}</if>
        </where>
    </select>
    
    <insert id="insertCedeoutBatchLog" parameterType="CedeoutBatchLogEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_cedeout_batch_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="batchNo != null and batchNo != ''">batch_no,</if>
            <if test="calcType != null">calc_type,</if>
            <if test="inputParams != null">input_params,</if>
            <if test="passCount != null">pass_count,</if>
            <if test="failCount != null">fail_count,</if>
            <if test="executor != null">executor,</if>
            <if test="progress != null">progress,</if>
            <if test="executeDate != null">execute_date,</if>
            <if test="status != null">status,</if>
            <if test="isBackTrack != null ">is_back_track,</if>
            <if test="remark != null">remark,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="batchNo != null and batchNo != ''">#{batchNo},</if>
            <if test="calcType != null">#{calcType},</if>
            <if test="inputParams != null">#{inputParams},</if>
            <if test="passCount != null">#{passCount},</if>
            <if test="failCount != null">#{failCount},</if>
            <if test="executor != null">#{executor},</if>
            <if test="progress != null">#{progress},</if>
            <if test="executeDate != null">#{executeDate},</if>
            <if test="status != null">#{status},</if>
            <if test="isBackTrack != null "> #{isBackTrack},</if>
            <if test="remark != null">#{remark},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCedeoutBatchLog" parameterType="CedeoutBatchLogEntity">
        update t_cedeout_batch_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="batchNo != null and batchNo != ''">batch_no = #{batchNo},</if>
            <if test="calcType != null">calc_type = #{calcType},</if>
            <if test="inputParams != null">input_params = #{inputParams},</if>
            <if test="passCount != null">pass_count = #{passCount},</if>
            <if test="failCount != null">fail_count = #{failCount},</if>
            <if test="executor != null">executor = #{executor},</if>
            <if test="progress != null">progress = #{progress},</if>
            <if test="executeDate != null">execute_date = #{executeDate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isBackTrack != null "> is_back_track = #{isBackTrack},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>
    
    <update id="updateCedeoutBatchLogCount" parameterType="CedeoutBatchLogEntity">
        update t_cedeout_batch_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="passCount != null">pass_count = pass_count + #{passCount},</if>
            <if test="failCount != null">fail_count = fail_count + #{failCount},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="executor != null">executor = #{executor},</if>
            <if test="progress != null">progress = #{progress},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where batch_no = #{batchNo}
    </update>

    <delete id="deleteCedeoutBatchLogById" parameterType="Long">
        delete from t_cedeout_batch_log where id = #{id}
    </delete>

    <delete id="deleteCedeoutBatchLogByIds" parameterType="String">
        delete from t_cedeout_batch_log where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>