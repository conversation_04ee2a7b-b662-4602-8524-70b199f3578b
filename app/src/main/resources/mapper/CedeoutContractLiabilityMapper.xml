<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reinsurance.mapper.CedeoutContractLiabilityMapper">
    
    <resultMap type="CedeoutContractLiabilityEntity" id="CedeoutContractLiabilityResult">
        <result property="id"    column="id"    />
        <result property="batchNo"    column="batch_no"    />
        <result property="version"    column="version"    />
        <result property="contractId"    column="contract_id"    />
        <result property="contractCode"    column="contract_code"    />
        <result property="riskCode"    column="risk_code"    />
        <result property="liabilityCode"    column="liability_code"    />
        <result property="startDate"    column="start_date"    />
        <result property="endDate"    column="end_date"    />
        <result property="cedeoutWay"    column="cedeout_way"    />
        <result property="selfAmount"    column="self_amount"    />
        <result property="selfRatio"    column="self_ratio"    />
        <result property="cedeoutRatio"    column="cedeout_ratio"    />
        <result property="retentionLine"    column="retention_line"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCedeoutContractLiabilityVo">
        select id, batch_no, version, contract_id, contract_code, risk_code, liability_code, start_date, end_date, cedeout_way, self_amount, self_ratio,cedeout_ratio, retention_line,status, remark, is_del, create_by, create_time, update_by, update_time from t_cedeout_contract_liability
    </sql>

    <select id="selectCedeoutContractLiabilityList" parameterType="CedeoutContractLiabilityQuery" resultMap="CedeoutContractLiabilityResult">
        <include refid="selectCedeoutContractLiabilityVo"/>
        <where>  
            <if test="batchNo != null  and batchNo != ''"> and batch_no = #{batchNo}</if>
            <if test="version != null "> and version = #{version}</if>
            <if test="contractId != null "> and contract_id = #{contractId}</if>
            <if test="contractCode != null  and contractCode != ''"> and contract_code = #{contractCode}</if>
            <if test="riskCode != null  and riskCode != ''"> and risk_code = #{riskCode}</if>
            <if test="liabilityCode != null  and liabilityCode != ''"> and liability_code = #{liabilityCode}</if>
            <if test="startDate != null "> and start_date = #{startDate}</if>
            <if test="endDate != null "> and end_date = #{endDate}</if>
            <if test="cedeoutWay != null "> and cedeout_way = #{cedeoutWay}</if>
            <if test="selfAmount != null "> and self_amount = #{selfAmount}</if>
            <if test="selfRatio != null "> and self_ratio = #{selfRatio}</if>
            <if test="cedeoutRatio != null "> and cedeout_ratio = #{cedeoutRatio}</if>
            <if test="retentionLine != null "> and retention_line = #{retentionLine}</if>
            <if test="status != null "> and status = #{status}</if>
            and is_del = 0
        </where>
    </select>
    
    <select id="selectCedeoutContractLiabilityById" parameterType="Long" resultMap="CedeoutContractLiabilityResult">
        <include refid="selectCedeoutContractLiabilityVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCedeoutContractLiability" parameterType="CedeoutContractLiabilityEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_cedeout_contract_liability
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="batchNo != null and batchNo != ''">batch_no,</if>
            <if test="version != null">version,</if>
            <if test="contractId != null ">contract_id,</if>
            <if test="contractCode != null and contractCode != ''">contract_code,</if>
            <if test="riskCode != null and riskCode != ''">risk_code,</if>
            <if test="liabilityCode != null">liability_code,</if>
            <if test="startDate != null">start_date,</if>
            <if test="endDate != null">end_date,</if>
            <if test="cedeoutWay != null">cedeout_way,</if>
            <if test="selfAmount != null">self_amount,</if>
            <if test="selfRatio != null">self_ratio,</if>
            <if test="cedeoutRatio != null">cedeout_ratio,</if>
            <if test="retentionLine != null">retention_line,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="batchNo != null and batchNo != ''">#{batchNo},</if>
            <if test="version != null">#{version},</if>
            <if test="contractId != null">#{contractId},</if>
            <if test="contractCode != null and contractCode != ''">#{contractCode},</if>
            <if test="riskCode != null and riskCode != ''">#{riskCode},</if>
            <if test="liabilityCode != null">#{liabilityCode},</if>
            <if test="startDate != null">#{startDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="cedeoutWay != null">#{cedeoutWay},</if>
            <if test="selfAmount != null">#{selfAmount},</if>
            <if test="selfRatio != null">#{selfRatio},</if>
            <if test="cedeoutRatio != null">#{cedeoutRatio},</if>
            <if test="retentionLine != null">#{retentionLine},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCedeoutContractLiability" parameterType="CedeoutContractLiabilityEntity">
        update t_cedeout_contract_liability
        <trim prefix="SET" suffixOverrides=",">
            <if test="batchNo != null and batchNo != ''">batch_no = #{batchNo},</if>
            <if test="version != null">version = #{version},</if>
            <if test="contractId != null">contract_id = #{contractId},</if>
            <if test="contractCode != null and contractCode != ''">contract_code = #{contractCode},</if>
            <if test="riskCode != null and riskCode != ''">risk_code = #{riskCode},</if>
            <if test="liabilityCode != null">liability_code = #{liabilityCode},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="cedeoutWay != null">cedeout_way = #{cedeoutWay},</if>
            <if test="selfAmount != null">self_amount = #{selfAmount},</if>
            <if test="selfRatio != null">self_ratio = #{selfRatio},</if>
            <if test="cedeoutRatio != null">cedeout_ratio = #{cedeoutRatio},</if>
            <if test="retentionLine != null">retention_line = #{retentionLine},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteCedeoutContractLiabilityById" parameterType="Long">
        update t_cedeout_contract_liability set is_del = 1 where id = #{id}
    </update>

    <delete id="deleteCedeoutContractLiabilityByIds" parameterType="String">
        delete from t_cedeout_contract_liability where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    
    <resultMap type="DwsEastZbcpxxbDTO" id="DwsEastZbcpxxbResult">
    	<result property="zbxhthm"    column="zbxhthm"    />
        <result property="zbxhtmc"    column="zbxhtmc"    />
        <result property="htfylx"    column="htfylx"    />
        <result property="zbxfyzhth"    column="zbxfyzhth"    />
        <result property="xzbm"    column="xzbm"    />
        <result property="xzjc"    column="xzjc"    />
        <result property="tgxz"    column="tgxz"    />
        <result property="xl"    column="xl"    />
        <result property="zrbm"    column="zrbm"    />
        <result property="zrmc"    column="zrmc"    />
        <result property="zbxgsdm"    column="zbxgsdm"    />
        <result property="zbxgsmc"    column="zbxgsmc"    />
		<result property="fbfs"    column="fbfs"    />
        <result property="bxqxlx"    column="bxqxlx"    />
        <result property="zle"    column="zle"    />
        <result property="zlbl"    column="zlbl"    />
        <result property="fbbl"    column="fbbl"    />
        <result property="zbrcyfebl"    column="zbrcyfebl"    />
    </resultMap>
    <select id="selectContractLiabilityAsEastZbcpxxb" resultMap="DwsEastZbcpxxbResult">
    	select cc.company_code as zbxgsdm, rc.company_name as zbxgsmc, l.contract_code as zbxhthm, cc.contract_name as zbxhtmc, cc.contract_type as htfylx, cc.main_contract_code as zbxfyzhth, 
		l.risk_code as xzbm, trl.risk_name as xzjc, trl.sale_chnl as tgxz, trl.ins_product_type_name as xl, l.liability_code as zrdm, trl.liability_name as zrmc, l.cedeout_way as fbfs, 
		(select group_concat(lp.period_type_name separator';') from t_risk_liability_period lp where lp.is_del=0 and lp.status=0 and lp.risk_code=l.risk_code and lp.liability_code=l.liability_code) as bxqxlx, 
		l.retention_line as zle, format(l.self_ratio * 100, 2) as zlbl, format(l.cedeout_ratio * 100, 2) as fbbl, format(l.self_amount * 100, 2) as zbrcyfebl from t_cedeout_contract_liability l inner join t_risk_liability trl 
		on l.risk_code=trl.risk_code and trl.liability_code=l.liability_code inner join t_cedeout_contract cc on cc.contract_code=l.contract_code inner join t_cedeout_company rc on cc.company_code=rc.company_code 
		where l.is_del=0 and l.status=#{status} and trl.is_del=0 and trl.status=#{status} and l.create_time &gt;= date_format(#{params.startDate}, '%Y-%m-%d') and l.create_time &lt;= date_format(#{params.endDate}, '%Y-%m-%d') order by l.id
    </select>

    <select id="selectContractLiabilityAsPrpProduct" resultType="DwsPrpProductDTO">
        select
        cc.company_code as ReinsurerCode,
        rc.company_name as ReinsurerName,
        l.contract_code as ReInsuranceContNo,
        cc.contract_name as ReInsuranceContName,
        cc.contract_abbr as ReInsuranceContTitle,
        cc.contract_type as ContOrAmendmentType,
        cc.main_contract_code as MainReInsuranceContNo,
        l.risk_code as ProductCode,
        trl.risk_name as ProductName,
        trl.sale_chnl as GPFlag,
        trl.prp_product_type as ProductType,
        l.liability_code as LiabilityCode,
        trl.liability_name as LiabilityName,
        l.cedeout_way as ReinsurMode,
        trl.prp_insu_period as TermType,
        l.retention_line as RetentionAmount,
        format(l.self_ratio * 100, 2) as RetentionPercentage,
        format(l.cedeout_ratio * 100, 2) as QuotaSharePercentage,
        format(l.self_amount * 100, 2) as ReinsuranceShare,
        cc.contract_class as ReInsuranceType
        from t_cedeout_contract_liability l
        inner join t_risk_liability trl on l.risk_code=trl.risk_code and trl.liability_code=l.liability_code
        inner join t_cedeout_contract cc on cc.contract_code=l.contract_code
        inner join t_cedeout_company rc on cc.company_code=rc.company_code
        where l.is_del=0 and l.status=0 and trl.is_del=0 and trl.status=0
        and l.create_time &gt;= date_format(#{params.startDate}, '%Y-%m-%d')
        and l.create_time &lt;= date_format(#{params.endDate}, '%Y-%m-%d')
        order by l.id
    </select>
</mapper>