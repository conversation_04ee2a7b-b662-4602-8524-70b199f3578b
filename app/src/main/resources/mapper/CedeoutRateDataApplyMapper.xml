<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reinsurance.mapper.CedeoutRateDataApplyMapper">
    
    <resultMap type="CedeoutRateDataApplyEntity" id="CedeoutRateDataApplyResult">
        <result property="id"    column="id"    />
        <result property="batchNo"    column="batch_no"    />
        <result property="version"    column="version"    />
        <result property="rateCode"    column="rate_code"    />
        <result property="rateType"    column="rate_type"    />
        <result property="insuredAge"    column="insured_age"    />
        <result property="insuredSex"    column="insured_sex"    />
        <result property="policyYear"    column="policy_year"    />
        <result property="rateValue"    column="rate_value"    />
        <result property="rateUnit"    column="rate_unit"    />
        <result property="sellType"    column="sell_type"    />
        <result property="riskType"    column="risk_type"    />
        <result property="insuredAppAge"    column="insured_app_age"    />
        <result property="insuredOccType"    column="insured_occ_type"    />
        <result property="extends1"    column="extends1"    />
        <result property="extends2"    column="extends2"    />
        <result property="extends3"    column="extends3"    />
        <result property="extends4"    column="extends4"    />
        <result property="extends5"    column="extends5"    />
        <result property="remark"    column="remark"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCedeoutRateDataApplyVo">
        select id, batch_no, version, rate_code, rate_type, insured_age, insured_sex, policy_year, rate_value, rate_unit,sell_type,risk_type,insured_app_age, insured_occ_type, extends1, extends2, extends3, extends4, extends5, remark, is_del, create_by, create_time, update_by, update_time from t_cedeout_rate_data_apply
    </sql>

    <select id="selectCedeoutRateDataApplyList" parameterType="CedeoutRateDataApplyQuery" resultMap="CedeoutRateDataApplyResult">
        <include refid="selectCedeoutRateDataApplyVo"/>
        <where>  
            <if test="batchNo != null  and batchNo != ''"> and batch_no = #{batchNo}</if>
            <if test="version != null "> and version = #{version}</if>
            <if test="rateCode != null  and rateCode != ''"> and rate_code = #{rateCode}</if>
            <if test="rateType != null "> and rate_type = #{rateType}</if>
            <if test="insuredAge != null "> and insured_age = #{insuredAge}</if>
            <if test="insuredSex != null "> and insured_sex = #{insuredSex}</if>
            <if test="policyYear != null "> and policy_year = #{policyYear}</if>
            <if test="rateValue != null "> and rate_value = #{rateValue}</if>
            <if test="rateUnit != null "> and rate_unit = #{rateUnit}</if>
            <if test="sellType != null "> and sell_type = #{sellType}</if>
            <if test="riskType != null "> and risk_type = #{riskType}</if>
            <if test="insuredAppAge != null "> and insured_app_age = #{insuredAppAge}</if>
            <if test="insuredOccType != null "> and insured_occ_type = #{insuredOccType}</if>
            <if test="extends1 != null "> and extends1 = #{extends1}</if>
            <if test="extends2 != null  and extends2 != ''"> and extends2 = #{extends2}</if>
            <if test="extends3 != null "> and extends3 = #{extends3}</if>
            <if test="extends4 != null  and extends4 != ''"> and extends4 = #{extends4}</if>
            <if test="extends5 != null  and extends5 != ''"> and extends5 = #{extends5}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
    </select>
    
    <select id="selectCedeoutRateDataApplyById" parameterType="Long" resultMap="CedeoutRateDataApplyResult">
        <include refid="selectCedeoutRateDataApplyVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCedeoutRateDataApply" parameterType="CedeoutRateDataApplyEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_cedeout_rate_data_apply
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="batchNo != null and batchNo != ''">batch_no,</if>
            <if test="version != null">version,</if>
            <if test="rateCode != null and rateCode != ''">rate_code,</if>
            <if test="rateType != null">rate_type,</if>
            <if test="insuredAge != null">insured_age,</if>
            <if test="insuredSex != null">insured_sex,</if>
            <if test="policyYear != null">policy_year,</if>
            <if test="rateValue != null">rate_value,</if>
            <if test="rateUnit != null">rate_unit,</if>
            <if test="sellType != null">sell_type,</if>
            <if test="riskType != null "> risk_type,</if>
            <if test="insuredAppAge != null "> insured_app_age,</if>
            <if test="insuredOccType != null">insured_occ_type,</if>
            <if test="extends1 != null">extends1,</if>
            <if test="extends2 != null">extends2,</if>
            <if test="extends3 != null">extends3,</if>
            <if test="extends4 != null">extends4,</if>
            <if test="extends5 != null">extends5,</if>
            <if test="remark != null">remark,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="batchNo != null and batchNo != ''">#{batchNo},</if>
            <if test="version != null">#{version},</if>
            <if test="rateCode != null and rateCode != ''">#{rateCode},</if>
            <if test="rateType != null">#{rateType},</if>
            <if test="insuredAge != null">#{insuredAge},</if>
            <if test="insuredSex != null">#{insuredSex},</if>
            <if test="policyYear != null">#{policyYear},</if>
            <if test="rateValue != null">#{rateValue},</if>
            <if test="riskType != null "> #{riskType},</if>
            <if test="insuredAppAge != null ">#{insuredAppAge},</if>
            <if test="insuredOccType != null">#{insuredOccType},</if>
            <if test="rateUnit != null">#{rateUnit},</if>
            <if test="sellType != null">#{sellType},</if>
            <if test="extends1 != null">#{extends1},</if>
            <if test="extends2 != null">#{extends2},</if>
            <if test="extends3 != null">#{extends3},</if>
            <if test="extends4 != null">#{extends4},</if>
            <if test="extends5 != null">#{extends5},</if>
            <if test="remark != null">#{remark},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCedeoutRateDataApply" parameterType="CedeoutRateDataApplyEntity">
        update t_cedeout_rate_data_apply
        <trim prefix="SET" suffixOverrides=",">
            <if test="batchNo != null and batchNo != ''">batch_no = #{batchNo},</if>
            <if test="version != null">version = #{version},</if>
            <if test="rateCode != null and rateCode != ''">rate_code = #{rateCode},</if>
            <if test="rateType != null">rate_type = #{rateType},</if>
            <if test="insuredAge != null">insured_age = #{insuredAge},</if>
            <if test="insuredSex != null">insured_sex = #{insuredSex},</if>
            <if test="policyYear != null">policy_year = #{policyYear},</if>
            <if test="rateValue != null">rate_value = #{rateValue},</if>
            <if test="rateUnit != null">rate_unit = #{rateUnit},</if>
            <if test="sellType != null">sell_type = #{sellType},</if>
            <if test="riskType != null ">  risk_type = #{riskType},</if>
            <if test="insuredAppAge != null ">  insured_app_age = #{insuredAppAge},</if>
            <if test="insuredOccType != null">insured_occ_type = #{insuredOccType},</if>
            <if test="extends1 != null">extends1 = #{extends1},</if>
            <if test="extends2 != null">extends2 = #{extends2},</if>
            <if test="extends3 != null">extends3 = #{extends3},</if>
            <if test="extends4 != null">extends4 = #{extends4},</if>
            <if test="extends5 != null">extends5 = #{extends5},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCedeoutRateDataApplyById" parameterType="Long">
        delete from t_cedeout_rate_data_apply where id = #{id}
    </delete>

    <delete id="deleteCedeoutRateDataApplyByIds" parameterType="String">
        delete from t_cedeout_rate_data_apply where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>