<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reinsurance.mapper.CedeoutCompanyBankTrackMapper">
    
    <resultMap type="CedeoutCompanyBankTrackEntity" id="CedeoutCompanyBankTrackResult">
        <result property="trackId"    column="track_id"    />
        <result property="id"    column="id"    />
        <result property="batchNo"    column="batch_no"    />
        <result property="version"    column="version"    />
        <result property="companyCode"    column="company_code"    />
        <result property="tradeName"    column="trade_name"    />
        <result property="accountCode"    column="account_code"    />
        <result property="tradeRegionCode"    column="trade_region_code"    />
        <result property="tradeBankCode"    column="trade_bank_code"    />
        <result property="tradeBankName"    column="trade_bank_name"    />
        <result property="interbankCode"    column="interbank_code"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCedeoutCompanyBankTrackVo">
        select track_id, id, batch_no, version, company_code, trade_name, account_code, trade_region_code, trade_bank_code, trade_bank_name, interbank_code, status, remark, is_del, create_by, create_time, update_by, update_time from t_cedeout_company_bank_track
    </sql>

    <select id="selectCedeoutCompanyBankTrackList" parameterType="CedeoutCompanyBankTrackQuery" resultMap="CedeoutCompanyBankTrackResult">
        <include refid="selectCedeoutCompanyBankTrackVo"/>
        <where>  
            <if test="batchNo != null  and batchNo != ''"> and batch_no = #{batchNo}</if>
            <if test="version != null "> and version = #{version}</if>
            <if test="companyCode != null  and companyCode != ''"> and company_code = #{companyCode}</if>
            <if test="tradeName != null  and tradeName != ''"> and trade_name like concat('%', #{tradeName}, '%')</if>
            <if test="accountCode != null  and accountCode != ''"> and account_code = #{accountCode}</if>
            <if test="tradeRegionCode != null  and tradeRegionCode != ''"> and trade_region_code = #{tradeRegionCode}</if>
            <if test="tradeBankCode != null  and tradeBankCode != ''"> and trade_bank_code = #{tradeBankCode}</if>
            <if test="tradeBankName != null  and tradeBankName != ''"> and trade_bank_name like concat('%', #{tradeBankName}, '%')</if>
            <if test="interbankCode != null  and interbankCode != ''"> and interbank_code = #{interbankCode}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
    </select>
    
    <select id="selectCedeoutCompanyBankTrackByTrackId" parameterType="Long" resultMap="CedeoutCompanyBankTrackResult">
        <include refid="selectCedeoutCompanyBankTrackVo"/>
        where track_id = #{trackId}
    </select>
        
    <insert id="insertCedeoutCompanyBankTrack" parameterType="CedeoutCompanyBankTrackEntity" useGeneratedKeys="true" keyProperty="trackId">
        insert into t_cedeout_company_bank_track
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="batchNo != null and batchNo != ''">batch_no,</if>
            <if test="version != null">version,</if>
            <if test="companyCode != null and companyCode != ''">company_code,</if>
            <if test="tradeName != null and tradeName != ''">trade_name,</if>
            <if test="accountCode != null">account_code,</if>
            <if test="tradeRegionCode != null">trade_region_code,</if>
            <if test="tradeBankCode != null">trade_bank_code,</if>
            <if test="tradeBankName != null">trade_bank_name,</if>
            <if test="interbankCode != null">interbank_code,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="batchNo != null and batchNo != ''">#{batchNo},</if>
            <if test="version != null">#{version},</if>
            <if test="companyCode != null and companyCode != ''">#{companyCode},</if>
            <if test="tradeName != null and tradeName != ''">#{tradeName},</if>
            <if test="accountCode != null">#{accountCode},</if>
            <if test="tradeRegionCode != null">#{tradeRegionCode},</if>
            <if test="tradeBankCode != null">#{tradeBankCode},</if>
            <if test="tradeBankName != null">#{tradeBankName},</if>
            <if test="interbankCode != null">#{interbankCode},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCedeoutCompanyBankTrack" parameterType="CedeoutCompanyBankTrackEntity">
        update t_cedeout_company_bank_track
        <trim prefix="SET" suffixOverrides=",">
            <if test="id != null">id = #{id},</if>
            <if test="batchNo != null and batchNo != ''">batch_no = #{batchNo},</if>
            <if test="version != null">version = #{version},</if>
            <if test="companyCode != null and companyCode != ''">company_code = #{companyCode},</if>
            <if test="tradeName != null and tradeName != ''">trade_name = #{tradeName},</if>
            <if test="accountCode != null">account_code = #{accountCode},</if>
            <if test="tradeRegionCode != null">trade_region_code = #{tradeRegionCode},</if>
            <if test="tradeBankCode != null">trade_bank_code = #{tradeBankCode},</if>
            <if test="tradeBankName != null">trade_bank_name = #{tradeBankName},</if>
            <if test="interbankCode != null">interbank_code = #{interbankCode},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where track_id = #{trackId}
    </update>

    <delete id="deleteCedeoutCompanyBankTrackByTrackId" parameterType="Long">
        delete from t_cedeout_company_bank_track where track_id = #{trackId}
    </delete>

    <delete id="deleteCedeoutCompanyBankTrackByTrackIds" parameterType="String">
        delete from t_cedeout_company_bank_track where track_id in 
        <foreach item="trackId" collection="array" open="(" separator="," close=")">
            #{trackId}
        </foreach>
    </delete>
</mapper>