<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reinsurance.mapper.RiskLiabilityMapper">
    
    <resultMap type="RiskLiabilityEntity" id="RiskLiabilityResult">
        <result property="id"    column="id"    />
        <result property="batchNo"    column="batch_no"    />
        <result property="version"    column="version"    />
        <result property="riskCode"    column="risk_code"    />
        <result property="riskName"    column="risk_name"    />
        <result property="liabilityCode"    column="liability_code"    />
        <result property="liabilityName"    column="liability_name"    />
        <result property="saleChnl"    column="sale_chnl"    />
        <result property="isCedeOut"    column="is_cede_out"    />
        <result property="intoCalamity"    column="into_calamity"    />
        <result property="reserveType"    column="reserve_type"    />
        <result property="periodType"    column="period_type"    />
        <result property="productType"    column="product_type"    />
        <result property="exemptType"    column="exempt_type"    />
        <result property="riskType"    column="risk_type"    />
        <result property="businessType"    column="business_type"    />
        <result property="rsCalcFrequency"    column="rs_calc_frequency"    />
        <result property="taxRate"    column="tax_rate"    />
        <result property="rsContractType"    column="rs_contract_type"    />
        <result property="periodFlag"    column="period_flag"    />
        <result property="deductibleType"    column="deductible_type"    />
        <result property="claimNotifyLimit"    column="claim_notify_limit"    />
        <result property="claimInvolvedLimit"    column="claim_involved_limit"    />
        <result property="claimCount"    column="claim_count"    />
        <result property="mainDuty"    column="main_duty"    />
        <result property="insGovFlag"    column="ins_gov_flag"    />
        <result property="insProductType"    column="ins_product_type"    />
        <result property="insProductTypeName"    column="ins_product_type_name"    />
        <result property="insLiabilityType"    column="ins_liability_type"    />
        <result property="insLiabilityTypeName"    column="ins_liability_type_name"    />
        <result property="prpLiabilityType"    column="prp_liability_type"    />
        <result property="prpProductType"    column="prp_product_type"    />
        <result property="prpInsuPeriod"    column="prp_insu_period"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectRiskLiabilityVo">
        select id, batch_no, version, risk_code, risk_name, liability_code, liability_name, sale_chnl, is_cede_out, into_calamity, reserve_type, period_type, product_type, exempt_type, risk_type, business_type, rs_calc_frequency, tax_rate, rs_contract_type, period_flag, deductible_type, claim_notify_limit, claim_involved_limit, claim_count, main_duty, ins_gov_flag, ins_product_type, ins_product_type_name, ins_liability_type, ins_liability_type_name, prp_liability_type, prp_product_type, prp_insu_period, status, remark, is_del, create_by, create_time, update_by, update_time from t_risk_liability
    </sql>

    <select id="selectRiskLiabilityList" parameterType="RiskLiabilityQuery" resultMap="RiskLiabilityResult">
        <include refid="selectRiskLiabilityVo"/>
        <where>
            <if test="batchNo != null  and batchNo != ''"> and batch_no = #{batchNo}</if>
            <if test="version != null "> and version = #{version}</if>
            <if test="riskCode != null  and riskCode != ''"> and risk_code = #{riskCode}</if>
            <if test="riskName != null  and riskName != ''"> and risk_name like concat('%', #{riskName}, '%')</if>
            <if test="liabilityCode != null  and liabilityCode != ''"> and liability_code = #{liabilityCode}</if>
            <if test="liabilityName != null  and liabilityName != ''"> and liability_name like concat('%', #{liabilityName}, '%')</if>
            <if test="saleChnl != null  and saleChnl != ''"> and sale_chnl = #{saleChnl}</if>
            <if test="isCedeOut != null "> and is_cede_out = #{isCedeOut}</if>
            <if test="intoCalamity != null "> and into_calamity = #{intoCalamity}</if>
            <if test="reserveType != null "> and reserve_type = #{reserveType}</if>
            <if test="periodType != null "> and period_type = #{periodType}</if>
            <if test="productType != null "> and product_type = #{productType}</if>
            <if test="exemptType != null "> and exempt_type = #{exemptType}</if>
            <if test="riskType != null  and riskType != ''"> and risk_type = #{riskType}</if>
            <if test="businessType != null "> and business_type = #{businessType}</if>
            <if test="rsCalcFrequency != null "> and rs_calc_frequency = #{rsCalcFrequency}</if>
            <if test="taxRate != null "> and tax_rate = #{taxRate}</if>
            <if test="rsContractType != null "> and rs_contract_type = #{rsContractType}</if>
            <if test="periodFlag != null "> and period_flag = #{periodFlag}</if>
            <if test="deductibleType != null "> and deductible_type = #{deductibleType}</if>
            <if test="claimNotifyLimit != null "> and claim_notify_limit = #{claimNotifyLimit}</if>
            <if test="claimInvolvedLimit != null "> and claim_involved_limit = #{claimInvolvedLimit}</if>
            <if test="claimCount != null "> and claim_count = #{claimCount}</if>
            <if test="mainDuty != null "> and main_duty = #{mainDuty}</if>
            <if test="insGovFlag != null and insGovFlag != ''"> and ins_gov_flag = #{insGovFlag}</if>
            <if test="insProductType != null and insProductType != ''"> and ins_product_type = #{insProductType}</if>
            <if test="insProductTypeName != null and insProductTypeName != ''"> and ins_product_type_name = #{insProductTypeName}</if>
            <if test="insLiabilityType != null and insLiabilityType != ''"> and ins_liability_type = #{insLiabilityType}</if>
            <if test="insLiabilityTypeName != null and insLiabilityTypeName != ''"> and ins_liability_type_name = #{insLiabilityTypeName}</if>
            <if test="prpLiabilityType != null and prpLiabilityType != ''"> and prp_liability_type = #{prpLiabilityType}</if>
            <if test="prpProductType != null and prpProductType != ''"> and prp_product_type = #{prpProductType}</if>
            <if test="prpInsuPeriod != null and prpInsuPeriod != ''"> and prp_insu_period = #{prpInsuPeriod}</if>
            <if test="status != null "> and status = #{status}</if>
            and is_del = 0
        </where>
    </select>
    
    <select id="selectRiskLiabilityById" parameterType="Long" resultMap="RiskLiabilityResult">
        <include refid="selectRiskLiabilityVo"/>
        where id = #{id}
    </select>

    <select id="selectRiskLiabilityByIds" parameterType="Long" resultMap="RiskLiabilityResult">
        <include refid="selectRiskLiabilityVo"/>
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectRiskLiabilityByRiskCodes" parameterType="String" resultMap="RiskLiabilityResult">
        <include refid="selectRiskLiabilityVo"/>
        where is_del = 0 and status = 0 and risk_code in
        <foreach item="riskCode" collection="riskCodes" open="(" separator="," close=")">
            #{riskCode}
        </foreach>
    </select>

    <select id="selectRiskLiabilityReserveType" resultMap="RiskLiabilityResult">
        select risk_code, reserve_type from t_risk_liability group by risk_code
    </select>

    <insert id="insertRiskLiability" parameterType="RiskLiabilityEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_risk_liability
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="batchNo != null and batchNo != ''">batch_no,</if>
            <if test="version != null">version,</if>
            <if test="riskCode != null and riskCode != ''">risk_code,</if>
            <if test="riskName != null and riskName != ''">risk_name,</if>
            <if test="liabilityCode != null and liabilityCode != ''">liability_code,</if>
            <if test="liabilityName != null and liabilityName != ''">liability_name,</if>
            <if test="saleChnl != null and saleChnl != ''">sale_chnl,</if>
            <if test="isCedeOut != null">is_cede_out,</if>
            <if test="intoCalamity != null">into_calamity,</if>
            <if test="reserveType != null">reserve_type,</if>
            <if test="periodType != null">period_type,</if>
            <if test="productType != null">product_type,</if>
            <if test="exemptType != null">exempt_type,</if>
            <if test="riskType != null and riskType != ''">risk_type,</if>
            <if test="businessType != null">business_type,</if>
            <if test="rsCalcFrequency != null">rs_calc_frequency,</if>
            <if test="taxRate != null">tax_rate,</if>
            <if test="rsContractType != null">rs_contract_type,</if>
            <if test="periodFlag != null">period_flag,</if>
            <if test="deductibleType != null">deductible_type,</if>
            <if test="claimNotifyLimit != null">claim_notify_limit,</if>
            <if test="claimInvolvedLimit != null">claim_involved_limit,</if>
            <if test="claimCount != null">claim_count,</if>
            <if test="mainDuty != null and mainDuty != ''">main_duty,</if>
            <if test="insGovFlag != null">ins_gov_flag,</if>
            <if test="insProductType != null">ins_product_type,</if>
            <if test="insProductTypeName != null">ins_product_type_name,</if>
            <if test="insLiabilityType != null">ins_liability_type,</if>
            <if test="insLiabilityTypeName != null">ins_liability_type_name,</if>
            <if test="prpLiabilityType != null">prp_liability_type,</if>
            <if test="prpProductType != null">prp_product_type,</if>
            <if test="prpInsuPeriod != null">prp_insu_period,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="batchNo != null and batchNo != ''">#{batchNo},</if>
            <if test="version != null">#{version},</if>
            <if test="riskCode != null and riskCode != ''">#{riskCode},</if>
            <if test="riskName != null and riskName != ''">#{riskName},</if>
            <if test="liabilityCode != null and liabilityCode != ''">#{liabilityCode},</if>
            <if test="liabilityName != null and liabilityName != ''">#{liabilityName},</if>
            <if test="saleChnl != null and saleChnl != ''">#{saleChnl},</if>
            <if test="isCedeOut != null">#{isCedeOut},</if>
            <if test="intoCalamity != null">#{intoCalamity},</if>
            <if test="reserveType != null">#{reserveType},</if>
            <if test="periodType != null">#{periodType},</if>
            <if test="productType != null">#{productType},</if>
            <if test="exemptType != null">#{exemptType},</if>
            <if test="riskType != null and riskType != ''">#{riskType},</if>
            <if test="businessType != null">#{businessType},</if>
            <if test="rsCalcFrequency != null">#{rsCalcFrequency},</if>
            <if test="taxRate != null">#{taxRate},</if>
            <if test="rsContractType != null">#{rsContractType},</if>
            <if test="periodFlag != null">#{periodFlag},</if>
            <if test="deductibleType != null">#{deductibleType},</if>
            <if test="claimNotifyLimit != null">#{claimNotifyLimit},</if>
            <if test="claimInvolvedLimit != null">#{claimInvolvedLimit},</if>
            <if test="claimCount != null">#{claimCount},</if>
            <if test="mainDuty != null and mainDuty != ''">#{mainDuty},</if>
            <if test="insGovFlag != null">#{insGovFlag},</if>
            <if test="insProductType != null">#{insProductType},</if>
            <if test="insProductTypeName != null">#{insProductTypeName},</if>
            <if test="insLiabilityType != null">#{insLiabilityType},</if>
            <if test="insLiabilityTypeName != null">#{insLiabilityTypeName},</if>
            <if test="prpLiabilityType != null">#{prpLiabilityType},</if>
            <if test="prpProductType != null">#{prpProductType},</if>
            <if test="prpInsuPeriod != null">#{prpInsuPeriod},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateRiskLiability" parameterType="RiskLiabilityEntity">
        update t_risk_liability
        <trim prefix="SET" suffixOverrides=",">
            <if test="batchNo != null and batchNo != ''">batch_no = #{batchNo},</if>
            <if test="version != null">version = #{version},</if>
            <if test="riskCode != null and riskCode != ''">risk_code = #{riskCode},</if>
            <if test="riskName != null and riskName != ''">risk_name = #{riskName},</if>
            <if test="liabilityCode != null and liabilityCode != ''">liability_code = #{liabilityCode},</if>
            <if test="liabilityName != null and liabilityName != ''">liability_name = #{liabilityName},</if>
            <if test="saleChnl != null and saleChnl != ''">sale_chnl = #{saleChnl},</if>
            <if test="isCedeOut != null">is_cede_out = #{isCedeOut},</if>
            <if test="intoCalamity != null">into_calamity = #{intoCalamity},</if>
            <if test="reserveType != null">reserve_type = #{reserveType},</if>
            <if test="periodType != null">period_type = #{periodType},</if>
            <if test="productType != null">product_type = #{productType},</if>
            <if test="exemptType != null">exempt_type = #{exemptType},</if>
            <if test="riskType != null and riskType != ''">risk_type = #{riskType},</if>
            <if test="businessType != null">business_type = #{businessType},</if>
            <if test="rsCalcFrequency != null">rs_calc_frequency = #{rsCalcFrequency},</if>
            <if test="taxRate != null">tax_rate = #{taxRate},</if>
            <if test="rsContractType != null">rs_contract_type = #{rsContractType},</if>
            <if test="periodFlag != null">period_flag = #{periodFlag},</if>
            <if test="deductibleType != null">deductible_type = #{deductibleType},</if>
            <if test="claimNotifyLimit != null">claim_notify_limit = #{claimNotifyLimit},</if>
            <if test="claimInvolvedLimit != null">claim_involved_limit = #{claimInvolvedLimit},</if>
            <if test="claimCount != null">claim_count = #{claimCount},</if>
            <if test="mainDuty != null and mainDuty != ''">main_duty = #{mainDuty},</if>
            <if test="insGovFlag != null and insGovFlag != ''">ins_gov_flag = #{insGovFlag},</if>
            <if test="insProductType != null and insProductType != ''">ins_product_type = #{insProductType},</if>
            <if test="insProductTypeName != null and insProductTypeName != ''">ins_product_type_name = #{insProductTypeName},</if>
            <if test="insLiabilityType != null and insLiabilityType != ''">ins_liability_type = #{insLiabilityType},</if>
            <if test="insLiabilityTypeName != null and insLiabilityTypeName != ''">ins_liability_type_name = #{insLiabilityTypeName},</if>
            <if test="prpLiabilityType != null and prpLiabilityType != ''">prp_liability_type = #{prpLiabilityType},</if>
            <if test="prpProductType != null and prpProductType != ''">prp_product_type = #{prpProductType},</if>
            <if test="prpInsuPeriod != null and prpInsuPeriod != ''">prp_insu_period = #{prpInsuPeriod},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteRiskLiabilityById" parameterType="Long">
        update t_risk_liability set is_del = 1 where id = #{id}
    </update>

    <update id="deleteRiskLiabilityByIds" parameterType="String">
        update t_risk_liability set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>