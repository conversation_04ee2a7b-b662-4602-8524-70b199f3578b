<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reinsurance.mapper.DwsPrpProductMapper">
    
    <resultMap type="DwsPrpProductEntity" id="DwsPrpProductResult">
        <result property="Id"    column="Id"    />
        <result property="TransactionNo"    column="TransactionNo"    />
        <result property="CompanyCode"    column="CompanyCode"    />
        <result property="ReInsuranceContNo"    column="ReInsuranceContNo"    />
        <result property="ReInsuranceContName"    column="ReInsuranceContName"    />
        <result property="ReInsuranceContTitle"    column="ReInsuranceContTitle"    />
        <result property="MainReInsuranceContNo"    column="MainReInsuranceContNo"    />
        <result property="ContOrAmendmentType"    column="ContOrAmendmentType"    />
        <result property="ProductCode"    column="ProductCode"    />
        <result property="ProductName"    column="ProductName"    />
        <result property="GPFlag"    column="GPFlag"    />
        <result property="ProductType"    column="ProductType"    />
        <result property="LiabilityCode"    column="LiabilityCode"    />
        <result property="LiabilityName"    column="LiabilityName"    />
        <result property="ReinsurerCode"    column="ReinsurerCode"    />
        <result property="ReinsurerName"    column="ReinsurerName"    />
        <result property="ReinsuranceShare"    column="ReinsuranceShare"    />
        <result property="ReinsurMode"    column="ReinsurMode"    />
        <result property="ReInsuranceType"    column="ReInsuranceType"    />
        <result property="TermType"    column="TermType"    />
        <result property="RetentionAmount"    column="RetentionAmount"    />
        <result property="RetentionPercentage"    column="RetentionPercentage"    />
        <result property="QuotaSharePercentage"    column="QuotaSharePercentage"    />
        <result property="ReportYear"    column="ReportYear"    />
        <result property="ReportMonth"    column="ReportMonth"    />
        <result property="AccountPeriod"    column="AccountPeriod"    />
        <result property="DataSource"    column="DataSource"    />
        <result property="PushStatus"    column="PushStatus"    />
        <result property="PushDate"    column="PushDate"    />
        <result property="PushBy"    column="PushBy"    />
        <result property="Remark"    column="Remark"    />
        <result property="IsDel"    column="IsDel"    />
        <result property="CreateBy"    column="CreateBy"    />
        <result property="CreateTime"    column="CreateTime"    />
        <result property="UpdateBy"    column="UpdateBy"    />
        <result property="UpdateTime"    column="UpdateTime"    />
    </resultMap>

    <sql id="selectDwsPrpProductVo">
        select Id, TransactionNo, CompanyCode, ReInsuranceContNo, ReInsuranceContName, ReInsuranceContTitle, MainReInsuranceContNo, ContOrAmendmentType, ProductCode, ProductName, GPFlag, ProductType, LiabilityCode, LiabilityName, ReinsurerCode, ReinsurerName, ReinsuranceShare, ReinsurMode, ReInsuranceType, TermType, RetentionAmount, RetentionPercentage, QuotaSharePercentage, ReportYear, ReportMonth, AccountPeriod, DataSource, PushStatus, PushDate, PushBy, Remark, IsDel, CreateBy, CreateTime, UpdateBy, UpdateTime from t_dws_prp_product
    </sql>

    <select id="selectDwsPrpProductList" parameterType="DwsPrpProductQuery" resultMap="DwsPrpProductResult">
        <include refid="selectDwsPrpProductVo"/>
        <where>  
            <if test="Id != null "> and Id = #{Id}</if>
            <if test="TransactionNo != null  and TransactionNo != ''"> and TransactionNo like concat('%', #{TransactionNo}, '%')</if>
            <if test="CompanyCode != null  and CompanyCode != ''"> and CompanyCode = #{CompanyCode}</if>
            <if test="ReInsuranceContNo != null  and ReInsuranceContNo != ''"> and ReInsuranceContNo like concat('%', #{ReInsuranceContNo}, '%')</if>
            <if test="ReInsuranceContName != null  and ReInsuranceContName != ''"> and ReInsuranceContName like concat('%', #{ReInsuranceContName}, '%')</if>
            <if test="ReInsuranceContTitle != null  and ReInsuranceContTitle != ''"> and ReInsuranceContTitle like concat('%', #{ReInsuranceContTitle}, '%')</if>
            <if test="MainReInsuranceContNo != null  and MainReInsuranceContNo != ''"> and MainReInsuranceContNo like concat('%', #{MainReInsuranceContNo}, '%')</if>
            <if test="ContOrAmendmentType != null  and ContOrAmendmentType != ''"> and ContOrAmendmentType = #{ContOrAmendmentType}</if>
            <if test="ProductCode != null  and ProductCode != ''"> and ProductCode like concat('%', #{ProductCode}, '%')</if>
            <if test="ProductName != null  and ProductName != ''"> and ProductName like concat('%', #{ProductName}, '%')</if>
            <if test="GPFlag != null  and GPFlag != ''"> and GPFlag = #{GPFlag}</if>
            <if test="ProductType != null  and ProductType != ''"> and ProductType like concat('%', #{ProductType}, '%')</if>
            <if test="LiabilityCode != null  and LiabilityCode != ''"> and LiabilityCode like concat('%', #{LiabilityCode}, '%')</if>
            <if test="LiabilityName != null  and LiabilityName != ''"> and LiabilityName like concat('%', #{LiabilityName}, '%')</if>
            <if test="ReinsurerCode != null  and ReinsurerCode != ''"> and ReinsurerCode like concat('%', #{ReinsurerCode}, '%')</if>
            <if test="ReinsurerName != null  and ReinsurerName != ''"> and ReinsurerName like concat('%', #{ReinsurerName}, '%')</if>
            <if test="ReinsuranceShare != null  and ReinsuranceShare != ''"> and ReinsuranceShare = #{ReinsuranceShare}</if>
            <if test="ReinsurMode != null  and ReinsurMode != ''"> and ReinsurMode = #{ReinsurMode}</if>
            <if test="ReInsuranceType != null  and ReInsuranceType != ''"> and ReInsuranceType = #{ReInsuranceType}</if>
            <if test="TermType != null  and TermType != ''"> and TermType = #{TermType}</if>
            <if test="RetentionAmount != null  and RetentionAmount != ''"> and RetentionAmount = #{RetentionAmount}</if>
            <if test="RetentionPercentage != null  and RetentionPercentage != ''"> and RetentionPercentage = #{RetentionPercentage}</if>
            <if test="QuotaSharePercentage != null  and QuotaSharePercentage != ''"> and QuotaSharePercentage = #{QuotaSharePercentage}</if>
            <if test="ReportYear != null "> and ReportYear = #{ReportYear}</if>
            <if test="ReportMonth != null "> and ReportMonth = #{ReportMonth}</if>
            <if test="AccountPeriod != null  and AccountPeriod != ''"> and AccountPeriod = #{AccountPeriod}</if>
            <if test="DataSource != null "> and DataSource = #{DataSource}</if>
            <if test="PushStatus != null "> and PushStatus = #{PushStatus}</if>
            <if test="PushDate != null "> and PushDate = #{PushDate}</if>
            <if test="PushBy != null  and PushBy != ''"> and PushBy = #{PushBy}</if>
            <if test="Remark != null  and Remark != ''"> and Remark like concat('%', #{Remark}, '%')</if>
            <if test="CreateBy != null  and CreateBy != ''"> and CreateBy = #{CreateBy}</if>
            <if test="CreateTime != null "> and CreateTime = #{CreateTime}</if>
            <if test="UpdateBy != null  and UpdateBy != ''"> and UpdateBy = #{UpdateBy}</if>
            <if test="UpdateTime != null "> and UpdateTime = #{UpdateTime}</if>
            and IsDel = 0
        </where>
        order by Id desc
    </select>
    
    <select id="selectDwsPrpProductById" parameterType="Long" resultMap="DwsPrpProductResult">
        <include refid="selectDwsPrpProductVo"/>
        where Id = #{Id}
    </select>

    <select id="selectDwsPrpProductByIds" parameterType="Long" resultMap="DwsPrpProductResult">
        <include refid="selectDwsPrpProductVo"/>
        where Id in
        <foreach item="Id" collection="array" open="(" separator="," close=")">
            #{Id}
        </foreach>
    </select>

    <insert id="insertDwsPrpProduct" parameterType="DwsPrpProductEntity" useGeneratedKeys="true" keyProperty="Id">
        insert into t_dws_prp_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="TransactionNo != null">TransactionNo,</if>
            <if test="CompanyCode != null">CompanyCode,</if>
            <if test="ReInsuranceContNo != null">ReInsuranceContNo,</if>
            <if test="ReInsuranceContName != null">ReInsuranceContName,</if>
            <if test="ReInsuranceContTitle != null">ReInsuranceContTitle,</if>
            <if test="MainReInsuranceContNo != null">MainReInsuranceContNo,</if>
            <if test="ContOrAmendmentType != null">ContOrAmendmentType,</if>
            <if test="ProductCode != null">ProductCode,</if>
            <if test="ProductName != null">ProductName,</if>
            <if test="GPFlag != null">GPFlag,</if>
            <if test="ProductType != null">ProductType,</if>
            <if test="LiabilityCode != null">LiabilityCode,</if>
            <if test="LiabilityName != null">LiabilityName,</if>
            <if test="ReinsurerCode != null">ReinsurerCode,</if>
            <if test="ReinsurerName != null">ReinsurerName,</if>
            <if test="ReinsuranceShare != null">ReinsuranceShare,</if>
            <if test="ReinsurMode != null">ReinsurMode,</if>
            <if test="ReInsuranceType != null">ReInsuranceType,</if>
            <if test="TermType != null">TermType,</if>
            <if test="RetentionAmount != null">RetentionAmount,</if>
            <if test="RetentionPercentage != null">RetentionPercentage,</if>
            <if test="QuotaSharePercentage != null">QuotaSharePercentage,</if>
            <if test="ReportYear != null">ReportYear,</if>
            <if test="ReportMonth != null">ReportMonth,</if>
            <if test="AccountPeriod != null">AccountPeriod,</if>
            <if test="DataSource != null">DataSource,</if>
            <if test="PushStatus != null">PushStatus,</if>
            <if test="PushDate != null">PushDate,</if>
            <if test="PushBy != null">PushBy,</if>
            <if test="Remark != null">Remark,</if>
            <if test="IsDel != null">IsDel,</if>
            <if test="CreateBy != null">CreateBy,</if>
            <if test="UpdateBy != null">UpdateBy,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="TransactionNo != null">#{TransactionNo},</if>
            <if test="CompanyCode != null">#{CompanyCode},</if>
            <if test="ReInsuranceContNo != null">#{ReInsuranceContNo},</if>
            <if test="ReInsuranceContName != null">#{ReInsuranceContName},</if>
            <if test="ReInsuranceContTitle != null">#{ReInsuranceContTitle},</if>
            <if test="MainReInsuranceContNo != null">#{MainReInsuranceContNo},</if>
            <if test="ContOrAmendmentType != null">#{ContOrAmendmentType},</if>
            <if test="ProductCode != null">#{ProductCode},</if>
            <if test="ProductName != null">#{ProductName},</if>
            <if test="GPFlag != null">#{GPFlag},</if>
            <if test="ProductType != null">#{ProductType},</if>
            <if test="LiabilityCode != null">#{LiabilityCode},</if>
            <if test="LiabilityName != null">#{LiabilityName},</if>
            <if test="ReinsurerCode != null">#{ReinsurerCode},</if>
            <if test="ReinsurerName != null">#{ReinsurerName},</if>
            <if test="ReinsuranceShare != null">#{ReinsuranceShare},</if>
            <if test="ReinsurMode != null">#{ReinsurMode},</if>
            <if test="ReInsuranceType != null">#{ReInsuranceType},</if>
            <if test="TermType != null">#{TermType},</if>
            <if test="RetentionAmount != null">#{RetentionAmount},</if>
            <if test="RetentionPercentage != null">#{RetentionPercentage},</if>
            <if test="QuotaSharePercentage != null">#{QuotaSharePercentage},</if>
            <if test="ReportYear != null">#{ReportYear},</if>
            <if test="ReportMonth != null">#{ReportMonth},</if>
            <if test="AccountPeriod != null">#{AccountPeriod},</if>
            <if test="DataSource != null">#{DataSource},</if>
            <if test="PushStatus != null">#{PushStatus},</if>
            <if test="PushDate != null">#{PushDate},</if>
            <if test="PushBy != null">#{PushBy},</if>
            <if test="Remark != null">#{Remark},</if>
            <if test="IsDel != null">#{IsDel},</if>
            <if test="CreateBy != null">#{CreateBy},</if>
            <if test="UpdateBy != null">#{UpdateBy},</if>
         </trim>
    </insert>

    <insert id="insertBatchDwsPrpProduct" parameterType="java.util.List">
        insert into t_dws_prp_product(TransactionNo, CompanyCode, ReInsuranceContNo, ReInsuranceContName, ReInsuranceContTitle, MainReInsuranceContNo, ContOrAmendmentType, ProductCode, ProductName, GPFlag, ProductType, LiabilityCode, LiabilityName, ReinsurerCode, ReinsurerName, ReinsuranceShare, ReinsurMode, ReInsuranceType, TermType, RetentionAmount, RetentionPercentage, QuotaSharePercentage, ReportYear, ReportMonth, AccountPeriod, DataSource, PushStatus, PushDate, PushBy, Remark, CreateBy, UpdateBy) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.TransactionNo}, #{item.CompanyCode}, #{item.ReInsuranceContNo}, #{item.ReInsuranceContName}, #{item.ReInsuranceContTitle}, #{item.MainReInsuranceContNo}, #{item.ContOrAmendmentType}, #{item.ProductCode}, #{item.ProductName}, #{item.GPFlag}, #{item.ProductType}, #{item.LiabilityCode}, #{item.LiabilityName}, #{item.ReinsurerCode}, #{item.ReinsurerName}, #{item.ReinsuranceShare}, #{item.ReinsurMode}, #{item.ReInsuranceType}, #{item.TermType}, #{item.RetentionAmount}, #{item.RetentionPercentage}, #{item.QuotaSharePercentage}, #{item.ReportYear}, #{item.ReportMonth}, #{item.AccountPeriod}, #{item.DataSource}, #{item.PushStatus}, #{item.PushDate}, #{item.PushBy}, #{item.Remark}, #{item.CreateBy}, #{item.UpdateBy})
        </foreach>
    </insert>

    <update id="updateDwsPrpProduct" parameterType="DwsPrpProductEntity">
        update t_dws_prp_product
        <trim prefix="SET" suffixOverrides=",">
            <if test="TransactionNo != null">TransactionNo = #{TransactionNo},</if>
            <if test="CompanyCode != null">CompanyCode = #{CompanyCode},</if>
            <if test="ReInsuranceContNo != null">ReInsuranceContNo = #{ReInsuranceContNo},</if>
            <if test="ReInsuranceContName != null">ReInsuranceContName = #{ReInsuranceContName},</if>
            <if test="ReInsuranceContTitle != null">ReInsuranceContTitle = #{ReInsuranceContTitle},</if>
            <if test="MainReInsuranceContNo != null">MainReInsuranceContNo = #{MainReInsuranceContNo},</if>
            <if test="ContOrAmendmentType != null">ContOrAmendmentType = #{ContOrAmendmentType},</if>
            <if test="ProductCode != null">ProductCode = #{ProductCode},</if>
            <if test="ProductName != null">ProductName = #{ProductName},</if>
            <if test="GPFlag != null">GPFlag = #{GPFlag},</if>
            <if test="ProductType != null">ProductType = #{ProductType},</if>
            <if test="LiabilityCode != null">LiabilityCode = #{LiabilityCode},</if>
            <if test="LiabilityName != null">LiabilityName = #{LiabilityName},</if>
            <if test="ReinsurerCode != null">ReinsurerCode = #{ReinsurerCode},</if>
            <if test="ReinsurerName != null">ReinsurerName = #{ReinsurerName},</if>
            <if test="ReinsuranceShare != null">ReinsuranceShare = #{ReinsuranceShare},</if>
            <if test="ReinsurMode != null">ReinsurMode = #{ReinsurMode},</if>
            <if test="ReInsuranceType != null">ReInsuranceType = #{ReInsuranceType},</if>
            <if test="TermType != null">TermType = #{TermType},</if>
            <if test="RetentionAmount != null">RetentionAmount = #{RetentionAmount},</if>
            <if test="RetentionPercentage != null">RetentionPercentage = #{RetentionPercentage},</if>
            <if test="QuotaSharePercentage != null">QuotaSharePercentage = #{QuotaSharePercentage},</if>
            <if test="ReportYear != null">ReportYear = #{ReportYear},</if>
            <if test="ReportMonth != null">ReportMonth = #{ReportMonth},</if>
            <if test="AccountPeriod != null">AccountPeriod = #{AccountPeriod},</if>
            <if test="DataSource != null">DataSource = #{DataSource},</if>
            <if test="PushStatus != null">PushStatus = #{PushStatus},</if>
            <if test="PushDate != null">PushDate = #{PushDate},</if>
            <if test="PushBy != null">PushBy = #{PushBy},</if>
            <if test="Remark != null">Remark = #{Remark},</if>
            <if test="IsDel != null">IsDel = #{IsDel},</if>
            <if test="CreateBy != null">CreateBy = #{CreateBy},</if>
            <if test="UpdateBy != null">UpdateBy = #{UpdateBy},</if>
            UpdateTime = now()
        </trim>
        where Id = #{Id}
    </update>

    <delete id="deleteDwsPrpProductById" parameterType="Long">
        delete from t_dws_prp_product where Id = #{Id}
    </delete>

    <delete id="deleteDwsPrpProductByIds" parameterType="String">
        delete from t_dws_prp_product where Id in 
        <foreach item="Id" collection="array" open="(" separator="," close=")">
            #{Id}
        </foreach>
    </delete>

    <select id="selectDwsPrpProductExists" parameterType="DwsPrpProductQuery" resultType="int">
        select count(1) from t_dws_prp_product where IsDel = 0 and ReportYear = #{ReportYear} and ReportMonth = #{ReportMonth}
    </select>

    <update id="updateDwsPrpProductPushStatus">
        update t_dws_prp_product set PushStatus = #{pushStatus}, PushBy = #{pushBy}, PushDate = now()
        where Id in 
        <foreach item="Id" collection="Ids" open="(" separator="," close=")">
            #{Id}
        </foreach>
    </update>

    <select id="selectAnnualReportShouldPushStatus" parameterType="int" resultType="Integer">
        select (case when notPushedCount>=0 and alreadyPushedCount=0 then 0 when notPushedCount>0 and alreadyPushedCount>0 then 1 else 2 end) as PushStatus
        from (select ifnull(sum(if(PushStatus = 0, total, 0)), 0) as notPushedCount, ifnull(sum(if(PushStatus = 1, total, 0)),0) as alreadyPushedCount
        from (select PushStatus, count(*) as total from t_dws_prp_product where IsDel = 0 and ReportYear = #{ReportYear} group by PushStatus)v )t
    </select>

    <select id="selectContractLiabilityAsPrpProduct" parameterType="DwsPrpProductQuery" resultMap="DwsPrpProductResult">
        select
            cc.company_code as ReinsurerCode,
            rc.company_name as ReinsurerName,
            l.contract_code as ReInsuranceContNo,
            cc.contract_name as ReInsuranceContName,
            cc.contract_abbr as ReInsuranceContTitle,
            cc.contract_type as ContOrAmendmentType,
            cc.main_contract_code as MainReInsuranceContNo,
            l.risk_code as ProductCode,
            trl.risk_name as ProductName,
            trl.sale_chnl as GPFlag,
            trl.ins_product_type as ProductType,
            l.liability_code as LiabilityCode,
            trl.liability_name as LiabilityName,
            l.cedeout_way as ReinsurMode,
            (select group_concat(lp.period_type_name separator';')
             from t_risk_liability_period lp
             where lp.is_del=0 and lp.status=0 and lp.risk_code=l.risk_code and lp.liability_code=l.liability_code) as TermType,
            l.retention_line as RetentionAmount,
            format(l.self_ratio * 100, 2) as RetentionPercentage,
            format(l.cedeout_ratio * 100, 2) as QuotaSharePercentage,
            format(l.self_amount * 100, 2) as ReinsuranceShare,
            cc.contract_class as ReInsuranceType
        from t_cedeout_contract_liability l
        inner join t_risk_liability trl on l.risk_code=trl.risk_code and trl.liability_code=l.liability_code
        inner join t_cedeout_contract cc on cc.contract_code=l.contract_code
        inner join t_cedeout_company rc on cc.company_code=rc.company_code
        where l.is_del=0 and l.status=0 and trl.is_del=0 and trl.status=0
        and l.create_time &gt;= date_format(#{params.startDate}, '%Y-%m-%d')
        and l.create_time &lt;= date_format(#{params.endDate}, '%Y-%m-%d')
        order by l.id
    </select>

</mapper>
