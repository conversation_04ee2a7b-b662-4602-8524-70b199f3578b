<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reinsurance.mapper.DwsPrpAccountMapper">
    
    <resultMap type="DwsPrpAccountEntity" id="DwsPrpAccountResult">
        <result property="Id"    column="Id"    />
        <result property="TransactionNo"    column="TransactionNo"    />
        <result property="CompanyCode"    column="CompanyCode"    />
        <result property="AccountID"    column="AccountID"    />
        <result property="AccountingPeriodfrom"    column="AccountingPeriodfrom"    />
        <result property="AccountingPeriodto"    column="AccountingPeriodto"    />
        <result property="ReinsurerCode"    column="ReinsurerCode"    />
        <result property="ReinsurerName"    column="ReinsurerName"    />
        <result property="ReInsuranceContNo"    column="ReInsuranceContNo"    />
        <result property="ReInsuranceContName"    column="ReInsuranceContName"    />
        <result property="ReinsurancePremium"    column="ReinsurancePremium"    />
        <result property="ReinsuranceCommssionRate"    column="ReinsuranceCommssionRate"    />
        <result property="ReinsuranceCommssion"    column="ReinsuranceCommssion"    />
        <result property="ReturnReinsurancePremium"    column="ReturnReinsurancePremium"    />
        <result property="ReturnReinsuranceCommssion"    column="ReturnReinsuranceCommssion"    />
        <result property="ReturnSurrenderPay"    column="ReturnSurrenderPay"    />
        <result property="ReturnClaimPay"    column="ReturnClaimPay"    />
        <result property="ReturnMaturity"    column="ReturnMaturity"    />
        <result property="ReturnAnnuity"    column="ReturnAnnuity"    />
        <result property="ReturnLivBene"    column="ReturnLivBene"    />
        <result property="AccountStatus"    column="AccountStatus"    />
        <result property="PairingStatus"    column="PairingStatus"    />
        <result property="PairingDate"    column="PairingDate"    />
        <result property="Currency"    column="Currency"    />
        <result property="CurrentRate"    column="CurrentRate"    />
        <result property="SettleBillNo"    column="SettleBillNo"    />
        <result property="ContImportStatus"    column="ContImportStatus"    />
        <result property="ContImportRemark"    column="ContImportRemark"    />
        <result property="EdorImportStatus"    column="EdorImportStatus"    />
        <result property="EdorImportRemark"    column="EdorImportRemark"    />
        <result property="ClaimImportStatus"    column="ClaimImportStatus"    />
        <result property="ClaimImportRemark"    column="ClaimImportRemark"    />
        <result property="BenefitImportStatus"    column="BenefitImportStatus"    />
        <result property="BenefitImportRemark"    column="BenefitImportRemark"    />
        <result property="ReportYear"    column="ReportYear"    />
        <result property="ReportMonth"    column="ReportMonth"    />
        <result property="AccountPeriod"    column="AccountPeriod"    />
        <result property="DataSource"    column="DataSource"    />
        <result property="PushStatus"    column="PushStatus"    />
        <result property="PushDate"    column="PushDate"    />
        <result property="PushBy"    column="PushBy"    />
        <result property="Remark"    column="Remark"    />
        <result property="IsDel"    column="IsDel"    />
        <result property="CreateBy"    column="CreateBy"    />
        <result property="CreateTime"    column="CreateTime"    />
        <result property="UpdateBy"    column="UpdateBy"    />
        <result property="UpdateTime"    column="UpdateTime"    />
    </resultMap>

    <sql id="selectDwsPrpAccountVo">
        select Id, TransactionNo, CompanyCode, AccountID, AccountingPeriodfrom, AccountingPeriodto, ReinsurerCode, ReinsurerName, ReInsuranceContNo, ReInsuranceContName, ReinsurancePremium, ReinsuranceCommssionRate, ReinsuranceCommssion, ReturnReinsurancePremium, ReturnReinsuranceCommssion, ReturnSurrenderPay, ReturnClaimPay, ReturnMaturity, ReturnAnnuity, ReturnLivBene, AccountStatus, PairingStatus, PairingDate, Currency, CurrentRate, SettleBillNo, ContImportStatus, ContImportRemark, EdorImportStatus, EdorImportRemark, ClaimImportStatus, ClaimImportRemark, BenefitImportStatus, BenefitImportRemark, ReportYear, ReportMonth, AccountPeriod, DataSource, PushStatus, PushDate, PushBy, Remark, IsDel, CreateBy, CreateTime, UpdateBy, UpdateTime from t_dws_prp_account
    </sql>

    <select id="selectDwsPrpAccountList" parameterType="DwsPrpAccountQuery" resultMap="DwsPrpAccountResult">
        <include refid="selectDwsPrpAccountVo"/>
        <where>  
            <if test="Id != null "> and Id = #{Id}</if>
            <if test="TransactionNo != null  and TransactionNo != ''"> and TransactionNo like concat('%', #{TransactionNo}, '%')</if>
            <if test="CompanyCode != null  and CompanyCode != ''"> and CompanyCode = #{CompanyCode}</if>
            <if test="AccountID != null  and AccountID != ''"> and AccountID like concat('%', #{AccountID}, '%')</if>
            <if test="AccountingPeriodfrom != null "> and AccountingPeriodfrom = #{AccountingPeriodfrom}</if>
            <if test="AccountingPeriodto != null "> and AccountingPeriodto = #{AccountingPeriodto}</if>
            <if test="ReinsurerCode != null  and ReinsurerCode != ''"> and ReinsurerCode like concat('%', #{ReinsurerCode}, '%')</if>
            <if test="ReinsurerName != null  and ReinsurerName != ''"> and ReinsurerName like concat('%', #{ReinsurerName}, '%')</if>
            <if test="ReInsuranceContNo != null  and ReInsuranceContNo != ''"> and ReInsuranceContNo like concat('%', #{ReInsuranceContNo}, '%')</if>
            <if test="ReInsuranceContName != null  and ReInsuranceContName != ''"> and ReInsuranceContName like concat('%', #{ReInsuranceContName}, '%')</if>
            <if test="ReinsurancePremium != null "> and ReinsurancePremium = #{ReinsurancePremium}</if>
            <if test="ReinsuranceCommssionRate != null "> and ReinsuranceCommssionRate = #{ReinsuranceCommssionRate}</if>
            <if test="ReinsuranceCommssion != null "> and ReinsuranceCommssion = #{ReinsuranceCommssion}</if>
            <if test="ReturnReinsurancePremium != null "> and ReturnReinsurancePremium = #{ReturnReinsurancePremium}</if>
            <if test="ReturnReinsuranceCommssion != null "> and ReturnReinsuranceCommssion = #{ReturnReinsuranceCommssion}</if>
            <if test="ReturnSurrenderPay != null "> and ReturnSurrenderPay = #{ReturnSurrenderPay}</if>
            <if test="ReturnClaimPay != null "> and ReturnClaimPay = #{ReturnClaimPay}</if>
            <if test="ReturnMaturity != null "> and ReturnMaturity = #{ReturnMaturity}</if>
            <if test="ReturnAnnuity != null "> and ReturnAnnuity = #{ReturnAnnuity}</if>
            <if test="ReturnLivBene != null "> and ReturnLivBene = #{ReturnLivBene}</if>
            <if test="AccountStatus != null  and AccountStatus != ''"> and AccountStatus = #{AccountStatus}</if>
            <if test="PairingStatus != null  and PairingStatus != ''"> and PairingStatus = #{PairingStatus}</if>
            <if test="PairingDate != null "> and PairingDate = #{PairingDate}</if>
            <if test="Currency != null  and Currency != ''"> and Currency = #{Currency}</if>
            <if test="CurrentRate != null "> and CurrentRate = #{CurrentRate}</if>
            <if test="SettleBillNo != null  and SettleBillNo != ''"> and SettleBillNo like concat('%', #{SettleBillNo}, '%')</if>
            <if test="ContImportStatus != null "> and ContImportStatus = #{ContImportStatus}</if>
            <if test="ContImportRemark != null  and ContImportRemark != ''"> and ContImportRemark like concat('%', #{ContImportRemark}, '%')</if>
            <if test="EdorImportStatus != null "> and EdorImportStatus = #{EdorImportStatus}</if>
            <if test="EdorImportRemark != null  and EdorImportRemark != ''"> and EdorImportRemark like concat('%', #{EdorImportRemark}, '%')</if>
            <if test="ClaimImportStatus != null "> and ClaimImportStatus = #{ClaimImportStatus}</if>
            <if test="ClaimImportRemark != null  and ClaimImportRemark != ''"> and ClaimImportRemark like concat('%', #{ClaimImportRemark}, '%')</if>
            <if test="BenefitImportStatus != null "> and BenefitImportStatus = #{BenefitImportStatus}</if>
            <if test="BenefitImportRemark != null  and BenefitImportRemark != ''"> and BenefitImportRemark like concat('%', #{BenefitImportRemark}, '%')</if>
            <if test="ReportYear != null "> and ReportYear = #{ReportYear}</if>
            <if test="ReportMonth != null "> and ReportMonth = #{ReportMonth}</if>
            <if test="AccountPeriod != null  and AccountPeriod != ''"> and AccountPeriod = #{AccountPeriod}</if>
            <if test="DataSource != null "> and DataSource = #{DataSource}</if>
            <if test="PushStatus != null "> and PushStatus = #{PushStatus}</if>
            <if test="PushDate != null "> and PushDate = #{PushDate}</if>
            <if test="PushBy != null  and PushBy != ''"> and PushBy = #{PushBy}</if>
            <if test="Remark != null  and Remark != ''"> and Remark like concat('%', #{Remark}, '%')</if>
            <if test="CreateBy != null  and CreateBy != ''"> and CreateBy = #{CreateBy}</if>
            <if test="CreateTime != null "> and CreateTime = #{CreateTime}</if>
            <if test="UpdateBy != null  and UpdateBy != ''"> and UpdateBy = #{UpdateBy}</if>
            <if test="UpdateTime != null "> and UpdateTime = #{UpdateTime}</if>
            and IsDel = 0
        </where>
        order by Id desc
    </select>
    
    <select id="selectDwsPrpAccountById" parameterType="Long" resultMap="DwsPrpAccountResult">
        <include refid="selectDwsPrpAccountVo"/>
        where Id = #{Id}
    </select>

    <select id="selectDwsPrpAccountByIds" parameterType="Long" resultMap="DwsPrpAccountResult">
        <include refid="selectDwsPrpAccountVo"/>
        where Id in
        <foreach item="Id" collection="array" open="(" separator="," close=")">
            #{Id}
        </foreach>
    </select>

    <insert id="insertDwsPrpAccount" parameterType="DwsPrpAccountEntity" useGeneratedKeys="true" keyProperty="Id">
        insert into t_dws_prp_account
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="TransactionNo != null">TransactionNo,</if>
            <if test="CompanyCode != null">CompanyCode,</if>
            <if test="AccountID != null">AccountID,</if>
            <if test="AccountingPeriodfrom != null">AccountingPeriodfrom,</if>
            <if test="AccountingPeriodto != null">AccountingPeriodto,</if>
            <if test="ReinsurerCode != null">ReinsurerCode,</if>
            <if test="ReinsurerName != null">ReinsurerName,</if>
            <if test="ReInsuranceContNo != null">ReInsuranceContNo,</if>
            <if test="ReInsuranceContName != null">ReInsuranceContName,</if>
            <if test="ReinsurancePremium != null">ReinsurancePremium,</if>
            <if test="ReinsuranceCommssionRate != null">ReinsuranceCommssionRate,</if>
            <if test="ReinsuranceCommssion != null">ReinsuranceCommssion,</if>
            <if test="ReturnReinsurancePremium != null">ReturnReinsurancePremium,</if>
            <if test="ReturnReinsuranceCommssion != null">ReturnReinsuranceCommssion,</if>
            <if test="ReturnSurrenderPay != null">ReturnSurrenderPay,</if>
            <if test="ReturnClaimPay != null">ReturnClaimPay,</if>
            <if test="ReturnMaturity != null">ReturnMaturity,</if>
            <if test="ReturnAnnuity != null">ReturnAnnuity,</if>
            <if test="ReturnLivBene != null">ReturnLivBene,</if>
            <if test="AccountStatus != null">AccountStatus,</if>
            <if test="PairingStatus != null">PairingStatus,</if>
            <if test="PairingDate != null">PairingDate,</if>
            <if test="Currency != null">Currency,</if>
            <if test="CurrentRate != null">CurrentRate,</if>
            <if test="SettleBillNo != null">SettleBillNo,</if>
            <if test="ContImportStatus != null">ContImportStatus,</if>
            <if test="ContImportRemark != null">ContImportRemark,</if>
            <if test="EdorImportStatus != null">EdorImportStatus,</if>
            <if test="EdorImportRemark != null">EdorImportRemark,</if>
            <if test="ClaimImportStatus != null">ClaimImportStatus,</if>
            <if test="ClaimImportRemark != null">ClaimImportRemark,</if>
            <if test="BenefitImportStatus != null">BenefitImportStatus,</if>
            <if test="BenefitImportRemark != null">BenefitImportRemark,</if>
            <if test="ReportYear != null">ReportYear,</if>
            <if test="ReportMonth != null">ReportMonth,</if>
            <if test="AccountPeriod != null">AccountPeriod,</if>
            <if test="DataSource != null">DataSource,</if>
            <if test="PushStatus != null">PushStatus,</if>
            <if test="PushDate != null">PushDate,</if>
            <if test="PushBy != null">PushBy,</if>
            <if test="Remark != null">Remark,</if>
            <if test="IsDel != null">IsDel,</if>
            <if test="CreateBy != null">CreateBy,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="TransactionNo != null">#{TransactionNo},</if>
            <if test="CompanyCode != null">#{CompanyCode},</if>
            <if test="AccountID != null">#{AccountID},</if>
            <if test="AccountingPeriodfrom != null">#{AccountingPeriodfrom},</if>
            <if test="AccountingPeriodto != null">#{AccountingPeriodto},</if>
            <if test="ReinsurerCode != null">#{ReinsurerCode},</if>
            <if test="ReinsurerName != null">#{ReinsurerName},</if>
            <if test="ReInsuranceContNo != null">#{ReInsuranceContNo},</if>
            <if test="ReInsuranceContName != null">#{ReInsuranceContName},</if>
            <if test="ReinsurancePremium != null">#{ReinsurancePremium},</if>
            <if test="ReinsuranceCommssionRate != null">#{ReinsuranceCommssionRate},</if>
            <if test="ReinsuranceCommssion != null">#{ReinsuranceCommssion},</if>
            <if test="ReturnReinsurancePremium != null">#{ReturnReinsurancePremium},</if>
            <if test="ReturnReinsuranceCommssion != null">#{ReturnReinsuranceCommssion},</if>
            <if test="ReturnSurrenderPay != null">#{ReturnSurrenderPay},</if>
            <if test="ReturnClaimPay != null">#{ReturnClaimPay},</if>
            <if test="ReturnMaturity != null">#{ReturnMaturity},</if>
            <if test="ReturnAnnuity != null">#{ReturnAnnuity},</if>
            <if test="ReturnLivBene != null">#{ReturnLivBene},</if>
            <if test="AccountStatus != null">#{AccountStatus},</if>
            <if test="PairingStatus != null">#{PairingStatus},</if>
            <if test="PairingDate != null">#{PairingDate},</if>
            <if test="Currency != null">#{Currency},</if>
            <if test="CurrentRate != null">#{CurrentRate},</if>
            <if test="SettleBillNo != null">#{SettleBillNo},</if>
            <if test="ContImportStatus != null">#{ContImportStatus},</if>
            <if test="ContImportRemark != null">#{ContImportRemark},</if>
            <if test="EdorImportStatus != null">#{EdorImportStatus},</if>
            <if test="EdorImportRemark != null">#{EdorImportRemark},</if>
            <if test="ClaimImportStatus != null">#{ClaimImportStatus},</if>
            <if test="ClaimImportRemark != null">#{ClaimImportRemark},</if>
            <if test="BenefitImportStatus != null">#{BenefitImportStatus},</if>
            <if test="BenefitImportRemark != null">#{BenefitImportRemark},</if>
            <if test="ReportYear != null">#{ReportYear},</if>
            <if test="ReportMonth != null">#{ReportMonth},</if>
            <if test="AccountPeriod != null">#{AccountPeriod},</if>
            <if test="DataSource != null">#{DataSource},</if>
            <if test="PushStatus != null">#{PushStatus},</if>
            <if test="PushDate != null">#{PushDate},</if>
            <if test="PushBy != null">#{PushBy},</if>
            <if test="Remark != null">#{Remark},</if>
            <if test="IsDel != null">#{IsDel},</if>
            <if test="CreateBy != null">#{CreateBy},</if>
        </trim>
    </insert>

    <insert id="insertBatchDwsPrpAccount" parameterType="java.util.List">
        insert into t_dws_prp_account(TransactionNo, CompanyCode, AccountID, AccountingPeriodfrom, AccountingPeriodto, ReinsurerCode, ReinsurerName, ReInsuranceContNo, ReInsuranceContName, ReinsurancePremium, ReinsuranceCommssionRate, ReinsuranceCommssion, ReturnReinsurancePremium, ReturnReinsuranceCommssion, ReturnSurrenderPay, ReturnClaimPay, ReturnMaturity, ReturnAnnuity, ReturnLivBene, AccountStatus, PairingStatus, PairingDate, Currency, CurrentRate, SettleBillNo, ContImportStatus, ContImportRemark, EdorImportStatus, EdorImportRemark, ClaimImportStatus, ClaimImportRemark, BenefitImportStatus, BenefitImportRemark, ReportYear, ReportMonth, AccountPeriod, DataSource, PushStatus, PushDate, PushBy, Remark, IsDel, CreateBy) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.TransactionNo}, #{item.CompanyCode}, #{item.AccountID}, #{item.AccountingPeriodfrom}, #{item.AccountingPeriodto}, #{item.ReinsurerCode}, #{item.ReinsurerName}, #{item.ReInsuranceContNo}, #{item.ReInsuranceContName}, #{item.ReinsurancePremium}, #{item.ReinsuranceCommssionRate}, #{item.ReinsuranceCommssion}, #{item.ReturnReinsurancePremium}, #{item.ReturnReinsuranceCommssion}, #{item.ReturnSurrenderPay}, #{item.ReturnClaimPay}, #{item.ReturnMaturity}, #{item.ReturnAnnuity}, #{item.ReturnLivBene}, #{item.AccountStatus}, #{item.PairingStatus}, #{item.PairingDate}, #{item.Currency}, #{item.CurrentRate}, #{item.SettleBillNo}, #{item.ContImportStatus}, #{item.ContImportRemark}, #{item.EdorImportStatus}, #{item.EdorImportRemark}, #{item.ClaimImportStatus}, #{item.ClaimImportRemark}, #{item.BenefitImportStatus}, #{item.BenefitImportRemark}, #{item.ReportYear}, #{item.ReportMonth}, #{item.AccountPeriod}, #{item.DataSource}, #{item.PushStatus}, #{item.PushDate}, #{item.PushBy}, #{item.Remark}, #{item.IsDel}, #{item.CreateBy})
        </foreach>
    </insert>

    <update id="updateDwsPrpAccount" parameterType="DwsPrpAccountEntity">
        update t_dws_prp_account
        <trim prefix="SET" suffixOverrides=",">
            <if test="TransactionNo != null">TransactionNo = #{TransactionNo},</if>
            <if test="CompanyCode != null">CompanyCode = #{CompanyCode},</if>
            <if test="AccountID != null">AccountID = #{AccountID},</if>
            <if test="AccountingPeriodfrom != null">AccountingPeriodfrom = #{AccountingPeriodfrom},</if>
            <if test="AccountingPeriodto != null">AccountingPeriodto = #{AccountingPeriodto},</if>
            <if test="ReinsurerCode != null">ReinsurerCode = #{ReinsurerCode},</if>
            <if test="ReinsurerName != null">ReinsurerName = #{ReinsurerName},</if>
            <if test="ReInsuranceContNo != null">ReInsuranceContNo = #{ReInsuranceContNo},</if>
            <if test="ReInsuranceContName != null">ReInsuranceContName = #{ReInsuranceContName},</if>
            <if test="ReinsurancePremium != null">ReinsurancePremium = #{ReinsurancePremium},</if>
            <if test="ReinsuranceCommssionRate != null">ReinsuranceCommssionRate = #{ReinsuranceCommssionRate},</if>
            <if test="ReinsuranceCommssion != null">ReinsuranceCommssion = #{ReinsuranceCommssion},</if>
            <if test="ReturnReinsurancePremium != null">ReturnReinsurancePremium = #{ReturnReinsurancePremium},</if>
            <if test="ReturnReinsuranceCommssion != null">ReturnReinsuranceCommssion = #{ReturnReinsuranceCommssion},</if>
            <if test="ReturnSurrenderPay != null">ReturnSurrenderPay = #{ReturnSurrenderPay},</if>
            <if test="ReturnClaimPay != null">ReturnClaimPay = #{ReturnClaimPay},</if>
            <if test="ReturnMaturity != null">ReturnMaturity = #{ReturnMaturity},</if>
            <if test="ReturnAnnuity != null">ReturnAnnuity = #{ReturnAnnuity},</if>
            <if test="ReturnLivBene != null">ReturnLivBene = #{ReturnLivBene},</if>
            <if test="AccountStatus != null">AccountStatus = #{AccountStatus},</if>
            <if test="PairingStatus != null">PairingStatus = #{PairingStatus},</if>
            <if test="PairingDate != null">PairingDate = #{PairingDate},</if>
            <if test="Currency != null">Currency = #{Currency},</if>
            <if test="CurrentRate != null">CurrentRate = #{CurrentRate},</if>
            <if test="SettleBillNo != null">SettleBillNo = #{SettleBillNo},</if>
            <if test="ContImportStatus != null">ContImportStatus = #{ContImportStatus},</if>
            <if test="ContImportRemark != null">ContImportRemark = #{ContImportRemark},</if>
            <if test="EdorImportStatus != null">EdorImportStatus = #{EdorImportStatus},</if>
            <if test="EdorImportRemark != null">EdorImportRemark = #{EdorImportRemark},</if>
            <if test="ClaimImportStatus != null">ClaimImportStatus = #{ClaimImportStatus},</if>
            <if test="ClaimImportRemark != null">ClaimImportRemark = #{ClaimImportRemark},</if>
            <if test="BenefitImportStatus != null">BenefitImportStatus = #{BenefitImportStatus},</if>
            <if test="BenefitImportRemark != null">BenefitImportRemark = #{BenefitImportRemark},</if>
            <if test="ReportYear != null">ReportYear = #{ReportYear},</if>
            <if test="ReportMonth != null">ReportMonth = #{ReportMonth},</if>
            <if test="AccountPeriod != null">AccountPeriod = #{AccountPeriod},</if>
            <if test="DataSource != null">DataSource = #{DataSource},</if>
            <if test="PushStatus != null">PushStatus = #{PushStatus},</if>
            <if test="PushDate != null">PushDate = #{PushDate},</if>
            <if test="PushBy != null">PushBy = #{PushBy},</if>
            <if test="Remark != null">Remark = #{Remark},</if>
            <if test="IsDel != null">IsDel = #{IsDel},</if>
            <if test="UpdateBy != null">UpdateBy = #{UpdateBy},</if>
            <if test="UpdateTime != null">UpdateTime = #{UpdateTime},</if>
        </trim>
        where Id = #{Id}
    </update>

    <delete id="deleteDwsPrpAccountById" parameterType="Long">
        delete from t_dws_prp_account where Id = #{Id}
    </delete>

    <delete id="deleteDwsPrpAccountByIds" parameterType="String">
        delete from t_dws_prp_account where Id in
        <foreach item="Id" collection="array" open="(" separator="," close=")">
            #{Id}
        </foreach>
    </delete>

    <select id="selectDwsPrpAccountExists" parameterType="DwsPrpAccountQuery" resultType="int">
        select count(1) from t_dws_prp_account
        <where>
            <if test="TransactionNo != null  and TransactionNo != ''"> and TransactionNo = #{TransactionNo}</if>
            <if test="AccountID != null  and AccountID != ''"> and AccountID = #{AccountID}</if>
            <if test="ReportYear != null "> and ReportYear = #{ReportYear}</if>
            <if test="ReportMonth != null "> and ReportMonth = #{ReportMonth}</if>
            and IsDel = 0
        </where>
    </select>

    <update id="updateDwsPrpAccountPushStatus">
        update t_dws_prp_account set PushStatus = #{pushStatus}, PushBy = #{pushBy}, PushDate = now()
        where Id in
        <foreach item="Id" collection="Ids" open="(" separator="," close=")">
            #{Id}
        </foreach>
    </update>

    <select id="selectAnnualReportShouldPushStatus" parameterType="int" resultType="Integer">
        select case when count(1) > 0 then 1 else 0 end from t_dws_prp_account
        where ReportYear = #{reportYear} and PushStatus = 0 and IsDel = 0
    </select>

</mapper>
