<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reinsurance.mapper.DwsReinsuPolicyLiabilityMapper">
    
    <resultMap type="DwsReinsuPolicyLiabilityEntity" id="DwsReinsuPolicyLiabilityResult">
        <result property="id"    column="id"    />
        <result property="batchNo"    column="batch_no"    />
        <result property="busiType"    column="busi_type"    />
        <result property="tdchIds"    column="tdch_ids"    />
        <result property="uniqueKey"    column="unique_key"    />
        <result property="programmeCode"    column="programme_code"    />
        <result property="contType"    column="cont_type"    />
        <result property="grpContNo"    column="grp_cont_no"    />
        <result property="grpPolNo"    column="grp_pol_no"    />
        <result property="contNo"    column="cont_no"    />
        <result property="polNo"    column="pol_no"    />
        <result property="mainPolNo"    column="main_pol_no"    />
        <result property="saleChnl"    column="sale_chnl"    />
        <result property="saleChnlName"    column="sale_chnl_name"    />
        <result property="sellType"    column="sell_type"    />
        <result property="sellTypeName"    column="sell_type_name"    />
        <result property="saleComCode"    column="sale_com_code"    />
        <result property="saleComName"    column="sale_com_name"    />
        <result property="agentComCode"    column="agent_com_code"    />
        <result property="agentComName"    column="agent_com_name"    />
        <result property="manageComCode"    column="manage_com_code"    />
        <result property="manageComName"    column="manage_com_name"    />
        <result property="bankBranchName"    column="bank_branch_name"    />
        <result property="busiOccurDate"    column="busi_occur_date"    />
        <result property="busiOccurTime"    column="busi_occur_time"    />
        <result property="signDate"    column="sign_date"    />
        <result property="signTime"    column="sign_time"    />
        <result property="riskValiDate"    column="risk_vali_date"    />
        <result property="riskEndDate"    column="risk_end_date"    />
        <result property="contMakeDate"    column="cont_make_date"    />
        <result property="contMakeTime"    column="cont_make_time"    />
        <result property="contYear"    column="cont_year"    />
        <result property="contMonth"    column="cont_month"    />
        <result property="contAnniversary"    column="cont_anniversary"    />
        <result property="previousContAnniversary"    column="previous_cont_anniversary"    />
        <result property="contAppFlag"    column="cont_app_flag"    />
        <result property="riskCode"    column="risk_code"    />
        <result property="riskName"    column="risk_name"    />
        <result property="riskType3"    column="risk_type3"    />
        <result property="planCode"    column="plan_code"    />
        <result property="polRiskType"    column="pol_risk_type"    />
        <result property="riskAppFlag"    column="risk_app_flag"    />
        <result property="subRiskFlag"    column="sub_risk_flag"    />
        <result property="riskPeriod"    column="risk_period"    />
        <result property="insuYear"    column="insu_year"    />
        <result property="insuYearFlag"    column="insu_year_flag"    />
        <result property="payIntv"    column="pay_intv"    />
        <result property="payendYear"    column="payend_year"    />
        <result property="payendYearFlag"    column="payend_year_flag"    />
        <result property="payToDate"    column="pay_to_date"    />
        <result property="payEndDate"    column="pay_end_date"    />
        <result property="getDutyCodes"    column="get_duty_codes"    />
        <result property="getDutyNames"    column="get_duty_names"    />
        <result property="liabilityCode"    column="liability_code"    />
        <result property="liabilityName"    column="liability_name"    />
        <result property="amount"    column="amount"    />
        <result property="basePremium"    column="base_premium"    />
        <result property="addPremium"    column="add_premium"    />
        <result property="addScale"    column="add_scale"    />
        <result property="sumPayMoney"    column="sum_pay_money"    />
        <result property="sumAddMoney"    column="sum_add_money"    />
        <result property="sumGetMoney"    column="sum_get_money"    />
        <result property="riskFreeFlag"    column="risk_free_flag"    />
        <result property="appntNo"    column="appnt_no"    />
        <result property="appntName"    column="appnt_name"    />
        <result property="appntIdType"    column="appnt_id_type"    />
        <result property="appntIdNo"    column="appnt_id_no"    />
        <result property="appntSex"    column="appnt_sex"    />
        <result property="appntBirthday"    column="appnt_birthday"    />
        <result property="appntOccType"    column="appnt_occ_type"    />
        <result property="appntOccCode"    column="appnt_occ_code"    />
        <result property="insuredPeoples"    column="insured_peoples"    />
        <result property="insuredAppAge"    column="insured_app_age"    />
        <result property="insuredName"    column="insured_name"    />
        <result property="insuredIdType"    column="insured_id_type"    />
        <result property="insuredNo"    column="insured_no"    />
        <result property="insuredIdNo"    column="insured_id_no"    />
        <result property="insuredSex"    column="insured_sex"    />
        <result property="insuredBirthday"    column="insured_birthday"    />
        <result property="insuredOccType"    column="insured_occ_type"    />
        <result property="insuredOccCode"    column="insured_occ_code"    />
        <result property="insuredPassFlag"    column="insured_pass_flag"    />
        <result property="cashValue"    column="cash_value"    />
        <result property="insuaccValue"    column="insuacc_value"    />
        <result property="cedeoutType"    column="cedeout_type"    />
        <result property="coreConclusion"    column="core_conclusion"    />
        <result property="reinsuAddScale"    column="reinsu_add_scale"    />
        <result property="cedeoutCount"    column="cedeout_count"    />
        <result property="handleStatus"    column="handle_status"    />
        <result property="handleDate"    column="handle_date"    />
        <result property="handleErrorNum"    column="handle_error_num"    />
        <result property="handleErrorMsg"    column="handle_error_msg"    />
        <result property="status"    column="status"    />
        <result property="backTrackData"    column="back_track_data"    />
        <result property="remark"    column="remark"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDwsReinsuPolicyLiabilityVo">
        select id, batch_no, busi_type, tdch_ids, unique_key, programme_code, cont_type, grp_cont_no, grp_pol_no, cont_no, pol_no, main_pol_no, sale_chnl, sale_chnl_name, sell_type, sell_type_name, sale_com_code, sale_com_name, agent_com_code, agent_com_name, manage_com_code, manage_com_name, bank_branch_name, busi_occur_date, busi_occur_time, sign_date, sign_time, risk_vali_date, risk_end_date, cont_make_date, cont_make_time, cont_year, cont_month, cont_anniversary, previous_cont_anniversary, cont_app_flag, risk_code, risk_name, risk_type3, plan_code, pol_risk_type, risk_app_flag, sub_risk_flag, risk_period, insu_year, insu_year_flag, pay_intv, payend_year, payend_year_flag, pay_to_date, pay_end_date, get_duty_codes, get_duty_names, liability_code, liability_name, amount, base_premium, add_premium, add_scale, sum_pay_money, sum_add_money, sum_get_money, risk_free_flag, appnt_no, appnt_name, appnt_id_type, appnt_id_no, appnt_sex, appnt_birthday, appnt_occ_type, appnt_occ_code, insured_peoples, insured_app_age, insured_name, insured_id_type, insured_no, insured_id_no, insured_sex, insured_birthday, insured_occ_type, insured_occ_code, insured_pass_flag, cash_value, insuacc_value, cedeout_type, core_conclusion, reinsu_add_scale, cedeout_count, handle_status, handle_date, handle_error_num, handle_error_msg, status, back_track_data, remark, is_del, create_by, create_time, update_by, update_time from t_dws_reinsu_policy_liability
    </sql>

    <select id="selectWaitReinsuPolicyLiabilityList" parameterType="DwsReinsuPolicyLiabilityEntity" resultMap="DwsReinsuPolicyLiabilityResult">
        <include refid="selectDwsReinsuPolicyLiabilityVo"/>
        where is_del=0 and status=0 and busi_type=#{busiType} and back_track_data=#{backTrackData}
        <if test="cedeoutType != null"> and cedeout_type = #{cedeoutType}</if>
        <if test="handleStatus != null"> and handle_status = #{handleStatus}</if>
        <if test="batchNo != null  and batchNo != ''"> and batch_no = #{batchNo}</if>
        <if test="programmeCode != null  and programmeCode != ''"> and programme_code = #{programmeCode}</if>
        <if test="params.limit != null">order by busi_occur_time limit #{params.limit}</if>
    </select>
    
    <select id="selectOneReinsuPolicyLiability" parameterType="DwsReinsuPolicyLiabilityEntity" resultMap="DwsReinsuPolicyLiabilityResult">
        <include refid="selectDwsReinsuPolicyLiabilityVo"/>where is_del=0 and status=0 and busi_type=#{busiType} and pol_no=#{polNo} and cont_year=#{contYear} and cont_month=#{contMonth} limit 1
    </select>
    
    <insert id="insertBatchDwsReinsuPolicyLiabilityNew" parameterType="DwsReinsuPolicyLiabilityEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_dws_reinsu_policy_liability(batch_no, busi_type, unique_key, programme_code, busi_occur_date, busi_occur_time, cont_type, grp_cont_no, grp_pol_no, cont_no, pol_no, main_pol_no,
        sale_chnl, sale_chnl_name, sell_type, sell_type_name, sale_com_code, sale_com_name, agent_com_code, agent_com_name, manage_com_code, manage_com_name, bank_branch_name, sign_date, 
        sign_time, risk_vali_date, risk_end_date, cont_make_date, cont_make_time, cont_year, cont_month, cont_anniversary, previous_cont_anniversary, cont_app_flag, risk_code, risk_name, 
        risk_type3, plan_code, pol_risk_type, risk_app_flag, sub_risk_flag, risk_period, insu_year, insu_year_flag, pay_intv, payend_year, payend_year_flag, pay_to_date, pay_end_date, 
        get_duty_codes, get_duty_names, liability_code, liability_name, amount, base_premium, add_premium, add_scale, sum_pay_money, sum_add_money, sum_get_money, risk_free_flag, 
        appnt_no, appnt_name, appnt_id_type, appnt_id_no, appnt_sex, appnt_birthday, appnt_occ_type, appnt_occ_code, insured_peoples, insured_app_age, insured_no, insured_name, insured_id_type, 
        insured_id_no, insured_sex, insured_birthday, insured_occ_type, insured_occ_code, insured_pass_flag, cash_value, insuacc_value, cedeout_type, core_conclusion, reinsu_add_scale, cedeout_count)
		select * from(select #{batchNo} as batch_no, #{busiType} as busi_type, concat_ws('#', #{programmeCode}, pol_no, insured_no , liability_code , cont_year, #{contMonth}) as unique_key, #{programmeCode} as programme_code, 
		busi_occur_date, busi_occur_time, cont_type, grp_cont_no, grp_pol_no, cont_no, pol_no, main_pol_no, sale_chnl, sale_chnl_name, sell_type, sell_type_name, sale_com_code, sale_com_name, agent_com_code, agent_com_name,
		manage_com_code, manage_com_name, bank_branch_name, sign_date, sign_time, risk_vali_date, risk_end_date, cont_make_date, cont_make_time, cont_year, #{contMonth} as cont_month, cont_anniversary, 
		previous_cont_anniversary, cont_app_flag, risk_code, risk_name, risk_type3, plan_code, pol_risk_type, risk_app_flag, sub_risk_flag, risk_period, insu_year, insu_year_flag, pay_intv, payend_year, 
		payend_year_flag, pay_to_date, pay_end_date, group_concat(get_duty_code) as get_duty_codes, group_concat(get_duty_name) as get_duty_names, liability_code, liability_name, amount, base_premium, add_premium, 
		add_scale, sum_pay_money, sum_add_money, sum_get_money, risk_free_flag, appnt_no, appnt_name, appnt_id_type, appnt_id_no, appnt_sex, appnt_birthday, appnt_occ_type, appnt_occ_code, insured_peoples, insured_app_age, 
		insured_no, insured_name, insured_id_type, insured_id_no, insured_sex, insured_birthday, insured_occ_type, insured_occ_code, insured_pass_flag, cash_value, insuacc_value, cedeout_type, core_conclusion, reinsu_add_scale, cedeout_count  
		from v_dws_policy where cont_year=1 
		<if test="cedeoutType != null"> and cedeout_type = #{cedeoutType}</if>
       	<if test="params.liabilitys != null and params.liabilitys.size > 0">
           and 
            <foreach item="liability" collection="params.liabilitys" open="(" separator=" or " close=")">
	           	(risk_code = #{liability.riskCode} and liability_code = #{liability.liabilityCode}
	            and risk_vali_date &gt;= date_format(#{liability.startDate}, '%Y-%m-%d')
	            and risk_vali_date &lt;= date_format(#{liability.endDate}, '%Y-%m-%d'))
            </foreach>
       	</if>
       	<if test="params.startDate != null">
            and cont_make_date &gt;= date_format(#{params.startDate}, '%Y-%m-%d')
        </if>
        <if test="params.endDate != null">
            and cont_make_date &lt;= date_format(#{params.endDate}, '%Y-%m-%d')
        </if>
        group by busi_occur_date, busi_occur_time, cont_type, grp_cont_no, grp_pol_no, cont_no, pol_no, main_pol_no, sale_chnl, sale_chnl_name, sell_type, sell_type_name, sale_com_code, sale_com_name, agent_com_code, agent_com_name,
		manage_com_code, manage_com_name, bank_branch_name, sign_date, sign_time, risk_vali_date, risk_end_date, cont_make_date, cont_make_time, cont_year, cont_anniversary, previous_cont_anniversary, 
		cont_app_flag, risk_code, risk_name, risk_type3, plan_code, pol_risk_type, risk_app_flag, sub_risk_flag, risk_period, insu_year, insu_year_flag, pay_intv, payend_year, payend_year_flag, pay_to_date, pay_end_date, amount, 
		base_premium, add_premium, add_scale, sum_pay_money, sum_add_money, sum_get_money, risk_free_flag, appnt_no, appnt_name, appnt_id_type, appnt_id_no, appnt_sex, appnt_birthday, appnt_occ_type, appnt_occ_code, insured_peoples, 
		insured_app_age, insured_no, insured_name, insured_id_type, insured_id_no, insured_sex, insured_birthday, insured_occ_type, insured_occ_code, insured_pass_flag, cash_value, insuacc_value, cedeout_type, core_conclusion, 
		reinsu_add_scale, cedeout_count, liability_code, liability_name)vdrcl where not exists(select 1 from t_dws_reinsu_policy_liability tdrpl where tdrpl.unique_key=vdrcl.unique_key) 
    </insert>

    <insert id="insertBatchDwsReinsuPolicyLiabilityRenewal" parameterType="DwsReinsuPolicyLiabilityEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_dws_reinsu_policy_liability(batch_no, busi_type, unique_key, programme_code, busi_occur_date, busi_occur_time, cont_type, grp_cont_no, grp_pol_no, cont_no, pol_no, main_pol_no,
        sale_chnl, sale_chnl_name, sell_type, sell_type_name, sale_com_code, sale_com_name, agent_com_code, agent_com_name, manage_com_code, manage_com_name, bank_branch_name, sign_date, 
        sign_time, risk_vali_date, risk_end_date, cont_make_date, cont_make_time, cont_year, cont_month, cont_anniversary, previous_cont_anniversary, cont_app_flag, risk_code, risk_name, 
        risk_type3, plan_code, pol_risk_type, risk_app_flag, sub_risk_flag, risk_period, insu_year, insu_year_flag, pay_intv, payend_year, payend_year_flag, pay_to_date, pay_end_date, 
        get_duty_codes, get_duty_names, liability_code, liability_name, amount, base_premium, add_premium, add_scale, sum_pay_money, sum_add_money, sum_get_money, risk_free_flag, 
        appnt_no, appnt_name, appnt_id_type, appnt_id_no, appnt_sex, appnt_birthday, appnt_occ_type, appnt_occ_code, insured_peoples, insured_app_age, insured_no, insured_name, insured_id_type, 
        insured_id_no, insured_sex, insured_birthday, insured_occ_type, insured_occ_code, insured_pass_flag, cash_value, insuacc_value, cedeout_type, core_conclusion, reinsu_add_scale, cedeout_count)
		select * from(select #{batchNo} as batch_no, #{busiType} as busi_type, concat_ws('#', #{programmeCode}, pol_no, insured_no , liability_code , cont_year, #{contMonth}) as unique_key, #{programmeCode} as programme_code, 
		busi_occur_date, busi_occur_time, cont_type, grp_cont_no, grp_pol_no, cont_no, pol_no, main_pol_no, sale_chnl, sale_chnl_name, sell_type, sell_type_name, sale_com_code, sale_com_name, agent_com_code, agent_com_name,
		manage_com_code, manage_com_name, bank_branch_name, sign_date, sign_time, risk_vali_date, risk_end_date, cont_make_date, cont_make_time, cont_year, #{contMonth} as cont_month, cont_anniversary, 
		previous_cont_anniversary, cont_app_flag, risk_code, risk_name, risk_type3, plan_code, pol_risk_type, risk_app_flag, sub_risk_flag, risk_period, insu_year, insu_year_flag, pay_intv, payend_year, 
		payend_year_flag, pay_to_date, pay_end_date, group_concat(get_duty_code) as get_duty_codes, group_concat(get_duty_name) as get_duty_names, liability_code, liability_name, amount, base_premium, add_premium, 
		add_scale, sum_pay_money, sum_add_money, sum_get_money, risk_free_flag, appnt_no, appnt_name, appnt_id_type, appnt_id_no, appnt_sex, appnt_birthday, appnt_occ_type, appnt_occ_code, insured_peoples, insured_app_age, 
		insured_no, insured_name, insured_id_type, insured_id_no, insured_sex, insured_birthday, insured_occ_type, insured_occ_code, insured_pass_flag, cash_value, insuacc_value, cedeout_type, core_conclusion, reinsu_add_scale, cedeout_count 
		from v_dws_policy where cont_year>1 
		<if test="cedeoutType != null"> and cedeout_type = #{cedeoutType}</if>
       	<if test="params.liabilitys != null and params.liabilitys.size > 0">
           and 
            <foreach item="liability" collection="params.liabilitys" open="(" separator=" or " close=")">
	           	(risk_code = #{liability.riskCode} and liability_code = #{liability.liabilityCode}
	            and risk_vali_date &gt;= date_format(#{liability.startDate}, '%Y-%m-%d')
	            and risk_vali_date &lt;= date_format(#{liability.endDate}, '%Y-%m-%d'))
            </foreach>
       	</if>
       	<if test="params.startDate != null">
            and previous_cont_anniversary &gt;= date_format(#{params.startDate}, '%Y-%m-%d')
        </if>
        <if test="params.endDate != null">
            and previous_cont_anniversary &lt;= date_format(#{params.endDate}, '%Y-%m-%d')
        </if>
		group by busi_occur_date, busi_occur_time, cont_type, grp_cont_no, grp_pol_no, cont_no, pol_no, main_pol_no, sale_chnl, sale_chnl_name, sell_type, sell_type_name, sale_com_code, sale_com_name, agent_com_code, agent_com_name,
		manage_com_code, manage_com_name, bank_branch_name, sign_date, sign_time, risk_vali_date, risk_end_date, cont_make_date, cont_make_time, cont_year, cont_anniversary, previous_cont_anniversary, 
		cont_app_flag, risk_code, risk_name, risk_type3, plan_code, pol_risk_type, risk_app_flag, sub_risk_flag, risk_period, insu_year, insu_year_flag, pay_intv, payend_year, payend_year_flag, pay_to_date, pay_end_date, amount, 
		base_premium, add_premium, add_scale, sum_pay_money, sum_add_money, sum_get_money, risk_free_flag, appnt_no, appnt_name, appnt_id_type, appnt_id_no, appnt_sex, appnt_birthday, appnt_occ_type, appnt_occ_code, insured_peoples, 
		insured_app_age, insured_no, insured_name, insured_id_type, insured_id_no, insured_sex, insured_birthday, insured_occ_type, insured_occ_code, insured_pass_flag, cash_value, insuacc_value, cedeout_type, core_conclusion, 
		reinsu_add_scale, cedeout_count, liability_code, liability_name)vdrcl where not exists(select 1 from t_dws_reinsu_policy_liability tdrpl where tdrpl.unique_key=vdrcl.unique_key) 
    </insert>
   
    <insert id="insertBatchReinsuPolicyLiabilityFromHistory" parameterType="DwsReinsuPolicyLiabilityEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_dws_reinsu_policy_liability(batch_no, busi_type, tdch_ids, unique_key, programme_code, busi_occur_date, busi_occur_time, cont_type, grp_cont_no, grp_pol_no, cont_no, pol_no, main_pol_no,
        sale_chnl, sale_chnl_name, sell_type, sell_type_name, sale_com_code, sale_com_name, agent_com_code, agent_com_name, manage_com_code, manage_com_name, bank_branch_name, sign_date, 
        sign_time, risk_vali_date, risk_end_date, cont_make_date, cont_make_time, cont_year, cont_month, cont_anniversary, previous_cont_anniversary, cont_app_flag, risk_code, risk_name, 
        risk_type3, plan_code, pol_risk_type, risk_app_flag, sub_risk_flag, risk_period, insu_year, insu_year_flag, pay_intv, payend_year, payend_year_flag, pay_to_date, pay_end_date, 
        get_duty_codes, get_duty_names, liability_code, liability_name, amount, base_premium, add_premium, add_scale, sum_pay_money, sum_add_money, sum_get_money, risk_free_flag,  
        appnt_no, appnt_name, appnt_id_type, appnt_id_no, appnt_sex, appnt_birthday, appnt_occ_type, appnt_occ_code, insured_peoples, insured_app_age, insured_no, insured_name, insured_id_type, 
        insured_id_no, insured_sex, insured_birthday, insured_occ_type, insured_occ_code, insured_pass_flag, cash_value, insuacc_value, cedeout_type, core_conclusion, reinsu_add_scale, cedeout_count, back_track_data)
		select * from(select #{batchNo} as batch_no, tdch.busi_type, group_concat(tdch.id) as tdch_ids, concat_ws('#', #{programmeCode}, tdch.pol_no, tdch.insured_no, tlm.rs_liability_code, tdch.cont_year, #{contMonth}) as unique_key, 
		#{programmeCode} as programme_code, tdch.busi_occur_date, tdch.busi_occur_time, tdch.cont_type, tdch.grp_cont_no, tdch.grp_pol_no, tdch.cont_no, tdch.pol_no, tdch.main_pol_no, tdch.sale_chnl, tdch.sale_chnl_name, tdch.sell_type, tdch.sell_type_name,
		tdch.sale_com_code, tdch.sale_com_name, tdch.agent_com_code, tdch.agent_com_name, tdch.manage_com_code, tdch.manage_com_name, tdch.bank_branch_name, tdch.sign_date, tdch.sign_time, tdch.risk_vali_date, tdch.risk_end_date, tdch.cont_make_date, 
		tdch.cont_make_time, tdch.cont_year, #{contMonth} as cont_month, tdch.cont_anniversary, tdch.previous_cont_anniversary, tdch.cont_app_flag, tdch.risk_code, tdch.risk_name, tdch.risk_type3, tdch.plan_code, tdch.pol_risk_type, 
		tdch.risk_app_flag, tdch.sub_risk_flag, tdch.risk_period, tdch.insu_year, tdch.insu_year_flag, tdch.pay_intv, tdch.payend_year, tdch.payend_year_flag, tdch.pay_to_date, tdch.pay_end_date, group_concat(tdch.get_duty_code) as get_duty_codes, 
		group_concat(tdch.get_duty_name) as get_duty_names, tlm.rs_liability_code as liability_code, tlm.rs_liability_name as liability_name, tdch.amount, tdch.base_premium, tdch.add_premium, tdch.add_scale, tdch.sum_pay_money, 
		tdch.sum_add_money, tdch.sum_get_money, tdch.risk_free_flag, tdch.appnt_no, tdch.appnt_name, tdch.appnt_id_type, tdch.appnt_id_no, tdch.appnt_sex, tdch.appnt_birthday, tdch.appnt_occ_type, tdch.appnt_occ_code, tdch.insured_peoples, 
		tdch.insured_app_age, tdch.insured_no, tdch.insured_name, tdch.insured_id_type, tdch.insured_id_no, tdch.insured_sex, tdch.insured_birthday, tdch.insured_occ_type, tdch.insured_occ_code, tdch.insured_pass_flag, tdch.cash_value, 
		tdch.insuacc_value, tdch.cedeout_type, tdch.core_conclusion, tdch.reinsu_add_scale, tdch.cedeout_count, #{backTrackData} as back_track_data 
		from t_dws_cont_history tdch inner join ${mysqlBaseName}.t_liability_mapping tlm on tdch.risk_code=tlm.risk_code and tdch.get_duty_code=tlm.lis_liability_code 
		where tdch.is_del=0 and tlm.is_del=0 and tlm.status=0 and tdch.busi_type in (0, 1) and (tdch.insured_peoples=1 or (tdch.insured_peoples=2 and tdch.main_insured_no != tdch.insured_no))
		<if test="cedeoutType != null"> and tdch.cedeout_type = #{cedeoutType}</if>
       	<if test="params.liabilitys != null and params.liabilitys.size > 0">
           and 
            <foreach item="liability" collection="params.liabilitys" open="(" separator=" or " close=")">
	           	(tlm.risk_code = #{liability.riskCode} and tlm.rs_liability_code = #{liability.liabilityCode}
	            and tdch.risk_vali_date &gt;= date_format(#{liability.startDate}, '%Y-%m-%d')
	            and tdch.risk_vali_date &lt;= date_format(#{liability.endDate}, '%Y-%m-%d'))
            </foreach>
       	</if>
       	<if test="params.startDate != null">
            and tdch.busi_occur_date &gt;= date_format(#{params.startDate}, '%Y-%m-%d')
        </if>
        <if test="params.endDate != null">
            and tdch.busi_occur_date &lt;= date_format(#{params.endDate}, '%Y-%m-%d')
        </if>
		group by tdch.busi_type, tdch.busi_occur_date, tdch.busi_occur_time, tdch.cont_type, tdch.grp_cont_no, tdch.grp_pol_no, tdch.cont_no, tdch.pol_no, tdch.main_pol_no, tdch.sale_chnl, tdch.sale_chnl_name, tdch.sell_type, tdch.sell_type_name, tdch.sale_com_code,
		tdch.sale_com_name, tdch.agent_com_code, tdch.agent_com_name, tdch.manage_com_code, tdch.manage_com_name, tdch.bank_branch_name, tdch.sign_date, tdch.sign_time, tdch.risk_vali_date, tdch.risk_end_date, tdch.cont_make_date, tdch.cont_make_time, 
		tdch.cont_year, tdch.cont_anniversary, tdch.previous_cont_anniversary, tdch.cont_app_flag, tdch.risk_code, tdch.risk_name, tdch.risk_type3, tdch.plan_code, tdch.pol_risk_type, tdch.risk_app_flag, tdch.sub_risk_flag, tdch.risk_period, tdch.insu_year, 
		tdch.insu_year_flag, tdch.pay_intv, tdch.payend_year, tdch.payend_year_flag, tdch.pay_to_date, tdch.pay_end_date, tdch.amount, tdch.base_premium, tdch.add_premium, tdch.add_scale, tdch.sum_pay_money, tdch.sum_add_money, tdch.sum_get_money, tdch.risk_free_flag, 
		tdch.appnt_no, tdch.appnt_name, tdch.appnt_id_type, tdch.appnt_id_no, tdch.appnt_sex, tdch.appnt_birthday, tdch.appnt_occ_type, tdch.appnt_occ_code, tdch.insured_peoples, tdch.insured_app_age, tdch.insured_no, tdch.insured_name, tdch.insured_id_type, 
		tdch.insured_id_no, tdch.insured_sex, tdch.insured_birthday, tdch.insured_occ_type, tdch.insured_occ_code, tdch.insured_pass_flag, tdch.cash_value, tdch.insuacc_value, tdch.cedeout_type, tdch.core_conclusion, tdch.reinsu_add_scale, tdch.cedeout_count, 
		tlm.rs_liability_code, tlm.rs_liability_name)vdrcl where not exists(select 1 from t_dws_reinsu_policy_liability tdrpl where tdrpl.unique_key=vdrcl.unique_key) 
    </insert>
</mapper>