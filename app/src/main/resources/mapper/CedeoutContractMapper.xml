<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reinsurance.mapper.CedeoutContractMapper">
    
    <resultMap type="CedeoutContractEntity" id="CedeoutContractResult">
        <result property="id"    column="id"    />
        <result property="batchNo"    column="batch_no"    />
        <result property="version"    column="version"    />
        <result property="companyCode"    column="company_code"    />
        <result property="contractCode"    column="contract_code"    />
        <result property="contractNo"    column="contract_no"    />
        <result property="contractName"    column="contract_name"    />
        <result property="contractAbbr"    column="contract_abbr"    />
        <result property="signDate"    column="sign_date"    />
        <result property="effectiveDate"    column="effective_date"    />
        <result property="expiredDate"    column="expired_date"    />
        <result property="contractType"    column="contract_type"    />
        <result property="mainContractId"    column="main_contract_id"    />
        <result property="mainContractCode"    column="main_contract_code"    />
        <result property="cedeoutType"    column="cedeout_type"    />
        <result property="contractClass"    column="contract_class"    />
        <result property="contractAttr"    column="contract_attr"    />
        <result property="fileName"    column="file_name"    />
        <result property="filePath"    column="file_path"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCedeoutContractVo">
        select id, batch_no, version, company_code, contract_code, contract_no, contract_name, contract_abbr, sign_date, effective_date, expired_date, contract_type, main_contract_id, main_contract_code, cedeout_type, contract_class, contract_attr, file_name, file_path, status, remark, is_del, create_by, create_time, update_by, update_time from t_cedeout_contract
    </sql>

    <select id="selectCedeoutContractByCode" parameterType="String" resultMap="CedeoutContractResult">
        <include refid="selectCedeoutContractVo"/>
        where contract_code = #{contractCode}
    </select>

    <select id="selectCedeoutContractList" parameterType="CedeoutContractQuery" resultMap="CedeoutContractResult">
        <include refid="selectCedeoutContractVo"/>
        <where>  
            <if test="batchNo != null  and batchNo != ''"> and batch_no = #{batchNo}</if>
            <if test="version != null "> and version = #{version}</if>
            <if test="companyCode != null  and companyCode != ''"> and company_code = #{companyCode}</if>
            <if test="contractCode != null  and contractCode != ''"> and contract_code = #{contractCode}</if>
            <if test="contractNo != null  and contractNo != ''"> and contract_no = #{contractNo}</if>
            <if test="contractName != null  and contractName != ''"> and contract_name like concat('%', #{contractName}, '%')</if>
            <if test="contractAbbr != null  and contractAbbr != ''"> and contract_abbr = #{contractAbbr}</if>
            <if test="signDate != null "> and sign_date = #{signDate}</if>
            <if test="effectiveDate != null "> and effective_date = #{effectiveDate}</if>
            <if test="expiredDate != null "> and expired_date = #{expiredDate}</if>
            <if test="contractType != null "> and contract_type = #{contractType}</if>
            <if test="mainContractId != null "> and main_contract_id = #{mainContractId}</if>
            <if test="mainContractCode != null "> and main_contract_code = #{mainContractCode}</if>
            <if test="cedeoutType != null "> and cedeout_type = #{cedeoutType}</if>
            <if test="contractClass != null  and contractClass != ''"> and contract_class = #{contractClass}</if>
            <if test="contractAttr != null  and contractAttr != ''"> and contract_attr = #{contractAttr}</if>
            <if test="fileName != null  and fileName != ''"> and file_name = #{fileName}</if>
            <if test="filePath != null  and filePath != ''"> and file_path = #{filePath}</if>
            <if test="status != null "> and status = #{status}</if>
            and is_del =0
        </where>
    </select>

    <select id="selectCedeoutContractCodeList" resultMap="CedeoutContractResult">
        <include refid="selectCedeoutContractVo"/>
        where is_del = 0 and status = 0 and contract_type = 0
    </select>

    <select id="selectCedeoutContractById" parameterType="Long" resultMap="CedeoutContractResult">
        <include refid="selectCedeoutContractVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCedeoutContract" parameterType="CedeoutContractEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_cedeout_contract
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="batchNo != null and batchNo != ''">batch_no,</if>
            <if test="version != null">version,</if>
            <if test="companyCode != null and companyCode != ''">company_code,</if>
            <if test="contractCode != null and contractCode != ''">contract_code,</if>
            <if test="contractNo != null">contract_no,</if>
            <if test="contractName != null">contract_name,</if>
            <if test="contractAbbr != null">contract_abbr,</if>
            <if test="signDate != null">sign_date,</if>
            <if test="effectiveDate != null">effective_date,</if>
            <if test="expiredDate != null">expired_date,</if>
            <if test="contractType != null">contract_type,</if>
            <if test="mainContractId != null">main_contract_id,</if>
            <if test="mainContractCode != null">main_contract_code,</if>
            <if test="cedeoutType != null">cedeout_type,</if>
            <if test="contractClass != null">contract_class,</if>
            <if test="contractAttr != null">contract_attr,</if>
            <if test="fileName != null">file_name,</if>
            <if test="filePath != null">file_path,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="batchNo != null and batchNo != ''">#{batchNo},</if>
            <if test="version != null">#{version},</if>
            <if test="companyCode != null and companyCode != ''">#{companyCode},</if>
            <if test="contractCode != null and contractCode != ''">#{contractCode},</if>
            <if test="contractNo != null">#{contractNo},</if>
            <if test="contractName != null">#{contractName},</if>
            <if test="contractAbbr != null">#{contractAbbr},</if>
            <if test="signDate != null">#{signDate},</if>
            <if test="effectiveDate != null">#{effectiveDate},</if>
            <if test="expiredDate != null">#{expiredDate},</if>
            <if test="contractType != null">#{contractType},</if>
            <if test="mainContractId != null">#{mainContractId},</if>
            <if test="mainContractCode != null">#{mainContractCode},</if>
            <if test="cedeoutType != null">#{cedeoutType},</if>
            <if test="contractClass != null">#{contractClass},</if>
            <if test="contractAttr != null">#{contractAttr},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="filePath != null">#{filePath},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCedeoutContract" parameterType="CedeoutContractEntity">
        update t_cedeout_contract
        <trim prefix="SET" suffixOverrides=",">
            <if test="batchNo != null and batchNo != ''">batch_no = #{batchNo},</if>
            <if test="version != null">version = #{version},</if>
            <if test="companyCode != null and companyCode != ''">company_code = #{companyCode},</if>
            <if test="contractCode != null and contractCode != ''">contract_code = #{contractCode},</if>
            <if test="contractNo != null">contract_no = #{contractNo},</if>
            <if test="contractName != null">contract_name = #{contractName},</if>
            <if test="contractAbbr != null">contract_abbr = #{contractAbbr},</if>
            <if test="signDate != null">sign_date = #{signDate},</if>
            <if test="effectiveDate != null">effective_date = #{effectiveDate},</if>
            <if test="expiredDate != null">expired_date = #{expiredDate},</if>
            <if test="contractType != null">contract_type = #{contractType},</if>
            <if test="mainContractId != null">main_contract_id = #{mainContractId},</if>
            <if test="mainContractCode != null">main_contract_code = #{mainContractCode},</if>
            <if test="cedeoutType != null">cedeout_type = #{cedeoutType},</if>
            <if test="contractClass != null">contract_class = #{contractClass},</if>
            <if test="contractAttr != null">contract_attr = #{contractAttr},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="filePath != null">file_path = #{filePath},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteCedeoutContractById" parameterType="Long">
        update t_cedeout_contract set is_del = 1 where id = #{id}
    </update>

    <delete id="deleteCedeoutContractByIds" parameterType="String">
        delete from t_cedeout_contract where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    
    <resultMap type="DwsEastZbhtxxbDTO" id="DwsEastZbhtxxbResult">
    	<result property="zbxhthm"    column="zbxhthm"    />
        <result property="zbxhtmc"    column="zbxhtmc"    />
        <result property="htfylx"    column="htfylx"    />
        <result property="zbxfyzhth"    column="zbxfyzhth"    />
        <result property="htzt"    column="htzt"    />
        <result property="lfhtbz"    column="lfhtbz"    />
        <result property="htqsrq"    column="htqsrq"    />
        <result property="htsxqq"    column="htsxqq"    />
        <result property="htsxzq"    column="htsxzq"    />
        <result property="zbxgsdm"    column="zbxgsdm"    />
        <result property="zbxgsmc"    column="zbxgsmc"    />
    </resultMap>
    <select id="selectOriginalContractAsEastZbhtxxb" parameterType="CedeoutContractQuery" resultMap="DwsEastZbhtxxbResult">
    	select t.contract_code as zbxhthm, t.contract_name as zbxhtmc, t.contract_type as htfylx, t.main_contract_code as zbxfyzhth, t.status as htzt, t.cedeout_type as lfhtbz, 
    	date_format(t.sign_date, '%Y%m%d') as htqsrq, date_format(t.effective_date, '%Y%m%d') as htsxqq, date_format(t.expired_date, '%Y%m%d') as htsxzq, t.company_code as zbxgsdm, c.company_name as zbxgsmc
    	from t_cedeout_contract t inner join t_cedeout_company c on t.company_code=c.company_code where t.is_del=0 and t.status=#{status} and t.create_time &gt;= date_format(#{params.startDate}, '%Y-%m-%d') 
    	and t.create_time &lt;= date_format(#{params.endDate}, '%Y-%m-%d') order by t.id
    </select>
    
</mapper>