# 保单登记再保产品信息管理需求规格说明书

## 文档信息

| 项目名称 | 保单登记再保产品信息管理 |
| -------- | ------------ |
| 文档版本 | V1.0         |
| 作者     | 系统生成       |
| 创建日期 | 2025-06-26   |
| 状态     | 已确认       |

## 变更履历

| 版本  | 日期        |  变更描述  | 修订人 | 审核 |
| ---- | ---------- | -------- | ------ | ---- |
| V1.0 | 2025-06-26 | 初次编写   | 系统生成 | 通过  |

## 1. 需求概述

### 1.1 需求背景

为满足监管报送要求，需要建立保单登记再保产品信息管理系统，实现再保产品信息的生成、维护、导入导出和推送功能。

### 1.2 需求目标

1. 实现再保产品信息的自动生成
2. 提供完整的CRUD操作功能
3. 支持数据导入导出
4. 实现数据推送到监管平台
5. 确保数据的准确性和完整性

### 1.3 需求范围

本系统涵盖保单登记再保产品信息的完整生命周期管理，包括数据生成、维护、导入导出、推送等功能。

## 2. 业务架构

### 2.1 模块列表

| 模块编号 | 模块名称 | 模块英文名 | 英文缩写    |
| -------- | -------- | ---------- |---------|
| MD0001   | 保单登记再保产品信息管理     | DWS PRP Product Management   | dws_prp |

### 2.2 数据模型

#### 2.2.1 表名字典
| 表编号   |    表中文名               | 表英文名                      |   备注     |
| ------- | ---------------------   | ---------------------------- | -----------|
| TB0001  | 保单登记再保产品信息表      | t_dws_prp_product            |   新建表    |

#### 2.2.2 表结构

**t_dws_prp_product 保单登记再保产品信息表**

| 字段名                       | 数据类型  | 长度  | 允许空  | 唯一索引  | 默认值 | 说明                                                                                 |
|---------------------------| -------- |-----| ------ | ------- | ------ |------------------------------------------------------------------------------------|
| **Id**                    | bigint   | 20  | 否     | 是       | 无     | 自增主键                                                                               |
| **TransactionNo**         | varchar  | 64  | 否     | 否       | 无     | 流水号                                                                               |
| **CompanyCode**           | varchar  | 64  | 否     | 否       | 无     | 保险机构代码,唯一固定值000166                                                                 |
| **ReInsuranceContNo**     | varchar  | 64  | 否     | 否       | 无     | 再保险合同号码                                                                            |
| **ReInsuranceContName**   | varchar  | 256 | 否     | 否       | 无     | 再保险合同名称                                                                            |
| **ReInsuranceContTitle**  | varchar  | 256 | 否     | 否       | 无     | 再保险合同简称                                                                            |
| **MainReInsuranceContNo** | varchar  | 64  | 否     | 否       | 无     | 再保险附约主合同号                                                                          |
| **ContOrAmendmentType**   | varchar  | 4   | 否     | 否       | 无     | 合同附约类型（1=主合同,2=附约）                                                                 |
| **ProductCode**           | varchar  | 64  | 否     | 否       | 无     | 产品编码                                                                               |
| **ProductName**           | varchar  | 128 | 否     | 否       | 无     | 产品名称                                                                               |
| **GPFlag**                | varchar  | 4   | 否     | 否       | 无     | 团个性质（01=个险,02=团险,99=其他）                                                             |
| **ProductType**           | varchar  | 64  | 否     | 否       | 无     | 险类代码                                                                               |
| **LiabilityCode**         | varchar  | 64  | 否     | 否       | 无     | 责任代码                                                                               |
| **LiabilityName**         | varchar  | 128 | 否     | 否       | 无     | 责任名称                                                                               |
| **ReinsurerCode**         | varchar  | 64  | 否     | 否       | 无     | 再保险公司代码                                                                            |
| **ReinsurerName**         | varchar  | 256 | 否     | 否       | 无     | 再保险公司名称                                                                            |
| **ReinsuranceShare**      | varchar  | 32  | 否     | 否       | 无     | 再保人参与份额比例                                                                          |
| **ReinsurMode**           | varchar  | 4   | 否     | 否       | 无     | 分保方式（1=溢额,2=成数,3=成数溢额混合,4=超赔）                                                       |
| **ReInsuranceType**       | varchar  | 4   | 否     | 否       | 无     | 再保类型（01=事故超赔,02=修正共保方式,03=共保方式,04=风险保费方式,05=赔付率超赔,06=损失终止,07=险位超赔）                  |
| **TermType**              | varchar  | 4   | 否     | 否       | 无     | 保险期限类型（10=长期险,11=定期(年),12=定期(岁),13=定期(两可),14=终身,20=短期险,21=短期,22=极短期,30=主险缴费期,90=未知） |
| **RetentionAmount**       | varchar  | 32  | 否     | 否       | 无     | 自留额                                                                                |
| **RetentionPercentage**   | varchar  | 32  | 否     | 否       | 无     | 自留比例                                                                               |
| **QuotaSharePercentage**  | varchar  | 32  | 否     | 否       | 无     | 分保比例                                                                               |
| **ReportYear**            | int      | 11  | 否     | 否       | 无     | 所属年份                                                                               |
| **ReportMonth**           | tinyint  | 4   | 否     | 否       | 无     | 所属月份                                                                               |
| **AccountPeriod**         | varchar  | 64  | 否     | 否       | 无     | 所属账期                                                                               |
| **DataSource**            | tinyint  | 4   | 否     | 否       | 无     | 数据来源（0=系统,1=人工）                                                                     |
| **PushStatus**            | tinyint  | 4   | 否     | 否       |'0'     | 推送状态（0=未推送,1=已推送）                                                                   |
| **PushDate**              | date     |     | 是     | 否       | 无     | 推送日期                                                                               |
| **PushBy**                | varchar  | 64  | 是     | 否       | 无     | 推送人                                                                                |
| **Remark**                | varchar  | 128 | 是     | 否       | 无     | 备注                                                                                 |
| **IsDel**                 | tinyint  | 4   | 否     | 否       | '0'    | 是否删除(0=未删除,1=已删除)                                                                 |
| **CreateBy**              | varchar  | 64  | 是     | 否       | 无     | 创建人                                                                                |
| **CreateTime**            | datetime |     | 是     | 否       | 无     | 创建时间                                                                               |
| **UpdateBy**              | varchar  | 64  | 是     | 否       | 无     | 修改人                                                                                |
| **UpdateTime**            | datetime |     | 是     | 否       | 无     | 修改时间                                                                               |

### 2.3 用例列表

| 用例编号   | 用例名称      | 用例描述                   | 模块编号 |
|--------|-----------|------------------------| ------- |
| UC0001 | 生成再保产品信息表 | 将再保产品配置信息转换为保单登记产品信息表  | MD0001  |
| UC0002 | 导入再保产品信息表 | 手工上传文件将保单登记产品信息表导入到系统中 | MD0001  |
| UC0003 | 推送再保产品信息表 | 将保单登记产品信息表推送到监管报送平台     | MD0001  |
| UC0004 | 删除再保产品信息表 | 删除再保产品信息表的数据           | MD0001  |
| UC0005 | 查询再保产品信息表 | 查询和展示再保产品信息表数据        | MD0001  |
| UC0006 | 修改再保产品信息表 | 修改再保产品信息表的数据          | MD0001  |
| UC0007 | 导出再保产品信息表 | 导出再保产品信息表数据到Excel文件   | MD0001  |

## 3. 功能需求

### 3.1 保单登记再保产品信息管理

#### 3.1.1 功能概述

保单登记再保产品信息管理模块提供完整的再保产品信息生命周期管理功能，包括数据生成、维护、导入导出、推送等。

#### 3.1.2 功能列表

##### 3.1.2.1 生成再保产品信息表数据（UC0001）

**功能描述：** 从再保合同责任配置数据中自动生成保单登记再保产品信息表数据

**输入参数：**
- 开始日期：数据查询起始日期
- 结束日期：数据查询结束日期  
- 报表年份：生成数据的所属年份
- 报表月份：生成数据的所属月份

**处理逻辑：**
1. 根据日期范围查询再保合同责任配置数据
2. 转换数据格式，映射到产品信息表字段
3. 生成唯一流水号
4. 设置系统字段（创建人、创建时间等）
5. 批量插入数据库

**输出结果：** 返回生成的数据条数

##### 3.1.2.2 查询再保产品信息表（UC0005）

**功能描述：** 提供多条件查询和分页展示功能

**查询条件：**
- 流水号、保险机构代码、再保险合同号码
- 合同附约类型、产品编码、产品名称
- 团个性质、分保方式、再保类型
- 保险期限类型、所属年份、所属月份
- 推送状态等

**展示字段：** 显示所有业务字段和系统字段

##### 3.1.2.3 新增/修改再保产品信息（UC0006）

**功能描述：** 提供手工新增和修改产品信息的功能

**验证规则：**
- 必填字段验证
- 字段长度验证
- 数据格式验证
- 业务规则验证

##### 3.1.2.4 删除再保产品信息（UC0004）

**功能描述：** 删除选中的产品信息记录

**删除规则：**
- 系统生成的数据不允许删除
- 已推送的数据不允许删除
- 支持批量删除

##### 3.1.2.5 导入再保产品信息（UC0002）

**功能描述：** 从Excel文件导入产品信息数据

**导入流程：**
1. 上传Excel文件
2. 数据格式验证
3. 业务规则校验
4. 生成流水号
5. 批量入库

##### 3.1.2.6 导出再保产品信息（UC0007）

**功能描述：** 将查询结果导出为Excel文件

**导出内容：** 根据当前查询条件导出对应数据

##### 3.1.2.7 推送再保产品信息（UC0003）

**功能描述：** 将选中的产品信息推送到监管平台

**推送流程：**
1. 选择要推送的数据
2. 更新推送状态
3. 记录推送时间和推送人

## 4. 技术实现

### 4.1 系统架构

- **后端框架：** Spring Boot + MyBatis
- **前端框架：** Vue.js + Element UI
- **数据库：** MySQL
- **权限控制：** Spring Security

### 4.2 代码结构

#### 4.2.1 后端代码

- **Entity：** DwsPrpProductEntity.java
- **DTO：** DwsPrpProductDTO.java  
- **Query：** DwsPrpProductQuery.java
- **Controller：** DwsPrpProductController.java
- **Service：** IDwsPrpProductService.java, DwsPrpProductServiceImpl.java
- **Mapper：** DwsPrpProductMapper.java, DwsPrpProductMapper.xml

#### 4.2.2 前端代码

- **页面：** /views/dws/prp/product/index.vue
- **API：** /api/dws/prp/product.js

### 4.3 接口清单

| 接口路径 | 请求方法 | 功能描述 | 权限标识 |
|---------|---------|---------|---------|
| /dws/prp/product/list | GET | 查询列表 | dws:prp:product:list |
| /dws/prp/product/{id} | GET | 查询详情 | dws:prp:product:query |
| /dws/prp/product | POST | 新增 | dws:prp:product:add |
| /dws/prp/product | PUT | 修改 | dws:prp:product:edit |
| /dws/prp/product/{ids} | DELETE | 删除 | dws:prp:product:remove |
| /dws/prp/product/export | POST | 导出 | dws:prp:product:export |
| /dws/prp/product/import | POST | 导入 | dws:prp:product:import |
| /dws/prp/product/push/{ids} | GET | 推送 | dws:prp:product:push |
| /dws/prp/product/generate | POST | 生成数据 | dws:prp:product:generate |

## 5. 验收标准

### 5.1 功能验收

- ✅ 完成所有CRUD操作功能
- ✅ 实现数据生成功能
- ✅ 实现导入导出功能
- ✅ 实现推送功能
- ✅ 实现权限控制
- ✅ 实现数据验证

### 5.2 性能验收

- 查询响应时间 < 3秒
- 导入1000条数据 < 30秒
- 导出10000条数据 < 60秒

### 5.3 安全验收

- 所有接口都有权限控制
- 数据输入都有验证
- 敏感操作都有日志记录

## 6. 部署说明

### 6.1 数据库脚本

执行以下SQL脚本：
- docs/sql/init.sql（建表脚本）
- docs/sql/dict.sql（字典数据）
- docs/sql/menu.sql（菜单权限）

### 6.2 配置说明

- 确保系统参数中配置了正确的保险公司代码
- 配置相关的字典数据
- 分配用户相应的菜单权限

---

**文档结束**
