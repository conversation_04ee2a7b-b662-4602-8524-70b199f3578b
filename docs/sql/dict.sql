INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark) VALUES
('保单登记责任分类', 'prp_liability_type', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', NULL),
('保单登记保险期限类型', 'prp_period_type', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', NULL),
('保单登记再保类型', 'prp_reinsu_class', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', NULL),
('保单登记合同属性', 'prp_contract_attr', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', NULL),
('保单登记合同附约类型', 'prp_contract_type', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', NULL),
('保单登记团个性质', 'prp_gp_flag', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', NULL),
('保单登记分保方式', 'prp_reinsu_mode', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', NULL),
('保单登记结算状态', 'prp_pairing_status', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', NULL),
('保单登记业务类型', 'prp_event_type', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', NULL),
('保单登记保单险种责任状态', 'prp_cont_pol_duty_status', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', NULL),
('保单登记投被保险人性别', 'prp_sex', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', NULL),
('保单登记核保结论', 'prp_uw_conclusion', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', NULL);


#保单登记责任分类
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)VALUES
(1, '身故', '0100', 'prp_liability_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(2, '伤残', '0200', 'prp_liability_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(3, '重大疾病', '0300', 'prp_liability_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(4, '高残', '0400', 'prp_liability_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(5, '全残', '0500', 'prp_liability_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(6, '失能', '0600', 'prp_liability_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(7, '医疗', '0700', 'prp_liability_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(8, '医疗费用-住院', '0701', 'prp_liability_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(9, '医疗费用-门诊', '0702', 'prp_liability_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(10, '医疗费用-生育', '0703', 'prp_liability_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(11, '医疗费用-体检', '0704', 'prp_liability_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(12, '预防接种', '0705', 'prp_liability_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(13, '定额给付', '0706', 'prp_liability_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(14, '医疗费用-住院前后门诊', '0707', 'prp_liability_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(15, '医疗费用-特定门诊', '0708', 'prp_liability_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(16, '医疗费用-慢性病门诊', '0709', 'prp_liability_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(17, '医疗费用-牙科', '0710', 'prp_liability_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(18, '医疗费用-眼科', '0711', 'prp_liability_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(19, '医疗费用-其他', '0799', 'prp_liability_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(20, '失业', '0800', 'prp_liability_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(21, '养老金', '0900', 'prp_liability_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(22, '满期保险金', '1000', 'prp_liability_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(23, '生存保险金', '1100', 'prp_liability_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(24, '护理', '1200', 'prp_liability_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(25, '特定疾病', '1300', 'prp_liability_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(26, '个人账户累积', '1400', 'prp_liability_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(27, '其他', '9900', 'prp_liability_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', '');


#保单登记保险期限类型
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)VALUES
(1, '长期险', '10', 'prp_period_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(2, '定期(年)', '11', 'prp_period_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(3, '定期(岁)', '12', 'prp_period_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(4, '定期(两可)', '13', 'prp_period_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(5, '终身', '14', 'prp_period_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(6, '短期险', '20', 'prp_period_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(7, '短期', '21', 'prp_period_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(8, '极短期', '22', 'prp_period_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(9, '主险缴费期', '30', 'prp_period_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(10, '未知', '90', 'prp_period_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', '');


#保单登记再保类型
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)VALUES
(1, '事故超赔', '01', 'prp_reinsu_class', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(2, '修正共保方式', '02', 'prp_reinsu_class', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(3, '共保方式', '03', 'prp_reinsu_class', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(4, '风险保费方式', '04', 'prp_reinsu_class', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(5, '赔付率超赔', '05', 'prp_reinsu_class', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(6, '损失终止', '06', 'prp_reinsu_class', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(7, '险位超赔', '07', 'prp_reinsu_class', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', '');


#保单登记合同属性
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)VALUES
(1, '保险合同', '1', 'prp_contract_attr', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(2, '混合合同', '2', 'prp_contract_attr', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(3, '非保险合同', '3', 'prp_contract_attr', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', '');


#保单登记合同附约类型
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)VALUES
(1, '主合同', '1', 'prp_contract_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(2, '附约', '2', 'prp_contract_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', '');


#保单登记团个性质
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)VALUES
(1, '个险', '01', 'prp_gp_flag', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(2, '团险', '02', 'prp_gp_flag', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(3, '其他', '99', 'prp_gp_flag', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', '');

#保单登记分保方式
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)VALUES
(1, '溢额', '1', 'prp_reinsu_mode', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(2, '成数', '2', 'prp_reinsu_mode', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(3, '成数溢额混合', '3', 'prp_reinsu_mode', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(4, '超赔', '4', 'prp_reinsu_mode', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', '');


#保单登记结算状态
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)VALUES
(1, '未结算', '1', 'prp_pairing_status', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(2, '已结算', '2', 'prp_pairing_status', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', '');


#保单登记业务类型
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)VALUES
(1, '新单', '01', 'prp_event_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(2, '续期', '02', 'prp_event_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(3, '续保', '03', 'prp_event_type', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', '');


#保单登记保单险种责任状态
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)VALUES
(1, '有效', '01', 'prp_cont_pol_duty_status', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(2, '中止', '02', 'prp_cont_pol_duty_status', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(3, '终止', '03', 'prp_cont_pol_duty_status', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(4, '未生效', '04', 'prp_cont_pol_duty_status', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(5, '其他', '99', 'prp_cont_pol_duty_status', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', '');


#保单登记性别
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)VALUES
(1, '未知', '0', 'prp_sex', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(2, '男', '1', 'prp_sex', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(3, '女', '2', 'prp_sex', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(4, '其他', '9', 'prp_sex', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', '');


#
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)VALUES
(1, '标准体', '10', 'prp_uw_conclusion', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(2, '次标准体', '20', 'prp_uw_conclusion', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(3, '加费', '21', 'prp_uw_conclusion', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(4, '限额', '32', 'prp_uw_conclusion', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(5, '特别约定', '33', 'prp_uw_conclusion', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(6, '延期', '40', 'prp_uw_conclusion', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(7, '拒保', '50', 'prp_uw_conclusion', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', ''),
(8, '优标体', '60', 'prp_uw_conclusion', '', '', 'N', '0', 'admin', '2025-05-31 17:00:00', 'admin', '2025-05-31 17:00:00', '');