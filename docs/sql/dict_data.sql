-- =============================================
-- 数据字典初始化SQL
-- 生成时间：2025-06-30
-- =============================================

-- 字典类型数据
INSERT INTO t_sys_dict_type (DictName, DictType, Status, Remark) VALUES
('团个性质', 'prp_gp_flag', '0', '团个性质字典'),
('主附险性质', 'prp_main_product_flag', '0', '主附险性质字典'),
('业务类型', 'prp_event_type', '0', '业务类型字典'),
('保单状态', 'prp_cont_pol_duty_status', '0', '保单状态字典'),
('证件类型', 'prp_cert_type', '0', '证件类型字典'),
('分保方式', 'prp_reinsur_mode', '0', '分保方式字典'),
('合同类型', 'prp_contract_type', '0', '合同类型字典'),
('合同属性', 'prp_contract_attr', '0', '合同属性字典'),
('结算状态', 'prp_pairing_status', '0', '结算状态字典'),
('推送状态', 'regulator_report_push_status', '0', '推送状态字典'),
('数据来源', 'regulator_report_data_source', '0', '数据来源字典'),
('用户性别', 'sys_user_sex', '0', '用户性别字典');

-- 团个性质字典数据
INSERT INTO t_sys_dict_data (DictSort, DictLabel, DictValue, DictType, Status, Remark) VALUES
(1, '个险', '01', 'prp_gp_flag', '0', '个人保险'),
(2, '团险', '02', 'prp_gp_flag', '0', '团体保险'),
(3, '其他', '99', 'prp_gp_flag', '0', '其他类型');

-- 主附险性质字典数据
INSERT INTO t_sys_dict_data (DictSort, DictLabel, DictValue, DictType, Status, Remark) VALUES
(1, '主险', '1', 'prp_main_product_flag', '0', '主险产品'),
(2, '附加险', '2', 'prp_main_product_flag', '0', '附加险产品'),
(3, '不区分', '3', 'prp_main_product_flag', '0', '不区分主附险');

-- 业务类型字典数据
INSERT INTO t_sys_dict_data (DictSort, DictLabel, DictValue, DictType, Status, Remark) VALUES
(1, '新单', '01', 'prp_event_type', '0', '新单业务'),
(2, '续期', '02', 'prp_event_type', '0', '续期业务'),
(3, '续保', '03', 'prp_event_type', '0', '续保业务');

-- 保单状态字典数据
INSERT INTO t_sys_dict_data (DictSort, DictLabel, DictValue, DictType, Status, Remark) VALUES
(1, '有效', '1', 'prp_cont_pol_duty_status', '0', '保单有效'),
(2, '失效', '2', 'prp_cont_pol_duty_status', '0', '保单失效'),
(3, '终止', '3', 'prp_cont_pol_duty_status', '0', '保单终止'),
(4, '满期', '4', 'prp_cont_pol_duty_status', '0', '保单满期');

-- 证件类型字典数据
INSERT INTO t_sys_dict_data (DictSort, DictLabel, DictValue, DictType, Status, Remark) VALUES
(1, '身份证', '1', 'prp_cert_type', '0', '居民身份证'),
(2, '护照', '2', 'prp_cert_type', '0', '护照'),
(3, '军官证', '3', 'prp_cert_type', '0', '军官证'),
(4, '驾驶证', '4', 'prp_cert_type', '0', '驾驶证'),
(5, '其他', '9', 'prp_cert_type', '0', '其他证件');

-- 分保方式字典数据
INSERT INTO t_sys_dict_data (DictSort, DictLabel, DictValue, DictType, Status, Remark) VALUES
(1, '溢额', '1', 'prp_reinsur_mode', '0', '溢额再保险'),
(2, '成数', '2', 'prp_reinsur_mode', '0', '成数再保险'),
(3, '成数溢额混合', '3', 'prp_reinsur_mode', '0', '成数溢额混合再保险'),
(4, '超赔', '4', 'prp_reinsur_mode', '0', '超额赔款再保险');

-- 合同类型字典数据
INSERT INTO t_sys_dict_data (DictSort, DictLabel, DictValue, DictType, Status, Remark) VALUES
(1, '合同', '1', 'prp_contract_type', '0', '再保险合同'),
(2, '附约', '2', 'prp_contract_type', '0', '再保险附约');

-- 合同属性字典数据
INSERT INTO t_sys_dict_data (DictSort, DictLabel, DictValue, DictType, Status, Remark) VALUES
(1, '比例', '1', 'prp_contract_attr', '0', '比例再保险'),
(2, '非比例', '2', 'prp_contract_attr', '0', '非比例再保险');

-- 结算状态字典数据
INSERT INTO t_sys_dict_data (DictSort, DictLabel, DictValue, DictType, Status, Remark) VALUES
(1, '未结算', '1', 'prp_pairing_status', '0', '未结算'),
(2, '已结算', '2', 'prp_pairing_status', '0', '已结算');

-- 推送状态字典数据
INSERT INTO t_sys_dict_data (DictSort, DictLabel, DictValue, DictType, Status, Remark) VALUES
(1, '未推送', '0', 'regulator_report_push_status', '0', '未推送'),
(2, '已推送', '1', 'regulator_report_push_status', '0', '已推送'),
(3, '推送失败', '2', 'regulator_report_push_status', '0', '推送失败');

-- 数据来源字典数据
INSERT INTO t_sys_dict_data (DictSort, DictLabel, DictValue, DictType, Status, Remark) VALUES
(1, '系统', '0', 'regulator_report_data_source', '0', '系统生成'),
(2, '人工', '1', 'regulator_report_data_source', '0', '人工录入');

-- 用户性别字典数据
INSERT INTO t_sys_dict_data (DictSort, DictLabel, DictValue, DictType, Status, Remark) VALUES
(1, '男', '0', 'sys_user_sex', '0', '男性'),
(2, '女', '1', 'sys_user_sex', '0', '女性'),
(3, '未知', '2', 'sys_user_sex', '0', '性别未知');
