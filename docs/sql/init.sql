-- =============================================
-- 保单登记再保险数据表DDL
-- 数据库：StarRocks 3.1
-- 字符集：utf8
-- 生成时间：2025-06-23
-- =============================================

-- TB0009: 再保保全险种明细表
CREATE TABLE `t_dws_prp_edor` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT "自增主键",
  `TransactionNo` varchar(64) NOT NULL COMMENT "交易编码",
  `CompanyCode` varchar(64) NOT NULL COMMENT "保险机构代码（唯一固定值：000166）",
  `GrpPolicyNo` varchar(64) NULL COMMENT "团体保单号",
  `GrpProductNo` varchar(64) NULL COMMENT "团单保险险种号码",
  `PolicyNo` varchar(64) NOT NULL COMMENT "个人保单号",
  `ProductNo` varchar(64) NOT NULL COMMENT "个单保险险种号码",
  `GPFlag` varchar(4) NOT NULL COMMENT "团个性质（01=个险,02=团险,99=其他）",
  `MainProductNo` varchar(64) NOT NULL COMMENT "主险保险险种号码",
  `MainProductFlag` varchar(4) NOT NULL COMMENT "主附险性质代码（1=主险,2=附加险,3=不区分）",
  `ProductCode` varchar(64) NOT NULL COMMENT "产品编码",
  `LiabilityCode` varchar(64) NOT NULL COMMENT "责任代码",
  `LiabilityName` varchar(128) NOT NULL COMMENT "责任名称",
  `Classification` varchar(64) NULL COMMENT "责任分类代码",
  `EventType` varchar(4) NOT NULL COMMENT "业务类型(01=新单,02=续期,03=续保)",
  `PolYear` tinyint(4) NOT NULL COMMENT "保单年度",
  `RenewalTimes` tinyint(4) NULL COMMENT "续期续保次数(保单年度-1)",
  `TermType` varchar(4) NULL COMMENT "保险期限类型",
  `ManageCom` varchar(4) NULL COMMENT "管理机构代码",
  `SignDate` date NOT NULL COMMENT "签单日期",
  `EffDate` date NOT NULL COMMENT "保险责任生效日期",
  `InvalidDate` date NOT NULL COMMENT "保险责任终止日期",
  `UWConclusion` varchar(64) NULL COMMENT "核保结论代码",
  `PolStatus` varchar(4) NOT NULL COMMENT "保单状态代码",
  `Status` varchar(4) NOT NULL COMMENT "保单险种状态代码",
  `BasicSumInsured` decimal(20, 2) NULL COMMENT "基本保额",
  `RiskAmnt` decimal(20, 2) NULL COMMENT "风险保额",
  `Premium` decimal(20, 2) NULL COMMENT "保费",
  `AccountValue` decimal(20, 2) NULL COMMENT "保险账户价值",
  `FacultativeFlag` varchar(64) NOT NULL COMMENT "临分标记（0=否,1=是）",
  `AnonymousFlag` varchar(4) NULL COMMENT "无名单标志",
  `WaiverFlag` varchar(4) NULL COMMENT "豁免险标志（0=否,1=是）",
  `WaiverPrem` decimal(20, 2) NULL COMMENT "所需豁免剩余保费",
  `FinalCashValue` decimal(20, 2) NULL COMMENT "期末现金价值",
  `FinalLiabilityReserve` decimal(20, 2) NULL COMMENT "期末责任准备金",
  `InsuredNo` varchar(64) NOT NULL COMMENT "被保人客户号",
  `InsuredName` varchar(64) NOT NULL COMMENT "被保人姓名",
  `InsuredSex` varchar(4) NOT NULL COMMENT "被保人性别",
  `InsuredCertType` varchar(4) NOT NULL COMMENT "被保人证件类型",
  `InsuredCertNo` varchar(64) NOT NULL COMMENT "被保人证件编码",
  `OccupationType` varchar(12) NOT NULL COMMENT "职业代码",
  `AppntAge` tinyint(4) NULL COMMENT "投保年龄",
  `PreAge` tinyint(4) NULL COMMENT "当前年龄",
  `ProfessionalFee` decimal(20, 2) NULL COMMENT "职业加费金额",
  `SubStandardFee` decimal(20, 2) NULL COMMENT "次标准体加费金额",
  `EMRate` decimal(10, 2) NULL COMMENT "EM加点",
  `ProjectFlag` varchar(4) NULL COMMENT "建工险标志",
  `InsurePeoples` tinyint(4) NULL COMMENT "被保人数",
  `SaparateFlag` varchar(4) NOT NULL COMMENT "分出标记（0=未达到溢额线保单,1=分出保单）",
  `ReInsuranceContNo` varchar(64) NOT NULL COMMENT "再保险合同号码",
  `ReinsurerCode` varchar(64) NOT NULL COMMENT "再保险公司代码",
  `ReinsurerName` varchar(256) NOT NULL COMMENT "再保险公司名称",
  `ReinsurMode` varchar(4) NOT NULL COMMENT "分保方式（1=溢额,2=成数,3=成数溢额混合,4=超赔）",
  `EdorNo` varchar(64) NOT NULL COMMENT "保全号",
  `EdorType` varchar(4) NOT NULL COMMENT "保全类型",
  `EdorDate` date NOT NULL COMMENT "保全日期",
  `EdorValidDate` date NOT NULL COMMENT "保全生效日期",
  `EdorReason` varchar(4) NOT NULL COMMENT "保全原因",
  `BasicSumInsuredChange` decimal(20, 2) NULL COMMENT "变更后基本保额",
  `RiskAmntChange` decimal(20, 2) NULL COMMENT "变更后风险保额",
  `PremiumChange` decimal(20, 2) NULL COMMENT "变更后保费",
  `RetentionAmount` decimal(20, 2) NULL COMMENT "变更后自留额",
  `ReinsurancePremiumChange` decimal(20, 2) NULL COMMENT "变更分保费",
  `ReinsuranceCommssionChange` decimal(20, 2) NULL COMMENT "变更分保佣金",
  `Currency` varchar(4) NOT NULL COMMENT "货币代码",
  `ReComputationsDate` date NULL COMMENT "分保计算日期",
  `AccountGetDate` date NULL COMMENT "账单归属日期",
  `AccTransNo` varchar(64) NOT NULL COMMENT "所属账单流水号",
  `DataSource` tinyint(4) NOT NULL COMMENT "数据来源（0=系统,1=人工）",
  `PushStatus` tinyint(4) NOT NULL DEFAULT "0" COMMENT "推送状态（0=未推送,1=已推送）",
  `PushDate` date NULL COMMENT "推送日期",
  `PushBy` varchar(64) NULL COMMENT "推送人",
  `Remark` varchar(128) NULL COMMENT "备注",
  `IsDel` tinyint(4) NULL DEFAULT "0" COMMENT "是否删除(0=未删除,1=已删除)",
  `CreateBy` varchar(64) NULL COMMENT "创建人",
  `CreateTime` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "创建时间",
  `UpdateBy` varchar(64) NULL COMMENT "修改人",
  `UpdateTime` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "修改时间"
) ENGINE=OLAP
PRIMARY KEY(`Id`)
COMMENT "再保保全险种明细表"
DISTRIBUTED BY HASH(`Id`) BUCKETS 16
PROPERTIES (
"compression" = "LZ4",
"enable_persistent_index" = "true",
"fast_schema_evolution" = "false",
"replicated_storage" = "true",
"replication_num" = "1"
);

-- TB0010: 再保理赔险种明细表
CREATE TABLE `t_dws_prp_claim` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT "自增主键",
  `TransactionNo` varchar(64) NOT NULL COMMENT "交易编码",
  `CompanyCode` varchar(64) NOT NULL COMMENT "保险机构代码（唯一固定值：000166）",
  `GrpPolicyNo` varchar(64) NULL COMMENT "团体保单号",
  `GrpProductNo` varchar(64) NULL COMMENT "团单保险险种号码",
  `PolicyNo` varchar(64) NOT NULL COMMENT "个人保单号",
  `ProductNo` varchar(64) NOT NULL COMMENT "个单保险险种号码",
  `GPFlag` varchar(4) NOT NULL COMMENT "团个性质（01=个险,02=团险,99=其他）",
  `MainProductNo` varchar(64) NOT NULL COMMENT "主险保险险种号码",
  `MainProductFlag` varchar(4) NOT NULL COMMENT "主附险性质代码（1=主险,2=附加险,3=不区分）",
  `ProductCode` varchar(64) NOT NULL COMMENT "产品编码",
  `LiabilityCode` varchar(64) NOT NULL COMMENT "责任代码",
  `LiabilityName` varchar(128) NOT NULL COMMENT "责任名称",
  `Classification` varchar(64) NULL COMMENT "责任分类代码",
  `EventType` varchar(4) NOT NULL COMMENT "业务类型(01=新单,02=续期,03=续保)",
  `PolYear` tinyint(4) NOT NULL COMMENT "保单年度",
  `RenewalTimes` tinyint(4) NULL COMMENT "续期续保次数(保单年度-1)",
  `TermType` varchar(4) NULL COMMENT "保险期限类型",
  `ManageCom` varchar(4) NULL COMMENT "管理机构代码",
  `SignDate` date NOT NULL COMMENT "签单日期",
  `EffDate` date NOT NULL COMMENT "保险责任生效日期",
  `InvalidDate` date NOT NULL COMMENT "保险责任终止日期",
  `UWConclusion` varchar(64) NULL COMMENT "核保结论代码",
  `PolStatus` varchar(4) NOT NULL COMMENT "保单状态代码",
  `Status` varchar(4) NOT NULL COMMENT "保单险种状态代码",
  `BasicSumInsured` decimal(20, 2) NULL COMMENT "基本保额",
  `RiskAmnt` decimal(20, 2) NULL COMMENT "风险保额",
  `Premium` decimal(20, 2) NULL COMMENT "保费",
  `AccountValue` decimal(20, 2) NULL COMMENT "保险账户价值",
  `FacultativeFlag` varchar(64) NOT NULL COMMENT "临分标记（0=否,1=是）",
  `AnonymousFlag` varchar(4) NULL COMMENT "无名单标志",
  `WaiverFlag` varchar(4) NULL COMMENT "豁免险标志（0=否,1=是）",
  `WaiverPrem` decimal(20, 2) NULL COMMENT "所需豁免剩余保费",
  `FinalCashValue` decimal(20, 2) NULL COMMENT "期末现金价值",
  `FinalLiabilityReserve` decimal(20, 2) NULL COMMENT "期末责任准备金",
  `InsuredNo` varchar(64) NOT NULL COMMENT "被保人客户号",
  `InsuredName` varchar(64) NOT NULL COMMENT "被保人姓名",
  `InsuredSex` varchar(4) NOT NULL COMMENT "被保人性别",
  `InsuredCertType` varchar(4) NOT NULL COMMENT "被保人证件类型",
  `InsuredCertNo` varchar(64) NOT NULL COMMENT "被保人证件编码",
  `OccupationType` varchar(12) NOT NULL COMMENT "职业代码",
  `AppntAge` tinyint(4) NULL COMMENT "投保年龄",
  `PreAge` tinyint(4) NULL COMMENT "当前年龄",
  `ProfessionalFee` decimal(20, 2) NULL COMMENT "职业加费金额",
  `SubStandardFee` decimal(20, 2) NULL COMMENT "次标准体加费金额",
  `EMRate` decimal(10, 2) NULL COMMENT "EM加点",
  `ProjectFlag` varchar(4) NULL COMMENT "建工险标志",
  `InsurePeoples` tinyint(4) NULL COMMENT "被保人数",
  `SaparateFlag` varchar(4) NOT NULL COMMENT "分出标记（0=未达到溢额线保单,1=分出保单）",
  `ReInsuranceContNo` varchar(64) NOT NULL COMMENT "再保险合同号码",
  `ReinsurerCode` varchar(64) NOT NULL COMMENT "再保险公司代码",
  `ReinsurerName` varchar(256) NOT NULL COMMENT "再保险公司名称",
  `ReinsurMode` varchar(4) NOT NULL COMMENT "分保方式（1=溢额,2=成数,3=成数溢额混合,4=超赔）",
  `ClaimNo` varchar(64) NOT NULL COMMENT "理赔号",
  `ClaimType` varchar(4) NOT NULL COMMENT "理赔类型",
  `ClaimDate` date NOT NULL COMMENT "理赔日期",
  `ClaimReason` varchar(4) NOT NULL COMMENT "理赔原因",
  `ClaimAmount` decimal(20, 2) NOT NULL COMMENT "理赔金额",
  `RetentionAmount` decimal(20, 2) NULL COMMENT "自留额",
  `ReinsuranceClaimAmount` decimal(20, 2) NULL COMMENT "分保理赔金额",
  `Currency` varchar(4) NOT NULL COMMENT "货币代码",
  `ReComputationsDate` date NULL COMMENT "分保计算日期",
  `AccountGetDate` date NULL COMMENT "账单归属日期",
  `AccTransNo` varchar(64) NOT NULL COMMENT "所属账单流水号",
  `DataSource` tinyint(4) NOT NULL COMMENT "数据来源（0=系统,1=人工）",
  `PushStatus` tinyint(4) NOT NULL DEFAULT "0" COMMENT "推送状态（0=未推送,1=已推送）",
  `PushDate` date NULL COMMENT "推送日期",
  `PushBy` varchar(64) NULL COMMENT "推送人",
  `Remark` varchar(128) NULL COMMENT "备注",
  `IsDel` tinyint(4) NULL DEFAULT "0" COMMENT "是否删除(0=未删除,1=已删除)",
  `CreateBy` varchar(64) NULL COMMENT "创建人",
  `CreateTime` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "创建时间",
  `UpdateBy` varchar(64) NULL COMMENT "修改人",
  `UpdateTime` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "修改时间"
) ENGINE=OLAP
PRIMARY KEY(`Id`)
COMMENT "再保理赔险种明细表"
DISTRIBUTED BY HASH(`Id`) BUCKETS 16
PROPERTIES (
"compression" = "LZ4",
"enable_persistent_index" = "true",
"fast_schema_evolution" = "false",
"replicated_storage" = "true",
"replication_num" = "1"
);

-- TB0011: 再保生存金险种明细表
CREATE TABLE `t_dws_prp_benefit` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT "自增主键",
  `TransactionNo` varchar(64) NOT NULL COMMENT "交易编码",
  `CompanyCode` varchar(64) NOT NULL COMMENT "保险机构代码（唯一固定值：000166）",
  `GrpPolicyNo` varchar(64) NULL COMMENT "团体保单号",
  `GrpProductNo` varchar(64) NULL COMMENT "团单保险险种号码",
  `PolicyNo` varchar(64) NOT NULL COMMENT "个人保单号",
  `ProductNo` varchar(64) NOT NULL COMMENT "个单保险险种号码",
  `GPFlag` varchar(4) NOT NULL COMMENT "团个性质（01=个险,02=团险,99=其他）",
  `MainProductNo` varchar(64) NOT NULL COMMENT "主险保险险种号码",
  `MainProductFlag` varchar(4) NOT NULL COMMENT "主附险性质代码（1=主险,2=附加险,3=不区分）",
  `ProductCode` varchar(64) NOT NULL COMMENT "产品编码",
  `LiabilityCode` varchar(64) NOT NULL COMMENT "责任代码",
  `LiabilityName` varchar(128) NOT NULL COMMENT "责任名称",
  `Classification` varchar(64) NULL COMMENT "责任分类代码",
  `EventType` varchar(4) NOT NULL COMMENT "业务类型(01=新单,02=续期,03=续保)",
  `PolYear` tinyint(4) NOT NULL COMMENT "保单年度",
  `RenewalTimes` tinyint(4) NULL COMMENT "续期续保次数(保单年度-1)",
  `TermType` varchar(4) NULL COMMENT "保险期限类型",
  `ManageCom` varchar(4) NULL COMMENT "管理机构代码",
  `SignDate` date NOT NULL COMMENT "签单日期",
  `EffDate` date NOT NULL COMMENT "保险责任生效日期",
  `InvalidDate` date NOT NULL COMMENT "保险责任终止日期",
  `UWConclusion` varchar(64) NULL COMMENT "核保结论代码",
  `PolStatus` varchar(4) NOT NULL COMMENT "保单状态代码",
  `Status` varchar(4) NOT NULL COMMENT "保单险种状态代码",
  `BasicSumInsured` decimal(20, 2) NULL COMMENT "基本保额",
  `RiskAmnt` decimal(20, 2) NULL COMMENT "风险保额",
  `Premium` decimal(20, 2) NULL COMMENT "保费",
  `AccountValue` decimal(20, 2) NULL COMMENT "保险账户价值",
  `FacultativeFlag` varchar(64) NOT NULL COMMENT "临分标记（0=否,1=是）",
  `AnonymousFlag` varchar(4) NULL COMMENT "无名单标志",
  `WaiverFlag` varchar(4) NULL COMMENT "豁免险标志（0=否,1=是）",
  `WaiverPrem` decimal(20, 2) NULL COMMENT "所需豁免剩余保费",
  `FinalCashValue` decimal(20, 2) NULL COMMENT "期末现金价值",
  `FinalLiabilityReserve` decimal(20, 2) NULL COMMENT "期末责任准备金",
  `InsuredNo` varchar(64) NOT NULL COMMENT "被保人客户号",
  `InsuredName` varchar(64) NOT NULL COMMENT "被保人姓名",
  `InsuredSex` varchar(4) NOT NULL COMMENT "被保人性别",
  `InsuredCertType` varchar(4) NOT NULL COMMENT "被保人证件类型",
  `InsuredCertNo` varchar(64) NOT NULL COMMENT "被保人证件编码",
  `OccupationType` varchar(12) NOT NULL COMMENT "职业代码",
  `AppntAge` tinyint(4) NULL COMMENT "投保年龄",
  `PreAge` tinyint(4) NULL COMMENT "当前年龄",
  `ProfessionalFee` decimal(20, 2) NULL COMMENT "职业加费金额",
  `SubStandardFee` decimal(20, 2) NULL COMMENT "次标准体加费金额",
  `EMRate` decimal(10, 2) NULL COMMENT "EM加点",
  `ProjectFlag` varchar(4) NULL COMMENT "建工险标志",
  `InsurePeoples` tinyint(4) NULL COMMENT "被保人数",
  `SaparateFlag` varchar(4) NOT NULL COMMENT "分出标记（0=未达到溢额线保单,1=分出保单）",
  `ReInsuranceContNo` varchar(64) NOT NULL COMMENT "再保险合同号码",
  `ReinsurerCode` varchar(64) NOT NULL COMMENT "再保险公司代码",
  `ReinsurerName` varchar(256) NOT NULL COMMENT "再保险公司名称",
  `ReinsurMode` varchar(4) NOT NULL COMMENT "分保方式（1=溢额,2=成数,3=成数溢额混合,4=超赔）",
  `BenefitNo` varchar(64) NOT NULL COMMENT "生存金号",
  `BenefitType` varchar(4) NOT NULL COMMENT "生存金类型",
  `BenefitDate` date NOT NULL COMMENT "生存金给付日期",
  `BenefitReason` varchar(4) NOT NULL COMMENT "生存金给付原因",
  `BenefitAmount` decimal(20, 2) NOT NULL COMMENT "生存金给付金额",
  `RetentionAmount` decimal(20, 2) NULL COMMENT "自留额",
  `ReinsuranceBenefitAmount` decimal(20, 4) NULL COMMENT "分保生存金金额",
  `Currency` varchar(4) NOT NULL COMMENT "货币代码",
  `ReComputationsDate` date NULL COMMENT "分保计算日期",
  `AccountGetDate` date NULL COMMENT "账单归属日期",
  `AccTransNo` varchar(64) NOT NULL COMMENT "所属账单流水号",
  `DataSource` tinyint(4) NOT NULL COMMENT "数据来源（0=系统,1=人工）",
  `PushStatus` tinyint(4) NOT NULL DEFAULT "0" COMMENT "推送状态（0=未推送,1=已推送）",
  `PushDate` date NULL COMMENT "推送日期",
  `PushBy` varchar(64) NULL COMMENT "推送人",
  `Remark` varchar(128) NULL COMMENT "备注",
  `IsDel` tinyint(4) NULL DEFAULT "0" COMMENT "是否删除(0=未删除,1=已删除)",
  `CreateBy` varchar(64) NULL COMMENT "创建人",
  `CreateTime` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "创建时间",
  `UpdateBy` varchar(64) NULL COMMENT "修改人",
  `UpdateTime` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "修改时间"
) ENGINE=OLAP
PRIMARY KEY(`Id`)
COMMENT "再保生存金险种明细表"
DISTRIBUTED BY HASH(`Id`) BUCKETS 16
PROPERTIES (
"compression" = "LZ4",
"enable_persistent_index" = "true",
"fast_schema_evolution" = "false",
"replicated_storage" = "true",
"replication_num" = "1"
);