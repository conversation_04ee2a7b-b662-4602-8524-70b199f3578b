-- =============================================
-- 保单登记再保险数据表DDL
-- 数据库：StarRocks 3.1
-- 字符集：utf8
-- 生成时间：2025-06-23
-- =============================================

-- TB0001: 保单登记再保产品信息表
CREATE TABLE `t_dws_prp_product` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT "自增主键",
  `TransactionNo` varchar(64) NOT NULL COMMENT "流水号",
  `CompanyCode` varchar(64) NOT NULL COMMENT "保险机构代码（唯一固定值：000166）",
  `ReInsuranceContNo` varchar(64) NOT NULL COMMENT "再保险合同号码",
  `ReInsuranceContName` varchar(256) NOT NULL COMMENT "再保险合同名称",
  `ReInsuranceContTitle` varchar(256) NOT NULL COMMENT "再保险合同简称",
  `MainReInsuranceContNo` varchar(64) NOT NULL COMMENT "再保险附约主合同号",
  `ContOrAmendmentType` varchar(4) NOT NULL COMMENT "合同附约类型（1=主合同,2=附约）",
  `ProductCode` varchar(64) NOT NULL COMMENT "产品编码",
  `ProductName` varchar(128) NOT NULL COMMENT "产品名称",
  `GPFlag` varchar(4) NOT NULL COMMENT "团个性质（01=个险,02=团险,99=其他）",
  `ProductType` varchar(64) NOT NULL COMMENT "险类代码",
  `LiabilityCode` varchar(64) NOT NULL COMMENT "责任代码",
  `LiabilityName` varchar(128) NOT NULL COMMENT "责任名称",
  `ReinsurerCode` varchar(64) NOT NULL COMMENT "再保险公司代码",
  `ReinsurerName` varchar(256) NOT NULL COMMENT "再保险公司名称",
  `ReinsuranceShare` varchar(32) NOT NULL COMMENT "再保人参与份额比例",
  `ReinsurMode` varchar(4) NOT NULL COMMENT "分保方式（1=溢额,2=成数,3=成数溢额混合,4=超赔）",
  `ReInsuranceType` varchar(4) NOT NULL COMMENT "再保类型（01=事故超赔,02=修正共保方式,03=共保方式,04=风险保费方式,05=赔付率超赔,06=损失终止,07=险位超赔）",
  `TermType` varchar(4) NOT NULL COMMENT "保险期限类型（10=长期险,11=定期(年),12=定期(岁),13=定期(两可),14=终身,20=短期险,21=短期,22=极短期,30=主险缴费期,90=未知）",
  `RetentionAmount` varchar(32) NOT NULL COMMENT "自留额",
  `RetentionPercentage` varchar(32) NOT NULL COMMENT "自留比例",
  `QuotaSharePercentage` varchar(32) NOT NULL COMMENT "分保比例",
  `ReportYear` int(11) NOT NULL COMMENT "所属年份",
  `ReportMonth` tinyint(4) NOT NULL COMMENT "所属月份",
  `AccountPeriod` varchar(64) NOT NULL COMMENT "所属账期",
  `DataSource` tinyint(4) NOT NULL COMMENT "数据来源（0=系统,1=人工）",
  `PushStatus` tinyint(4) NOT NULL DEFAULT "0" COMMENT "推送状态（0=未推送,1=已推送）",
  `PushDate` date NULL COMMENT "推送日期",
  `PushBy` varchar(64) NULL COMMENT "推送人",
  `Remark` varchar(128) NULL COMMENT "备注",
  `IsDel` tinyint(4) NULL DEFAULT "0" COMMENT "是否删除(0=未删除,1=已删除)",
  `CreateBy` varchar(64) NULL COMMENT "创建人",
  `CreateTime` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "创建时间",
  `UpdateBy` varchar(64) NULL COMMENT "修改人",
  `UpdateTime` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT "修改时间"
) ENGINE=OLAP
PRIMARY KEY(`Id`)
COMMENT "保单登记再保产品信息表"
DISTRIBUTED BY HASH(`Id`) BUCKETS 16
PROPERTIES (
"compression" = "LZ4",
"enable_persistent_index" = "true",
"fast_schema_evolution" = "false",
"replicated_storage" = "true",
"replication_num" = "1"
);

-- TB0002: 保单登记再保合同信息表
CREATE TABLE `t_dws_prp_insure_cont` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT "自增主键",
  `TransactionNo` varchar(64) NOT NULL COMMENT "流水号",
  `CompanyCode` varchar(64) NOT NULL COMMENT "保险机构代码（唯一固定值：000166）",
  `ReInsuranceContNo` varchar(64) NOT NULL COMMENT "再保险合同号码",
  `ReInsuranceContName` varchar(256) NOT NULL COMMENT "再保险合同名称",
  `ReInsuranceContTitle` varchar(256) NOT NULL COMMENT "再保险合同简称",
  `MainReInsuranceContNo` varchar(64) NOT NULL COMMENT "再保险附约主合同号",
  `ContOrAmendmentType` varchar(4) NOT NULL COMMENT "合同附约类型（1=主合同,2=附约）",
  `ContAttribute` varchar(4) NOT NULL COMMENT "合同属性（1=保险合同,2=混合合同,3=非保险合同）",
  `ContStatus` varchar(4) NOT NULL COMMENT "合同状态（1=有效,2=终止）",
  `TreatyOrFacultativeFlag` varchar(4) NOT NULL COMMENT "合同/临分标志（0=否,1=是）",
  `ContSigndate` date NOT NULL COMMENT "合同签署日期",
  `PeriodFrom` date NOT NULL COMMENT "合同生效起期",
  `PeriodTo` date NOT NULL COMMENT "合同生效止期",
  `ContType` varchar(4) NOT NULL COMMENT "合同类型（1=比例合同,2=非比例合同）",
  `ReinsurerCode` varchar(64) NOT NULL COMMENT "再保险公司代码",
  `ReinsurerName` varchar(256) NOT NULL COMMENT "再保险公司名称",
  `ChargeType` varchar(4) NOT NULL COMMENT "佣金核算方式（1=业务年度，2=财务年度）",
  `ReportYear` int(11) NOT NULL COMMENT "所属年份",
  `ReportMonth` tinyint(4) NOT NULL COMMENT "所属月份",
  `AccountPeriod` varchar(64) NOT NULL COMMENT "所属账期",
  `DataSource` tinyint(4) NOT NULL COMMENT "数据来源（0=系统,1=人工）",
  `PushStatus` tinyint(4) NOT NULL DEFAULT "0" COMMENT "推送状态（0=未推送,1=已推送）",
  `PushDate` date NULL COMMENT "推送日期",
  `PushBy` varchar(64) NULL COMMENT "推送人",
  `Remark` varchar(128) NULL COMMENT "备注",
  `IsDel` tinyint(4) NULL DEFAULT "0" COMMENT "是否删除(0=未删除,1=已删除)",
  `CreateBy` varchar(64) NULL COMMENT "创建人",
  `CreateTime` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "创建时间",
  `UpdateBy` varchar(64) NULL COMMENT "修改人",
  `UpdateTime` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "修改时间"
) ENGINE=OLAP
PRIMARY KEY(`Id`)
COMMENT "保单登记再保合同信息表"
DISTRIBUTED BY HASH(`Id`) BUCKETS 16
PROPERTIES (
"compression" = "LZ4",
"enable_persistent_index" = "true",
"fast_schema_evolution" = "false",
"replicated_storage" = "true",
"replication_num" = "1"
);

CREATE TABLE `t_dws_prp_account` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT "自增主键",
  `TransactionNo` varchar(64) NOT NULL COMMENT "流水号",
  `CompanyCode` varchar(64) NOT NULL COMMENT "保险机构代码（唯一固定值：000166）",
  `AccountID` varchar(64) NOT NULL COMMENT "账单编号",
  `AccountingPeriodfrom` date NOT NULL COMMENT "账单起期",
  `AccountingPeriodto` date NOT NULL COMMENT "账单止期",
  `ReinsurerCode` varchar(64) NOT NULL COMMENT "再保险公司代码",
  `ReinsurerName` varchar(256) NOT NULL COMMENT "再保险公司名称",
  `ReInsuranceContNo` varchar(64) NOT NULL COMMENT "再保险合同号码",
  `ReInsuranceContName` varchar(256) NOT NULL COMMENT "再保险合同名称",
  `ReinsurancePremium` decimal(20, 2) NOT NULL DEFAULT "0" COMMENT "分保费",
  `ReinsuranceCommssionRate` decimal(20, 2) NOT NULL DEFAULT "0" COMMENT "分保佣金率",
  `ReinsuranceCommssion` decimal(20, 2) NOT NULL DEFAULT "0" COMMENT "分保佣金",
  `ReturnReinsurancePremium` decimal(20, 2) NOT NULL DEFAULT "0" COMMENT "退回分保费",
  `ReturnReinsuranceCommssion` decimal(20, 2) NOT NULL DEFAULT "0" COMMENT "退回分保佣金",
  `ReturnSurrenderPay` decimal(20, 2) NOT NULL DEFAULT "0" COMMENT "摊回退保金",
  `ReturnClaimPay` decimal(20, 2) NOT NULL DEFAULT "0" COMMENT "摊回理赔款",
  `ReturnMaturity` decimal(20, 2) NOT NULL DEFAULT "0" COMMENT "摊回满期金",
  `ReturnAnnuity` decimal(20, 2) NOT NULL DEFAULT "0" COMMENT "摊回年金",
  `ReturnLivBene` decimal(20, 2) NOT NULL DEFAULT "0" COMMENT "摊回生存金",
  `AccountStatus` varchar(4) NOT NULL COMMENT "账单状态（1=有效,2=无效）",
  `PairingStatus` varchar(4) NOT NULL COMMENT "结算状态（1=未结算,2=已结算）",
  `PairingDate` date NULL COMMENT "结算日期",
  `Currency` varchar(4) NOT NULL COMMENT "货币代码",
  `CurrentRate` decimal(20, 6) NULL COMMENT "结算汇率",
  `SettleBillNo` varchar(64) NULL COMMENT "结算账单号",
  `ContImportStatus` tinyint(4) NOT NULL COMMENT "首续期明细导入状态（0=未导入,1=已导入,2=不需要导入）",
  `ContImportRemark` varchar(128) NULL COMMENT "首续期明细导入描述",
  `EdorImportStatus` tinyint(4) NOT NULL COMMENT "保全明细导入状态（0=未导入,1=已导入,2=不需要导入）",
  `EdorImportRemark` varchar(128) NULL COMMENT "保全明细导入描述",
  `ClaimImportStatus` tinyint(4) NOT NULL COMMENT "理赔明细导入状态（0=未导入,1=已导入,2=不需要导入）",
  `ClaimImportRemark` varchar(128) NULL COMMENT "理赔明细导入描述",
  `BenefitImportStatus` tinyint(4) NOT NULL COMMENT "生存金明细导入状态（0=未导入,1=已导入,2=不需要导入）",
  `BenefitImportRemark` varchar(128) NULL COMMENT "生存金明细导入描述",
  `ReportYear` int(11) NOT NULL COMMENT "所属年份",
  `ReportMonth` tinyint(4) NOT NULL COMMENT "所属月份",
  `AccountPeriod` varchar(64) NOT NULL COMMENT "所属账期",
  `DataSource` tinyint(4) NOT NULL COMMENT "数据来源（0=系统,1=人工）",
  `PushStatus` tinyint(4) NOT NULL DEFAULT "0" COMMENT "推送状态（0=未推送,1=已推送）",
  `PushDate` date NULL COMMENT "推送日期",
  `PushBy` varchar(64) NULL COMMENT "推送人",
  `Remark` varchar(128) NULL COMMENT "备注",
  `IsDel` tinyint(4) NULL DEFAULT "0" COMMENT "是否删除(0=未删除,1=已删除)",
  `CreateBy` varchar(64) NULL COMMENT "创建人",
  `CreateTime` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "创建时间",
  `UpdateBy` varchar(64) NULL COMMENT "修改人",
  `UpdateTime` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "修改时间"
) ENGINE=OLAP
PRIMARY KEY(`Id`)
COMMENT "保单登记再保账单信息表"
DISTRIBUTED BY HASH(`Id`) BUCKETS 16
PROPERTIES (
"compression" = "LZ4",
"enable_persistent_index" = "true",
"fast_schema_evolution" = "false",
"replicated_storage" = "true",
"replication_num" = "1"
);