-- =============================================
-- 系统配置初始化SQL
-- 生成时间：2025-06-30
-- =============================================

-- 系统基础配置
INSERT INTO t_sys_config (ConfigKey, ConfigValue, ConfigType, ConfigGroup, ConfigName, ConfigDesc, IsSystem, IsEncrypt, SortOrder, Status) VALUES
('system.company.code', '000166', 'string', 'system', '保险机构代码', '弘康人寿保险股份有限公司机构代码', 1, 0, 1, 1),
('system.company.name', '弘康人寿保险股份有限公司', 'string', 'system', '保险机构名称', '弘康人寿保险股份有限公司全称', 1, 0, 2, 1),
('system.manage.com', '000166', 'string', 'system', '管理机构代码', '默认管理机构代码', 1, 0, 3, 1);

-- 监管报送配置
INSERT INTO t_sys_config (ConfigKey, ConfigValue, ConfigType, ConfigGroup, ConfigName, ConfigDesc, IsSystem, IsEncrypt, SortOrder, Status) VALUES
('regulator.report.url', 'https://api.regulator.gov.cn/report', 'string', 'regulator', '监管报送接口地址', '监管机构数据报送接口地址', 1, 0, 10, 1),
('regulator.report.timeout', '30000', 'number', 'regulator', '报送超时时间', '监管数据报送超时时间（毫秒）', 1, 0, 11, 1),
('regulator.report.retry.times', '3', 'number', 'regulator', '报送重试次数', '监管数据报送失败重试次数', 1, 0, 12, 1),
('regulator.report.batch.size', '1000', 'number', 'regulator', '批量报送大小', '单次批量报送数据条数', 1, 0, 13, 1);

-- 数据处理配置
INSERT INTO t_sys_config (ConfigKey, ConfigValue, ConfigType, ConfigGroup, ConfigName, ConfigDesc, IsSystem, IsEncrypt, SortOrder, Status) VALUES
('data.import.max.size', '10000', 'number', 'data', '导入最大条数', '单次数据导入最大条数限制', 1, 0, 20, 1),
('data.export.max.size', '50000', 'number', 'data', '导出最大条数', '单次数据导出最大条数限制', 1, 0, 21, 1),
('data.generate.batch.size', '5000', 'number', 'data', '数据生成批次大小', '数据生成处理批次大小', 1, 0, 22, 1);

-- 文件处理配置
INSERT INTO t_sys_config (ConfigKey, ConfigValue, ConfigType, ConfigGroup, ConfigName, ConfigDesc, IsSystem, IsEncrypt, SortOrder, Status) VALUES
('file.upload.max.size', '10485760', 'number', 'file', '文件上传最大大小', '文件上传最大大小（字节），默认10MB', 1, 0, 30, 1),
('file.upload.allowed.types', '.xlsx,.xls,.csv', 'string', 'file', '允许上传文件类型', '允许上传的文件扩展名，逗号分隔', 1, 0, 31, 1),
('file.temp.path', '/tmp/reinsurance', 'string', 'file', '临时文件路径', '临时文件存储路径', 1, 0, 32, 1);

-- 缓存配置
INSERT INTO t_sys_config (ConfigKey, ConfigValue, ConfigType, ConfigGroup, ConfigName, ConfigDesc, IsSystem, IsEncrypt, SortOrder, Status) VALUES
('cache.dict.expire.time', '3600', 'number', 'cache', '字典缓存过期时间', '数据字典缓存过期时间（秒）', 1, 0, 40, 1),
('cache.config.expire.time', '1800', 'number', 'cache', '配置缓存过期时间', '系统配置缓存过期时间（秒）', 1, 0, 41, 1);

-- 日志配置
INSERT INTO t_sys_config (ConfigKey, ConfigValue, ConfigType, ConfigGroup, ConfigName, ConfigDesc, IsSystem, IsEncrypt, SortOrder, Status) VALUES
('log.level', 'INFO', 'string', 'log', '日志级别', '系统日志级别（DEBUG,INFO,WARN,ERROR）', 1, 0, 50, 1),
('log.retention.days', '30', 'number', 'log', '日志保留天数', '系统日志文件保留天数', 1, 0, 51, 1),
('log.max.file.size', '100MB', 'string', 'log', '日志文件最大大小', '单个日志文件最大大小', 1, 0, 52, 1);

-- 安全配置
INSERT INTO t_sys_config (ConfigKey, ConfigValue, ConfigType, ConfigGroup, ConfigName, ConfigDesc, IsSystem, IsEncrypt, SortOrder, Status) VALUES
('security.password.min.length', '8', 'number', 'security', '密码最小长度', '用户密码最小长度要求', 1, 0, 60, 1),
('security.session.timeout', '1800', 'number', 'security', '会话超时时间', '用户会话超时时间（秒）', 1, 0, 61, 1),
('security.login.max.attempts', '5', 'number', 'security', '登录最大尝试次数', '用户登录失败最大尝试次数', 1, 0, 62, 1);

-- 业务配置
INSERT INTO t_sys_config (ConfigKey, ConfigValue, ConfigType, ConfigGroup, ConfigName, ConfigDesc, IsSystem, IsEncrypt, SortOrder, Status) VALUES
('business.default.currency', 'CNY', 'string', 'business', '默认货币代码', '业务处理默认货币代码', 1, 0, 70, 1),
('business.decimal.places', '4', 'number', 'business', '金额小数位数', '金额字段保留小数位数', 1, 0, 71, 1),
('business.report.year.range', '5', 'number', 'business', '报表年份范围', '报表查询年份范围限制', 1, 0, 72, 1);

-- 邮件配置
INSERT INTO t_sys_config (ConfigKey, ConfigValue, ConfigType, ConfigGroup, ConfigName, ConfigDesc, IsSystem, IsEncrypt, SortOrder, Status) VALUES
('mail.smtp.host', 'smtp.company.com', 'string', 'mail', 'SMTP服务器地址', '邮件发送SMTP服务器地址', 0, 0, 80, 0),
('mail.smtp.port', '587', 'number', 'mail', 'SMTP服务器端口', '邮件发送SMTP服务器端口', 0, 0, 81, 0),
('mail.smtp.username', '', 'string', 'mail', 'SMTP用户名', '邮件发送SMTP用户名', 0, 1, 82, 0),
('mail.smtp.password', '', 'string', 'mail', 'SMTP密码', '邮件发送SMTP密码', 0, 1, 83, 0),
('mail.from.address', '<EMAIL>', 'string', 'mail', '发件人地址', '系统邮件发件人地址', 0, 0, 84, 0);

-- 定时任务配置
INSERT INTO t_sys_config (ConfigKey, ConfigValue, ConfigType, ConfigGroup, ConfigName, ConfigDesc, IsSystem, IsEncrypt, SortOrder, Status) VALUES
('job.data.generate.cron', '0 0 2 * * ?', 'string', 'job', '数据生成定时任务', '数据生成定时任务执行时间表达式', 1, 0, 90, 1),
('job.report.push.cron', '0 0 8 * * ?', 'string', 'job', '报表推送定时任务', '监管报表推送定时任务执行时间表达式', 1, 0, 91, 1),
('job.cleanup.cron', '0 0 1 * * ?', 'string', 'job', '数据清理定时任务', '临时数据清理定时任务执行时间表达式', 1, 0, 92, 1);
