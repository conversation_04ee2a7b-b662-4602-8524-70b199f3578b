# 数据库DDL说明文档

## 概述

本目录包含弘康人寿再保险数据报送系统的完整数据库DDL脚本和初始化数据。

## 文件说明

### 核心DDL文件

| 文件名 | 说明 | 用途 |
|--------|------|------|
| `init.sql` | 数据库表结构DDL | 创建所有业务表和索引 |
| `dict_data.sql` | 数据字典初始化 | 初始化系统字典数据 |
| `config_data.sql` | 系统配置初始化 | 初始化系统配置参数 |
| `menu.sql` | 菜单权限初始化 | 初始化系统菜单和权限 |
| `execute_ddl.sql` | 执行脚本 | DDL执行顺序和验证脚本 |

### 表结构说明

#### 业务数据表

| 表名 | 表编号 | 说明 | 主要用途 |
|------|--------|------|----------|
| `t_dws_prp_product` | TB0001 | 保单登记再保产品信息表 | 存储再保险产品基础信息 |
| `t_dws_prp_insure_cont` | TB0002 | 保单登记再保合同信息表 | 存储再保险合同信息 |
| `t_dws_prp_account` | TB0003 | 保单登记再保账单信息表 | 存储再保险账单信息 |
| `t_dws_prp_cont` | TB0004 | 再保首续期险种明细表 | 存储首续期业务明细 |
| `t_dws_prp_endor` | TB0005 | 再保保全险种明细表 | 存储保全业务明细 |
| `t_dws_prp_claim` | TB0006 | 再保理赔险种明细表 | 存储理赔业务明细 |
| `t_dws_prp_benefit` | TB0007 | 再保生存金险种明细表 | 存储生存金业务明细 |

#### 系统管理表

| 表名 | 表编号 | 说明 | 主要用途 |
|------|--------|------|----------|
| `t_dws_regulatory_report` | TB0008 | 监管报表推送状态表 | 管理监管报表推送状态 |
| `t_sys_config` | TB0009 | 系统配置表 | 存储系统配置参数 |
| `t_sys_dict_data` | TB0010 | 数据字典表 | 存储字典数据项 |
| `t_sys_dict_type` | TB0011 | 数据字典类型表 | 存储字典类型定义 |

## 执行步骤

### 1. 环境准备

确保数据库环境已准备就绪：
- MySQL 8.0+ 或 StarRocks 2.5+
- 具有DDL执行权限的数据库用户
- 足够的存储空间

### 2. 执行DDL

按以下顺序执行SQL脚本：

```sql
-- 1. 创建表结构
source docs/sql/init.sql;

-- 2. 初始化数据字典
source docs/sql/dict_data.sql;

-- 3. 初始化系统配置
source docs/sql/config_data.sql;

-- 4. 初始化菜单权限（可选）
source docs/sql/menu.sql;
```

### 3. 验证安装

执行验证脚本检查安装结果：

```sql
source docs/sql/execute_ddl.sql;
```

查看验证结果：
- 表创建情况
- 字典数据初始化情况
- 系统配置初始化情况
- 索引创建情况

## 数据库设计特点

### 1. 字段设计规范

- **主键**: 所有表使用`Id`作为自增主键
- **审计字段**: 包含`CreateBy`, `CreateTime`, `UpdateBy`, `UpdateTime`
- **软删除**: 使用`IsDel`字段实现软删除
- **状态管理**: 使用`PushStatus`管理数据推送状态
- **数据来源**: 使用`DataSource`区分系统生成和人工录入

### 2. 索引设计

为提高查询性能，在以下字段上创建了索引：
- 公司代码 (`CompanyCode`)
- 推送状态 (`PushStatus`)
- 数据来源 (`DataSource`)
- 报表年份 (`ReportYear`/`PolYear`)
- 保单号 (`PolicyNo`)

### 3. 数据类型选择

- **金额字段**: 使用`decimal(20, 4)`确保精度
- **日期字段**: 使用`date`类型存储日期
- **状态字段**: 使用`tinyint`节省存储空间
- **编码字段**: 根据业务需要选择合适的`varchar`长度

## 字典数据说明

### 核心字典类型

| 字典类型 | 说明 | 用途 |
|----------|------|------|
| `prp_gp_flag` | 团个性质 | 区分个险、团险 |
| `prp_main_product_flag` | 主附险性质 | 区分主险、附加险 |
| `prp_event_type` | 业务类型 | 区分新单、续期、续保 |
| `prp_cont_pol_duty_status` | 保单状态 | 保单生命周期状态 |
| `prp_cert_type` | 证件类型 | 被保人证件类型 |
| `prp_reinsur_mode` | 分保方式 | 再保险分保方式 |
| `regulator_report_push_status` | 推送状态 | 监管报送推送状态 |
| `regulator_report_data_source` | 数据来源 | 数据来源标识 |

## 系统配置说明

### 配置分组

| 配置分组 | 说明 | 主要配置项 |
|----------|------|------------|
| `system` | 系统基础配置 | 公司代码、公司名称等 |
| `regulator` | 监管报送配置 | 报送接口、超时时间等 |
| `data` | 数据处理配置 | 导入导出限制等 |
| `file` | 文件处理配置 | 文件大小限制等 |
| `cache` | 缓存配置 | 缓存过期时间等 |
| `log` | 日志配置 | 日志级别、保留时间等 |
| `security` | 安全配置 | 密码策略、会话超时等 |
| `business` | 业务配置 | 货币代码、小数位数等 |
| `mail` | 邮件配置 | SMTP服务器配置等 |
| `job` | 定时任务配置 | 任务执行时间等 |

## 注意事项

### 1. 数据安全

- 敏感配置项已标记为加密存储
- 生产环境请修改默认配置值
- 定期备份数据库

### 2. 性能优化

- 根据实际数据量调整分桶数量
- 定期分析查询性能并优化索引
- 考虑数据分区策略

### 3. 维护建议

- 定期清理软删除数据
- 监控表空间使用情况
- 定期更新统计信息

## 故障排除

### 常见问题

1. **表创建失败**
   - 检查数据库权限
   - 确认存储引擎支持
   - 检查字符集设置

2. **索引创建失败**
   - 检查字段是否存在
   - 确认索引名称唯一性
   - 检查存储空间

3. **数据初始化失败**
   - 检查外键约束
   - 确认字符集兼容性
   - 检查数据格式

### 联系支持

如遇到问题，请联系系统管理员或查看相关日志文件。
