-- =============================================
-- 保单登记再保产品信息管理菜单SQL
-- 生成时间：2025-06-23
-- =============================================

-- 请根据实际的父菜单ID替换下面的parent_id值
INSERT INTO alm.sys_menu (menu_name,parent_id,order_num,`path`,component,query,route_name,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('保单登记再保产品信息管理',1,1,'dwsPrpProduct','dws/prp/product/index','','',1,0,'C','0','0','dws:prp:product:list','table','admin','2025-06-23 12:44:11','',NULL,'保单登记再保产品信息管理菜单');

-- 功能权限菜单（请根据上面插入的菜单ID替换parent_id）
INSERT INTO alm.sys_menu (menu_name,parent_id,order_num,`path`,component,query,route_name,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('保单登记再保产品信息查询',0,1,'','','','',1,0,'F','0','0','dws:prp:product:query','#','admin','2025-06-23 12:44:11','',NULL,''),
('保单登记再保产品信息新增',0,2,'','','','',1,0,'F','0','0','dws:prp:product:add','#','admin','2025-06-23 12:44:11','',NULL,''),
('保单登记再保产品信息修改',0,3,'','','','',1,0,'F','0','0','dws:prp:product:edit','#','admin','2025-06-23 12:44:11','',NULL,''),
('保单登记再保产品信息删除',0,4,'','','','',1,0,'F','0','0','dws:prp:product:remove','#','admin','2025-06-23 12:44:11','',NULL,''),
('保单登记再保产品信息导出',0,5,'','','','',1,0,'F','0','0','dws:prp:product:export','#','admin','2025-06-23 12:44:11','',NULL,''),
('保单登记再保产品信息导入',0,6,'','','','',1,0,'F','0','0','dws:prp:product:import','#','admin','2025-06-23 12:44:11','',NULL,''),
('保单登记再保产品信息推送',0,7,'','','','',1,0,'F','0','0','dws:prp:product:push','#','admin','2025-06-26 12:44:11','',NULL,''),
('保单登记再保产品信息生成',0,8,'','','','',1,0,'F','0','0','dws:prp:product:generate','#','admin','2025-06-26 12:44:11','',NULL,'');

-- =============================================
-- 保单登记再保产品信息管理菜单SQL
-- 生成时间：2025-06-23
-- =============================================

-- 请根据实际的父菜单ID替换下面的parent_id值
INSERT INTO sys_menu (menu_name,parent_id,order_num,`path`,component,query,route_name,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('保单登记再保产品信息管理',1,1,'product','dws/prp/product/index','','',1,0,'C','0','0','dws:prp:product:list','table','admin','2025-06-23 12:44:11','',NULL,'保单登记再保产品信息管理菜单');

-- 功能权限菜单（请根据上面插入的菜单ID替换parent_id）
INSERT INTO sys_menu (menu_name,parent_id,order_num,`path`,component,query,route_name,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('保单登记再保产品信息查询',0,1,'','','','',1,0,'F','0','0','dws:prp:product:query','#','admin','2025-06-23 12:44:11','',NULL,''),
('保单登记再保产品信息新增',0,2,'','','','',1,0,'F','0','0','dws:prp:product:add','#','admin','2025-06-23 12:44:11','',NULL,''),
('保单登记再保产品信息修改',0,3,'','','','',1,0,'F','0','0','dws:prp:product:edit','#','admin','2025-06-23 12:44:11','',NULL,''),
('保单登记再保产品信息删除',0,4,'','','','',1,0,'F','0','0','dws:prp:product:remove','#','admin','2025-06-23 12:44:11','',NULL,''),
('保单登记再保产品信息导出',0,5,'','','','',1,0,'F','0','0','dws:prp:product:export','#','admin','2025-06-23 12:44:11','',NULL,''),
('保单登记再保产品信息导入',0,6,'','','','',1,0,'F','0','0','dws:prp:product:import','#','admin','2025-06-23 12:44:11','',NULL,''),
('保单登记再保产品信息推送',0,7,'','','','',1,0,'F','0','0','dws:prp:product:push','#','admin','2025-06-26 12:44:11','',NULL,''),
('保单登记再保产品信息生成',0,8,'','','','',1,0,'F','0','0','dws:prp:product:generate','#','admin','2025-06-26 12:44:11','',NULL,'');
