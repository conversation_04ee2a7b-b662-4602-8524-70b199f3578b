-- =============================================
-- DDL执行脚本
-- 生成时间：2025-06-30
-- 说明：按顺序执行以下SQL文件来初始化数据库
-- =============================================

-- 1. 创建数据库表结构
-- 执行命令：source docs/sql/init.sql;

-- 2. 初始化数据字典
-- 执行命令：source docs/sql/dict_data.sql;

-- 3. 初始化系统配置
-- 执行命令：source docs/sql/config_data.sql;

-- 4. 初始化菜单权限（如果需要）
-- 执行命令：source docs/sql/menu.sql;

-- =============================================
-- 验证脚本
-- =============================================

-- 验证表是否创建成功
SELECT 
    TABLE_NAME as '表名',
    TABLE_COMMENT as '表注释',
    TABLE_ROWS as '数据行数'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME LIKE 't_%'
ORDER BY TABLE_NAME;

-- 验证字典数据是否初始化成功
SELECT 
    DictType as '字典类型',
    COUNT(*) as '字典项数量'
FROM t_sys_dict_data 
WHERE Status = '0'
GROUP BY DictType
ORDER BY DictType;

-- 验证系统配置是否初始化成功
SELECT 
    ConfigGroup as '配置分组',
    COUNT(*) as '配置项数量'
FROM t_sys_config 
WHERE Status = 1
GROUP BY ConfigGroup
ORDER BY ConfigGroup;

-- 验证索引是否创建成功
SELECT 
    TABLE_NAME as '表名',
    INDEX_NAME as '索引名',
    COLUMN_NAME as '索引字段'
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME LIKE 't_%'
    AND INDEX_NAME != 'PRIMARY'
ORDER BY TABLE_NAME, INDEX_NAME;

-- =============================================
-- 清理脚本（谨慎使用）
-- =============================================

-- 如果需要重新初始化，可以执行以下清理脚本
-- 注意：这将删除所有数据，请谨慎使用

/*
-- 删除所有表
DROP TABLE IF EXISTS t_dws_regulatory_report;
DROP TABLE IF EXISTS t_dws_prp_benefit;
DROP TABLE IF EXISTS t_dws_prp_claim;
DROP TABLE IF EXISTS t_dws_prp_endor;
DROP TABLE IF EXISTS t_dws_prp_cont;
DROP TABLE IF EXISTS t_dws_prp_account;
DROP TABLE IF EXISTS t_dws_prp_insure_cont;
DROP TABLE IF EXISTS t_dws_prp_product;
DROP TABLE IF EXISTS t_sys_dict_data;
DROP TABLE IF EXISTS t_sys_dict_type;
DROP TABLE IF EXISTS t_sys_config;
*/
